const axios = require('axios')
const querystring = require('querystring')
let _ = {
  updateConf: (option) => {
    const pushurl = option.env === 'uat'
    ? 'http://uat-sven.bilibili.co/x/admin/config/home/<USER>/update' : 'http://sven.bilibili.co/x/admin/config/home/<USER>/update'

    let params = querystring.stringify({
      app_name: 'mobile.studio.audit-manager-server',
      env: option.env,
      zone: 'sh001',
      tree_id: 121252,
      token: option.token,
      user: 'jianghuirong',
      data: JSON.stringify(option.data)
    })
    return axios.post(pushurl, params).then((res) => {
      console.log(`${option.env} updateconf`, res.data)
      console.log(`${option.env} update success`)
    }).catch((err) => {
      console.error(`Error ${option.env} ${err.message}`)
    })
  }
}

module.exports = _
