// sven-webpack-plugin.js 文件（独立模块）
const path = require('path')
const { updateConf } = require('./sven')
const { getCommitInfo, getMasterCommitId } = require('../utils')

const { commitId } = getCommitInfo()

// 同步 sven 配置中心 prod
const pro_csvenEnv = 'prod'
const pro_csvenToken = 'dc4edb8b34408a731d10d14e3dc37cf6'

// pre
const pre_csvenEnv = 'pre'
const pre_csvenToken = '1a3abd6ac6c75425c9cc14d8c40502f6'

// uat
const uat_csvenEnv = 'uat'
const uat_csvenToken = '5f07de8dea9e46e760ca9626b471e073'

// 模块对外暴露的 js 函数
function svenConfigPlugin() {}

// 原型定义一个 apply 函数，并注入了 compiler 对象
svenConfigPlugin.prototype.apply = function (compiler) {
  // mounted webpack hook
  compiler.plugin('emit', function (compilation, callback) {
    callback();
  })

  compiler.plugin('done', function (stats) {
    // 操作 compilation 对象的内部数据
    const infs = compiler.inputFileSystem
    const jsonStats = stats.toJson({
      chunks: true,
    })
    if(process.env.NODE_ENV === 'production') {
      jsonStats.assets.forEach(({ name }) => {
        if (name === 'index.html') {
          const filepath = path.join(compiler.outputPath, name);
          infs.readFile(filepath, (err, data) => {
            let obj = {}
            if (!err) {
              exist = true
              obj = {
                data: '' + data
              }
            }

            const newData = [{
              name,
              comment: JSON.stringify(obj),
              mark: `${name} changed with ${commitId}`
            }]

            const pro_update = {
              env: pro_csvenEnv,
              token: pro_csvenToken,
              data: newData
            }
            const pre_update = {
              env: pre_csvenEnv,
              token: pre_csvenToken,
              data: newData
            }
            const uat_update = {
              env: uat_csvenEnv,
              token: uat_csvenToken,
              data: newData
            }
            updateConf(uat_update)
            updateConf(pre_update)
              .then(() => getMasterCommitId())
              .then(masterCommitID => {
                console.log('masterCommitID', masterCommitID)
                console.log('commitId', commitId)
                if (masterCommitID === commitId) {
                  // master 分支同步sven prod 环境
                  return updateConf(pro_update)
                }
              }).catch(e => {
                console.log('pre|prod sync sven err', e.stack)
              })
          })
        }
      })
    }
  })
}

module.exports = svenConfigPlugin
