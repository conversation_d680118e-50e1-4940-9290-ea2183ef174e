import { adminBase, templateBase, apiBase } from '@/api/base'

export const TRACK_TYPE_ENUM = {
  UV: 'uv', //uv
  PV: 'pv', //访问
  DURATION: 'duration', // 停留时长
  SOURCE: 'source',
  RENDER: 'render', //渲染时长 mixin引入
  FMP: 'fmp',
  RESPONSE_TIME: 'response-time' // 接口响应时间
}

export const TRACK_PATH = [
  {
    path: '/workbench/todo/newer',
    key: '/工作台/我的待办/新人模式',
    type: []
  },
  {
    path: '/workbench/todolist',
    key: '/工作台/我的待办',
    type: [TRACK_TYPE_ENUM.DURATION]
  },
  {
    path: '/workbench/todo/video/detail',
    key: '/工作台/我的待办/视频详情页',
    type: []
  },
  {
    path: '/audit/tasks/detail/11',
    key: '稿件详情页',
    type: [TRACK_TYPE_ENUM.DURATION, TRACK_TYPE_ENUM.SOURCE]
  },
  {
    path: '/archive/archive-video-task/resource/detail',
    key: '/视频审核/视频审核详情',
    type: [TRACK_TYPE_ENUM.DURATION]
  },
  {
    path: '/archive/archive-video-task/resource/list',
    key: '/稿件审核/视频审核/一审视频列表',
    type: [TRACK_TYPE_ENUM.DURATION]
  },
  {
    path: '/workbench/todo-config/detail',
    key: '/工作台/我的待办/配置详情页',
    type: [TRACK_TYPE_ENUM.DURATION]
  },
  {
    path: '/workbench/todo/push',
    key: '/工作台/我的待办/推送模式',
    type: [TRACK_TYPE_ENUM.DURATION]
  },
  {
    path: '/v2/archive/detail',
    key: '稿件详情页V2',
    type: [TRACK_TYPE_ENUM.DURATION, TRACK_TYPE_ENUM.SOURCE]
  },
]

export const TRACK_URL = [
  {
    url: '/x/admin/aegis-gateway/task/next',
    key: '任务详情',
    type: [TRACK_TYPE_ENUM.RESPONSE_TIME]
  },
  {
    url: '/x/admin/aegis-gateway/sensitive-word/filter',
    key: '敏感词接口',
    type: [TRACK_TYPE_ENUM.RESPONSE_TIME]
  },
  {
    url: '/x/admin/aegis-gateway/task/batchsubmit',
    key: '批量提交接口',
    type: [TRACK_TYPE_ENUM.RESPONSE_TIME]
  },
  {
    url: '/x/admin/aegis-gateway/task/batchsubmit/v2',
    key: '批量提交接口v2',
    type: [TRACK_TYPE_ENUM.RESPONSE_TIME]
  },
  {
    url: '/x/admin/aegis-gateway/task/submit',
    key: '提交接口',
    type: [TRACK_TYPE_ENUM.RESPONSE_TIME]
  },
  {
    url: `${adminBase}/aegis/engine/auth`,
    key: '全局获取用户信息',
    type: [TRACK_TYPE_ENUM.RESPONSE_TIME]
  },
  {
    url: `${templateBase}/audit/all/dynamic-route`,
    key: '获取所有业务下的详情页路由',
    type: [TRACK_TYPE_ENUM.RESPONSE_TIME]
  },
  {
    url: `${adminBase}/workbench/todo/business/list`,
    key: '获取业务列表',
    type: [TRACK_TYPE_ENUM.RESPONSE_TIME]
  },
  {
    url: `${adminBase}/workbench/todo/receiver/list`,
    key: '获取业务列表',
    type: [TRACK_TYPE_ENUM.RESPONSE_TIME]
  },
  {
    url: `${adminBase}/videoup/archive/typeid/list`,
    key: '获取分区列表',
    type: [TRACK_TYPE_ENUM.RESPONSE_TIME]
  },
  {
    url: `${templateBase}/karl/archive-info/v2`,
    key: 'getInfoKarlV2',
    type: [TRACK_TYPE_ENUM.RESPONSE_TIME]
  },
  {
    url: `${templateBase}/karl/archive-info/v3`,
    key: 'getInfoKarlV3',
    type: [TRACK_TYPE_ENUM.RESPONSE_TIME]
  },
  {
    url: `${adminBase}/videoup/archive/common/data`,
    key: 'getConstant',
    type: [TRACK_TYPE_ENUM.RESPONSE_TIME]
  },
  {
    url: `${adminBase}/filter/rule/config`,
    key: 'getRuleConfig',
    type: [TRACK_TYPE_ENUM.RESPONSE_TIME]
  },
  {
    url: `${adminBase}/aegis-gateway/archive/video/info`,
    key: 'getInfo',
    type: [TRACK_TYPE_ENUM.RESPONSE_TIME]
  },
  {
    url: `${apiBase}/archive/filter/archivedetail`,
    key: 'getKeywords',
    type: [TRACK_TYPE_ENUM.RESPONSE_TIME]
  },
  {
    url: `${adminBase}/videoup/archive/video/shots`,
    key: 'getVideoshots',
    type: [TRACK_TYPE_ENUM.RESPONSE_TIME]
  },
  {
    url: `${adminBase}/videoup/archive/video/playurl`,
    key: '获取视频播放地址',
    type: [TRACK_TYPE_ENUM.RESPONSE_TIME]
  },
  {
    url: `${adminBase}/videoup/archive/video/probe`,
    key: 'getMediaInfo',
    type: [TRACK_TYPE_ENUM.RESPONSE_TIME]
  },
  {
    url: `${adminBase}/videoup/search/copyright/v2`,
    key: '获取版权信息',
    type: [TRACK_TYPE_ENUM.RESPONSE_TIME]
  },
  {
    url: `${adminBase}/videoup/archive/video/crash`,
    key: 'getCrashInfo',
    type: [TRACK_TYPE_ENUM.RESPONSE_TIME]
  },
  {
    url: `${adminBase}/content-classify/video-audit/tag/list`,
    key: 'getContentClassifyTagList',
    type: [TRACK_TYPE_ENUM.RESPONSE_TIME]
  },
  {
    url: `${adminBase}/videoup/search/video`,
    key: 'getArchiveResourceList',
    type: [TRACK_TYPE_ENUM.RESPONSE_TIME]
  },
  {
    url: `${templateBase}/audit/workbench/detail`,
    key: 'getTemplate',
    type: [TRACK_TYPE_ENUM.RESPONSE_TIME]
  },
  {
    url: `${adminBase}/aegis/business/config/reserve`,
    key: 'getExtra',
    type: [TRACK_TYPE_ENUM.RESPONSE_TIME]
  },
  {
    url: `${adminBase}/workbench/todo/resource/detail`,
    key: '工作台-待办-获取资源详情',
    type: [TRACK_TYPE_ENUM.RESPONSE_TIME]
  },
  {
    url: `${adminBase}/workbench/todo/opers`,
    key: '工作台-待办-获取操作项',
    type: [TRACK_TYPE_ENUM.RESPONSE_TIME]
  },
  {
    url: `${adminBase}/workbench/todo/resource/log`,
    key: '工作台-待办-获取资源操作历史',
    type: [TRACK_TYPE_ENUM.RESPONSE_TIME]
  },
  {
    url: `${adminBase}/workbench/todo/trans/list`,
    key: '工作台-待办-获取流转待办列表',
    type: [TRACK_TYPE_ENUM.RESPONSE_TIME]
  }
]
