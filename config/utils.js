// 获取commit信息
const { logSync } = require('@bilibili/gitcmd')
const axios = require('axios')

exports.getCommitInfo = (cwd) => {
  let logmsg = ''
  let commitId = ''
  let author = ''
  try {
    logmsg = logSync(['-n1'], cwd || process.cwd())
    commitId = logmsg.match(/^commit\ {1}(\w+)/)[1]
  } catch (e) {
    console.error(e);
    logmsg = 'no_commit_message'
    commitId = 'no_commit_id'
  }

  try {
    author = logmsg.match(/Author: {1}(\w+)/)[1]
  } catch (e) {
    console.error(e);
    author = 'unknown'
  }

  return {
    logmsg,
    commitId,
    author
  }
}

exports.getMasterCommitId = () => {
  // curl --header "PRIVATE-TOKEN: HTvaVkx3kUBmz6mDSeHT" "https://git.bilibili.co/api/v4/projects/6004/repository/commits/master"
  const token = 'HTvaVkx3kUBmz6mDSeHT' // token created at 2019-07-10 expired 1 year
  const url = 'https://git.bilibili.co/api/v4/projects/6004/repository/commits/master'
  return axios({
    url,
    method: 'get',
    headers: {
      'PRIVATE-TOKEN': token
    }
  }).then(({data}) => {
    console.log('master:', data)
    return data.id
  })
}
