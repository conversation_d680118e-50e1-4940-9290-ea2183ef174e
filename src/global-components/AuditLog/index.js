import Vue from 'vue'
import AuditLogDialog from './AuditLog.vue'
import AuditLogApi from '@/api/auditLog.js'
import $notify from '@/lib/notify'

export function onAuditLog(propsData) {
  const AuditLog = Vue.extend(AuditLogDialog)
  const instance = new AuditLog({ el: document.createElement('div'), propsData })

  instance.visible = true
  instance.getLog() // 初始化获取数据

  instance.closeCallback = () => {
    instance.closeCallback = null
    instance.visible = false
    instance.$destroy()
    Vue.nextTick(() => {
      document.body.removeChild(instance.$el)
    })
  }

  // dom 需要在组件生命周期结束时删除
  document.body.appendChild(instance.$el)
}

/**
 * 上报审核日志
 * 功能：和setAuditAction接口功能一致  支持多字段上报和查询
 * @param module 模块Id 区分业务
 * @param params 储存后续需要索引的字段 数组第n+1项对应str_n字段
 * 自定义字段索引：
    str_1 存字符串 匹配  str_1_like字段可以支持模糊搜索
    str_2 存字符串 匹配  str_2_like字段可以支持模糊搜索
    str_3 新开放字段 存数组 用str_3_or精确匹配数组中的某一项
    str_4 新开放字段 存数组 用str_4_or精确匹配数组中的某一项
 * @param extra 额外数据 用于展示
 */

export async function reportAuditLog(module, object_id, content, param = [], extra = {}, ...args) {
    const business = (args && args[0] && args[0].business) || 151
    const paramObj = {}
    param.forEach((v, index) => {
      paramObj[`str_${index + 1}`] = v
    })
    const params = {
      module,
      business,
      record: {
        content,
        object_id,
        ...paramObj
      },
      extra
    }
    try {
      const res = await AuditLogApi.setLog(params)
      if (res?.code !== 0) {
        $notify.error('上报日志失败')
      }
    } catch (e) {
      console.error(`上报日志失败 Error ${e}`)
    }
}
