<template>
  <div class="wk-images" v-show="showing">
    <!-- 按钮 -->
    <ul class="wk-ib-controller">
      <li class="wk-ib-con" @click="close" v-if="showClose">
        <i class="el-icon-top"></i>收起</li>
      <li class="wk-ib-con" @click="$emit('overview', activeIndex)">
        <i class="el-icon-search"></i>查看大图</li>
    </ul>
    <!-- 内容 -->
    <div class="wk-ib-img">
      <div class="wk-ib-img__container">
        <img class="wk-ib-img__content" :src="images[activeIndex] && images[activeIndex].src" :style="rotate" @click="close"/>
      </div>
      <div class="wk-ib-img__prev" v-show="activeIndex !== 0" @click="changeActiveIndex(activeIndex - 1)">
        <i class="el-icon-arrow-left"></i>
      </div>
      <div class="wk-ib-img__next" v-show="activeIndex !== images.length - 1" @click="changeActiveIndex(activeIndex + 1)">
        <i class="el-icon-arrow-right"></i>
      </div>
    </div>
    <!-- 游廊 -->
    <div class="wk-ib-slider">
      <ul class="wk-ib-slider__ul">
        <li class="wk-ib-slider__li" v-for="(image, idx) in images" :key="image.src + idx" :class="[{
          'is-active': activeIndex === idx
        }]" @click="changeActiveIndex(idx)">
          <img :src="image.src" />
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
export default {
  name: 'WkImagesBox',
  data() {
    return {
      rotateStatus: 0, // 0 -> 0度 1 -> 90度 2 -> 180度 3 -> 270度
      activeIndex: 0
    }
  },
  props: {
    images: {
      type: Array,
      default() {
        return []
      }
    },
    showing: {
      type: Boolean,
      default: false
    },
    showClose: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    rotate() {
      return {
        transform: `rotate(${90 * this.rotateStatus }deg)`
      }
    }
  },
  methods: {
    handleTurnLeft() {
      let rotateStatus = this.rotateStatus--
      if (rotateStatus < 0) rotateStatus = 3
      this.rotateStatus = rotateStatus
    },
    handleTurnRight() {
      let rotateStatus = this.rotateStatus++
      if (rotateStatus > 3) rotateStatus = 0
      this.rotateStatus = rotateStatus
    },
    changeActiveIndex (index) {
      this.activeIndex = index
    },
    initActiveIndex(index) {
      this.activeIndex = index
    },
    show(index) {
      this.changeActiveIndex(index || 0)
      this.$emit('update:showing', true)
    },
    close() {
      this.$emit('update:showing', false)
    }
  }
}
</script>
<style lang="stylus" scoped>
.wk-images
  width 100%
  user-select none
  text-align center
  .wk-ib-controller
    height 32px
    background #f4f5f7
    border-radius 4px 4px 0 0
    text-align left
  .wk-ib-con
    font-size 12px
    color var(--text-color)
    line-height 34px
    display inline-block
    margin 0 16px
    cursor pointer
  .wk-ib-img
    width 100%
    background #f4f5f7
    line-height 0
    cursor zoom-out
    position relative
    .wk-ib-img__container
      position relative
    .wk-ib-img__content
      max-width 100%
    .wk-ib-img__prev
      left 0
      top 0
      cursor pointer
      z-index 10
      position absolute
      width 155.4px
      height 100%
      background rgba(0,0,0,0)
      outline none
      border none
      zoom 1
      display flex
      align-items center
      justify-content left
      i
        font-size 80px
        color var(--text-color-reverse)
        display none
      &:hover
        background #888888
        opacity 0.5
        i
          display block
    .wk-ib-img__next
      right 0
      top 0
      cursor pointer
      z-index 10
      position absolute
      width 155.4px
      height 100%
      background rgba(0,0,0,0)
      outline none
      border none
      zoom 1
      display flex
      align-items center
      justify-content right
      i
        font-size 80px
        color var(--text-color-reverse)
        display none
      &:hover
        background #888888
        opacity 0.5
        i
          display block
  .wk-ib-slider
    margin-top 15px
    position relative
    white-space wrap
    .wk-ib-slider__ul
      display flex
      flex-wrap wrap
    .wk-ib-slider__li
      width 54px
      height 54px
      display inline-block
      margin-left 4px
      position relative
      cursor pointer
      opacity 0.5
      border 1px solid transparent
      border-radius 4px
      padding 2px
      &.is-active
        border 1px solid #fb7299
        border-radius 4px
        opacity 1
      img
        width 100%
        height 100%
        border-radius 4px
</style>
