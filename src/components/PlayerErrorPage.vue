<template>
  <div class="absolute top-0 p-[5px] bg-[#000] h-[100%]">
    <div class="text-red-500">解码失败..</div>
    <div class="mt-[10px] text-[#e6a23c]">
      建议本地安装VLC播放器后使用以下方案解决 ↓ ↓ ↓
      <el-button @click="downloadVlcPlayer" type="text">
        (下载VLC播放器)
      </el-button>
    </div>
    <div class="mt-[10px]">
      <p class="mb-[10px] text-[#67c23a]">
        解决方案1: 复制播放地址手动打开VLC播放器进行播放
      </p>
      <el-button type="text" class="mt-[-3px]" @click="copyPlayUrl">
        复制播放地址到剪贴板
      </el-button>
    </div>

    <div class="mt-[15px]">
      <p class="mb-[10px] text-[#67c23a]">
        解决方案2:
        通过浏览器直接打开VLC播放器播放当前媒体流（需注册vlc协议）【推荐】
      </p>
      <div class="mb-[5px]">
        <div class="text-[#fb7299] leading-[20px]">
          <div class="text-[red]">已注册vlc协议的可以忽略以下步骤</div>
          <el-button type="text" @click="downloadVlcProtocolRegisterBat">
            下载协议注册脚本
          </el-button>
          <div>
            将vlc-protocol-register.bat文件放入VLC安装目录（安装目录可以通过右键VLC图标打开，通常是C:\ProgramFiles(x86)\VideoLAN\VLC），然后以管理员身份运行vlc-protocol-register.bat（右键单击该文件并使用以管理员身份运行）
          </div>
        </div>
        <el-button type="primary" class="mt-[10px]" @click="openVlcPlayer">
          打开本地VLC播放器
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import notify from '@/lib/notify'
import { techReportPb } from '@bilibili/bili-mirror'
import { mapState } from 'vuex'

export default {
  props: {
    playUrl: {
      type: String,
      default: ''
    },
    data() {
      return {}
    },
    computed: {
      ...mapState({
        uid: (state) => state.user.uid,
        username: (state) => state.user.username
      })
    },
    mounted() {
      this.mirrorReportRealtime('mounted')
    },
    methods: {
      mirrorReportRealtime(trackType) {
        techReportPb({
          type: 'error',
          eventId: 'main.aegis.ERROR.decodeError',
          msg: {
            url: this.playUrl,
            uid: this.uid,
            username: this.username,
            trackType
          }
        })
      },

      openVlcPlayer() {
        window.open(`vlc://https:${this.playUrl}`)
        this.mirrorReportRealtime('openVlcPlayer')
      },

      downloadVlcProtocolRegisterBat() {
        window.open(
          'https://shylf-inner-boss.bilibili.co/tianshu-log/vlc-protocol/vlc-protocol.bat'
        )
        this.mirrorReportRealtime('downloadVlcProtocolRegisterBat')
      },

      downloadVlcPlayer() {
        window.open('https://www.videolan.org/')
        this.mirrorReportRealtime('downloadVlcPlayer')
      },

      copyPlayUrl() {
        navigator.clipboard.writeText(`https:${this.playUrl}`).then(() => {
          notify.success('复制成功！')
          this.mirrorReportRealtime('copyPlayUrl')
        })
      }
    }
  }
}
</script>
