<template>
  <div class="archive-list">
    <el-row class="header-row sticky-top-bar">
      <!-- <span class="page-name">{{ LIST_MAP[$route.params.id] }}</span> -->
      <Breadcrumb :breadCrumbs="breadCrumbs"/>
      <el-button size="medium" type="primary" @click="toggleSelection">{{
        this.multipleBtn ? '全选' : '取消'
      }}</el-button>
      <el-button
        v-for="item in BATCH_BUTTON_LIST"
        :key="item.name"
        @click="openDialog(item.name)"
        v-track="item.trackId"
        size="medium"
        type="primary"
        plain
        :disabled="isDisabled"
        >{{ item.name }}</el-button
      >
      <!-- 批量添加备注 -->
      <el-button
        v-if="perms.BATCH_ARC_NOTETAG"
        @click="openDialog('批量添加备注')"
        v-track="'click-archive-list-batch-remarks'"
        size="medium"
        type="primary"
        plain
        :disabled="isDisabled"
        >批量添加备注</el-button
      >
      <el-button
        v-if="listType === 21"
        @click="openDialog('批量审核频道')"
        size="medium"
        type="primary"
        plain
        :disabled="isDisabled"
        >批量审核频道</el-button
      >
      <el-button
        v-if="perms.BATCH_ARC_ARGUMENT"
        @click="openDialog('批量修改争议标识')"
        v-track="'click-archive-list-batch-dispute'"
        size="medium"
        type="primary"
        plain
        :disabled="isDisabled"
        >批量修改争议标识</el-button
      >
      <el-button
        size="medium"
        type="primary"
        v-if="perms.ARC_RECHECK && useBackConfig"
        plain
        @click="goRecheckSetting"
        >回查配置</el-button
      >
      <!-- 批量添加备注 -->
      <el-button
        v-if="SHOW_LIMIT_BUTTON.includes(originListType) && perms.BATCH_LIMIT_NOTIFY"
        @click="openDialog('批量限流通知')"
        size="medium"
        type="primary"
        plain
        :disabled="isDisabled"
        >批量限流通知</el-button
      >
      <el-button v-if="originListType === '00'" size="medium" type="info" plain @click="showExtraDialog" v-track="'click-archive-list-export'">
        导出CSV
      </el-button>
      <el-button v-else size="medium" type="info" plain @click="exportCSV" v-track="'click-archive-list-export'">
        导出CSV
      </el-button>
      <el-button @click="transition" size="medium">
        过滤条件
        <i class="el-icon-arrow-up collapse" :style="{ 'transform': !expandShow ? 'rotate(-180deg)' : 'unset'}"></i>
      </el-button>
    </el-row>
    <el-row :class="[expandShow&&'filter-block']">
      <el-collapse-transition>
        <ListFilter
          v-show="expandShow"
          v-if="formItems.length"
          class="filter"
          ref="filter"
          :showSwitch="true"
          :formData="formData"
          :formItems="formItems"
          :filterOptions="filterOptions"
          :displayModel="displayModel"
          @getList="beforeGetTableConfig"
          @resetForm="resetQueryForm"
          @updateDisplayModel="updateDisplayModel"
        >
        </ListFilter>
      </el-collapse-transition>
    </el-row>

    <!-- <el-row class="content-row"> -->
        <!-- <div
          class="expand"
          :class="{ 'expand-height': !expandShow }"
          v-if="formItems.length"
        >
          <i @click="transition" :class="{ 'display-none': !expandShow }">
            <icon name="expand"></icon>
          </i>
        </div> -->
        <el-row class="content-row main-row">
          <el-table
            v-show="displayModel === DISPLAY_MODEL.LIST_MODEL"
            id="archive-list-id"
            border
            stripe
            size="small"
            style="width: 100%"
            ref="archiveTable"
            row-key="id"
            :data="tableData"
            :expand-row-keys="toggleRowExpansion.expandRowKeys"
            @selection-change="handleSelectionChange"
            @expand-change="expandChange"
            @row-click="rowClick"
          >
            <el-table-column type="selection" align="left" fixed width="55">
            </el-table-column>
            <el-table-column
              type="expand"
              align="left"
            >
              <template slot="header" slot-scope="scope">
                <div
                  :style="{
                    cursor: 'pointer',
                    transform: `rotate(${toggleRowExpansion.all ? '90' : '0'}deg)`,
                    transition: '.2s ease-in-out'
                  }"
                  @click="toggleAllExpansion"
                ><i class="el-icon-arrow-right"></i></div>
              </template>
              <template slot-scope="scope">
                <ul class="ml-80 mt-8 mb-8">
                  <li class="mt-8" style="line-height: 16px">稿件简介： <ArchiveTitle :text="scope.row.content" /></li>
                  <li class="mt-8">
                    tag展示：
                    <span v-for="tag in scope.row.tag.split(',')" class="ml-10">
                      <el-tag size="small">{{ tag }}</el-tag>
                    </span>
                  </li>
                </ul>
              </template>
            </el-table-column>
            <el-table-column
              v-for="(column, index) in columns"
              :align="column.align || 'center'"
              :width="column.width"
              :key="index"
              :label="column.label"
              :prop="column.prop"
            >
              <template v-slot="scope">
                <span
                  v-if="column.render"
                  v-html="
                    column.render
                      ? column.render(scope.row)
                      : scope.row[column.prop]
                  "
                  @click.stop
                ></span>
                <div v-else-if="column.prop === 'mid'" @click.stop>
                  <UserInfo
                    :userGroups="scope.row.user_group"
                    :official="scope.row.official_verify"
                    :fans="scope.row.fans"
                  >
                    <a
                      :href="`http://space.bilibili.com/${scope.row.mid}/#!/index`"
                      target="_blank"
                      style="color: var(--link-color)"
                    >
                      {{ scope.row.author }}
                    </a>
                  </UserInfo>
                </div>
                <div v-else-if="column.prop === 'title'" @click.stop>
                  <NewTooltip>
                    <img
                      slot="content"
                      loading="lazy"
                      :src="cutImage(scope.row && scope.row.cover)"
                      width="170px"
                      height="110px"
                    />
                    <p style="color: var(--red); display: inline-block"> [封面1] </p>
                  </NewTooltip>
                  <NewTooltip v-if="scope.row.cover_v2">
                    <img
                      slot="content"
                      loading="lazy"
                      :src="cutImage(scope.row && scope.row.cover_v2)"
                      width="170px"
                      height="110px"
                    />
                    <p style="color: var(--red); display: inline-block"> [封面2] </p>
                  </NewTooltip>
                  <a
                    style="
                      color: var(--link-color);
                      cursor: pointer;
                      display: inline-block;
                      padding: 0px 2px;
                    "
                    target="_blank"
                    :href="`${path}#/audit/tasks/detail/11?business_id=11&oid=${
                      scope.row.id
                    }&list_type=${
                      $route.query.list_type
                    }&review=${review}&back=${encodeURIComponent(
                      $route.fullPath
                    )}`"
                  >
                    <ArchiveTitle :text="scope.row.title" />
                  </a>
                  <span style="color: var(--red)">{{ scope.row.labels }}</span>
                </div>
                <p v-else @click.stop>{{ scope.row[column.prop] }}</p>
              </template>
            </el-table-column>
          </el-table>

          <el-row v-show="displayModel === DISPLAY_MODEL.CARD_MODEL">
            <el-col
              v-for="(archive, index) in tableData"
              :key="archive.id"
              :span="12"
            >
              <ArchiveCard
                ref="archiveCard"
                :multipleSelection="multipleSelection"
                :archive="archive"
                :index="index"
                :review="review"
                @selection-change="handleSelectionChange"
              />
            </el-col>
          </el-row>
        <el-row style="margin-top: 12px">
          <Pagination
            class="pager"
            :pager="pager"
            justify="start"
            showSize="medium"
            @getList="beforeGetTableConfig"
          ></Pagination>
        </el-row>

        <ArchiveDialog
          ref="dialog"
          :multipleSelection="multipleSelection"
          @clearSelection="clearSelection"
        ></ArchiveDialog>
      </el-row>
  </div>
</template>
<script>
import { mapState, mapActions, mapMutations } from 'vuex'
import routeHelper from '@/mixins/route-helper'
import ListFilter from '@/components/ListFilter'
import ArchiveDialog from '@/components/ArchiveDialog'
import * as common from '../pages/contentAudit/common'
import { templateApi } from '@/api/index'
import NewTooltip from '@/components/element-update/Tooltip'
import UserInfo from '@/components/ArchiveList/UserInfo'
import ArchiveCard from '@/components/ArchiveCard.vue'
import Pagination from '@/components/Pagination'
import ArchiveTitle from '@/v2/biz-components/archive/ArchiveTitle'
import { isBoolean } from '../utils/type'
import { report } from '@/utils/index'
import { STATES, LIST_MAP, SHOW_LIMIT_BUTTON, DISPLAY_MODEL } from '@/utils/constant'
import { cutImage } from '@/plugins/bfsImage'
import { rowSelectFilter, downloadCsvInterval } from '@/utils'
import { genHost } from '@/api/utils'
import notify from '@/lib/notify'

const BATCH_BUTTON_LIST = [{
  name: '批量审核',
  trackId: 'click-archive-list-batch-audit'
}, {
  name: '批量IP限制',
  trackId: 'click-archive-list-batch-ip'
}, {
  name: '批量添加属性',
  trackId: 'click-archive-list-batch-attr-add'
}, {
  name: '批量删除属性',
  trackId: 'click-archive-list-batch-attr-delete'
}]

const CSV_URL = '/x/admin/videoup/archive/export?ids='

export const COLORS = {
  0: 'var(--success-color)', // PASS
  1: 'var(--success-color)', // ORANGE_PASS_ALL
  '-15': 'var(--primary-color)', // DISTRIBUTING
  '-9': 'var(--primary-color)', // TRANSCODING
  '-1': 'var(--primary-color)', // PENDING
  '-6': 'var(--primary-color)' // REPAIRED
}

// const MEMBER_ACCESS = 10000 // 普通会员

const LIST_ATTRS_MAP = {
  // 用于显示标题后面的类别
  17: '跳转',
  18: '影视',
  23: '付费',
  25: '合作'
}

const LIST_FLOW_MAP = {
  49: '排行',
  50: '动态',
  53: '搜索',
  55: '推荐'
}

export default {
  components: {
    ArchiveCard,
    ArchiveDialog,
    ArchiveTitle,
    ListFilter,
    NewTooltip,
    Pagination,
    UserInfo
  },
  mixins: [routeHelper],
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.enterTime = Date.now()
    })
  },
  beforeRouteUpdate(to, from, next) {
    next()
  },
  beforeRouteLeave(to, from, next) {
    this.setLeaveListTime(Date.now())
    next()
  },
  data() {
    const expandShow = localStorage.expandShow === 'false' ? !!0 : !0
    return {
      queryKeys: {},
      formData: {},
      formItems: [],
      filterOptions: {},
      tableData: [],
      displayModel: 'display_model',
      DISPLAY_MODEL,
      SHOW_LIMIT_BUTTON,
      columns: [
        {
          label: '稿件id',
          prop: 'aid',
          width: '140',
          align: 'left',
          render(row) {
            const href = `http://www.bilibili.com/video/av${row.id}`
            const elStr = `<a href="${href}" class="a-oid user-select" target="_blank">${row.id}</a></br><em class="a-oid">(</em><a href="${href}" class="a-oid user-select" target="_blank">${row.bvid}</a><em class="a-oid">)</em>`
            return elStr
          }
        },
        {
          label: '投稿时间',
          prop: 'ctime',
          width: '160'
        },
        {
          label: 'UP对接方',
          prop: '_arcTypeV1',
          align: 'left',
          width: '160'
        },
        {
          label: '频道',
          prop: 'tids',
          align: 'left',
          width: '170',
          render(row) {
            return row.tid_names ? row.tid_names.join(',') : ''
          }
        },
        {
          label: '视频标题',
          prop: 'title',
          align: 'left'
        },
        {
          label: '稿件状态',
          prop: 'state',
          width: '100',
          render(row) {
            const color = COLORS[row.state] || 'var(--red)'

            let stateText = STATES[row.state] || ''
            if (row.access && row.access > 0 && row.state >= 0) {
              stateText = STATES[row.access] || ''
            }
            return `<p style="color: ${color}">
            ${stateText}
          </p>`
          }
        },
        {
          label: 'UP主',
          prop: 'mid',
          align: 'left',
          width: '160'
        }
      ],
      expandShow,
      toggleRowExpansion: {
        all: false,
        expandRowKeys: []
      },
      businessId: 11,
      filterId: null,
      keyCode: null,
      BATCH_BUTTON_LIST,
      multipleSelection: [],
      review: '',
      enterTime: 0,
      dataReady: false,
      pager: {
        pn: 1,
        total: 0,
        ps: 30
      },
      tableRequestTime: 0,
      backListTime: 0,
      listEnterTime: 0,
      path: '',
      LIST_MAP
    }
  },
  watch: {
    dataReady(nextVal) {
      if (nextVal) {
        this.$nextTick(() => {
          const dataReadytime = Date.now()
          if (this.enterTime) {
            // 进入页面到表格数据返回的耗时
            const diffTime = dataReadytime - this.enterTime
            this.listEnterTime = diffTime
            this.enterTime = 0
          }
          if (this.leaveDetailTime) {
            const backDiffTime = dataReadytime - this.leaveDetailTime
            this.backListTime = backDiffTime
            this.setLeaveDetailTime(0)
          }
        })
      }
    },
    tableData() {
      this.toggleRowExpansion = {
        all: false,
        expandRowKeys: []
      }
      this.multipleSelection = []
    }
  },
  computed: {
    ...mapState({
      perms: (state) => state.user.perms,
      arctypeMap: (state) => state.arctype.arctypeMap,
      leaveDetailTime: (state) => state.time.leaveDetailTime,
      username: (state) => state.user.username,
      uid: (state) => state.user.uid
    }),
    breadCrumbs() {
      const pBreadCrumbs = this.$route.meta?.breadCrumbs || []
      return [
        ...pBreadCrumbs,
        {
          name: LIST_MAP[this.$route.params.id]
        }
      ]
    },
    multipleBtn() {
      return !!(this.multipleSelection.length !== this.tableData.length)
    },
    isDisabled() {
      return !this.multipleSelection.length
    },
    listType() {
      return +this.$route.path.split('/')[3]
    },
    originListType() {
      return this.$route.path.split('/')[3]
    },
    useBackConfig() {
      return (
        (+this.$route.params.id > 22 && +this.$route.params.id < 26) ||
        ['23_new', '24_new', '25_new'].indexOf(this.$route.params.id) !== -1
      )
    }
  },
  created() {
    this.path = window.location.origin + window.location.pathname
    this.displayModel = sessionStorage.displayModel || 'list_model'
    this.getPageList()
    this.getArctype()
    this.fetchCommon()
  },
  mounted() {
    if (window.MutationObserver) {
      const targetNode = document.getElementById('archive-list-id')
      const config = {
        childList: true,
        subtree: true
      }
      const observer = new MutationObserver((records, itself) => {
        const tableRenderedTime = Date.now()
        if (this.tableRequestTime) {
          const diffTime = tableRenderedTime - this.tableRequestTime
          let backTime = 0
          let listTime = 0
          const reportData = {
            username: this.username,
            uid: this.uid,
            tableRenderTime: diffTime
          }
          // listEnterTime: 进入页面到拿到表格数据的耗时
          // backListTime: 详情页返回列表页到拿到表格数据的耗时
          // diffTime: 表格解析数据到渲染的耗时
          // backTime: 详情页返回列表总耗时
          // listTime: 列表页正常加载总耗时
          if (this.backListTime) {
            backTime = this.backListTime + diffTime
            reportData.backListTime = this.backListTime
            reportData.backTime = backTime
          }
          if (this.listEnterTime) {
            listTime = this.listEnterTime + diffTime
            reportData.listTime = listTime
            reportData.listEnterTime = this.listEnterTime
          }
          report('list-load', reportData)
          this.setLeaveListTime(0)
          this.tableRequestTime = 0
          this.backListTime = 0
          this.listEnterTime = 0
          // 只记录刚进入页面那一次DOM变化
          observer.disconnect()
        }
      })
      observer.observe(targetNode, config)
    }
    window.addEventListener('resize', this.calcArchiveCardWidth)
  },
  methods: {
    ...mapActions({
      getArctype: 'arctype/getArctype',
      fetchCommon: 'common/fetchCommon'
    }),
    ...mapMutations({
      setLeaveListTime: 'time/SET_LEAVELISTTIME',
      setLeaveDetailTime: 'time/SET_LEAVEDETAILTIME'
    }),
    getPageList() {
      templateApi.getTemplateAll().then((res) => {
        const data = res.data || []
        const configs = data.filter((item) => {
          if (
            +item.name === this.businessId &&
            item.path === this.$route.path
          ) {
            return item
          }
        })
        if (configs.length) {
          this.setPageId(configs)
        }
      })
    },
    setPageId(data) {
      const hasTemplate = data.length
      if (hasTemplate) {
        this.filterId =
          (data.find((item) => +item.meta.type === 4) || {}).id || null
      } else {
        this.filterId = null
      }
      this.getFilterConfig()
    },
    getFilterConfig() {
      const params = {
        page_list_id: this.filterId
      }
      templateApi
        .getPageDetail(params)
        .then((res) => {
          if (!res.data) {
            return
          }
          const filterConfig = JSON.parse(res.data.content)
          this.setData(filterConfig)
        })
        .catch((e) => console.error(e))
    },
    setData(data) {
      const items = common.setFilters(data)
      const dataTemplate = common.setFilterModels(items)
      const options = common.getFilterOptions(data, this.filterOptions)

      const stateItem = items.find((i) => i.name === 'state') || {}
      if (stateItem.type !== 'hide') {
        // 为了解决稿件state绑定的值不唯一的问题，前端需要维护一个accessState字段
        dataTemplate.accessState = dataTemplate.state
        options.accessState = options.state
        ;(options.accessState || []).forEach((opt, index) => {
          // accessState渲染使用的值为val(state选项数组中的index)， 真正传给后端的值为value
          opt.value = opt.val
          opt.val = index.toString()
        })
        const stateIndex = items.findIndex((i) => i.name === 'state')
        if (stateIndex > -1) items[stateIndex].name = 'accessState'
        const newItem = items[stateIndex]
        if (stateIndex > -1) items[stateIndex] = newItem
        delete dataTemplate.state
        delete options.state
      }

      const hasQuery = Object.keys(this.$route.query).length
      this.formItems = items
      this.filterOptions = options
      this.formData = dataTemplate
      this.queryKeys = { ...dataTemplate }
      // 将路由传的参数的值赋给formdata
      if (hasQuery) {
        const query = this.$route.query
        for (const key in query) {
          const formItemConf = this.formItems.find(e => e.name === key)
          if (key === 'accessState') {
            this.formData[key] = query[key]?.includes(',')
              ? query[key].split(',').filter(e => !!e)
              : query[key] || ''
          } else if (
            ['typeid', 'kw_fields', 'lang', 'region'].includes(key) ||
            (['attribute', 'up_froms', 'state'].includes(key) && formItemConf?.type?.includes('checkbox'))
          ) {
            this.formData[key] = query[key] && query[key].split(',')
          } else {
            this.formData[key] = query[key]
          }
        }
      }
      this.beforeGetTableConfig()
    },
    beforeGetTableConfig(pn) {
      const params = { ...this.formData }
      this.formItems.forEach((item) => {
        const name = item.name
        const isMult = Array.isArray(params[name])
        if (item.type === 'checkbox') {
          params[name] = isMult ? params[name].join(',') : params[name]
        } else if (item.type === 'multi_value_checkbox') {
          ;(params[name] || []).forEach((val) => {
            const option =
              this.filterOptions[name].find((i) => i.val === val) || {}
            if (option.extra_kv) {
              option.extra_kv.forEach((kv) => {
                params[kv.name] = kv.value
              })
            }
            if (name === 'accessState') {
              params.state = params.accessState.map(index => this.filterOptions.accessState?.[index]?.value).filter(e => !!e).join(',')
            }
          })
          params[name] =
            params[name].length > 1 ? params[name].join(',') : params[name][0]
        } else if (item.type === 'multi_value_select') {
          const option =
            this.filterOptions[name].find((i) => i.val === params[name]) || {}
          // 只有state这个字段有这样的操作
          if (name === 'state' || name === 'accessState') {
            params.state = option.value || ''
          }
          if (option.extra_kv) {
            option.extra_kv.forEach((kv) => {
              params[kv.name] = kv.value
            })
          } else {
            params.access && delete params.access
          }
        } else if (item.type === 'arctype') {
          params[name] = params[name] && params[name].join(',')
        }
      })
      for (const key in this.filterOptions) {
        const opts = this.filterOptions[key] || {}
        if (Array.isArray(opts)) {
          opts.forEach((opt) => {
            if (opt.extra_kv && opt.extra_kv.length && !params[key]) {
              opt.extra_kv.forEach((kv) => {
                params[kv.name] = ''
              })
            }
          })
        }
      }
      this.multipleSelection = []
      if (isBoolean(pn) || pn === 'true') pn = 1
      this.routeRefresh(params, pn)
    },
    routeRefresh(params, pn) {
      this.$_route_refresh(
        {
          ...params,
          page: pn || this.$route.query.page || 1,
          list_id: this.tableDetailId,
          business_id: this.businessId,
          list_type: params.list_type || this.$route.params.id,
          t: +new Date()
        },
        true
      )
    },
    fetchData() {
      if (!Object.keys(this.$route.query).length) {
        this.getPageList()
        return
      }
      const params = this.$route.query
      delete params.accessState
      this.$ajax
        .get('/x/admin/videoup/search/archive', {
          params
        })
        .then((result) => {
          const data = result.data || {}
          const archives = this.sortArchivesUserType(data.result || [])
          archives.forEach((item) => {
            const labels = []
            // 属性位
            const attributeArr = item.attribute || []
            attributeArr.forEach((attr) => {
              if (LIST_ATTRS_MAP[attr]) {
                const str = `[${LIST_ATTRS_MAP[attr]}]`
                labels.push(str)
              }
            })
            const archiveFlowArr = item.archive_flow || []
            archiveFlowArr.forEach((flow) => {
              if (LIST_FLOW_MAP?.[flow?.meal_id] && flow.state === 1) {
                labels.push(`[${LIST_FLOW_MAP[flow.meal_id]}]`)
              }
            })
            if (parseInt(item.access, 10) === 10000) {
              labels.push('[会员]')
            }
            if (parseInt(item.mission_id, 10) !== 0) {
              labels.push('[活动]')
            }
            item.labels = labels ? labels.join('') : ''
          })
          this.generateTypeName(archives, 'arc_type_v1')
          this.review =
            (this.$route.query.review &&
              this.$route.query.review.split(',')[0]) ||
            ''
          this.tableData = archives
          if (data.page) {
            this.pager.pn = data.page.num
            this.pager.total = data.page.total
          }
          // 返回表格数据的时间
          if (!this.dataReady) {
            this.tableRequestTime = Date.now()
            this.dataReady = true
          }
        })
    },
    sortArchivesUserType(archives) {
      if (!archives || archives.length === 0) {
        return []
      }
      return archives.map((arc) => {
        // 数据没有user_type字段。
        if (!arc.user_type) {
          arc.user_type = []
          return arc
        }
        arc.user_type = arc.user_type.sort((a, b) => {
          return parseInt(a, 10) - parseInt(b, 10)
        })
        return arc
      })
    },
    generateTypeName(list, typeIdKey, typeNameKey = '_arcTypeV1') {
      return list.map((oneEntry) => {
        oneEntry[typeNameKey] = oneEntry[typeIdKey]
        return oneEntry
      })
    },
    getNameStr(arctypeMap, ids) {
      if (arctypeMap === undefined || !ids) {
        return ''
      }
      let idArr = ids
      if (!Array.isArray(idArr)) {
        idArr = ids.toString().split(',')
      }
      return idArr.map((typeId) => arctypeMap[typeId]).join(',')
    },
    cutImage(imgUrl) {
      return cutImage(imgUrl, 160, 100)
    },
    updateDisplayModel(val) {
      sessionStorage.displayModel = val
      this.displayModel = val
      this.multipleSelection = []
      val === DISPLAY_MODEL.LIST_MODEL && this.clearSelection()
      this.$nextTick(() => {
        this.calcArchiveCardWidth()
      })
    },
    calcArchiveCardWidth() {
      this.displayModel === DISPLAY_MODEL.CARD_MODEL && this.$refs.archiveCard?.forEach(vc => {
        vc.getShowTooltip()
      })
    },
    handleSelectionChange(val) {
      if (this.displayModel === DISPLAY_MODEL.LIST_MODEL) {
        this.multipleSelection = val
      } else if (this.displayModel === DISPLAY_MODEL.CARD_MODEL) {
        const unchangedArr = this.multipleSelection.filter(archive => archive.id !== val.id)
        if (unchangedArr.length === this.multipleSelection.length) {
          this.multipleSelection.push(val)
        } else {
          this.multipleSelection = unchangedArr
        }
      }
    },
    openDialog(name) {
      this.$refs.dialog.openDialog(name)
    },
    goRecheckSetting() {
      this.$router.push({
        path: '/v2/archive/recheck-setting'
      })
    },
    resetQueryForm() {
      this.formData = { ...this.queryKeys }
      this.beforeGetTableConfig(1)
    },
    toggleSelection() {
      if (this.multipleBtn) {
        this.displayModel === DISPLAY_MODEL.LIST_MODEL
          ? this.tableData?.forEach((row) => {
            this.$refs.archiveTable.toggleRowSelection(row, true)
          })
          : this.multipleSelection = this.tableData
      } else {
        this.displayModel === DISPLAY_MODEL.LIST_MODEL ? this.clearSelection() : this.multipleSelection = []
      }
    },
    expandChange(row, expandedRows) {
      this.toggleRowExpansion.expandRowKeys = expandedRows.map(row => row.id)
      this.toggleRowExpansion.all = !!this.toggleRowExpansion.expandRowKeys.length
    },
    toggleAllExpansion() {
      this.toggleRowExpansion.expandRowKeys = []
      if (!this.toggleRowExpansion.all) {
        this.toggleRowExpansion.expandRowKeys = this.tableData.map(row => row.id)
      }
      this.toggleRowExpansion.all = !this.toggleRowExpansion.all
    },
    exportCSV() {
      if (Array.isArray(this.multipleSelection) && this.multipleSelection.length) {
        const ids = this.multipleSelection.map((item) => item.id).join(',')
        window.open(`${CSV_URL}${ids}`, '_blank')
      } else {
        notify.error('请至少选中一条数据')
      }
    },
    showExtraDialog() {
      this.$confirm('请确定导出数据的范围', '提示', {
          confirmButtonText: '导出全部',
          cancelButtonText: '导出选中',
          type: 'warning'
        }).then(() => { // 导出全部
          const EXPORT_MAX = 10000
          if (this.pager.total >= EXPORT_MAX) {
            notify.error(`全部视频的总量已经超过 ${EXPORT_MAX} 条的导出上限，请重新选择搜索条件缩小总量`)
          } else {
            const params = { ...this.$route.query }
            params.export_csv	= true
            delete params.accessState
            if (params.arc_duration_max === undefined) delete params.arc_duration_max
            if (params.arc_duration_min === undefined) delete params.arc_duration_min
            if (params.list_id) delete params.list_id
            if (params.aids) params.aids = params.aids.split('\n').join(',')
            if (params.mids) params.mids = params.mids.split('\n').join(',')
            downloadCsvInterval({
              total: this.pager.total,
              url: `${genHost()}/x/admin/videoup/search/archive`,
              params,
              contentName: 'csv_content',
              pnName: 'page',
              psName: 'pagesize',
              size: 50,
              withCredentials: true
            })
          }
        }).catch(() => { // 导出选中
          this.exportCSV()
        })
    },
    transition() {
      this.expandShow = !this.expandShow
      localStorage.expandShow = this.expandShow
      this.$nextTick(() => {
        this.$refs.archiveTable.doLayout()
      })
    },
    rowClick(row, col, event) {
      if (row && this.$refs.archiveTable && rowSelectFilter(event)) {
        this.$refs.archiveTable.toggleRowSelection(row)
      }
    },
    clearSelection() {
      this.$refs.archiveTable.clearSelection()
    }
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.calcArchiveCardWidth)
  }
}
</script>
<style lang="stylus">
.archive-list
  .filter-block
    padding: var(--filter-padding)
    background var(--content-bg-color)
    margin-bottom 8px
  .header-row
    margin-bottom 8px
    background var(--content-bg-color)
    padding var(--header-padding)
    .batch-row
      margin-bottom 12px
  .content-row
    background var(--content-bg-color)
    padding var(--content-padding)
  .expand
    position relative
    i
      position absolute
      z-index 10
      right 0px
      top -39px
      color var(--grey-light-1)
      cursor pointer
      &:hover
        color var(--link-color)
  .expand-height
    height 20px
  .display-none
    top -5px !important
  .filter
    // margin-bottom 24px
    position relative
    -webkit-transition height 0.6s
    -moz-transition height 0.6s
    -o-transition height 0.6s
    transition height 0.6s
  .a-oid
    color var(--link-color)
    cursor pointer
</style>
