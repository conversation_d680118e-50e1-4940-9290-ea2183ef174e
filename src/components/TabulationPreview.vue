<template>
  <div class="tabulation">
    <el-table
      border
      ref="multipleTable"
      style="width: 100%"
      tooltip-effect="dark"
      :data="tableData.data">
      <el-table-column
        v-if="tableData.select"
        type="selection"
        align="center"
        fixed
        width="55">
      </el-table-column>
      <el-table-column
        v-for="(item, index) in tableData.columns"
        align="center"
        :key="index"
        :prop="item.name"
        :label="item.label"
        :width="item.width">
        <template v-slot="scope" style="display: flex">
          <div
            v-if ="scope.row[item.name].name !== 'content' && scope.row[item.name].name !== 'operations' && scope.row[item.name].val"
            v-for="(col, colIndex) in ((scope.row[item.name] && scope.row[item.name].val) || [])"
            :key="colIndex"
          >
            <a v-if="col.type === 'link' && !col.action" :href="col.href" class="a-oid user-select" target="_blank">
              {{ col.text }}
            </a>

            <router-link
            v-else-if="col.type === 'link' && col.action === 'target'"
            class="a-oid user-select"
            :target="col.target"
            :to="{ path: col.href }">
              {{ col.text }}
            </router-link>

            <p v-else-if="col.type === 'hit'" v-html="col.text"></p>

            <el-tooltip
              v-else-if="col.type === 'img' || ((col.type === 'link' || col.type === 'text') && col.action === 'hover_text')"
              placement="bottom">
              <img slot="content"
                v-if="col.type === 'img'"
                :src="col.href + (col.text === '[图片]' ? '_160x100.jpg' : '')"
                :width="col.text !== '[图片]' ? '150px' : '170px'"
                :height="col.text !== '[图片]' ? '150px' : '110px'"
              >
              <p slot="content" style="white-space:pre;" v-else>{{ col.action_params }}</p>
              <em v-if="col.type === 'text'">{{ col.text }}</em>
              <a v-if="col.type === 'link'" class="a-oid user-select" :href="col.href" target="_blank">{{ col.text }}</a>
              <p v-if="col.type === 'img'" style="color: red">{{col.text}}</p>
            </el-tooltip>

            <div v-else-if="col.type === 'up_info'">
              <div style="display:flex;justify-content: center;">
                <em
                  v-for="g in scope.row.user_group"
                  v-if="g"
                  :key="g.tag"
                  :style="{
                    'color': `rgba(${g.font_color})`,
                    'background': `rgba(${g.bg_color})`,
                      'font-size': '8px',
                      'height': '23px'}"
                  :title="g.group_note">
                  {{ g.short_tag }}
                </em>
                <span
                  v-if="scope.row.user_info && scope.row.user_info.official.role !== 0"
                  class="verifyIcon"
                  :class="{
                    individual: INDIVIDUAL_LIST.includes(scope.row.user_info.official.role),
                    enterprise: ENTERPRISE_LIST.includes(scope.row.user_info.official.role)
                  }">
                </span>
              </div>
              <el-tooltip placement="bottom">
                <p slot="content">{{ col.text }}</p>
                <a :href="`https://space.bilibili.com/${col.text}`" class="a-oid user-select" target="_blank">
                  <span>{{ scope.row.user_info && scope.row.user_info.name }}</span>
                </a>
              </el-tooltip>
              <p v-if="col.action === 'is_up' && col.action_params === 'true'"><em style="color: var(--red)">（up主）</em></p>
              <p v-if="scope.row.user_info">
                粉丝数：
                <em
                  :class="{
                    'blue-fans-font': scope.row.user_info.follower >= 10000 && scope.row.user_info.follower < 100000,
                    'orange-fans-font': scope.row.user_info.follower >= 100000 && scope.row.user_info.follower < 1000000,
                    'red-fans-font': scope.row.user_info.follower >= 1000000
                  }">
                  {{scope.row.user_info.follower || '暂无'}}
                </em>
              </p>
            </div>

            <el-tag
              v-else-if="col.action && col.action === 'colormap'"
              :hit="false"
              :class="findActionClass(col)">
              {{ col.text }}
            </el-tag>

            <p v-else-if="col.action === 'arctype'">{{arctype(col.text)}}</p>

            <p v-else-if="col.type === 'text'" :style="{'color': col.color}">{{ col.text }}</p>
            <p v-else>{{ col.text }}</p>
          </div>

          <div v-if="scope.row[item.name] && scope.row[item.name].name === 'content'">
            <p v-if="scope.row[item.name].val[0].type === 'hit' && !scope.row[item.name].val[0].action" style="font-weight: bold" v-html="scope.row[item.name].val[0].text"></p>
            <el-tooltip
              v-else-if="scope.row[item.name].val[0].type === 'hit' && scope.row[item.name].val[0].action === 'show_limit'"
              popper-class="popper-max-width"
              placement="bottom">
              <p slot="content" v-html="setContent(scope.row[item.name].val[0].text, +scope.row[item.name].val[0].action_params, scope.row)" style="font-weight: bold"></p>
              <p v-html="setContent(scope.row.origin_content, +scope.row[item.name].val[0].action_params, scope.row, 'slice')" style="font-weight: bold"></p>
            </el-tooltip>
            <p v-else style="font-weight: bold">{{scope.row[item.name].val[0].text}}</p>
          </div>

          <div v-if="scope.row[item.name] && scope.row[item.name].name === 'operations'">
            <el-button
              v-for="(op, index) in scope.row[item.name].val"
              :key="index"
              v-if="op.text && op.type === 'oper'"
              :type="op.text.indexOf('通过') > -1 ? 'success': op.text.indexOf('驳回') > -1 ? 'danger' : 'warning'"
              @click="openDialog(op, scope.row)"
              size="small"
              class="op-button"
              plain
            >
              {{ op.text }}
            </el-button>

            <el-button
              v-else-if="op.type === 'log'"
              type="info"
              size="small"
              plain
              @click="openLog(op)"
            >
              {{ op.text }}
            </el-button>

            <el-button
              v-else-if="op.type === 'button' && op.action_params"
              :type="getButtonAttribute(op.action_params, 'type')"
              size="small"
              :plain="getButtonAttribute(op.action_params, 'plain')"
              @click="clickBtn(op, scope.row)"
            >
              {{ op.text }}
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <el-row>
      <Pagination v-if="tableData.usePager" :pager="tableData.pager.data" :justify="tableData.justify" class="pager" @getList="getList"></Pagination>
    </el-row>
  </div>
</template>
<script>
import { formatRegExp } from '../pages/contentAudit/common.js'
import { commonApi, templateApi } from '@/api/index'
import { INDIVIDUAL_LIST, ENTERPRISE_LIST } from '@/pages/workbench/constants'

export default {
  props: {
    tableData: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },

  data() {
    return {
      arctypes: [],
      ENTERPRISE_LIST,
      INDIVIDUAL_LIST
    }
  },

  computed: {
  },

  watch: {
  },

  mounted() {
  },

  methods: {
    findActionClass(col) {
      const { text, action_params: actionParams } = col || {}
      const path = this.$route.path
      const archiveRoutes = ['/archive/archive-review-list', '/archive/archive-list']
      const isArchive = archiveRoutes.some(item => path.indexOf(item) !== -1)
      // 对稿件的状态颜色做特殊处理，这里用状态名称判断稿件状态三大颜色
      if (isArchive) {
        if (['开放浏览', '橙色通过'].indexOf(text) !== -1) {
          return 'action-color-green'
        } else if (['待审', '修复待审', '等待转码', '分发中'].indexOf(text) !== -1) {
          return 'action-color-blue'
        } else {
          return 'action-color-red'
        }
      } else {
        return `action-color-${actionParams}`
      }
    },
    doLayout() {
      this.$refs['multipleTable'].doLayout()
    },
    openLog(op) {
      if (op.action === 'log' && op.href) {
        this.$emit('getOperationLog', op)
      }
    },
    setContent(text, length, row, useSlice = '') {
      text = useSlice && text && text.length > length ? `${text.slice(0, length)}...` : text;
      (row.hit || []).forEach(key => {
        let newStr = formatRegExp(key)
        let regexp = new RegExp(newStr, 'g')
        text = text.replace(regexp, `<em style="color:red;">${key}</em>`)
      })
      return text
    },
    getButtonAttribute(op, key) {
      const params = JSON.parse(op)
      return params[key]
    },
    clickBtn(op, row) {
      const path = JSON.parse(op.action_params).href || ''
      if (op.action === 'link') {
        this.$router.push({
          path
        })
      } else if (op.action === 'detail') {
        templateApi.getAllDynamicRoutes().then(res => {
          if (res.data.length) {
            const route = res.data.find(d => +d.name === +row.business_id)
            this.$router.push({
              path: route.path,
              query: {
                business_id: row.business_id,
                rid: row.rid,
                oid: row.oid.val[0].text,
                flow_id: row.flow_id,
                isResource: 1,
                businessType: this.$route.path.split('/')[3]
              }
            })
          }
        }).catch(_ => {})
      }
    },
    getArchiveType() {
      this.arctypes = []
      commonApi.getArctype().then(res => {
        (res.data || []).forEach(d => {
          if (d.children) this.arctypes.push(...d.children)
        })
      }).catch(_ => {})
    },
    arctype(id) {
      return (this.arctypes.find(t => t.id === id) || {}).name
    }
  }
}
</script>
<style lang="stylus">
.tabulation {
  width: 100%
  height: auto
  margin: 0px 0px -20px
  .a-oid {
    color: var(--link-color)
    cursor: pointer
  }
  .verifyIcon {
    display: inline-block
    vertical-align: bottom
    margin-top: 2px
    width: 18px
    height: 18px
    background-image: url('~@/assets/user-auth.png')
  }
  .individual {
    background-position: -39px -82px
  }
  .enterprise {
    background-position: -4px -81px
  }
  .delete {
    color: var(--purple)
    border-color: var(--purple)
    background: var(--purple-light-1)
  }
  .caret {
    position: relative
    .el-icon-caret-top {
      position: absolute
      top: 9px
      cursor: pointer
      &:hover {
        color: var(--link-color)
      }
    }
    .el-icon-caret-bottom {
      position: absolute
      top: 17px
      cursor: pointer
       &:hover {
        color: var(--link-color)
      }
    }
  }
  .el-tag {
    border: none
  }
  .user-select {
    user-select: all
    -moz-user-select: all;
    -webkit-user-select: all;
    -ms-user-select: all;
  }
  .blue-fans-font {
    color: var(--blue)
    font-weight: bold
  }
  .red-fans-font {
    color: var(--red)
    font-weight: bold
  }
  .orange-fans-font {
    color: var(--orange)
    font-weight: bold
  }
  .action-color-green {
    background-color: rgba(92, 184, 92, .1)
    color: rgb(92, 184, 92)
  }
  .action-color-blue {
    background-color: rgba(2, 117, 216, .1)
    color: rgb(2, 117, 216)
  }
  .action-color-red {
    background-color: rgba(238, 80, 55, .1)
    color: rgb(238, 80, 55)
  }
  .action-color-1 {
    background-color: rgba(64,158,255,.1)
    color: var(--blue)
  }
  .action-color-2 {
    background-color: rgba(103,194,58,.1)
    color: var(--success-color)
  }
  .action-color-3 {
    background-color: rgba(144,147,153,.1)
    color: var(--grey-light-1)
  }
  .action-color-4 {
    background-color: rgba(230,162,60, .1)
    color: var(--warning-color)
  }
  .action-color-5 {
    background-color: rgba(253,0,84,.1)
    color: #fd0054
  }
  .action-color-6 {
    background-color: rgba(250,219,20,.1)
    color: #fadb14
  }
  .action-color-7 {
    background-color: rgba(19,194,194,.1)
    color: #13c2c2
  }
  .action-color-8 {
    background-color: rgba(49,97,163,.1)
    color: #3161a3
  }
  .action-color-9 {
    background-color: rgba(162,110,161,.1)
    color: #a26ea1
  }
  .action-color-10 {
    background-color: rgba(255,214,231,.1)
    color: #ffd6e7
  }
  .action-color-11 {
    background-color: rgba(28,129,157,.1)
    color: #1c819e
  }
  .action-color-12 {
    background-color: rgba(34,40,49,.1)
    color: #222831
  }
  .action-color-13 {
    background-color: rgba(169,104,81,.1)
    color: #a96851
  }
  .action-color-14 {
    background-color: rgba(200,217,235,.1)
    color: #c8d9eb
  }
  .action-color-15 {
    background-color: rgba(241,138,155,.1)
    color: #f18a9b
  }
  .action-color-16 {
    background-color: rgba(232,234,161,.1)
    color: #e8eaa1
  }
  .action-color-17 {
    background-color: rgba(204,162,225,.1)
    color: #cca2e1
  }
  .action-color-18 {
    background-color: rgba(157,143,143,.1)
    color: #9d8f8f
  }
  .action-color-19 {
    background-color: rgba(255,175,135,.1)
    color: #ffaf87
  }
  .action-color-20 {
    background-color: rgba(147,222,255,.1)
    color: #93deff
  }
  .el-tooltip {
    display: inline-block
  }
  .op-button {
    width: 200px
    span {
      display block
      text-overflow ellipsis
      overflow hidden
    }
  }
}
</style>
