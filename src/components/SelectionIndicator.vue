<template>
  <span>
    {{ `已选择 ${selected} / ${total}` }}
    <el-button @click="onToggleAll" class="ml-8" type="primary">
      {{ selected === total ? '全不选' : '全选' }}
    </el-button>
  </span>
</template>

<script>
export default {
  props: {
    total: {
      type: Number,
      default: 0
    },
    selected: {
      type: Number,
      default: 0
    },
    onToggleAll: Function
  }
}
</script>
