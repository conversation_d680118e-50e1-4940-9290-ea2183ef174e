<template>
  <div class="tabulation">
    <el-table
      v-if="tableHeight"
      border
      :size="size"
      v-loading="loading"
      stripe
      highlight-current-row
      ref="multipleTable"
      style="width: 100%"
      tooltip-effect="dark"
      :data="tableData.data"
      @row-click="rowClick"
      @current-change="changeRow"
      @selection-change="handleSelectionChange"
      :max-height="tableHeight">
      <el-table-column
        v-if="tableData.multSelect"
        type="selection"
        align="center"
        fixed
        width="55">
      </el-table-column>
      <el-table-column
        v-for="(item, index) in tableData.columns"
        :key="index"
        :prop="item.name"
        :sortable="item.sortable"
        :label="item.label"
        :width="item.width"
        align="left"
        :fixed="item.fixed"
        :min-width="item.minWidth">
        <template v-slot="scope" style="display: flex">
          <div
            :class="{ 'cell-one-line': scope.row[item.name].name === 'title' }"
            v-if ="scope.row[item.name].name !== 'content' && scope.row[item.name].name !== 'operations' && scope.row[item.name].val && scope.row[item.name].val.length"
          >
            <div v-for="(col, colIndex) in scope.row[item.name].val" :key="colIndex">
              <div v-if="col.type === 'link' && !col.action">
                <a :href="col.href" class="a-oid user-select" target="_blank">
                  {{ col.text }}
                </a>
              </div>

              <router-link
              v-else-if="col.type === 'link' && col.action === 'target'"
              class="a-oid user-select"
              :target="col.action_params"
              :to="{ path: col.href }">
                {{ col.text }}
              </router-link>

              <p v-else-if="col.type === 'hit'" v-html="col.text"></p>

              <el-tooltip
                v-else-if="col.type === 'hover_img' || ((col.type === 'link' || col.type === 'text') && col.action === 'hover_text')"
                placement="bottom">
                <img slot="content"
                  loading="lazy"
                  v-if="col.type === 'hover_img'"
                  :src="col.href + (col.text === '[图片]' ? '_160x100.jpg' : '')"
                  :width="col.text !== '[图片]' ? '150px' : '170px'"
                  :height="col.text !== '[图片]' ? '150px' : '110px'"
                >
                <p slot="content" style="white-space:pre-wrap;" v-else>{{ col.action_params }}</p>
                <em v-if="col.type === 'text'">{{ col.text }}</em>
                <a v-if="col.type === 'link'" class="a-oid user-select" :href="col.href" target="_blank">{{ col.text }}</a>
                <p v-if="col.type === 'hover_img'" style="color: var(--red)">{{col.text}}</p>
              </el-tooltip>

              <div v-else-if="col.type === 'up_info'">
                <div style="display:flex;">
                  <em
                    v-for="g in scope.row.user_group"
                    v-if="g"
                    :key="g.tag"
                    :style="{
                      'color': `rgba(${g.font_color})`,
                      'background': `rgba(${g.bg_color})`,
                      'font-size': '8px',
                      'height': '23px'}"
                    :title="g.group_note">
                    {{ g.short_tag }}
                  </em>
                  <span
                    v-if="scope.row.user_info && scope.row.user_info.official.role !== 0"
                    class="verifyIcon"
                    :class="{
                      individual: INDIVIDUAL_LIST.includes(scope.row.user_info.official.role),
                      enterprise: ENTERPRISE_LIST.includes(scope.row.user_info.official.role)
                    }">
                  </span>
                  <el-tooltip placement="bottom">
                    <p slot="content">{{ col.text }}</p>
                    <a :href="`https://space.bilibili.com/${col.text}`" class="a-oid user-select" target="_blank">
                      <span>{{ scope.row.user_info && scope.row.user_info.name }}</span>
                    </a>
                  </el-tooltip>
                  <span v-if="col.action === 'is_up' && col.action_params === 'true'"><em style="color: var(--red)">（up主）</em></span>
                </div>
                <p v-if="scope.row.user_info">
                  粉丝数：
                  <em
                    :class="{
                      'blue-fans-font': scope.row.user_info.follower >= 10000 && scope.row.user_info.follower < 100000,
                      'orange-fans-font': scope.row.user_info.follower >= 100000 && scope.row.user_info.follower < 1000000,
                      'red-fans-font': scope.row.user_info.follower >= 1000000
                    }">
                    {{scope.row.user_info.follower || '暂无'}}
                  </em>
                </p>
              </div>

              <div
                v-else-if="col.type === 'user_profession'"
              >
                <div><b>认证类型：</b>{{ CERTIFICATION_TYPE_ENUMS[safeGetValue(scope.row, 'user_info.official.role')] }}</div>
                <div><b>认证身份：</b>{{ safeGetValue(scope.row, 'user_info.official.title') }}</div>
                <div><b>认证后缀：</b>{{ safeGetValue(scope.row, 'user_info.official.desc') }}</div>
                <div>
                  <b>职业资质：</b>
                  {{ `${safeGetValue(scope.row, 'user_profession.title')}-${safeGetValue(scope.row, 'user_profession.department')}` }}
                </div>
              </div>

              <el-tag
                v-else-if="col.action && col.action === 'colormap'"
                :hit="false"
                :class="findActionClass(col)">
                {{ col.text }}
              </el-tag>

              <p v-else-if="col.action === 'arctype'">{{arctype(col.text)}}</p>

              <span v-else-if="col.type === 'text'" :style="{'color': col.color}">{{ col.text }}</span>

              <img v-else-if="col.type === 'img'" :src="col.href" alt="" width="150px" height="150px">
              <p v-else>{{ col.text }}</p>
            </div>
          </div>

          <div v-if="scope.row[item.name].name === 'content'">
            <div v-for="(col2, colIndex2) in scope.row[item.name].val" :key="colIndex2">
              <p v-if="col2.type === 'hit' && !col2.action" style="font-weight: bold; line-height: 22px" v-html="col2.text"></p>

              <el-tooltip
                v-else-if="col2.type === 'hit' && col2.action === 'show_limit'"
                popper-class="popper-max-width"
                placement="bottom">
                <p slot="content" v-html="scope.row.origin_content" style="font-weight: bold;white-space:pre-wrap;"></p>
                <p v-html="col2.text" style="font-weight: bold"></p>
              </el-tooltip>

              <img
                v-else-if="col2.type === 'img'"
                :src="col2.href"
                alt=""
                :width="getActionParams(col2.action_params, 'width') || '150px'"
                :height="getActionParams(col2.action_params, 'height')  || '150px'"
                @click.stop="openImg(col2.href)"
              >

              <p v-else style="font-weight: bold">{{scope.row[item.name].val[0].text}}</p>
            </div>
          </div>

          <div v-if="scope.row[item.name].name === 'operations'">
            <el-button
              v-for="(op, index) in scope.row[item.name].val"
              :key="index"
              v-if="op.text && op.type === 'oper'"
              :type="op.text.indexOf('通过') > -1 ? 'success': op.text.indexOf('驳回') > -1 ? 'danger' : 'warning'"
              @click.stop="openDialog(op, scope.row)"
              size="small"
              plain
            >
              {{ op.text }}
            </el-button>

            <el-button
              v-else-if="op.type === 'log'"
              type="info"
              size="small"
              plain
              @click.stop="openLog(op)"
            >
              {{ op.text }}
            </el-button>

            <el-button
              v-else-if="op.type === 'button' && op.action_params"
              :type="getActionParams(op.action_params, 'type')"
              size="small"
              :plain="getActionParams(op.action_params, 'plain')"
              @click.stop="clickBtn(op, scope.row)"
            >
              {{ op.text }}
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <el-row>
      <Pagination
        v-if="tableData.usePager"
        class="pager"
        :pager="tableData.pager.data"
        :justify="tableData.pager.justify"
        :customPageSize="tableData.pager.customPageSize"
        :showSize="tableData.pager.showSize"
        @getList="getList"
      ></Pagination>
    </el-row>
  </div>
</template>
<script>
import store from '@/store'
import { commonApi, templateApi } from '@/api/index'
import Pagination from '@/components/Pagination'
import { INDIVIDUAL_LIST, ENTERPRISE_LIST, CERTIFICATION_TYPE_ENUMS } from '@/pages/workbench/constants'
import { rowSelectFilter } from '@/utils'
import { get } from 'lodash-es'

export default {
  components: {
    Pagination
  },
  props: {
    tableData: {
      type: Object,
      default: function () {
        return {}
      }
    },
    size: {
      type: String,
      default: () => {
        return 'medium'
      }
    },
    tableHeight: String
  },

  data() {
    return {
      CERTIFICATION_TYPE_ENUMS,
      multipleSelection: [],
      currentRow: {},
      loading: false,
      arctypes: [],
      ENTERPRISE_LIST,
      INDIVIDUAL_LIST,
      isArchive: false
    }
  },

  computed: {
  },

  watch: {
    tableData: {
      handler: function (val) {
        if (!val || !val.columns) return
        const hasArctype = (val.columns || []).findIndex(col => col.label === '分区') > -1
        if (hasArctype && (!this.arctypes || this.arctypes.length === 0)) {
          this.getArchiveType()
        }
      },
      deep: true
    }
  },

  mounted() {
    const path = this.$route.path
    const archiveRoutes = [
      '/archive/archive-review-list',
      '/archive/archive-list'
    ]
    this.isArchive = archiveRoutes.some(item => path.indexOf(item) !== -1)
  },

  methods: {
    safeGetValue(dataSource, getPath, defaultVal = '') {
      try {
        return get(dataSource, getPath)
      } catch (e) {
        console.error(e)
        return defaultVal
      }
    },
    rowClick(row, col, event) {
      if (rowSelectFilter(event)) this.$emit('rowClick', row)
    },
    changeRow(row, preRow) {
      this.currentRow = row || {}
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
      store.dispatch('tabulation/toggleSeletion', this.multipleSelection)
    },
    setCurrent(row) {
      this.$nextTick(() => {
        this.$refs.multipleTable && this.$refs.multipleTable.setCurrentRow(row)
      })
    },
    toggleSelection() {
      this.multipleSelection = []
      this.cancelSelection()
      this.tableData.data.forEach(row => {
        this.$refs.multipleTable.toggleRowSelection(row)
      })
    },
    cancelSelection() {
      this.$refs.multipleTable.clearSelection()
    },
    findActionClass(col) {
      const { text, action_params: actionParams } = col || {}
      // 对稿件的状态颜色做特殊处理，这里用状态名称判断稿件状态三大颜色
      if (this.isArchive) {
        if (['开放浏览', '橙色通过'].indexOf(text) !== -1) {
          return 'action-color-green'
        } else if (['待审', '修复待审', '等待转码', '分发中'].indexOf(text) !== -1) {
          return 'action-color-blue'
        } else {
          return 'action-color-red'
        }
      } else {
        return `action-color-${actionParams}`
      }
    },
    doLayout() {
      this.$refs.multipleTable.doLayout()
    },
    openDialog(op, row) {
      this.$emit('openDialog', op, row)
    },
    openLog(op) {
      if (op.action === 'log' && op.href) {
        this.$emit('getOperationLog', op)
      }
    },
    getActionParams(op, key) {
      const params = JSON.parse(op)
      return params[key]
    },
    clickBtn(op, row) {
      const path = JSON.parse(op.action_params).href || ''
      const useBack = this.$route.path.split('/')[3] === 'text' || this.$route.path.split('/')[3] === 'audio'
      if (op.action === 'link') {
        this.$router.push({
          path,
          query: {
            back: useBack ? this.$route.fullPath : ''
          }
        })
      } else if (op.action === 'detail') {
        templateApi.getAllDynamicRoutes().then(res => {
          if (res.data.length) {
            const route = res.data.find(d => +d.name === +row.business_id)
            this.$router.push({
              path: route.path,
              query: {
                business_id: row.business_id,
                rid: row.rid,
                oid: row.oid.val[0].text,
                flow_id: row.flow_id,
                isResource: 1,
                businessType: this.$route.path.split('/')[3],
                back: useBack ? this.$route.fullPath : ''
              }
            })
          }
        }).catch(_ => {})
      }
    },
    getArchiveType() {
      this.arctypes = []
      commonApi.getArctype().then(res => {
        (res.data || []).forEach(d => {
          if (d.children) this.arctypes.push(...d.children)
        })
      }).catch(_ => {})
    },
    arctype(id) {
      return (this.arctypes.find(t => t.id === id) || {}).name
    },
    getList(val) {
      this.$emit('getList', false, val)
    },
    openImg(href) {
      window.open(href)
    }
  }
}
</script>
<style lang="stylus">
.tabulation {
  width: 100%
  height: auto
  margin: 0px 0px -20px
  .a-oid {
    color: var(--link-color)
    cursor: pointer
    font-weight: 400
  }
  .verifyIcon {
    display: inline-block
    vertical-align: bottom
    margin-top: 2px
    width: 18px
    height: 18px
    background-image: url('~@/assets/user-auth.png')
  }
  .individual {
    background-position: -39px -82px
  }
  .enterprise {
    background-position: -4px -81px
  }
  .delete {
    color: var(--purple)
    border-color: var(--purple)
    background: var(--purple-light-1)
  }
  .caret {
    position: relative
    .el-icon-caret-top {
      position: absolute
      top: 9px
      cursor: pointer
      &:hover {
        color: var(--link-color)
      }
    }
    .el-icon-caret-bottom {
      position: absolute
      top: 17px
      cursor: pointer
       &:hover {
        color: var(--link-color)
      }
    }
  }
  .el-tag {
    border: none
  }
  .pager {
    margin: 14px 0px
  }
  .user-select {
    user-select: all
    -moz-user-select: all;
    -webkit-user-select: all;
    -ms-user-select: all;
  }
  .blue-fans-font {
    color: var(--blue)
    font-weight: bold
  }
  .red-fans-font {
    color: var(--red)
    font-weight: bold
  }
  .orange-fans-font {
    color: var(--orange)
    font-weight: bold
  }
  .action-color-green {
    background-color: rgba(92, 184, 92, .1)
    color: rgb(92, 184, 92)
  }
  .action-color-blue {
    background-color: rgba(64, 169, 255, .1)
    color: rgb(64, 169, 255)
  }
  .action-color-red {
    background-color: rgba(238, 80, 55, .1)
    color: rgb(238, 80, 55)
  }
  .action-color-1 {
    background-color: rgba(64,158,255,.1)
    color: var(--blue)
  }
  .action-color-2 {
    background-color: rgba(103,194,58,.1)
    color: var(--success-color)
  }
  .action-color-3 {
    background-color: rgba(144,147,153,.1)
    color: var(--grey-light-1)
  }
  .action-color-4 {
    background-color: rgba(230,162,60, .1)
    color: var(--warning-color)
  }
  .action-color-5 {
    background-color: rgba(253,0,84,.1)
    color: #fd0054
  }
  .action-color-6 {
    background-color: rgba(250,219,20,.1)
    color: #fadb14
  }
  .action-color-7 {
    background-color: rgba(19,194,194,.1)
    color: #13c2c2
  }
  .action-color-8 {
    background-color: rgba(49,97,163,.1)
    color: #3161a3
  }
  .action-color-9 {
    background-color: rgba(162,110,161,.1)
    color: #a26ea1
  }
  .action-color-10 {
    background-color: rgba(121, 201, 189,.1)
    color: #79C9BD
  }
  .action-color-11 {
    background-color: rgba(28,129,157,.1)
    color: #1c819e
  }
  .action-color-12 {
    background-color: rgba(34,40,49,.1)
    color: #222831
  }
  .action-color-13 {
    background-color: rgba(169,104,81,.1)
    color: #a96851
  }
  .action-color-14 {
    background-color: rgba(200,217,235,.1)
    color: #c8d9eb
  }
  .action-color-15 {
    background-color: rgba(241,138,155,.1)
    color: #f18a9b
  }
  .action-color-16 {
    background-color: rgba(232,234,161,.1)
    color: #e8eaa1
  }
  .action-color-17 {
    background-color: rgba(204,162,225,.1)
    color: #cca2e1
  }
  .action-color-18 {
    background-color: rgba(157,143,143,.1)
    color: #9d8f8f
  }
  .action-color-19 {
    background-color: rgba(255,175,135,.1)
    color: #ffaf87
  }
  .action-color-20 {
    background-color: rgba(147,222,255,.1)
    color: #93deff
  }
  .el-tooltip {
    display: inline-block
  }
  .cell-one-line {
    div, p {
      display: inline
    }
  }
}
</style>
