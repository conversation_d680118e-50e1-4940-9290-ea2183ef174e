<template>
  <div>
    <el-row type="flex" :justify="justify">
      <el-pagination
        :small="showPager.small"
        @size-change="pagerSizeChange"
        @current-change="pagerPageChange"
        :current-page.sync="showPager.pn"
        :page-sizes="showPageSize"
        :page-size="showPager.ps"
        :layout="layout"
        :total="totalSize">
      </el-pagination>
    </el-row>
  </div>
</template>

<script>
export default {
  props: {
    // 分页数据
    pager: Object,
    // 相对位置
    justify: {
      type: String,
      default: 'start'
    },
    // 是否启用前端分页
    computedPager: {
      type: Boolean,
      default: false
    },
    tableData: {
      type: Array,
      default: () => {
        return []
      }
    },
    customPageSize: {
      type: Array,
      default: () => {
        return []
      }
    },
    showSize: {
      type: String,
      default: () => {
        return 'large'
      }
    }
  },
  data() {
    return {
      pageSize: [10, 20, 30, 50]
    }
  },
  computed: {
    totalSize() {
      return this.computedPager ? this.tableData.length : this.showPager.total
    },
    showPageSize() {
      return this.customPageSize.length ? this.customPageSize : this.pageSize
    },
    layout() {
      return this.showSize === 'large'
        ? 'total, sizes, prev, pager, next, jumper'
        : this.showSize === 'small'
        ? 'prev, pager, next'
        : 'total, prev, pager, next, jumper'
    },
    showPager() {
      return this.pager || {}
    }
  },

  created() {},

  methods: {
    pagerSizeChange(val) {
      this.pager.ps = val
      !this.computedPager && this.$emit('getList')
    },
    pagerPageChange(val) {
      this.pager.pn = val
      !this.computedPager && this.$emit('getList', val)
    }
  }
}
</script>
