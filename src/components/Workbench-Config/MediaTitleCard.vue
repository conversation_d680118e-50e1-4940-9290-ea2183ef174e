<template>
  <div>
    <el-card>
      <div slot="header">
        <span>播单标题</span>
      </div>
      <el-table
        border
        :data="showTableData"
        tooltip-effect="dark"
        class="w-full mt-20 mb-20">
        <el-table-column align="center" label="播单标题">
          <template v-slot="scope">
            <p v-highlight-config="card.hglAreaName" :key="scope.row.Name">{{scope.row.Name}}</p>
          </template>
        </el-table-column>
        <el-table-column label="播单状态" align="center">
          <template v-slot="scope">
            <span>{{titleState[scope.row.Attr & ATTR_STATUS_NUM]}}</span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page.sync="pager.pn"
      :page-size="10"
      :page-sizes="[5, 10, 20]"
      layout="total, sizes, prev, pager, next"
      :total="pager.total">
      </el-pagination>
    </el-card>
  </div>
</template>
<script>
import { mediaApi } from '@/api/index'
// 播单标题判断是否公开位标识
const ATTR_STATUS_NUM = 1
export default {
  name: 'MediaTitleCard',
  props: {
    detail: {
      type: Object,
      default: () => {
        return {}
      }
    },
    // 工作台卡片配置
    card: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data () {
    return {
      tableData: [],
      pager: {
        ps: 10,
        pn: 1,
        total: 0
      },
      titleState: {
        0: '公开',
        1: '非公开'
      },
      ATTR_STATUS_NUM
    }
  },
  computed: {
    showTableData() {
      return [...this.tableData].splice((this.pager.pn - 1) * this.pager.ps, this.pager.ps)
    }
  },
  watch: {
    'detail.resource': {
			handler(val) {
				if (val) {
					this.getTitle()
				}
      },
      immediate: true,
      deep: true
		},
    showTableData: {
      handler(val) {
        if (val.length > 0) {
          this.$nextTick(() => {
            this.$EventBus.$emit('sensitiveWordChange')
          })
        }
      }
    }
  },
  methods: {
    getTitle() {
      this.tableData = []
      const resource = (this.detail && this.detail.resource) || {}
      mediaApi.getMediaTitle({
        typ: 2,
        mid: +resource.mid,
        vmid: +resource.mid
      }).then(res => {
        this.tableData = res.data.res || []
        this.pager.total = this.tableData.length
      }).catch(_ => {})
    },
    handleSizeChange(val) {
      this.pager.ps = val
    },
    handleCurrentChange(val) {
      this.pager.pn = val
    }
  }
}
</script>
<style lang="stylus" scoped>

</style>
