<template>
  <div class="comic-community-card">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>审核内容</span>
      </div>
      <div class="hit-msg" v-if="hitReason">命中：{{ hitReason }}</div>
      <!-- 图片 -->
      <ImagesBox style="margin-bottom: 20px;" :showClose="false" :images="comicData.images" :showing="true" @overview="openViewer"></ImagesBox>
      <ViewerBox
        ref="viewer"
        :imgArray="comicData.images"
        :options="options"
        viewerID="ViewBoxImages"
      />
      <p class="comic-desc">
        <span>文字描述：{{ comicData.topicId }}</span>
        <span style="margin-left: 50px">话题：{{ comicData.topicTitle }}</span>
      </p>
    </el-card>
  </div>
</template>
<script>
import { mappingKV } from '@/pages/workbench/common.js'
import ImagesBox from '@/components/ImagesBox/imagesBox.vue'
import ViewerBox from '@/components/ViewerBoxV2'

export default {
  components: {
    ImagesBox,
    ViewerBox
  },
  props: {
    resource: {
      type: Object,
      default: () => {
        return {}
      }
    },
    card: {
      type: Object,
      default: () => {
        return {}
      }
    },
    detail: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      options: {
        initialViewIndex: 0,
        keyboard: false,
        loading: false,
        transition: false
      }
    }
  },
  computed: {
    computedResource() {
      if (this.card?.source) {
        return mappingKV(this.card.source, this.detail)?.resource || {}
      }
      return this.resource
    },
    hitReason() {
      if (this.computedResource?.metas) {
        const { hit, reason } = this.computedResource.metas
        const reasons = []

        if (hit) {
          reasons.push(hit)
        }

        if (reason) {
          reasons.push(reason)
        }

        return reasons.join('；')
      }

      return ''
    },
    imgArray() {
      const images = this.computedResource?.metas?.images || []
      return images
    },
    comicData() {
      const images = this.computedResource?.metas?.images || []
      return {
        topicId: this.computedResource?.metas?.topic_id,
        topicTitle: this.computedResource?.metas?.topic_title,
        images: images.map(item => {
          return {
            src: item
          }
        })
      }
    }
  },
  methods: {
    openViewer(index) {
      this.$refs.viewer.openViewer(index)
    }
  }
}
</script>
<style lang="stylus" scoped>
.comic-community-card
  height calc(100% - 2px)
  .el-card
    margin-bottom 0px
    height 100%
  .hit-msg
    height 20px
    background #FEF0F0
    color var(--error-color)
    font-size 12px
    padding 9px
    overflow auto
    margin-left 10px
    line-height 20px
  .pictures
    display flex
    width 100%
    overflow auto
    img
      width 600px
      height 300px
      margin-right 20px
</style>
