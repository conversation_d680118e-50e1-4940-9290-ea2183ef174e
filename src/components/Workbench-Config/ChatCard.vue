<template>
  <div class="chat-card" :style="{'height': `${(card.style && card.style.height) || 'unset'}`}">
    <el-card class="box-card chat-info">
      <div slot="header" class="clearfix">
        <span>{{card.title}}</span>
      </div>
        <IM
          :msgs="currentMsgs"
          :noMore="currentNoMore"
          @load-more="getComputedNextPageMsg"
        />
    </el-card>
  </div>
</template>
<script>
import { mapGetters, mapState, mapActions } from 'vuex'
import IM from '@/components/IM/IM'
import { chatApi } from '@/api'

export default {
  name: 'ChatCard',
  inheritAttrs: false,
  components: {
    IM
  },
  props: {
    card: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      sessionId: '',
      sessionType: '',
      pageNum: 0,
      allMsgs: [],
      pageSize: 20
    }
  },
  watch: {
    detail(nextVal) {
      if ((nextVal.resource && nextVal.resource.oid) || (+this.$route.query.todo_type) === 1) {
        // 赋值参数
        this.sessionId = nextVal.resource.oid
        const metas = nextVal.resource.metas || {}
        this.sessionType = metas.receiver_type || 1
        this.set([])
        // 获取全部数据
        this.getAllData()
      }
    }
  },
  computed: {
    ...mapGetters({
      getEnv: 'env/getEnv'
    }),
    ...mapState('todoDetail', [
      'history',
      'detail'
    ]),
    ...mapState('chat', [
      'chatMsgsData'
    ]),
    currentMsgs() {
      return this.allMsgs.slice(0, this.pageNum * this.pageSize)
    },
    currentNoMore() {
      if (+this.$route.query.todo_type === 1) {
        // 质检详情页
        return this.currentMsgs.length === this.detail.snapshotDetail?.snapshotChat?.length
      } else {
        return this.currentMsgs.length === this.chatMsgsData?.length
      }
    }
  },
  methods: {
    ...mapActions({
      setChatCardData: 'chat/setChatCardData'
    }),
    getComputedNextPageMsg() {
      if (this.currentNoMore) return
      this.pageNum += 1
    },
    fetchAuditMsg(otherParams = {}) {
      const params = {
        session_id: this.sessionId,
        session_type: this.sessionType,
        size: this.pageSize,
        ...otherParams
      }
      chatApi.getAuditMsg(params).then((res) => {
        const { msgs, next_offset: nextOffset } = res.data
        const msgData = (msgs && msgs.map((msg) => {
          return {
            ...msg,
            content: JSON.parse(msg.content),
            face: msg.sender_face,
            uname: msg.sender_name
          }
        })) || []
        const totalMsgs = this.chatMsgsData.concat(msgData)
        this.set(totalMsgs)
        if (nextOffset !== 0) {
          this.fetchAuditMsg({offset: nextOffset})
        } else {
          this.allMsgs = [...this.chatMsgsData]
          this.getComputedNextPageMsg()
        }
        }).catch(_ => {})
    },
    set(msgs) {
      this.setChatCardData(msgs)
    },
    getAllData() {
      if (+this.$route.query.todo_type === 1) {
        this.allMsgs = this.detail.snapshotDetail?.snapshotChat
        this.getComputedNextPageMsg()
      } else {
        this.fetchAuditMsg()
      }
    }
  }
}
</script>
<style lang="stylus" scoped>
.chat-card
  height 100% !important
  .chat-info
    height 100%
    .el-card_body
      height calc(100% - 40px)
      overflow auto
</style>
