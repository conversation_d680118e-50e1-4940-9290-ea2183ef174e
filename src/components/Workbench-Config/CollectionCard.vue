<template>
  <div class="collection-card">
    <el-card class="box-card">
      <div slot="header" class="clearfix header-flex">
        <span>合集信息</span>
      </div>
      <el-form
        :model="seasonBaseInfo"
        class="drama-info"
        @submit.stop.prevent.native
      >
        <el-form-item label="合集标题：" class="drama-title">
          <TextareaDivVue
            v-highlight-config="card.sensitive_words && card.sensitive_words.title"
            :rows="6"
            :key="seasonBaseInfo.title"
          >
            {{ seasonBaseInfo.title }}
          </TextareaDivVue>
          <div
            class="cover nothing"
            v-if="
              !seasonBaseInfo.cover ||
              seasonBaseInfo.cover.indexOf('transparent') >= 0
            "
          >
            无预览
          </div>
          <img
            :src="seasonBaseInfo.cover"
            @click="showPreivewImage"
            class="cover"
            v-else
          />
          <ViewerBox
            :imgArray="imgArray"
            :options="options"
            viewerID="coverViewBoxImages"
            @close="onClose"
          />
        </el-form-item>
        <el-form-item label="合集简介：" class="desc">
          <TextareaDivVue
            v-highlight-config="card.sensitive_words && card.sensitive_words.desc"
            :rows="3"
            :key="seasonBaseInfo.desc"
          >
            {{seasonBaseInfo.desc}}
          </TextareaDivVue>
        </el-form-item>
        <el-form-item label="合集价格：" v-if="seasonBaseInfo.paySeason" >{{ seasonBaseInfo.price / 100 }}元</el-form-item>
        <el-form-item label="总集数：" v-if="seasonBaseInfo.paySeason">{{ seasonBaseInfo.epNum }}</el-form-item>
      </el-form>
    </el-card>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import ViewerBox from '@/components/ViewerBox'
import TextareaDivVue from '@/components/element-update/TextareaDiv'

export default {
  name: 'CollectionCard',
  data() {
    return {
      imgArray: [],
      options: {
        initialViewIndex: 0
      }
    }
  },
  props: {
    card: {
      type: Object,
      default: () => {}
    }
  },
  components: {
    ViewerBox,
    TextareaDivVue
  },
  computed: {
    ...mapState('todoDetail', [
      'detail'
    ]),
    seasonBaseInfo() {
      const resource = this.detail.resource || {}
      return {
        title: resource.content || '',
        cover: resource.metas?.cover || '',
        desc: resource.metas?.desc || resource.extra1s || '',
        paySeason: !!resource.metas?.pay_season,
        price: resource.metas?.price,
        epNum: resource.metas?.ep_num
      }
    }
  },
  methods: {
    mappingKV(keys, source = null) {
      let result = ''
      if (!source) {
        source = this
      }
      if (keys) {
        const newArray = keys.split('.')
        newArray.forEach((key, index) => {
          if (index === 0) {
            result = (key in source) ? source[key] : {}
          } else {
            result = (result && (key in result)) ? result[key] : ''
          }
        })
      }
      return result
    },
    showPreivewImage() {
      this.imgArray = [
        {
          src: this.seasonBaseInfo.cover,
          name: '合集封面'
        }
      ]
    },
    onClose() {
      this.imgArray = []
    }
  }
}
</script>
<style lang="stylus" scoped>
.collection-card
  .header-flex
    display flex
    justify-content space-between
    &:nth-of-type(1)
      line-height 2
  .drama-info
    .desc
      display flex
      width 100%
      margin-top 8px
    >>>.el-form-item__content
      // width calc(100% - 82px)
      display flex
    .drama-title
      >>>.el-form-item__content
        display flex
      .el-textarea
        width 54%
    .cover
      height 138px
      width 45%
      display inline-block
      margin-left 12px
    .nothing
      border 1px solid #fbfbfb
      text-align center
      line-height 132px
      font-size 20px
      color var(--text-color-light-2)
      font-weight bold
      border-radius 4px
  >>>.el-textarea__inner
    background-color: initial !important
    color: var(--text-color-dark-1) !important
</style>
