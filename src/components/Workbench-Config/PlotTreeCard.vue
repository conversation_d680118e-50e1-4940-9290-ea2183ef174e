<template>
  <div class="workbench-plot-tree-card">
    <el-card class="box-card history" :style="card.style">
      <div slot="header" class="clearfix">
        <span>剧情树信息</span>
      </div>
       <div class="modify" v-if="detail.resource && detail.resource.metas && detail.resource.metas.diff_msg">
        <p>{{( detail.resource.metas.diff_msg) || '暂无修改'}}</p>
      </div>
      <div class="vars-name">隐藏数值名：{{detail.resource && detail.resource.metas && detail.resource.metas.vars_name}}</div>
      <el-table
      :data="tableData"
      :height="detail.resource && detail.resource.metas && detail.resource.metas.diff_msg ? 580 : 622"
      :span-method="spanMethod"
      :cell-class-name="cellClassName"
      :row-class-name="emptyRowClassName"
      @selection-change="handleSelectionChange"
      style="width: 100%">
        <el-table-column
        type="selection"
        align="center"
        width="55">
        </el-table-column>
        <el-table-column
        align="center"
        label="内容">
          <el-table-column
          label="剧情信息">
            <template v-slot="scope">
              <span v-if="scope.row.type === 'option'"><em class="option">剧情{{scope.row.node_id}}-{{scope.row.text}}</em>&nbsp;{{scope.row.title}}</span>
              <span v-else-if="scope.row.type === 'node'"><em class="node">剧情{{scope.row.node_id}}</em>&nbsp;{{scope.row.name}}</span>
              <p></p>
            </template>
          </el-table-column>
          <el-table-column
          label="分P信息">
            <template v-slot="scope">
              <span v-if="scope.row.type === 'node'">{{`${scope.row.video_title}（${scope.row.cid})`}}</span>
              <p></p>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column
          align="center"
          prop="ops"
          label="操作">
          <template v-slot="scope">
            <el-button v-if="scope.row.type === 'node'" size="mini" plain type="primary" @click="previewVideo(scope.row)">预览</el-button>
            <el-button v-if="scope.row.type === 'option'" size="mini" plain type="primary" @click="seeMoreDetail(scope.row)">查看选项设置</el-button>
          </template>
        </el-table-column>
      </el-table>
      <VideoDialog ref="video" :detail="detail"></VideoDialog>
      <MoreDetailDialog ref="configDetail"></MoreDetailDialog>
    </el-card>
  </div>
</template>
<script>
import store from '@/store'
import { mapState } from 'vuex'
import VideoDialog from '../../pages/contentAudit/task/iv/dialog/VideoDialog'
import MoreDetailDialog from '../../pages/contentAudit/task/iv/dialog/MoreDetailDialog'
export default {
  components: {
    VideoDialog,
    MoreDetailDialog
  },
  props: {
    card: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    const OPTION_MAP = {
      0: 'A',
      1: 'B',
      2: 'C',
      3: 'D'
    }
    return {
      multipleSelection: [],
      tableData: [],
      OPTION_MAP,
      currentGraphData: []
    }
  },
  computed: {
    ...mapState('todoDetail', [
      'detail',
      'enumData'
    ])
  },
  watch: {
    'enumData.graphData': {
      handler: function(val) {
        if (val) {
          this.setTableData()
        }
      },
      deep: true
    }
  },
  methods: {
    setTableData() {
      this.tableData = []
      this.enumData.graphData.forEach((node, index) => {
        const choices = node.choices || []
        node.type = 'node'
        this.tableData.push(node)
        if (node.show_time === 0) this.tableData.push({})
        if (choices.length) {
          if (node.show_time === 0) return
          choices.forEach((c, i) => {
            const row = {
              type: 'option',
              node_id: node.node_id,
              text: `选项${this.OPTION_MAP[i]}`,
              ...c
            }
            this.tableData.push(row)
            this.tableData.push({
              type: 'node',
              ...c.to_node
            })
          })
          this.tableData.push({})
        }
      })
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      if (row.type === 'option') {
        if (columnIndex === 1) {
          return [1, 2]
        } else if (columnIndex === 2) {
          return [0, 0]
        }
      }
      if (!row.type && columnIndex === 0) {
        return [1, 4]
      }
    },
    emptyRowClassName({row, rowIndex}) {
      if (!row.type) {
        return 'empty-row'
      }
      return ''
    },
    cellClassName({row, column, rowIndex, columnIndex}) {
      if (!row.type) {
        return 'empty-cell'
      }
      return ''
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
      store.dispatch('graph/selectedNodes', this.multipleSelection)
    },
    previewVideo(row) {
      this.$refs.video.openDialog(row)
    },
    seeMoreDetail(row) {
      this.$refs.configDetail.openDialog(row)
    }
  }
}
</script>
<style lang="stylus">
.workbench-plot-tree-card {
  .modify {
    height: 36px
    background: #FEF0F0
    color: var(--error-color)
    font-size: 13px
    padding: 11px
    box-sizing: border-box
    overflow: auto
    margin-bottom: 10px
  }
  .vars-name {
    padding: 8px 0px 20px 0px
    font-size: 14px
  }
  .el-table .empty-row {
    background: #F8F9FB
  }
  .el-table .empty-cell > .cell > .el-checkbox {
    display: none
  }
  .option {
    height: 24px
    padding: 6px
    background: #FDF5EC
    color: #E8AB51
    border-radius: 7px
  }
  .node {
    height: 24px
    padding: 6px
    border-radius: 7px
    color: #54A8FF
    background: var(--blue-light-2)
  }
}
</style>
