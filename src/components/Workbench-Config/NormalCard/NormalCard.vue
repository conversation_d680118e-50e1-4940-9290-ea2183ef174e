<template>
  <div class="workbench-normal-card" :style="card.style">
    <el-card v-if="show()">
      <div slot="header" class="clearfix">
        <span>{{card.title}}</span>
        <span :style="card.extra_title.style" v-if="card.extra_title && card.extra_title.type === 'mapping'">{{mapping(card.extra_title)}}</span>
      </div>
      <el-form inline @submit.stop.prevent.native v-bind="card.attributes">
        <el-row v-for="(row, key) in card.data" :key="key" :style="row.style">
          <el-form-item
            v-for="(item, index) in row"
            v-if="item.hide_on_empty ? !!mappingKV(item.key) : true"
            :key="index"
            :label="`${item.label ? `${item.label}：` : ''}`"
            v-bind="item.options"
            :class="{
							'text': item.type === 'textarea' || item.type === 'hit' || item.type === 'hitPrue',
							'tag-item': item.type === 'tag' ,
							'multiple-img': item.type === 'multipleImg',
              'full-width': item.type === 'wash_text'
						}">

            <!-- 粉丝数 -->
            <span
              v-if="item.type === 'follower' && getUserInfo(item)"
              :class="[{
                'emphasis-text': item.dark,
                'blue-fans-font': getUserInfo(item).follower >= 10000 && getUserInfo(item).follower < 100000,
                'orange-fans-font': getUserInfo(item).follower >= 100000 && getUserInfo(item).follower < 1000000,
                'red-fans-font': getUserInfo(item).follower >= 1000000,
                'font-bold': item.bold
              }]"
            >
              {{ getUserInfo(item).follower }}
            </span>

            <!-- 文字域展示 -->
            <el-input
              v-else-if="item.type === 'textarea'"
              type="textarea"
              class="textarea-highlight"
              :class="[{'warning': item.status === 'warning'}]"
              v-bind="item.attributes"
              :value="mappingKV(item.key)"
              disabled>
            </el-input>

            <!-- 单图展示 -->
            <div v-else-if="item.type === 'img'">
              <div
                v-if="!mappingKV(item.key) || mappingKV(item.key).indexOf('transparent') >= 0"
                class="cover nothing"
                :style="item.style"
              >
                无预览
              </div>
              <img
                v-else
                :src="mappingKV(item.key)"
                class="cover"
                :style="item.style"
                @click="showPreivewImage(item.key, item.label, index)"
              />
              <ViewerBox
                :imgArray="imgObj[index]"
                :options="{...options, ...(item.viewer_params|| {})}"
                :viewerID="`coverViewBoxImages${index}`"
                @close="onClose"
              />
            </div>

            <!-- 多图展示 -->
            <div v-else-if="item.type === 'multipleImg' && mappingKV(item.key)">
              <div v-for="(pic, imgIndex) in imgs(item.key)" :key="imgIndex" style="display: inline-block">
                <img
                  :src="pic"
                  class="cover"
                  :style="item.style"
                  @click="showMultiplePreivewImage(imgIndex, item, index)"
                  v-if="pic && pic.indexOf('transparent') < 0"
                />
                <div
                  v-else
                  class="cover nothing"
                  :style="item.style"
                >
                  无预览
                </div>
              </div>
              <ViewerBox
                :imgArray="multiImg[index]"
                :options="multiImgOptions[index]"
                :viewerID="`multipleCoverViewBoxImages${index}`"
                @close="onCloseMultiImg"
              />
            </div>
            <!-- 多图无预览展示 -->
            <div
              v-else-if="item.type === 'multipleImg' && !mappingKV(item.key)"
              class="cover nothing"
              :style="itme.style"
            >
              无预览
            </div>

            <!-- tag -->
						<div class="tags" v-else-if="item.type === 'tag'">
              <el-tag type="info" size="mini" v-for="(tag, tagInx) in (mappingKV(item.key) || '').split(',')" :key="tagInx">{{tag}}</el-tag>
            </div>

            <!-- 文字按钮跳转 -->
						<el-button class="link-btn" type="text" v-else-if="item.type === 'link'" @click="jump(item)">{{mappingKV(item.key)}}</el-button>

            <!-- 敏感词匹配内容 -->
            <p v-else-if="item.type === 'hit'" v-html="getHitContent(item.key)" class="hit el-textarea__inner" :class="{ 'font-bold': item.bold, 'emphasis-text': item.dark }" v-highlight-config="item.hglAreaName" :key="getHitContent(item.key)"></p>
            <!-- 敏感词匹配内容，无textarea -->
            <p v-else-if="item.type === 'hitPure'" v-html="getHitContent(item.key)" class="hit hit-pure"></p>

            <!-- 普通文字 -->
            <div v-else-if="item.type === 'text'">
              <span class="base-text" :class="{ 'font-bold': item.bold, 'emphasis-text': item.dark }" v-if="item.action === 'kv'">{{ handleValueChange(item) }}</span>
              <span class="base-text" :class="{ 'font-bold': item.bold, 'emphasis-text': item.dark }" v-else-if="item.action === 'extra'">{{ extraValue(item) }}</span>
              <span class="base-text" :class="{ 'font-bold': item.bold, 'emphasis-text': item.dark }" v-else-if="item.action === 'mapping'">{{ mapping(item) }}</span>
              <span class="base-text" :class="{ 'font-bold': item.bold, 'emphasis-text': item.dark }" v-else-if="item.action === 'formattime'">{{ formatTime(item) }}</span>
              <span class="base-text" :class="{ 'font-bold': item.bold, 'emphasis-text': item.dark }" v-else v-highlight-config="item.hglAreaName" :key="mappingKV(item.key)">{{ mappingKV(item.key) }}</span>
            </div>

            <!-- 未成年标识 -->
            <div v-else-if="item.type === 'under_age'">
               <span :class="{ 'base-text': true, 'warn-value': mappingKV(item.key) === 0 }">{{ mapping(item, USER_UNDERAGE_TIP_MAP) }}</span>
            </div>
            <!-- 认证用户信息 -->
            <div v-else-if="item.type === 'up_nickname'" style="display:flex;">
              <!-- 用户认证 -->
              <span
                v-if="detail.resource && detail.resource.user_info"
                class="verifyIcon"
                :class="{
                  individual: INDIVIDUAL_LIST.includes(detail.resource.user_info.official.role),
                  enterprise: ENTERPRISE_LIST.includes(detail.resource.user_info.official.role)
                }">
              </span>
              <!-- 用户名 -->
              <span class="base-text">{{  detail.resource && detail.resource.user_info && detail.resource.user_info.name }}</span>
            </div>

            <div v-else-if="item.type==='oper_tag'">
              <el-tag
                v-bind="item"
                :type="OPER_BUTTON_TYPES[mappingKV(item.key)]"
              >
              {{ mappingKV(item.key) }}
              </el-tag>
            </div>

            <div v-else-if="item.type === 'boolean_text'" :class="{ 'warning-text': mappingKV(item.key) }">
              {{ mappingKV(item.key) ? '是' : '否' }}
            </div>

            <div v-else-if="item.type === 'enum_text'">
              {{ getEnumValue(mappingKV(item.key), item) }}
            </div>

            <div v-else-if="item.type === 'textarea-div'">
              <TextareaDiv :rows="item.rows" :style="item.style" v-html="getCustomHtml(item)">
              </TextareaDiv>
            </div>
            <div v-else-if="item.type === 'wash_text'">
              <Warnings :warnings="getWashText(item)" />
            </div>
            <!-- custom content -->
            <div v-else>
              <div :style="item.style" v-html="getCustomHtml(item)"></div>
            </div>
          </el-form-item>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>
<script>
/**
 * @component
 * @assetTitle 工作台详情配置-NormalCard组件
 * @assetDescription 工作台详情页支持自定义配置的卡片组件
 * @assetImportName NormalCard
 * @assetTag 工作台详情配置组件
 */
import { mapGetters, mapState } from 'vuex'
import moment from 'moment'
import { formatRegExp, escapeText } from '@/pages/contentAudit/common'
import ViewerBox from '@/components/ViewerBox'
import { INDIVIDUAL_LIST, ENTERPRISE_LIST, OPER_BUTTON_TYPES } from '@/pages/workbench/constants'
import { evalExpression } from '@/utils/index'
import { USER_UNDERAGE_TIP_MAP } from '@/utils/constant'
import TextareaDiv from '@/components/element-update/TextareaDiv.vue'
import { genWarnings } from '@/v2/data-source/archive/info/adapter'
import Warnings from '@/v2/biz-components/archive/Warnings'

export default {
  components: {
    ViewerBox,
    TextareaDiv,
    Warnings
  },
  props: {
    /**
     * 单个卡片的具体配置
     * @param card.title {string} 卡片标题
     * @param card.attributes {object} 会传入<el-form />的v-bind指令，配置element表单全局属性或者style
     * @param card.data {array} normal卡片里每一行配置
     */
    card: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },

  data() {
    return {
      options: {
        initialViewIndex: 0,
        keyboard: true
      },
      multiImgOptions: {},
      imgObj: {},
      multiImg: {},
      INDIVIDUAL_LIST,
      ENTERPRISE_LIST,
      USER_UNDERAGE_TIP_MAP,
      OPER_BUTTON_TYPES
    }
  },

  computed: {
    ...mapGetters({
      getEnv: 'env/getEnv'
    }),
    ...mapState('todoDetail', [
      'extra',
      'detail',
      'enumData'
    ]),
    ...mapState({
      perms: state => state.user.perms
    }),
    // 判断卡片内是否有敏感词高亮 Area
    hasHglArea() {
      const rows = Object.values(this.card.data)
      for (let i = 0; i < rows.length; i++) {
        for (let j = 0; j < rows[i].length; j++) {
          if (rows[i][j].hglAreaName) return true
        }
      }
      return false
    }
  },
  methods: {
    getUserInfo(item) {
      if (item.userSource) {
        return this.mappingKV(item.userSource, this)?.user_info || this.mappingKV(item.userSource, this) || {}
      }

      return this.detail?.resource?.user_info
    },
    show() {
      if (this.card.action === 'show') {
        /* eslint-disable */
        const fn = new Function(this.card.action_params.expression)
        return fn()
        /* eslint-enable */
      }
      return true
    },
    mappingKV(keys, source = null) {
      let result = ''
      if (!source) {
        source = this
      }
      if (keys) {
        const newArray = keys.split('.')
        newArray.forEach((key, index) => {
          if (index === 0) {
            result = (key in source) ? source[key] : {}
          } else {
            result = (result && (key in result)) ? result[key] : ''
          }
        })
      }
      return result
    },
    getEnumValue(key, itemSchema) {
      let value = key
      const { type, mapping } = itemSchema.source || {}
      if (type === 'local' && typeof mapping === 'object' && mapping.hasOwnProperty(`${key}`)) {
        value = mapping[`${key}`]
      }
      return value
    },
    extraValue(item) {
      let val = '暂无'
      const resource = this.detail.resource || {}
      let key = item.key
      let value = resource[key]
      if (item.source === 'global') {
        value = this.mappingKV(key, this)
        const keys = key.split('.') || []
        key = keys[keys.length - 1]
      }
      val = this.extra[key] && this.extra[key].enum && this.extra[key].enum[value]
      return val
    },
    mapping(item, defaultMapping) {
      let { mapping } = item
      mapping = mapping || defaultMapping
      const value = this.mappingKV(item.key)
      return mapping[value] === undefined ? value : mapping[value]
    },
    formatTime(schema) {
      let resource = this.detail.resource || {}
      const { key, action_params: params = {}, source = '' } = schema
      let data = resource[key]
      if (source === 'global') {
        // 快照
        data = this.mappingKV(key, this)
      }
      const {
        input_type: input,
        output_length: length,
        output_type: output
      } = params
      if (input === 'timestamp') {
        const newTime = length === 10 ? (data / 1000) : data
        if (output === 'timestamp') {
          return newTime
        } else {
          return data ? moment.unix(newTime).format(output) : ''
        }
      } else {
        return data ? moment(data).format(output) : ''
      }
    },
    getHitContent(key) {
      const resource = this.detail.resource || {}
      const hit = resource.hit || []
      let content = escapeText(this.mappingKV(key))
      hit.forEach(key => {
        let newStr = formatRegExp(key)
        let regexp = new RegExp(newStr, 'g')
        content = content.replace(regexp, `<em style="color:var(--red)">${key}</em>`)
      })
      return content
    },
    showPreivewImage(key, label, vIndex) {
      let temp = []
      this.imgObj = {}
      temp.push({
        src: this.mappingKV(key),
        name: label
      })
      this.imgObj[vIndex] = [...temp]
    },
    showMultiplePreivewImage(index, item, vIndex) {
      const imgs = this.imgs(item.key) || []
      this.multiImgOptions[vIndex] = {
        initialViewIndex: index,
        keyboard: true
      }
      this.multiImg = {}
      this.multiImg[vIndex] = imgs.map(pic => {
        return {
          src: pic,
          name: item.label
        }
      })
    },
    onClose() {
			this.imgObj = {}
    },
    onCloseMultiImg() {
      this.multiImg = {}
    },
		imgs(key) {
      let vals = this.mappingKV(key)
      if (vals) {
        return vals.split(',')
      }
      return []
    },
    jump(item) {
      let { url } = item.action_params
      if (item.action === 'dynamic') {
        url = evalExpression('string', url, this.detail)
      }
      window.open(url)
    },
		handleValueChange(item) {
      const key = +this.mappingKV(item.key)
      return item.action_params.enums[key]
    },
    getCustomHtml(item) {
      if (item.action === 'dangerouslyUseHTMLString') {
        return evalExpression('string', item.action_params.html, this)
      }
    },
    getWashText(item) {
      switch (item?.action_params?.washer) {
        case 'genArchiveWarning':
          const rawText = {
            archive: { title: ''},
            ...(this.mappingKV(item.key) || {})
          }
          const washedText = genWarnings(rawText, {
            showSenUserGroup: this.perms.SENSITIVE_USERGROUP,
            env: this.getEnv()
          })
          return washedText
        default:
          return ''
      }
    }
  }
}
</script>
<style lang="stylus">
  .workbench-normal-card {
    .cover {
      display: block
      margin-right: 8px
      margin-bottom 8px
    }
    .nothing {
      border: 1px solid #fbfbfb
      text-align: center
      font-size: 20px
      color: var(--text-color-light-2)
      font-weight: bold
      border-radius: 4px
      line-height: 120px
    }
		.tag-item {
      display: flex
			width: 100%
			.el-form-item__content {
				width 80%
			}
      margin-bottom: 10px !important
    }
		.multiple-img {
			display: flex
			width: 100%
			.el-form-item__content {
				display flex
				width 85%
				flex-wrap wrap
			}
		}
    .full-width {
      width 100%
      .el-form-item__content {
        width 100%
      }
    }
		.tags {
			width 100%
			height: 30px
			border: 1px solid var(--border-color-light-1)
			padding: 4px 0px 8px 13px
			border-radius: 5px
			overflow: auto
			line-height: 30px
      .el-form-item__label {
        width: 100px
      }
			.el-tag {
				margin-right: 8px
			}
		}
    .link-btn {
      padding 8px !important
    }
    .verifyIcon {
      display: inline-block
      vertical-align: bottom
      margin-top: 10px
      width: 18px
      height: 18px
      background-image: url('~@/assets/user-auth.png')
    }
    .individual {
      background-position: -39px -82px
    }
    .enterprise {
      background-position: -4px -81px
    }
    .hit {
      height: 70px
      overflow: auto
      cursor: not-allowed
      width: 100%
    }
    .hit-pure {
      height auto
    }
    .el-textarea__inner {
      background-color: initial !important
      color: var(--text-color-dark-1) !important
    }
    .base-text {
      color: var(--grey-light-1)
    }
    .emphasis-text {
      color: var(--black)
    }
    .warn-value {
      color var(--error-color)
    }
    .warning-text {
      color var(--red)
      font-weight bold
    }
    .text {
      width: 100%
      display: flex
      margin-bottom: 8px !important
      .el-form-item__content {
        flex: 1
      }
    }
    .textarea-highlight {
      textarea{
        background-color: initial !important
        color: var(--text-color-dark-1) !important
      }
      &.warning {
        textarea{
          background-color: initial !important
          font-weight  bold
          color: var(--red) !important
        }
      }
    }
    .blue-fans-font {
      color: var(--blue)
      font-weight: bold
    }
    .red-fans-font {
      color: var(--red)
      font-weight: bold
    }
    .orange-fans-font {
      color: var(--orange)
      font-weight: bold
    }
  }
</style>
