<template>
  <div
    class="vomic-card"
    :style="{ height: `${(card.style && card.style.height) || 'unset'}` }"
  >
    <el-card class="box-card vomic-info">
      <div slot="header" class="clearfix">
        <span>{{ card.title }}</span>
      </div>
      <div class="vomic-content">
        <div class="vomic-title">
          <p>章节短标题：{{ vomicData.shortTitle }}</p>
          <p>章节长标题：{{ vomicData.longTitle }}</p>
        </div>
        <div class="vomic-cover">
          <span class="vomic-desc">封面：</span>
          <img
            class="vomic-img"
            :src="vomicData.cover"
            @click="showPreivewImage"
          />
          <ViewerBox :imgArray="imgArray" :options="options" @close="onClose" />
        </div>
      </div>
      <div class="vomic-video">
        <NanoModulePlayer :key="vomicData.video" :src="vomicData.video" :styleConfig="styleConfig" />
      </div>
    </el-card>
  </div>
</template>
<script>
import { mapGetters, mapState } from 'vuex'
import ViewerBox from '@/components/ViewerBox'
import NanoModulePlayer from '@/components/package/VideoPlayer/NanoModulePlayer'

export default {
  name: 'ChatCard',
  inheritAttrs: false,
  components: {
    NanoModulePlayer,
    ViewerBox
  },
  props: {
    card: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      imgArray: [],
      options: {
        initialViewIndex: 0
      },
      styleConfig: {
        minWidth: 640
      }
    }
  },
  watch: {
  },
  computed: {
    ...mapGetters({
      getEnv: 'env/getEnv'
    }),
    ...mapState('todoDetail', ['detail']),
    vomicData() {
      const resource = this.detail.resource || {}
      return {
        shortTitle: resource.content || '',
        longTitle: resource.extra1s || '',
        video: resource.comic_video_url || '',
        cover: resource.metas?.cover || ''
      }
    }
  },
  methods: {
    showPreivewImage() {
      this.imgArray = [
        {
          src: this.vomicData.cover,
          name: '封面'
        }
      ]
    },
    onClose() {
      this.imgArray = []
    }
  }
}
</script>
<style lang="stylus" scoped>
.vomic-card
  height 100% !important
  .vomic-info
    height 100%
    >>>.el-card__body
      height calc(100% - 40px)
      overflow auto
    .vomic-content
      display flex
      justify-content space-between
      margin-bottom 10px
      width 640px
      .vomic-title
        margin-right 10px
        p
          font-size 14px
          line-height 20px
          margin-bottom 10px
      .vomic-cover
        display flex
        .vomic-desc
          font-size 14px
          line-height 20px
          width 50px
        .vomic-img
          width 200px
          height 150px
</style>
