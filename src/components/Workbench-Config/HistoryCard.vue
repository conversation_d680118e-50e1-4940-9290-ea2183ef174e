<template>
  <div class="workbench-history-card">
    <el-card :class="['box-card','history', isQa && 'qa-card-left']" :style="card.style">
      <div slot="header" class="clearfix">
        <span>{{ title }}</span>
      </div>
       <HistoryLog :history="historyData"></HistoryLog>
    </el-card>

    <el-card class="box-card history qa-card-right" :style="card.style" v-if="isQa">
      <div slot="header" class="clearfix">
        <span>审核记录</span>
      </div>
       <HistoryLog :history="auditHistoryOnQa"></HistoryLog>
    </el-card>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import HistoryLog from '@/components/Common/history-log'
import { resourceApi, workbenchApi } from '@/api'
import { getValueByKeyPath } from '@/v2/utils'

export default {
  components: {
    HistoryLog
  },
  props: {
    card: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    ...mapState('todoDetail', [
      'businessId',
      'todoType',
      'todoId',
      'itemId',
      'detail',
      'history'
    ]),
    ...mapState({
      isAutoQa: (state) => state.qa.isAutoQa
    }),
    isQa() { return this.card.qa || this.isAutoQa },
    title() { return this.isQa ? this.card.title || '质检历史' : this.card.title || '审核历史' },
    historyData() {
      if (this.isAutoQa) return this.history
      const arr = this.card.resourceHistory ? this.resourceHistory : this.history
      // 额外的补充的日志
      const extraLogStr = this.card.extra_log ? getValueByKeyPath(this.detail, this.card.extra_log) : ''
      return this.card.extra_log && extraLogStr ? [...arr, ...extraLogStr.split('，')] : arr
    },
    auditHistoryOnQa() {
      return this.isAutoQa ? this.detail?.resource?.history_snapshot : this.detail?.snapshotDetail?.snapshotHistory || []
    }
  },
  created() {
    this.getResourceHistory()
  },
  data() {
    return {
      resourceHistory: [] // 资源历史
    }
  },
  watch: {
    'detail': {
      handler() {
        this.getResourceHistory()
      }
    }
  },
  methods: {
    getResourceHistory() {
      if (this.card.resourceHistory && this.detail?.resource?.id) {
        // 资源历史
        resourceApi.getResourceLog({
          rid: this.detail?.resource?.id,
          business_id: Number(this.$route.query.business_id),
          action: 'rscsubmit,batchrscsubmit'
        }).then(res => {
          this.resourceHistory = res.data || []
        })
      }
    }
  }
}
</script>
<style lang="stylus">
.workbench-history-card {
  display: table;
  width: 100%;
  table-layout: fixed;
  margin-bottom:12px
  .history {
    overflow: hidden
    display: table-cell;
    .el-card__body {
      height: calc(100% - 70px)
      max-height: 220px
      min-height: 28px
      overflow: auto
      div {
        color: var(--text-color-light-1)
        font-size: 14px
        p {
          padding: 0px 0px 8px
        }
      }
    }
  }
  .qa-card {
    &-left {
      border-top-right-radius: 0
      border-bottom-right-radius: 0
    }
    &-right {
      border-left: none
      border-top-left-radius: 0
      border-bottom-left-radius: 0
    }
  }
}
</style>
