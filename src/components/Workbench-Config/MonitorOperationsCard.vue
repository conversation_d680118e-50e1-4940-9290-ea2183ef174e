<template>
  <div class="workbench-add-to-monitor-dialog">
    <el-card>
      <div slot="header" class="clearfix">
        <span>操作记录</span>
      </div>
      <div class="monitor-oper-log">
        <!-- 1、资源历史 -->
        <HistoryLog :history="monitorLog" class="workbench-log" />
        <!-- 2、一审处理结果&建议 -->
        <div class="result-and-note">
          <div class="reason">
            <p v-if="displayReason">进审理由</p>
            <p v-if="displayReason && displayReason.length">{{ displayReason.join('；') }}</p>
          </div>
        </div>
        <!-- 3、已加监控 -->
        <div class="monitor">
          <p v-if="displayMonitors && displayMonitors.length">已加监控</p>
          <p v-for="(item, idx) in displayMonitors" :key="idx">
            {{ item }}
          </p>
        </div>
      </div>
    </el-card>

    <el-card>
      <div slot="header" class="clearfix">
        <span>{{ card.title || '审核操作' }}</span>
      </div>
      <FlowOper
        v-if="dataReady"
        :operations="operations"
        :tags="tags"
        :taskForm="taskForm"
        :initForm="initForm"
        @update-form="updateForm"
      />
    </el-card>
  </div>
</template>
<script>
import { tagApi } from '@/api'
import { FlowOper } from '@/v2/biz-components/workbench/index'
import { cloneDeep } from 'lodash-es'
import HistoryLog from '@/components/Common/history-log'

export default {
  components: {
    FlowOper,
    HistoryLog
  },
  props: {
    card: {
      type: Object,
      default: () => {}
    },
    resource: {
      type: Object,
      default: () => {}
    },
    detail: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    monitorLog() {
      return this.resource?.monitor_log
    },
    remark() {
      return this.detail?.remark || ''
    }
  },
  watch: {
    'resource.oid': {
      async handler(val) {
        if (!val) return
        await this.getMonitorOpers()
        await this.getMonitorStrategy()
      },
      immediate: true
    },
    remark: {
      handler(val) {
        if (val) this.initForm.remark = val
      },
      immediate: true
    }
  },
  data() {
    return {
      oid: undefined,
      dialogVisible: false,
      tagBusinuessId: undefined,
      initForm: {
        annotation: {},
        remark: ''
      },
      taskForm: {
        annotation: {},
        remark: ''
      },
      operations: [],
      tags: [],
      displayReason: [],
      displayMonitors: [],
      dataReady: false
    }
  },
  methods: {
    async openDialog(oper, row = null) {
      this.dialogVisible = true
      this.oid = row?.resource?.oid
      this.initForm.remark = row?.remark || ''
    },
    close() {
      this.dialogVisible = false
    },
    updateForm(e) {
      this.taskForm = cloneDeep(e)
      this.$emit('getMonitorOpers', {
        ...this.taskForm,
        validateMsg: this.validateTaskForm(this.taskForm)
      })
    },
    async getMonitorOpers () {
      try {
        const res = await tagApi.getMonitorTags({
          classify_business: this.card?.classify_business
        })
        const {
          fast_remark_tags: remarkTags = [],
          monitor_tags: operations = [],
          monitor_tag_biz: tagBusinuessId
        } = res.data || {}
        this.operations = operations
        this.tags = remarkTags.map(e => ({ ...e, tag_id: e.id }))
        this.tagBusinuessId = tagBusinuessId
      } catch (e) {
        console.error(e)
      }
    },
    async getMonitorStrategy() {
      try {
        const res = await tagApi.getMonitorStrategy({
          classify_business: this.card?.classify_business,
          oid1: this.resource?.oid,
          scene: 'moni_cls',
          tag_business: this.tagBusinuessId
        })
        const {
          result, // 监控策略
          monitor_list = [], // 监控看板
          reason_list = [] // 进审理由
        } = res.data
        this.initForm.annotation = result
        this.taskForm = cloneDeep(this.initForm)
        this.displayMonitors = monitor_list
        this.displayReason = reason_list
        this.dataReady = true
      } catch (e) {
        console.error(e)
      }
    },
    validateTaskForm(form) {
      const { annotation = {}, remark = '' } = form
      // 空或者空对象
      if (!annotation || !Object.keys(annotation).length) {
        return '请选择审核操作'
      }

      const unchangeItem = (this.operations || []).find(
        (e) => e?.extra_data?.op === 'monitor_unchanged'
      )
      if (unchangeItem?.id && annotation[unchangeItem?.id]) {
        // 【不改变当前监控】备注可以为空
        return ''
      }

      if (!remark) {
        for (let key in annotation) {
          if (annotation[key]?.length) {
            // 有 非“无需监控”
            return '请填写备注'
          }
        }
      }
      return ''
    }
  }
}
</script>

<style lang="stylus" scoped>
.monitor-oper-log {
  display: flex;
  flex-direction: row;
  color: var(--text-color);
  .workbench-log {
    flex: 1;
    border-right: 1px solid var(--border-color-light-2);
    height: 116px;
    overflow: auto;
    padding: 2px;

    >>> p {
      line-height: 1.5;
      font-size: 13px;
      margin-bottom: 4px;
    }
  }

  .result-and-note {
    flex: 1;
    height: 116px;
    overflow: auto;
    padding: 2px;
    line-height: 1.5;
    font-size: 13px;
  }

  .monitor {
    flex: 1;
    border-left: 1px solid var(--border-color-light-2);
    overflow: auto;
    font-size: 13px;
    height: 116px;
  }
}
</style>
