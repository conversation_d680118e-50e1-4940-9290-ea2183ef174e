<template>
  <el-card class="box-card">
    <div slot="header" class="flex-lr flex-ac">
      <span>{{ card.title }}</span>
      <SelectionIndicator
        :total="pictures.length"
        :selected="checkedData.length"
        :onToggleAll="handleToggleAll"
      />
    </div>
    <PictureBox
      ref="pictureBox"
      :pictures="pictures"
      :checkedData="checkedData"
      :checkboxOverImage="false"
      showImagesBox
      @getCheckedPicsLength="getCheckedPicsLength"
    />
  </el-card>
</template>
<script>
import PictureBox from '@/components/PictureBox'
import SelectionIndicator from '@/components/SelectionIndicator'

export default {
  props: {
    card: {
      type: Object,
      default: () => {}
    },
    resource: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      pictures: [],
      checkedData: []
    }
  },
  watch: {
    resource(val) {
      const pics = []
      const images = val?.metas?.image_url
      if (images && Object.keys(images).length > 0) {
        Object.keys(images).forEach(key => {
          pics.push({
            index: key,
            minSrc: `${images[key]}@170w_133h.webp`,
            maxSrc: images[key]
          })
        })
      }
      this.pictures = pics
    }
  },
  methods: {
    getCheckedPicsLength(checkedPics) {
      this.checkedData = checkedPics
    },
    handleToggleAll() {
      if (this.checkedData.length === this.pictures.length) {
        this.$refs.pictureBox.checkNone()
      } else {
        this.$refs.pictureBox.checkedAll()
      }
    }
  },
  components: {
    PictureBox,
    SelectionIndicator
  }
}
</script>
