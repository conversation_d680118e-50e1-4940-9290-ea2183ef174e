<template>
  <div class="workbench-operations-card" :class="[{ 'no-card': card.no_card }]">
    <template v-if="!card.no_card">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>{{card.title}}</span>
        </div>
        <el-form inline @submit.stop.prevent.native>
          <el-form-item v-if="computedAuditSingle && computedAuditSingle.length">
              <el-button
                v-for="oper in computedAuditSingle"
                :key="oper.bind_id_list"
                :type="showType(oper)"
                size="medium"
                v-bind="oper.attributes"
                @click="clickedOper(oper)">
                  {{ oper.hot_key ? `${oper.ch_name}(${oper.hot_key})` : `${oper.ch_name}` }}
              </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </template>
    <template v-else>
      <el-form inline @submit.stop.prevent.native>
        <el-form-item v-if="computedAuditSingle && computedAuditSingle.length && !isAutoQa">
          <el-button
          v-for="oper in computedAuditSingle"
          :key="oper.bind_id_list"
          :type="showType(oper)"
          size="medium"
          v-bind="oper.attributes"
          @click="clickedOper(oper)">
            {{ oper.hot_key ? `${oper.ch_name}(${oper.hot_key})` : `${oper.ch_name}` }}
          </el-button>
        </el-form-item>
        <el-form-item v-else>
          <QaOperButtons
            @clickQaOper="(qaOper) => handleQaOperClick(qaOper)"
            :hideLogBtn="true"
          />
        </el-form-item>
      </el-form>
    </template>
    <!-- 通过弹窗 -->
    <PassDialog ref="pass" :businessId="businessId" @submit="submit" :card="card" :bizConfig="bizConfig" @changeVisibleState="changeVisibleState" :monitorVariation="monitorVariation" />
    <!-- 驳回弹窗 -->
    <component :is="getCurrentComponent()" ref="reject" :bizConfig="bizConfig" @submit="submit" :card="card" @changeVisibleState="changeVisibleState" :enumData="enumData"></component>
    <!-- 封禁弹窗 -->
    <BannedDialog ref="banned" @submit="submit" :card="card" @changeVisibleState="changeVisibleState"/>
    <!-- 锁定弹窗 -->
    <LockDialog ref="lock" @submit="submit" :card="card" :bizConfig="bizConfig" @changeVisibleState="changeVisibleState"/>
    <!-- 课程审核的驳回弹窗 -->
    <ClassContentsReject ref="classContentReject" @submit="submit" :card="card" @changeVisibleState="changeVisibleState"/>
    <ExtraInfoDialog ref="extract" :card="card"/>
    <QaDialog ref="qa" @submit="submit"></QaDialog>
    <ApplyDialog ref="apply"></ApplyDialog>

    <IgnoreDialog ref="ignore" @submit="submit"></IgnoreDialog>
    <DeleteDialog ref="delete" @submit="submit" :enumData="enumData"></DeleteDialog>
    <DynamicReportDeleteDialog ref="reportDelete" @submit="submit" :enumData="enumData"></DynamicReportDeleteDialog>
    <QaPictureDialog ref="pictureTextQa" @submit="submit" :bizConfig="bizConfig"></QaPictureDialog>
    <AutoQaOperationDialog ref="qaOperation" @submit="onQaSubmit" :bizConfig="bizConfig" />
    <!-- 申诉有效无效弹窗 -->
    <AppealDialog ref="appeal" @submit="submit"></AppealDialog>
    <!-- 建议监控弹窗 -->
    <AddToMonitorDialog ref="addToMonitor" @monitor-changed="handleDynMonitorChanged" />
    <!-- 监控弹窗 -->
    <MonitorDialog ref="monitor" />
    <!-- 评论举报弹窗 -->
    <ReportDialog ref="report" @submit="submit"></ReportDialog>
    <UniversalDialog ref="universal" @submit="submit" :bizConfig="bizConfig"></UniversalDialog>
    <UniversalMultipleDialog ref="universalMultiple" @submit="submit" :bizConfig="bizConfig"></UniversalMultipleDialog>
    <TagOperationDialog ref="TagOperation" @submit="submit" :todo="todo"></TagOperationDialog>
  </div>
</template>
<script>
import { mapActions, mapState } from 'vuex'
import { newSubmitMixin } from '@/mixins/workbench-config'
import RejectDialog from '@/pages/workbench/dialogConfig/RejectDialog'
import MediaRejectDialog from '@/pages/workbench/dialogConfig/MediaRejectDialog'
import PassDialog from '@/pages/workbench/dialogConfig/PassDialog'
import BannedDialog from '@/pages/workbench/dialogConfig/BannedDialog'
import LockDialog from '@/pages/workbench/dialogConfig/LockDialog'
import ClassContentsReject from '@/pages/workbench/dialogConfig/ClassContentsReject'
import ExtraInfoDialog from '@/pages/workbench/dialogConfig/ExtraInfoDialog.vue'
import { OPER_BUTTON_TYPES } from '@/pages/workbench/constants'
import notify from '@/lib/notify'
import QaDialog from '@/pages/workbench/dialogConfig/QaDialog.vue'
import ApplyDialog from '@/pages/workbench/dialogConfig/ApplyDialog.vue'
import IgnoreDialog from '@/pages/workbench/dialogConfig/IgnoreDialog'
import DeleteDialog from '@/pages/workbench/dialogConfig/DeleteDialog'
import DynamicReportDeleteDialog from '@/pages/workbench/dialogConfig/DynamicReportDeleteDialog.vue'
import QaPictureDialog from '@/pages/workbench/dialogConfig/QaPictureDialog.vue'
import AppealDialog from '@/pages/workbench/dialogConfig/AppealDialog'
import AddToMonitorDialog from '@/pages/workbench/dialogConfig/AddToMonitorDialog'
import MonitorDialog from '@/pages/workbench/dialogConfig/MonitorDialog.vue'
import ReportDialog from '@/pages/workbench/dialogConfig/ReportDialog.vue'
import UniversalDialog from '@/pages/workbench/dialogConfig/UniversalDialog.vue'
import UniversalMultipleDialog from '@/pages/workbench/dialogConfig/UniversalMultipleDialog.vue'
import { QaOperButtons } from '@/v2/biz-components/quality-assurance'
import { clickQaOperHandler, submitQaOperHandler } from '@/v2/biz-utils/submission/qa'
import AutoQaOperationDialog from '@/pages/workbench/dialogConfig/AutoQaOperationDialog'
import { setLogData } from '@/utils/task-tracker'
import { getBackUrl } from '@/utils'
import TagOperationDialog from '@/v2/biz-components/workbench/TagOperationDialog.vue'

// const CODE_KEY_MAP = [83, 68, 71, 70]

export default {
  components: {
    RejectDialog,
    PassDialog,
    BannedDialog,
    MediaRejectDialog,
    LockDialog,
    ExtraInfoDialog,
    ClassContentsReject,
    QaDialog,
    ApplyDialog,
    IgnoreDialog,
    DynamicReportDeleteDialog,
    DeleteDialog,
    QaPictureDialog,
    QaOperButtons,
    AutoQaOperationDialog,
    AppealDialog,
    AddToMonitorDialog,
    MonitorDialog,
    ReportDialog,
    UniversalDialog,
    UniversalMultipleDialog,
    TagOperationDialog
  },
  mixins: [newSubmitMixin],
  props: {
    card: {
      type: Object,
      default: () => {
        return {}
      }
    },
    staffTableData: {
      type: Array,
      default: () => {
        return []
      }
    },
    monitorOpers: {
      type: Object,
      default: () => {}
    },
    todo: Object
  },
  data() {
    return {
      dialogVisible: false,
      monitorVariation: 0
    }
  },
  computed: {
    ...mapState('todoDetail', [
      'detail',
      'auditSingle',
      'businessId',
      'todoId',
      'itemId',
      'taskId',
      'dataReady',
      'isTask',
      'auditType',
      'isDelay',
      'schema',
      'auditMode',
      'enumData',
      'bizConfig',
      'quitAfterSubmit',
      'inWorkMode',
      'todoConfig'
    ]),
    ...mapState({
      isAutoQa: (state) => state.qa.isAutoQa
    }),
    showBannedButton() {
      return !(this.bizConfig?.hide_banned_in_banned_button || []).includes(this.todoId)
    },
    computedAuditSingle() {
      if (this.showBannedButton) {
        return this.auditSingle
      } else {
        return this.auditSingle.filter(item => item.ch_name !== '封禁')
      }
    }
  },

  mounted() {
    this.$nextTick(_ => {
      document.addEventListener('keyup', this.keyup)
		})
  },

  beforeDestroy() {
    document.removeEventListener('keyup', this.keyup)
  },

  methods: {
    ...mapActions('todoDetail', [
      'getTask'
    ]),
    getCurrentComponent() {
      const rejectButton = (this.auditSingle || []).find(op => op.ch_name === '驳回')
      if (rejectButton) {
        // 特殊驳回组件
        return rejectButton.component || 'RejectDialog'
      }
    },
    showType(oper) {
      return OPER_BUTTON_TYPES[oper.name] || oper.buttonType
    },
    // 返回列表页
    backToList() {
      const back = this.$route.query.back || ''
      const url = getBackUrl(back)
      if (url) {
        window.location.href = url
      } else {
        this.$router.go(-1)
      }
    },
    handleDynMonitorChanged(monitorVariation) {
      this.monitorVariation = monitorVariation
    },
    handleQaOperClick(qaOper) {
      clickQaOperHandler(
        qaOper,
        this.detail,
        this.businessId,
        this.todoId,
        this.isTask,
        this.$refs.qaOperation.openDialog,
        this.isTask ? this.getTask : this.backToList
      )
    },
    async onQaSubmit(operation, qaOper, row = {}) {
      submitQaOperHandler(
        operation,
        qaOper,
        row,
        this.businessId,
        this.todoId,
        this.isTask,
        this.isTask ? this.getTask : this.backToList
      )
    },
    async recheck(oper) {
      let isChecked = true
      if (oper?.action_params?.is_negative && this.todoConfig?.negativeRecheckMsg) {
        isChecked = false
        await this.$confirm(
          `${this.todoConfig.negativeRecheckMsg || ''} 确定要${oper.name}吗？`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
          .then(() => {
            isChecked = true
          })
          .catch(() => {
            isChecked = false
          })
      }
      return isChecked
    },
    async clickedOper(oper) {
      if (!this.dataReady) {
        notify.warning('请稍后再试~')
        return
      }
      const isChecked = await this.recheck(oper)
      if (!isChecked) return
      if (oper.action === 'submitMonitor') {
        const { annotation = {}, remark = '', validateMsg = '' } = this.monitorOpers
        if (validateMsg) {
          notify.error(validateMsg)
          return
        }
        this.submit({
          resource_result: {},
          annotation,
          remark
        }, oper)
        return true
      }
      if (oper.action === 'submit') {
        this.submit({ resource_result: {} }, oper)
        return true
      }
      if (oper.action === 'confirm') {
        this.confirm(oper)
        return true
      }
      if (oper.action === 'dialog') {
        if (oper.reference) {
          this.$refs[oper.reference].openDialog(oper, this.detail)
        } else {
          switch (oper.ch_name) {
            case '通过':
              this.dialogVisible = true
              this.$refs.pass.openDialog(oper)
              break
            case '驳回':
              // 课程审核
              this.dialogVisible = true
              if (this.businessId === 54) {
                this.$refs.classContentReject.openDialog(oper)
              } else {
                this.$refs.reject.openDialog(oper)
              }
              break
            case '封禁':
              this.dialogVisible = true
              this.$refs.banned.openDialog(oper)
              break
            case '锁定':
              this.dialogVisible = true
              this.$refs.lock.openDialog(oper)
              break
            default:
              break
          }
        }
        return true
      }
      // 联合投稿提交前需要校验staff数据状态
      if (oper.action === 'staffSubmit') {
        if (this.staffTableData && this.staffTableData.findIndex(d => d.apply_state === -1) > -1) {
          notify.warning('存在未完成审核的合作申请')
          return false
        }
        this.submit({ resource_result: {} }, oper)
        return true
      }
    },
    confirm(oper) {
      this.$confirm(`此操作将${oper.ch_name}当前内容, 是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.submit({ resource_result: {} }, oper)
      }).catch((e) => {})
    },
    async submit(data, oper) {
      const s1 = Date.now()
      setLogData('submitStartTime', s1)
      const params = await this.getSingleSubmitParams(data, oper)
      this.submitAuditSingle(params, oper)
      this.afterSubmit()
    },
    afterSubmit() {
      this.monitorVariation = 0
    },
    changeVisibleState() {
      this.dialogVisible = false
    },
    keyup(e) {
      const opers = this.auditSingle || []
      let oper
      // S | D | G
      if (document.activeElement.tagName === 'INPUT' || document.activeElement.tagName === 'TEXTAREA' || !opers.length || this.dialogVisible) {
          return
      }
      switch (e.keyCode) {
        case 70:
          oper = opers.find(op => op.name === '折叠') || opers.find(op => op.name === '锁定') || opers.find(op => op.hot_key === 'F')
          break
        case 83:
          oper = opers.find(op => op.name === '通过') || opers.find(op => op.name === '提交') || opers.find(op => op.name === '无问题') || opers.find(op => op.hot_key === 'S')
          break
        case 68:
          oper = opers.find(op => op.name === '驳回') || opers.find(op => op.name === '错判') || opers.find(op => op.hot_key === 'D')
          break
        case 71:
          oper = opers.find(op => op.name === '封禁') || opers.find(op => op.hot_key === 'G')
          break
        default:
          break
      }
      if (oper && oper.name && oper.hot_key) this.clickedOper(oper)
    }
  }
}
</script>
<style lang="stylus" scoped>
.workbench-operations-card {
  &.no-card{
    .el-form > .el-form-item {
      margin-bottom 0px
    }
    .el-form > .el-row > .el-form-item {
      margin-bottom 0px
    }
  }
}
</style>
