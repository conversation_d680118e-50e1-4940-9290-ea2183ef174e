<template>
  <div class="workbench-comic-card">
    <el-card class="box-card comic-info">
      <div slot="header" class="clearfix">
        <span>关联漫画作品</span>
      </div>
      <div class="comic-info">
        <span>漫画ID：<em>{{ comicData && comicData.cid }}</em></span>
        <span>漫画标题：<em>{{ comicData && comicData.title}}</em></span>
      </div>
      <el-table
      :data="tableDataShow"
      stripe
      border
      height="202"
      style="width: 100%;margin-top: 8px">
        <el-table-column
          align="center"
          prop="id">
          <template v-slot="scope">
            <router-link
            target="_blank"
            :to="{path:`/audit/list/picture?oid=${scope.row.id}&businessId=${$route.query.business_id}&isResource=${1}`}">
              {{scope.row.id}}
            </router-link>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="title">
        </el-table-column>
      </el-table>

      <Pagination
      class="pagination"
      :pager="pager"
      @getList="getList"
      :computedPager="true"
      :tableData="comicData && comicData.ep_list"
      showSize="small">
      </Pagination>
    </el-card>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import Pagination from '@/components/Pagination.vue'
import fetch from '@/lib/fetch'
const ONLINE_PATH = 'manga-mng.bilibili.co'
export default {
  components: {
    Pagination
  },
  data() {
    return {
      comicData: [],
      pager: {
        ps: 10,
        pn: 1,
        total: 0
      }
    }
  },
  computed: {
    ...mapState('todoDetail', [
      'detail'
    ]),
    tableDataShow() {
      if (this.comicData && this.comicData.ep_list) {
        return [...this.comicData.ep_list].splice((this.pager.pn - 1) * this.pager.ps, this.pager.ps)
      }
      return []
    }
  },
  watch: {
    detail(val) {
      if (val && val.resource) {
        this.getRelatedComic()
      }
    }
  },
  methods: {
    getRelatedComic() {
      const path = ONLINE_PATH
      fetch({
        url: `//${path}/twirp/verify.v0.Verify/ComicInfoByEp`,
        method: 'post',
        data: {
          oid: this.detail.resource && this.detail.resource && this.detail.resource.oid
        }
      }).then((res) => {
        this.comicData = res.data || []
      }).catch(_ => {})
    },
    getList() {
      this.$emit('getTask')
    }
  }
}
</script>
<style lang="stylus">
.workbench-comic-card {
  .comic-info {
    a {
      color: var(--link-color)
    }
    span {
      font-size: 14px
      color: var(--text-color)
      em {
        color: var(--grey-light-1)
        margin-right: 8px
      }
    }
    .pagination {
      margin-top: 8px
      display: flex
      justify-content: center
    }
  }
}
</style>
