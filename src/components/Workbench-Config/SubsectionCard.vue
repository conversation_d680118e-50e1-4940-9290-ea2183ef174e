<template>
  <div class="subsection">
    <el-card class="box-card section-card">
      <div slot="header" class="clearfix header-flex">
        <span>小节信息</span>
        <div>
          <el-button
            type="success"
            plain
            size="small"
            :disabled="sectionsBatchBtnDisabled"
            @click="batchSubmit('小节')"
          >
            批量通过
          </el-button>
          <el-button
            type="danger"
            plain
            size="small"
            :disabled="sectionsBatchBtnDisabled"
            @click="openDialog('批量驳回', '小节')"
          >
            批量驳回
          </el-button>
          <el-button
            type="info"
            plain
            size="small"
            :disabled="!sections.length"
            @click="openLog('小节')"
          >
            查看小节日志
          </el-button>
        </div>
      </div>
      <div class="sections">
        <el-checkbox-group
          v-model="sections"
          size="medium"
          @change="getAllEps('default')"
        >
          <el-checkbox
            v-for="(item, index) in allSectionData"
            :label="item"
            :key="index"
            border
          >
            <span>{{ `${item.title}（${item.uncheck_ep_cnt}）` }}</span>
            <span :class="STATE_CLASS[item.state]">{{ SECTION_STATES[item.state] }}</span>
          </el-checkbox>
        </el-checkbox-group>
        <div class="section-submit">
          <el-button
            type="success"
            plain
            size="small"
            :disabled="sectionsBtnDisabled"
            @click="submitSeasonResult({}, 0, 2)"
          >
            通过
          </el-button>
          <el-button
            type="danger"
            plain
            size="small"
            :disabled="sectionsBtnDisabled"
            @click="openDialog('驳回', '小节')"
          >
            驳回
          </el-button>
        </div>
      </div>
      <div>
        <el-button
          type="success"
          plain
          size="medium"
          :disabled="epsBatchBtnDisabled"
          @click="batchSubmit('单集')"
        >
          批量通过
        </el-button>
        <el-button
          type="danger"
          plain
          size="medium"
          :disabled="epsBatchBtnDisabled"
          @click="openDialog('批量驳回', '单集')"
        >
          批量驳回
        </el-button>
        <el-button
          type="primary"
          size="medium"
          style="margin-left: 26px"
          @click="getAllEps"
          :disabled="!sections.length"
        >
          查看全部状态
        </el-button>
      </div>
      <el-table
        @selection-change="handleSelectionChange"
        @current-change="changeRow"
        @row-click="rowClick"
        :row-class-name="rowClassName"
        :data="tableData"
        :height="tableHeight"
        highlight-current-row
        ref="multipleTable"
        style="width: 100%"
        class="table"
        border
      >
        <el-table-column
          type="selection"
          width="55"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="aid"
          label="单集aid/bvid"
          align="center"
          width="150"
        >
          <template v-slot="scope">
            <p class="link" @click.stop="openAv(scope.row)">
              {{ scope.row.aid }}
            </p>
            <p>{{ scope.row.bvid }}</p>
          </template>
        </el-table-column>
        <el-table-column
          prop="title"
          label="单集标题"
          align="center"
        >
          <template v-slot="scope">
            <div v-if="scope.row.is_free" class="red-label">[免费试看]</div>
            <span v-highlight-config="card.sensitive_words && card.sensitive_words.title" :key="scope.row.title">{{ scope.row.title }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="arc_title" label="稿件标题" align="center">
          <template v-slot="scope">
            <el-tooltip placement="bottom">
              <img
                slot="content"
                :src="scope.row.arc_cover"
                width="150px"
                height="150px"
              />
              <em v-highlight-config="card.sensitive_words && card.sensitive_words.arc_title" :key="scope.row.arc_title">{{ scope.row.arc_title }}</em>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="state" label="稿件所属UP主" align="center">
          <template v-slot="scope">
            <a
              :href="`https://space.bilibili.com/${scope.row.mid}`"
              style="color: var(--link-color)"
            >
              {{ scope.row.up_name }}
            </a>
          </template>
        </el-table-column>
        <el-table-column prop="state" label="状态" align="center" width="80">
          <template v-slot="scope">
            <p :class="STATE_CLASS[scope.row.state]">{{ scope.row.state }}</p>
          </template>
        </el-table-column>
        <el-table-column prop="ops" label="操作" width="260" align="center">
          <template v-slot="scope">
            <el-button
              type="success"
              plain
              size="small"
              @click.stop="submitSeasonResult(scope.row, 0, 3)"
            >
              通过
            </el-button>
            <el-button
              type="danger"
              plain
              size="small"
              @click.stop="openDialog('驳回', '单集', scope.row)"
            >
              驳回
            </el-button>
            <el-button
              type="info"
              plain
              size="small"
              @click.stop="openLog('单集', scope.row)"
            >
              日志
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <RejectDialog
      ref="reject"
      :filterReason="filterReason"
      :tips="rejectTips"
      @submit="handleReject"
    />
    <OperationLogDialog
      ref="log"
      :operationLog="operationLog"
    ></OperationLogDialog>
  </div>
</template>
<script>
import { sectionApi, seasonApi, resourceApi } from '@/api/index'
import RejectDialog from '@/pages/workbench/dialogConfig/RejectDialog.vue'
import OperationLogDialog from '@/components/Dialog/operationLogDialog'
import { mapState, mapGetters } from 'vuex'
import notifiction from '@/lib/notify'
require('intersection-observer')

export default {
  name: 'SubsectionCard',
  components: {
    RejectDialog,
    OperationLogDialog
  },
  data() {
    return {
      sections: [],
      multipleSelection: [],
      tableData: [],
      allSectionData: [],
      ops: [],
      tableHeight: null,
      allEpData: [],
      default: [-1, -6],
      operationLog: [],
      dialogTitle: '驳回',
      SECTION_STATES: {
       0: '已通过',
        '-1': '待审',
        '-2': '已驳回',
        '-4': '已锁定',
        '-6': '修复待审',
        '-100': '已删除'
      },
      ALL_STATES: {
        0: '通过',
        '-1': '待审',
        '-2': '打回',
        '-4': '锁定',
        '-6': '修复待审',
        '-100': '删除'
      },
      TYPE_MAP: {
        剧集: 1,
        小节: 2,
        单集: 3
      },
      STATE_CLASS: {
        待审: 'action-color-1',
        通过: 'action-color-2',
        删除: 'action-color-3',
        打回: 'action-color-4',

        锁定: 'action-color-5',
        修复待审: 'action-color-6',
        0: 'action-color-2',
        '-2': 'action-color-5',
        '-4': 'action-color-4',
        '-100': 'action-color-3'
      },
      secId: '',
      flowId: '',
      type: '',
      seasonVersion: null
    }
  },
  props: {
    card: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    detail() {
      this.$nextTick(() => {
        this.getResource()
      })
    }
  },
  computed: {
    ...mapState('todoDetail', ['detail', 'isTask']),
    ...mapGetters({
      getEnv: 'env/getEnv'
    }),
    seasonId() {
      const value = this.mappingKV(this.card.key, this.detail)
      return parseInt(value, 10) || ''
    },
    sectionsBatchBtnDisabled() {
      return !this.sections.length || this.sections.length === 1
    },
    sectionsBtnDisabled() {
      return !this.sections.length || this.sections.length > 1
    },
    epsBatchBtnDisabled() {
      return !this.multipleSelection.length
    },
    rejectTips() {
      return `确认${this.dialogTitle}选中${this.type}?`
    }
  },
  methods: {
    mappingKV(keys, source = null) {
      let result = ''
      if (!source) {
        source = this
      }
      if (keys) {
        const newArray = keys.split('.')
        newArray.forEach((key, index) => {
          if (index === 0) {
            result = key in source ? source[key] : {}
          } else {
            result = result && key in result ? result[key] : ''
          }
        })
      }
      return result
    },
    async getFlowId() {
      try {
        const res = await resourceApi.getResourceInfo({
          business_id: this.$route.query.business_id,
          oid: this.detail?.resource?.oid
        })
        this.flowId = res?.data?.flow?.flow_id
      } catch (_) {
        this.flowId = ''
      }
    },
    getResource() {
      this.getSections()
    },
    async getRealtimeSeasonInfo() {
      try {
        const res = await seasonApi.getRealtimeInfo({ season_id: this.seasonId })
        const { version } = res.data || {}
        this.seasonVersion = version
      } catch (err) {
        console.error(err)
      }
    },
    // 选中
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    getSecId(type) {
      let secId = ''
      const env = this.getEnv()
      switch (type) {
        case '小节':
          secId = env === 'uat' ? 125 : 21
          break
        case '单集':
          secId = env === 'uat' ? 127 : 22
          break
      }
      return secId
    },
    openDialog(op, type, row = null) {
      const secId = this.getSecId(type)
      this.secId = secId
      this.type = type
      const opInfo = {
        name: op,
        action_params: {
          tag_group: true,
          hide_reason: true,
          hide_reject_reason: true,
          notify: true,
          note: true
        }
      }
      this.dialogTitle = op
      if (opInfo.name.indexOf('驳回') > -1) {
        const info =
          row || (type === '小节' ? this.sections : this.multipleSelection)
        this.$refs.reject.openDialog(opInfo, info)
      }
    },
    handleReject(reject, oper, ops) {
      if (!this.flowId) {
        notifiction.error('未取到flow_id，无法提交审核')
        return
      }
      const reasonId = reject?.resource_result?.reason_id
      const rejectReason = reject?.resource_result?.reject_reason
      const notify = reject?.extra_data?.notify
      const comment = reject?.resource_result?.note || ''
      // 驳回 -2
      const state = -2
      const type = this.TYPE_MAP[this.type]
      if (this.dialogTitle === '批量驳回') {
        const params = {
          ids: ops.map((o) => {
            return o.id
          }),
          season_id: this.seasonId,
          type,
          state,
          reject_id: reasonId,
          reject_reason: rejectReason,
          with_notify: !!notify,
          flow_id: +this.flowId,
          comment,
          version: this.seasonVersion
        }
        seasonApi
          .batchState(params)
          .then((res) => {
            notifiction.success(`成功批量${!state ? '通过' : '驳回'}`)
            if (state) {
              this.$refs.reject.dialogVisible = false
            }
            this.$refs.multipleTable.clearSelection()
          })
          .catch((_) => {})
      } else {
        seasonApi
          .editState({
            comment,
            flow_id: +this.flowId,
            id: ops.id || this.sections[0].id,
            reject_id: +reasonId,
            reject_reason: rejectReason,
            season_id: this.seasonId,
            state,
            type,
            version: this.seasonVersion,
            with_notify: !!notify
          })
          .then((res) => {
            notifiction.success(`成功${!state ? '通过' : '驳回'}`)
            if (state) {
              this.$refs.reject.dialogVisible = false
            }
          })
          .catch((_) => {})
      }
    },
    filterReason(reason) {
      // reason
      return reason.filter((item) => item.sec_id === this.secId)
    },
    setTableHeight() {
      const card = document.getElementsByClassName('section-card')[0]
      const cardHeight = card.offsetHeight
      const cardHeaderHeight = card.childNodes[0].offsetHeight
      const epsHeight = card.childNodes[1].childNodes[0].offsetHeight
      const batchHeight = card.childNodes[1].childNodes[1].offsetHeight
      this.tableHeight =
        cardHeight - cardHeaderHeight - epsHeight - batchHeight - 70
      this.$nextTick(() => {
        this.$refs.multipleTable.doLayout()
      })
    },
    // 选中行
    rowClick(row) {
      this.$refs.multipleTable.setCurrentRow(row)
      if (this.$refs.multipleTable) {
        this.$refs.multipleTable.toggleRowSelection(row)
      }
    },
    // 打开日志
    openLog(type, row = {}) {
      const apiFn = type === '小节' ? sectionApi.getLog : sectionApi.getEpLog
      apiFn({
        id: row.id || this.sections[0].id
      })
        .then((res) => {
          this.operationLog = res.data || []
          this.$refs.log.dialogTableVisible = true
        })
        .catch((_) => {})
    },
    clearData() {
      this.sections = []
      this.allSectionData = []
      this.allEpData = []
      this.tableData = []
      this.$nextTick(() => {
        this.setTableHeight()
      })
    },
    // 获取小节
    async getSections() {
      if (!this.seasonId) {
        this.clearData()
        return
      }
      this.getRealtimeSeasonInfo()
      await this.getFlowId()
      sectionApi
        .getSections({
          season_id: this.seasonId,
          with_uncheck_cnt: true,
          flow_id: this.flowId
        })
        .then((res) => {
          this.allSectionData = res.data || []
          this.$nextTick(() => {
            this.setTableHeight()
          })

          if (this.allSectionData.length) {
            this.sections.push(this.allSectionData[0])

            this.getAllEps('default')
          }
        })
        .catch((_) => {})
    },
    getEps(states, section) {
      return sectionApi
        .getEps({
          section_id: section.id,
          states: (this[states] || []).join(','),
          flow_id: this.flowId
        })
        .then((res) => {
          if (!res.data) {
            res.data = []
          }
          return Promise.resolve(res)
        })
        .catch((_) => {
          return Promise.reject(new Error('请求出错'))
        })
    },
    getAllEps(state = undefined) {
      this.allEpData = []
      this.tableData = []
      const promiseArr = []
      this.sections.forEach((section) => {
        const res = this.getEps(state, section).then((res) => {
          res.data.forEach((d) => {
            d.state = this.ALL_STATES['' + d.state]
          })

          this.allEpData = this.allEpData.concat(res.data)
          this.allEpData.forEach((d, i) => {
            d.key = i + 1
          })

          this.tableData = this.allEpData.filter((d, i) => {
            return i >= 0 && i <= 9
          })
          this.$nextTick(() => {
            this.$EventBus.$emit('sensitiveWordChange')
          })
          this.tableData.length && this.rowClick(this.tableData[0])
        })
        promiseArr.push(res)
      })

      // 所有请求完成后 初始化observer
      Promise.all(promiseArr).then((data) => {
        this.$nextTick(() => {
          this.initObserver()
        })
      })
    },
    initObserver() {
      const intersectionObserver = new IntersectionObserver((entries) => {
        if (entries[0].intersectionRatio <= 0) {
          // do nothing
          return
        }

        if (this.tableData.length !== this.allEpData.length) {
          const nextData = []
          this.allEpData.forEach((d, i) => {
            if (i >= this.tableData.length && i <= this.tableData.length + 9) {
              // 在循环里不能直接操作tableData 否则 会影响当前tableData的length 从而影响加载数据的数量
              nextData.push(d)
            }
          })
          this.tableData = this.tableData.concat(nextData)
          this.$nextTick(() => {
            this.$EventBus.$emit('sensitiveWordChange')
          })

          // 因为加载了新数据之后observer的位置发生了变化 所以在这之后需要进行迭代 在本次执行结果的基础上重新监控observer
          this.$nextTick(() => {
            this.initObserver()
          })
        } else {
          // 表格剧集数量等于接口返回剧集数量什么都不做
        }
      })
      const footer = document.querySelector('.observer')
      if (footer) {
        intersectionObserver.observe(footer)
      }
    },
    // 类名
    rowClassName({ row, rowIndex }) {
      return rowIndex === this.tableData.length - 1 ? 'observer' : ''
    },
    // 换行
    changeRow(cur, pre) {
      this.currentRow = cur || {}
    },
    // 通过内容
    submitSeasonResult(params = {}, state = null, type) {
      if (!this.flowId) {
        notifiction.error('未取到flow_id，无法提交审核')
        return
      }
      seasonApi
        .editState({
          comment: '',
          flow_id: +this.flowId,
          id: params.id || this.sections[0].id,
          season_id: this.seasonId,
          state,
          type,
          version: this.seasonVersion,
          with_notify: true
        })
        .then((res) => {
          notifiction.success(`成功${!state ? '通过' : '驳回'}`)
          if (state) {
            this.$refs.reject.dialogVisible = false
          }
        })
        .catch((_) => {})
    },
    batchSubmit(name) {
      const ops = name === '小节' ? this.sections : this.multipleSelection
      const type = this.TYPE_MAP[name]
      if (!this.multipleSelection.length && !this.sections.length) {
        return
      }

      this.$confirm('此操作将批量通过当前选中审核内容, 是否继续?', '批量通过', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.confirmOps(ops, 0, type)
        })
        .catch(() => {})
    },
    confirmOps(ops, state, type) {
      if (!this.flowId) {
        notifiction.error('未取到flow_id，无法提交审核')
        return
      }
      const params = {
        ids: ops.map((o) => {
          return o.id
        }),
        season_id: this.seasonId,
        type,
        state,
        with_notify: true,
        flow_id: +this.flowId,
        comment: '',
        version: this.seasonVersion
      }
      seasonApi
        .batchState(params)
        .then((res) => {
          notifiction.success(`成功批量${!state ? '通过' : '驳回'}`)
          if (state) {
            this.$refs.reject.dialogVisible = false
          }
          this.$refs.multipleTable.clearSelection()
        })
        .catch((_) => {})
    },
    openAv(row) {
      window.open(`https://www.bilibili.com/video/av${row.aid}`, '_blank')
    }
  },
  mounted() {
    this.getResource()
    window.addEventListener('resize', this.setTableHeight)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.setTableHeight)
  }
}
</script>
<style lang="stylus" scoped>
.subsection
  .red-label
    color: var(--red)
  .header-flex
    display flex
    justify-content space-between
    &:nth-of-type(1)
      line-height 2
  >>>.el-card
    margin-top 8px
    color var(--text-color)
    font-size 15px
    &:nth-of-type(1)
      margin-top 0px
  .sections
    margin 20px 0px 8px
    display flex
    flex-wrap wrap
    .section-submit
      width 130px
      display inline-block
      margin-top 4px
    .el-checkbox-group
      margin-right 12px
      label
        margin-bottom 12px
</style>
