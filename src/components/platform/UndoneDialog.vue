<template>
  <div class="undone-dialog">
    <el-dialog title="未完成任务情况" :visible.sync="quitConfirmModal.isShow" @close="quitConfirmModal.close">
      <div class="undone-modal">
        <table>
          <thead>
            <tr>
              <!-- <th>指派任务数</th> -->
              <!-- <th>延迟任务数</th> -->
              <th>普通任务数</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <!-- <td>{{undoneStat.assign_total}}</td>
              <td>{{undoneStat.delay_total}}</td> -->
              <td>{{undoneStat.normal_total}}</td>
            </tr>
          </tbody>
        </table>
        <p>确定退出？</p>
        <div>
          <el-button type="info" size="small" @click="$emit('cancel')">取消</el-button>
          <el-button type="danger" size="small" @click="$emit('confirm')">确定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template> 
<script>
export default {
  name: 'UndoneDialog',
  props: {
    quitConfirmModal: {
      type: Object,
      default() {
        return {}
      }
    },
    undoneStat: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {

    }
  }
}
</script>
<style lang="stylus" scoped>
.undone-modal {
  th {
    text-align center
    background-color var(--table-th-bg-color)
  }

  th,
  td {
    padding 5px 10px
    border 1px solid var(--border-color)
  }

  p {
    font-size 16px
    margin 15px 0
  }
}
</style>
