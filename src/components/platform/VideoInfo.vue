<template>
  <div class="pd-video-info">
    <div class="box wrapper-title">视频信息</div>
    <div class="box ep-title-cell">分P标题</div>
    <CopyrightHighlight
      class="box ep-title-content"
      v-model="videoInfo.eptitle"
      :isInput="false"
      :keywordData="highlightKeywords"
      :highlightWrapperStyle="{
        lineHeight: '32px'
      }">
    </CopyrightHighlight>
    <div class="box desc-cell">分P简介</div>
    <div class="box">{{videoInfo.desc}}</div>
  </div>
</template>
<script>
import CopyrightHighlight from '@/components/TaskDetail/archive/CopyrightHighlight'
export default {
  name: 'VideoInfo',
  data() {
    return {}
  },
  props: {
    videoInfo: {
      type: Object,
      default() { return {} }
    },
    highlightKeywords: {
      type: Array,
      default() {
        return []
      }
    }
  },
  components: {
    CopyrightHighlight
  },
  computed: {
    form: {
      get() {
        return this.taskForm
      },
      set(newForm) {
        this.$emit('update-form', newForm)
      }
    }
  },
  methods: {

  }
}
</script>
<style lang="stylus" scoped>
@import "~@/styl/grid-layout.styl"
.pd-video-info{
  font-size 14px
  grid-template-columns wrapper-width wrapper-width 295px wrapper-width 1fr
  wrapper()
  box-sizing border-box
  border-top 0px
  .box{
    box-mixin()
  }
  .wrapper-title{
    grid-column 1
  }
  .ep-title-cell{
    grid-column 2
  }
  .ep-title-content{
    grid-column 3
    padding 5px
    .hl-wrapper{
      max-width 305px
    }
  }
  .desc-cell{
    grid-column 4
  }
  .desc-content{
    grid-column 5
    overflow hidden
  }
}
</style>
