<template>
  <div
    class="radio-tag"
    :class="{
      'radio-tag__active' : active,
      'radio-tag__disabled' : disabled
    }"
    @click="$emit('click')"
  >
    <span class="radio-tag__label">{{text}}</span>
    <i v-if="hasDropdown" class="el-icon-arrow-down el-icon--right"></i>
  </div>
</template>
<script>
export default {
  name: 'RadioTag',
  data() {
    return {}
  },
  props: {
    text: {
      type: String,
      default: ''
    },
    hasDropdown: {
      type: Boolean,
      default: false
    },
    active: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  }
}
</script>
<style lang="stylus" scoped>
.radio-tag{
  display inline-flex
  min-width 70px
  // margin-top 5px
  // margin-bottom 5px
  display inline-block
  // height 32px
  padding 0 5px
  line-height 25px
  font-size 12px
  border-width 1px
  border-style solid
  border-radius 15px
  box-sizing border-box
  white-space nowrap
  background-color #f4f4f5
  border-color #e9e9eb
  color var(--text-color-light-1)
  text-align center
  cursor pointer
  outline none
  &__active{
    background-color var(--blue-light-2)
    border-color #d9ecff
    color var(--link-color)
  }
  &__disabled {
    user-select none
    cursor not-allowed
  }
  &__label{
    outline none
    width 100%
    text-align center
    user-select none
  }
}
</style>
