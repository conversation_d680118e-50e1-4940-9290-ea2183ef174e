<template>
  <div class="pd-operation">
    <!-- 操作模块 -->
    <div class="box title">操作模块</div>
    <div class="box content">
      <div class="form-row">
        <label class="form-label">分类：</label>
        <div class="form-content">
          <el-cascader
            class="tag-selector"
            v-model="form.annotation"
            :options="annotationOpers"
            size="small"
            filterable
            :props="{
              expandTrigger: 'hover',
              children: 'options',
              label: 'name',
              value: 'id',
              multiple: !isMockFirstAudit,
              checkStrictly: isMockFirstAudit
            }"
          />
        </div>
      </div>
      <div class="form-row" v-if="showGrayTags && hasGrayTagAuth">
        <label class="form-label">稿件灰标：</label>
        <div class="form-content">
          <TagGroup
            :tagList="grayTagTree"
            multiple
            :value="workForm.gray_tags"
            @input="handleGrayTagChange"
            :disabled="!(perms && perms.VIDEO_GRAY_TAGS_WRITE)"
          />
        </div>
      </div>
      <div class="form-row">
        <label class="form-label">备注：</label>
        <div class="form-content">
          <div class="pd-o__other">
            <NewSelect
              v-model="remark"
              style="width: 120px"
              placeholder="选择快捷文本"
              size="mini"
              filterable
              @change="changeRemark"
            >
              <el-option
                v-for="reason in remarkOptions"
                :key="reason"
                :label="reason"
                :value="reason"
              ></el-option>
            </NewSelect>
            <div class="pd-o__content">
              <AgTextarea
                type="textarea"
                :rows="1"
                size="small"
                placeholder=""
                v-model="form.remark"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import MarkTag from '@/components/platform/MarkTag'
import NewSelect from '@/components/element-update/Select'
import { REASONS, VIDEO_AUDIT_REASONS } from '@/utils/constant.js'
import AgTextarea from '@/components/element-update/Textarea'
import TagGroup from '@/v2/pure-components/TagGroup.vue'

export default {
  name: 'Operation',
  data() {
    return {
      remark: '',
      tags: []
    }
  },
  props: {
    showGrayTags: Boolean,
    grayTagTree: {
      type: Array,
      default: () => []
    },
    workForm: {
      type: Object,
      default: () => {}
    },
    video: {
      type: Object,
      default: () => {}
    },
    annotationOpers: {
      type: Array,
      default: () => []
    },
    remarkConfigName: {
      type: String,
      default: 'REASON'
    }
  },
  watch: {
    'video.filename': {
      handler() {
        this.remark = ''
      }
    },
    'workForm.remark': {
      handler(val) {
        if (val.length === 0) {
          this.remark = ''
        }
      }
    }
  },
  computed: {
    ...mapState({
      perms: state => state.user.perms
    }),
    hasGrayTagAuth() {
      return this.perms?.VIDEO_GRAY_TAGS_READ || this.perms?.VIDEO_GRAY_TAGS_WRITE
    },
    form: {
      get() {
        return this.workForm
      },
      set(newForm) {
        this.$emit('update-form', newForm)
      }
    },
    isMockFirstAudit() {
      return this.remarkConfigName === 'VIDEO_AUDIT_REASONS'
    },
    remarkOptions() {
      return (this.isMockFirstAudit ? VIDEO_AUDIT_REASONS : REASONS).concat([
        '封面1',
        '封面2'
      ])
    }
  },
  components: {
    MarkTag,
    NewSelect,
    AgTextarea,
    TagGroup
  },
  methods: {
    handleGrayTagChange(tagIds) {
      const newTags = tagIds.filter(
        (e) => (this.workForm.gray_tags || []).indexOf(e) === -1
      )
      let grayNote = ''
      if (newTags?.length) {
        newTags.forEach((id) => {
          const name = this.grayTagTree.find((e) => e.id === id)?.name || ''
          if (name) {
            grayNote += name
          }
        })
      }
      let newNote = this.workForm.remark || ''
      if (grayNote) {
        if (newNote.length) {
          newNote += '\n'
        }
        newNote += grayNote
      }
      this.$emit('update-form', {
        ...this.workForm,
        gray_tags: tagIds,
        remark: newNote
      })
    },
    changeRemark(selected) {
      const form = this.form
      if (form.remark.length > 0) {
        form.remark += '\n'
      }
      form.remark += selected
    }
  }
}
</script>
<style lang="stylus" scoped>
.pd-operation {
  display: grid;
  border: 1px solid var(--border-color);
  border-width: 1px 0 0 1px;
  grid-template-columns: 40px 1fr;
  font-size: 14px;

  .tag-selector {
    width: 100%;
    margin-right: 20px;
  }

  .box {
    display: grid;
    align-items: center;
    padding: 1px 5px;
    border: 1px solid var(--border-color);
    border-width: 0 1px 1px 0;
    line-height: 20px;
    padding: 1px 0 1px 5px;
  }

  .form-row {
    display: flex;
  }

  .form-content {
    display: flex;
    box-sizing: border-box;
    flex: 1;
    align-items: center;
  }

  .form-label {
    text-align: right;
    vertical-align: middle;
    float: left;
    font-size: 14px;
    color: var(--text-color);
    line-height: 40px;
    padding: 0 12px 0 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 88px;
  }

  .title {
    grid-column: 1;
    font-size: 14px;
  }

  .pd-o__form {
    .el-form-item {
      margin-bottom: 0;
    }
  }

  .pd-o__other {
    display: flex;
    align-items: center;
    width: 100%;
    padding-right: 20px;
    box-sizing: border-box;
  }

  .pd-o__content {
    margin-left: 10px;
    flex: 1;
  }
}
</style>
