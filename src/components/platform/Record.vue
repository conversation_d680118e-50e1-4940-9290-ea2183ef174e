<template>
  <div class="pd-record">
    <div class="box wrapper-title">
      <span class="blue-text">操作记录</span>
    </div>
    <div class="box related-state operation-log-v2">
      <HistoryLog :history="history" class="workbench-log"></HistoryLog>
      <!-- 2、视频历史，视频抽样回查通道隐藏视频log -->
      <VideoLog
        v-if="!(todoConfig && todoConfig.hideVideoLog)"
        class="video-log"
        :videoHistory="videoHistory"
      />
    </div>
  </div>
</template>
<script>
import { WorkState } from '@/utils/constant'
import { mapState } from 'vuex'
import HistoryLog from '@/components/Common/history-log'
import { VideoLog } from '@/v2/biz-components/workbench/index'
export default {
  name: 'Record',
  components: {
    HistoryLog,
    VideoLog
  },
  data() {
    return {
      WorkState
    }
  },
  props: {
    history: {
      type: Array,
      default: () => {
        return []
      }
    },
    videoHistory: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  computed: {
    ...mapState('pageState', [
      'todoConfig'
    ])
  }
}
</script>
<style lang="stylus" scoped>
@import "~@/styl/grid-layout.styl"
.pd-record{
  wrapper()
  grid-template-columns wrapper-width base-width * 3
  height base-height * 3 + 30px
  font-size 14px
  border-top 0px
  .box{
    box-mixin()
  }
  .wrapper-title{
    grid-column 1
  }
  layout() {
    align-items stretch
    max-height base-height * 3 + 30px
    overflow auto
  }
  .related-state {
    layout()
    grid-column 2 / 8
    grid-template-rows repeat(5, 1fr)
  }
  .count-item {
    margin 0
  }
  .count-clip{
    margin-right 5px
    .unprocessed{
      color #3d85c6
    }
    .processed{
      color #6aa84f
    }
    .closed{
      color var(--error-color)
    }
  }

  >>> .history-log {
    overflow: visible
  }
  .operation-log-v2 {
    display: flex;
    flex-direction: row;
  
    .workbench-log {
      flex: 1;
      border-right: 1px solid var(--border-color-light-2);
      height: 128px;
      overflow: auto;
      padding: 2px;
  
      >>> p {
        line-height: 1.5;
        font-size: 13px;
        margin-bottom: 4px;
      }
    }
  
    .video-log {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: auto;
      height: 128px;
      font-size: 13px;
      padding: 2px;
  
      >>> li {
        margin-bottom: 4px;
      }
    }
  }
}
</style>
