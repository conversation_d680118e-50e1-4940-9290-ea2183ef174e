<template>
  <div class="capture">
    <!-- 播放器 -->
    <div class="capture-player">
      <div class="capture-player__title">
        P1 {{eptitle}}
      </div>
      <NanoModulePlayer
        :cid="cid"
        :src="videoPlayUrl"
        @init="getPlayerInstance"
        @nano-canplay="handleCanPlay">
      </NanoModulePlayer>
      <el-button
        class="mt-12"
        type="primary"
        size="small"
        @click="() => handleToggle()"
        v-behavior-track="'toggle-play-btn'"
        v-track="{event:'switch-video-src',value:{cid}}"
      >切换播放源
      </el-button>
      <el-button
        type="info"
        size="small"
        @click="openBvcflow"
        v-behavior-track="'bvcflow-btn'"
      >
        bvcflow
      </el-button>
      <el-button
        type="info"
        size="small"
        @click="getRawVideoUrl"
      >
        查看原片
      </el-button>
    </div>
    <!-- 画面截图 -->
    <div class="capture-gallery">
      <div class="capture-gallery__head">
        画面证据信息
      </div>
      <div class="capture-gallery__content">
        <!-- 截图数组 -->
        <div class="capture-box" v-for="(cap, idx) in captureList" :key="cap.time" >
          <div class="capture-box__top">
            <div class="capture-note">备注:{{cap.note}}</div>
          </div>
          <div class="capture-box__bottom">
            <div class="capture-time">{{secondTimeFormat(cap.time)}}</div>
            <img :class="captureImgClass" :src="cutImage(cap.url)" @click="lookBigImg(cap.url)"/>
            <div class="capture-close">
              <i class="el-icon-close capture-close-icon" @click.stop="handleDelete(cap, idx)"></i>
            </div>
          </div>
        </div>
      </div>
      <!-- footer -->
      <div class="capture-gallery__footer">
        <el-button plain icon="el-icon-edit" @click="openCaptureModal" size="small">提取当前画面为人审证据</el-button>
      </div>
    </div>
    <!-- 弹窗 -->
    <el-dialog
      title="提取视频画面"
      :visible.sync="showCaptureModal"
      @close="reset"
      width="30%">
      <div class="capture-modal">
        <img class="capture-modal__img" :src="canvasBlobURL" @click="lookBigImg(canvasBlobURL)"/>
        <el-form label-width="100px" class="capture-form">
          <el-form-item label="截图时间：">
            {{secondTimeFormat(canvasBlobTime)}}
          </el-form-item>
          <el-form-item label="备注：" maxlength="16" show-word-limit>
            <el-input v-model="note" size="small"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { secondTimeFormat, DataURIToBlob, genBvcUrl } from '@/utils/index'
import { uploadImageByFile } from '@/plugins/bvcflow-upload'
import NanoModulePlayer from '@/components/package/VideoPlayer/NanoModulePlayer'
import { workbenchDetailApi, videoApi } from '@/api/index'
import { cutImage } from '@/plugins/bfsImage'
import notify from '@/lib/notify'
import PlayUrlV2Mixin from '@/mixins/playurl-v2.js'

export default {
  name: 'Capture',
  props: {
    playurl: {
      type: String,
      default: ''
    },
    captureList: {
      type: Array,
      default() {
        return []
      }
    },
    cid: {
      type: [Number, String],
      default: ''
    },
    eptitle: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      player: null,
      showCaptureModal: false,
      note: '',
      // 截图blob文件
      canvasBlob: '',
      // 截图的blob地址
      canvasBlobURL: '',
      // 截图时的视频播放时间
      canvasBlobTime: 0,
      secondTimeFormat,
      media: null,
      // 是否竖屏
      isPortrait: false,
      toggle: false // true为hd
    }
  },
  mixins: [
    PlayUrlV2Mixin
  ],
  components: {
    NanoModulePlayer
  },
  computed: {
    captureImgClass() {
      return this.isPortrait ? 'capture-img__portrait' : 'capture-img'
    },
    videoPlayUrl() {
      return this.toggle ? this.playurlV2 : this.playurl
    }
  },
  watch: {
    cid() {
      this.toggle = false
      this.playurlV2 = ''
    }
  },
  methods: {
    handleCanPlay() {
      this.checkIfPortrait()
    },
    checkIfPortrait() {
      const mediaInfo = this.player.getMediaInfo()
      const { videoWidth: width, videoHeight: height } = mediaInfo
      // 横屏视频
      if (width > height) {
        this.isPortrait = false
      } else {
        // 竖屏视频
        this.isPortrait = true
      }
    },
    async takeSnapshot() {
      this.player.pause()
      const imageDataUri = await this.player.readFrameAsDataURL()
      const imageBlob = DataURIToBlob(imageDataUri)
      const objectURL = URL.createObjectURL(imageBlob)
      return {
        blob: imageBlob,
        blobUrl: objectURL
      }
    },
    lookBigImg(imgSrc) {
      window.open(imgSrc, '_blank')
    },
    // 展示提取视频画面弹窗
    async openCaptureModal() {
      // 1.截屏
      await this.takeSnapshot().then((blobObj) => {
        const { blob, blobUrl } = blobObj
        // 时间向下取整
        this.canvasBlobTime = Math.floor(this.player.getCurrentTime())
        this.canvasBlobURL = blobUrl
        this.canvasBlob = blob
        // 2.打开弹窗
        this.showCaptureModal = true
        this.$emit('setDialogShow', true)
      }).catch(e => {
        notify.error('截图失败')
      })
    },
    reset() {
      this.note = ''
      this.canvasBlobTime = 0
      URL.revokeObjectURL(this.canvasBlobURL)
      this.canvasBlobURL = ''
      this.$emit('setDialogShow', false)
    },
    handleCancel() {
      this.showCaptureModal = false
    },
    // 确定删除
    handleDelete(item, idx) {
      const h = this.$createElement
      this.$emit('setDialogShow', true)
      this.$msgbox({
        title: '提示',
        message: h('p', null, [
          h('i', { style: 'color: teal;margin-right: 8px;color: var(--orange);', class: 'el-icon-warning' }, ''),
          h('span', null, '确认删除吗？')
        ]),
        $type: 'confirm',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        beforeClose: (action, instance, done) => {
          // done是用来关闭弹窗的
          done()
          this.$emit('setDialogShow', false)
        }
      }).then(action => {
        workbenchDetailApi.deleteFrame({
          note: item.note,
          time_stamp: item.time,
          cid: this.cid
        }).then((res) => {
          notify.success('删除成功')
          this.$emit('update-capture-list', this.captureList.toSpliced(idx, 1))
        }).catch(_ => {})
      }).catch(_ => {})
    },
    // 确定截图
    async handleConfirm() {
      // 1.判断是否已经有这个时间了
      const timeIdx = this.captureList.findIndex((item) => item.time === this.canvasBlobTime)
      const canvasBlobTime = this.canvasBlobTime
      const note = this.note
      if (timeIdx > -1) {
        this.$confirm('已存在该时间点截图，是否覆盖？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          // 上传后再传给数据
          await uploadImageByFile(this.canvasBlob).then((bfsUrl) => {
            workbenchDetailApi.uploadFrame({
              note,
              frame_url: bfsUrl,
              time_stamp: canvasBlobTime,
              cid: this.cid
            }).then((_res) => {
              this.$emit(
                'update-capture-list',
                this.captureList.toSpliced(timeIdx, 1, {
                  note,
                  time: canvasBlobTime,
                  url: bfsUrl
                })
              )
            }).catch(_ => {})
          }).catch(_ => {
            notify.error('截图上传失败, 无法添加')
          })
          // 2.设置变量
          this.showCaptureModal = false
        }).catch(() => {})
      } else {
        // 没有
        await uploadImageByFile(this.canvasBlob).then((bfsUrl) => {
          // 两种情况
          workbenchDetailApi.uploadFrame({
            note,
            frame_url: bfsUrl,
            time_stamp: canvasBlobTime,
            cid: this.cid
          }).then((res) => {
            this.$emit(
              'update-capture-list',
              this.captureList.toSpliced(1, 0, {
                note,
                time: canvasBlobTime,
                url: bfsUrl
              })
            )
          }).catch(_ => {})
        }).catch(_ => {
          notify.error('截图上传失败, 无法添加')
        })
        // 2.设置变量
        this.showCaptureModal = false
      }
    },
    getPlayerInstance() {
      this.player = window.nanoPlayer
    },
    cutImage(cover) {
      // 判断是竖屏还是横屏
      return this.isPortrait ? cutImage(cover, 110, 195) : cutImage(cover, 195, 110)
    },
    async handleToggle(toggle) {
      const nextToggle = toggle || !this.toggle
      this.toggle = nextToggle
      if (nextToggle) {
        await this.getPlayUrl(this.cid)
      }
    },
    openBvcflow() {
      const url = genBvcUrl('panel_info', {
        flowid: this.filename
      })
      window.open(url, '_blank')
    },
    async getRawVideoUrl() {
      try {
        const res = await videoApi.getRawVideoUrl({ cid: this.cid })
        const { url } = res.data
        window.open(url, '_blank')
      } catch (e) {
        console.error('获取原片链接失败', e)
      }
    }
  }
}
</script>
<style lang="stylus" scoped>
.capture
  display flex
  min-height 400px
  position relative
  &-player
    margin-right 10px
    border 1px solid #f4f4f4
    &__title
      font-size 14px
      line-height 20px
      text-indent 10px
      max-width 600px
  &-close
    z-index 200
    right -10px
    top -10px
    cursor pointer
    position absolute
    border 2px solid var(--border-color-reverse)
    border-radius 50%
    display flex
    &-icon
      background red
      color var(--text-color-reverse)
      border-radius 50%
      font-size 16px
  &-gallery
    border 1px solid var(--border-color)
    display flex
    align-items center
    flex-direction column
    width 400px
    min-height 400px
    height auto
    box-sizing border-box
    &__head
      border-bottom 1px solid var(--border-color)
      width 100%
      text-align center
      font-size 14px
      line-height 30px
      box-sizing border-box
    &__content
      overflow-y auto
      width 100%
      // height 290px
      // max-height: 324px;
      // overflow: auto;
      flex 1
      padding 10px
      box-sizing border-box
    &__footer
      border-top 1px solid var(--border-color)
      width 100%
      padding 0 10px
      box-sizing border-box
      display flex
      justify-content center
      align-items center
      height 40px
  &-box
    display inline-flex
    flex-direction column
    margin-bottom 20px
    margin-right 50px
    &__bottom
      position relative
      display flex
  &-time
    position absolute
    right 0
    bottom 0
    color var(--text-color-reverse)
    background rgba(0,0,0,.4)
    border-radius 2px
    font-size 12px
    padding 4px
  &-note
    font-size 14px
    line-height 20px
  &-img
    width 195px
    cursor pointer
    &__portrait
      width 100px
      cursor pointer
  &-modal
    display flex
    width 100%
    flex-direction column
    align-items center
    &__img
      width 200px
      cursor pointer
  &-form
    width 100%
    margin-top 20px
    .el-form-item
      margin-bottom 0
</style>
