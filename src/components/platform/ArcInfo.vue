<template>
  <div class="pd-arc-info">
    <div class="box wrapper-title">稿件信息</div>
    <div class="box title-cell">标题</div>
    <CopyrightHighlight
      class="box title-content"
      :value="archive.title"
      :isInput="false"
      :keywordData="titleKeywordData"
      :highlightWrapperStyle="{
        lineHeight: '39px'
      }"
    />
      <!-- 视频信息 -->
    <div class="box ep-cell">分P标题</div>
    <div class="box ep-content">
      <CopyrightHighlight
        v-model="video.eptitle"
        :isInput="false"
        :keywordData="epTitleKeywordData"
        :highlightWrapperStyle="{
          lineHeight: '32px'
        }">
      </CopyrightHighlight>
    </div>
    <div class="box cover-info">
      <span>
        封面1: 原图尺寸
        <template v-if="archive.cover">{{ coverDimension }}</template>
      </span>
    </div>
    <div class="box cover-cell">
      <Cover :src="archive.cover" size="240x150" />
      <span>
        封面2: 原图尺寸
        <template v-if="archive.cover2">{{ coverDimension2 }}</template>
      </span>
      <Cover :src="archive.cover2" size="240x150" />
    </div>
    <div class="box tag-cell">TAG</div>
    <div class="box tag-content">
      <HighlightText
        :value="archive.tag"
        :keywordList="sensitiveKeyword.tag"
      ></HighlightText>
    </div>
    <div class="box up-cell">
      <span class="up-label">UP</span>
      <span class="up-label-right">主</span>
    </div>
    <div class="box up-content">
      <NewTooltip maxWidth="200">
        <template slot="content">
          <span>{{ sign ? `个签：${sign}` : '暂无个签' }}</span>
        </template>
        <div class="up-name">
          <UserIcon :official="veryfyState" />
          {{ archive.author }}
        </div>
      </NewTooltip>
    </div>
    <div class="box qualification-cell">
      <span class="qualification-label">资质</span>
    </div>
    <div class="box qualification-content">{{ profession }}</div>
    <div class="box fans-cell">粉丝数</div>
    <div class="box fans-content">
      <UserFans :fans="userInfo.follower" />
    </div>
    <div class="box warning-cell">提示</div>
    <div class="box warning-content">
      <ul class="user-warn-list">
        <li v-for="(warn, index) in userWarnings" :key="index">
          <strong :class="warn.className">{{ warn.name }}</strong>
          &nbsp; {{ warn.note }}
          <span v-if="warn.mid">&nbsp;mid：{{ warn.mid }}</span>
        </li>
      </ul>
    </div>
    <div class="box desc-cell">简介</div>
    <div class="box desc-content">
      <template v-if="typeof archive.content === 'string'">
        <HighlightText
        :value="archive.content"
        :keywordList="sensitiveKeyword.desc"
      ></HighlightText>
      </template>
      <RichText
        v-else
        :value="archive.content"
        :keywordList="sensitiveKeyword.desc"
      ></RichText>
    </div>
    <div class="box dynamic-cell">稿件动态</div>
    <div class="box dynamic-content">
      <template v-if="typeof archive.dynamic === 'string'">
         <HighlightText
          :value="archive.dynamic"
          :keywordList="sensitiveKeyword.dynamic"
        ></HighlightText>
      </template>
      <RichText
        v-else
        :value="archive.dynamic"
        :keywordList="sensitiveKeyword.dynamic"
      ></RichText>
    </div>
    <div class="box source-type-cell">类型</div>
    <div class="box source-type-content">
      {{ COPYRIGHT_TYPE_MAP[archive.copyright] }}
    </div>
    <div class="box arctype-cell">分区</div>
    <div class="box arctype-content">{{ arctypeMap[archive.typeid] }}</div>
    <div class="box area-cell">投稿地区</div>
    <div class="box area-content">{{ archive.area || '其他' }}</div>
    <div class="box source-cell">转载来源</div>
    <div class="box source-content">{{ archive.source }}</div>
    <div class="box ctime-cell">投稿时间</div>
    <div class="box ctime-content">{{ archive.ctime }}</div>
  </div>
</template>
<script>
import CopyrightHighlight from '@/components/TaskDetail/archive/CopyrightHighlight'
import {
  COPYRIGHT_TYPE_MAP
} from '@/utils/constant'
import UserIcon from '@/components/TaskDetail/archive/UserIcon.vue'
import UserFans from '@/components/TaskDetail/archive/UserFans.vue'
import Cover from '@/components/ArchiveTask/Cover'
import HighlightText from '@/components/HighlightText.vue'
import { mapState, mapActions } from 'vuex'
import RichText from '../element-update/RichText.vue'
import NewTooltip from '@/components/element-update/Tooltip.vue'
import { loadImg } from '@/utils/image'

export default {
  name: 'ArcInfo',
  data() {
    return {
      coverDimension: '',
      coverDimension2: '',
      COPYRIGHT_TYPE_MAP
    }
  },
  props: {
    archive: {
      type: Object,
      default() {
        return {}
      }
    },
    video: {
      type: Object,
      default() {
        return {}
      }
    },
    userInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    userWarnings: {
      type: Array,
      default() {
        return []
      }
    },
    highlightKeywords: {
      type: Array,
      default() {
        return []
      }
    },
    videoHighlightKeywords: {
      type: Array,
      default() {
        return []
      }
    },
    archiveFilter: {
      type: Object,
      default() {
        return {}
      }
    },
    sign: String
  },
  components: {
    CopyrightHighlight,
    Cover,
    UserIcon,
    UserFans,
    RichText,
    HighlightText,
    NewTooltip
  },
  computed: {
    veryfyState() {
      if (!this.userInfo.official) {
        return {}
      }
      return this.userInfo.official
    },
    ...mapState({
      arctypeMap: (state) => state.arctype.arctypeMap
    }),
    titleKeywordData() {
      const copyrightTitleKeywords = this.highlightKeywords || []
      const sensitiveTitleKeywords = this.archiveFilter?.title || []

      return [...copyrightTitleKeywords, ...sensitiveTitleKeywords]
    },
    epTitleKeywordData() {
      const copyrightTitleKeywords = this.highlightKeywords || []
      const sensitiveEpTitleKeywords = this.archiveFilter?.p_title || []

      return [...copyrightTitleKeywords, ...sensitiveEpTitleKeywords]
    },
    sensitiveKeyword() {
      return this.archiveFilter || {}
    },
    profession() {
      // FIXME: 这文件也不用了
      const profession = this.userInfo?.profile?.profession || {}
      return profession.name || ''
    }
  },
  methods: {
    ...mapActions({
      getArctype: 'arctype/getArctype'
    }),
    onChangeForm(val) {
      this.$emit('update-form', val)
    }
  },
  watch: {
    'archive.cover': {
      async handler(newCover) {
        if (!newCover) {
          return
        }
        const { width, height } = await loadImg(newCover)
        this.coverDimension = `${width}x${height}`
      },
      immediate: true
    },
    'archive.cover2': {
      async handler(newCover) {
        if (!newCover) {
          return
        }
        const { width, height } = await loadImg(newCover)
        this.coverDimension2 = `${width}x${height}`
      },
      immediate: true
    }
  },
  mounted() {
    this.getArctype()
  }
}
</script>
<style lang="stylus" scoped>
@import "~@/styl/grid-layout.styl"
.pd-arc-info{
  font-size 14px
  display grid
  border 1px solid var(--border-color)
  border-width 1px 0 0 1px
  border-top 0px
  grid-template-columns \[col1-start\] 40px \[col2-start\] 40px \[col3-start\] 130px  \[col4-start\] 10px  \[col5-start\] 50px \[col6-start\] 105px  \[col7-start\] 40px \[col8-start\] 1fr \[col9-start\] 260px \[col9-end\]
  grid-template-rows \[row1-start\] 1fr \[row2-start\] 42px \[row3-start\] 42px \[row4-start\] 42px \[row5-start\] 42px \[row6-start\] 126px \[row7-start\] 42px \[row8-start\] 42px \[row9-start\] 42px \[row10-start\] 42px \[row10-end\]
  .wrapper-title{
    grid-column 1
    grid-row 1 / 11
  }
  .box{
    box-mixin()
  }
  .title-cell{
    grid-column 2
    grid-row 1
  }
  .title-content {
    grid-column 3 / 9
    grid-row 1
  }
  .ep-cell{
    grid-column 2
    grid-row 2
  }
  .ep-content {
    content-overflow()
    grid-column 3 / 9
    grid-row 2
  }
  .cover-cell{
    // grid-column 7 / 10
    grid-column 9
    grid-row 2 / 11
    align-items stretch
    // grid-template-columns wrapper-width
    padding 10px
    box-sizing border-box
  }
  .tag-cell {
    grid-column 2
    grid-row 3
  }
  .tag-content {
    content-overflow()
    grid-column 3 / 9
    grid-row 3
  }
  .cover-info{
    grid-column 9
    grid-row 1
  }
  .up-cell {
    grid-column 2
    grid-row 4
  }
  .up-content {
    content-overflow()
    grid-column 3 / 5
    grid-row 4
  }
  .qualification-cell{
    grid-column 5
    grid-row 4
  }
  .qualification-content {
    color var(--red)
    grid-column 6 / 7
    grid-row 4
  }
  .fans-cell {
    grid-column 7
    grid-row 4
  }
  .fans-content {
    grid-column 8 / 9
    grid-row 4
  }
  .desc-cell {
    grid-column 2
    grid-row 6
  }
  .desc-content {
    content-overflow()
    grid-column 3 / 9
    grid-row 6
    align-items normal
    white-space pre-wrap
  }
  .dynamic-cell {
    grid-column 2
    grid-row 7 / 10
  }
  .dynamic-content {
    content-overflow()
    grid-column 3 / 7
    grid-row 7 / 10
    align-items normal
    white-space pre-wrap
    word-break break-all
  }
  .source-type-cell {
    grid-column 7
    grid-row 7
  }
  .source-type-content {
    grid-column 8
    grid-row 7
  }
  .source-cell {
    grid-column 2
    grid-row 10
  }
  .source-content {
    content-overflow()
    grid-column 3 / 7
    grid-row 10
  }
  .ctime-cell{
    grid-column 7
    grid-row 10
  }
  .ctime-content{
    grid-column 8
    grid-row 10
  }
  .arctype-cell {
    grid-column 7
    grid-row 8
  }
  .arctype-content {
    grid-column 8
    grid-row 8
  }
  .area-cell {
    grid-column 7
    grid-row 9
  }
  .area-content {
    grid-column 8
    grid-row 9
  }
  .cover-title {
    display grid
    grid-column 1
    border-right 1px solid var(--border-color)
    align-items center
  }
  .cover-content {
    grid-column 2
    padding 2px
  }
  .up-label {
    font-size 12px
    grid-column 1
    grid-row 1
  }
  .up-label-right {
    grid-column 2
    grid-row 1
  }
  .warning-cell{
    grid-column 2
    grid-row 5
  }
  .warning-content{
    grid-column 3 / 9
    grid-row 5
    position relative
  }
  .user-warn-list {
    overflow auto
    width 100%
    height 100%
    padding 5px
    box-sizing border-box
    li {
      line-height 15px
      margin-bottom 5px
      &:last-child {
        margin-bottom 0
      }
    }
    &::-webkit-scrollbar-thumb {
      color var(--scrollbar-color)
      background var(--scrollbar-color)
    }
  }
  .up-name {
    position relative
    word-break break-word
  }

  .top-user {
    color var(--green)
  }
  .danger-user {
    color var(--error-color)
  }
  .politic-user {
    color var(--orange)
  }
  .block-user {
    color var(--purple)
  }
  .enterprise-user {
    color var(--blue)
  }
}
</style>
