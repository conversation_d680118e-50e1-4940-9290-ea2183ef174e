<template>
  <div class="nav-flow">
    <el-button type="info" size="small" @click="openInfoSearch" v-if="!hideInfo"  v-behavior-track="'info-search-btn'">信息追踪</el-button>
  </div>
</template>
<script>
import { genHost } from '@/api/utils'

export default {
  name: 'NavFlow',
  data() {
    return {}
  },
  props: {
    aid: {
      type: [String, Number],
      default: ''
    },
    cid: {
      type: [String, Number],
      default: ''
    },
    filename: {
      type: String,
      default: ''
    },
    hideInfo: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    openInfoSearch() {
      const url = `${genHost()}/#!/archive_utils/arc-track-video?aid=${this.aid}&cid=${this.cid}&filename=${this.filename}`
      window.open(url, '_blank')
    }
  }
}
</script>
<style lang="stylus" scoped>
.nav-flow
  line-height 50px
  display inline-block
</style>
