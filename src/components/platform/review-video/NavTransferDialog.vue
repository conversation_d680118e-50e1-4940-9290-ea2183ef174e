<template>
  <!-- 延迟弹窗 -->
  <el-dialog 
    title="延迟/流转任务" 
    :visible.sync="transferDialgoVisible" 
    append-to-body
    @close="$emit('close')">
    <el-form label-width="120px" :model="form" :rules="rules" ref="form">
      <el-form-item label="延迟/流转至" prop="trans_todo">
        <el-radio-group v-model="form.trans_todo">
          <el-radio v-for="item in todoOptions" :label="item.value" :key="item.value">{{item.label}}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="延迟/流转原因" prop="remark">
        <el-input v-model="form.remark"></el-input>
      </el-form-item>
    </el-form>
    <el-button plain size="normal" @click="transferDialgoVisible = false">取消</el-button>
    <el-button size="primary" @click="transferTask">确定</el-button>
  </el-dialog>
</template>
<script>
import { workbenchDetailApi } from '@/api'
export default {
  props: {
    value: {
      type: Boolean,
      default: false
    },
    business_id: {
      type: [String, Number]
    },
    todo_id: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      form: {
        remark: '',
        trans_todo: ''
      },
      rules: {
        trans_todo: [{ required: true, message: '请选择流转至待办', trigger: 'blur' }],
        remark: [{ required: true, message: '请填写流转原因', trigger: 'blur' }]
      },
      todoOptions: []
    }
  },
  computed: {
    transferDialgoVisible: {
      get: function () {
        return this.value
      },
      set: function (newValue) {
        this.$emit('input', newValue)
      }
    }
  },
  watch: {
    todo_id: {
      handler: function (newVal, oldVal) {
        if (newVal !== oldVal) {
          this.getTransTodos()
        }
      },
      immediate: true
    }
  },
  methods: {
    resetForm () {
      this.$refs.form.resetFields()
    },
    transferTask() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$emit('transferConfirm', this.form)
          this.resetForm()
        }
      })
    },
    async getTransTodos () {
      try {
        if (!this.business_id || !this.todo_id) return
        const res = await workbenchDetailApi.getTransTodo({
          business_id: this.business_id,
          todo_id: this.todo_id
        })
        if (res.code === 0) {
          this.todoOptions = (res.data || []).map(i => ({
            label: i.name,
            value: i.id
          }))
          if (this.todoOptions.length) {
            this.form.trans_todo = this.todoOptions[0].value
          }
        }
      } catch (e) {
        console.error(e)
      }

      // 兼容方案，暴露给外部用于判断是否隐藏流转按钮
      this.$emit('has-trans', this.todoOptions.length !== 0)
    }
  }
}
</script>
