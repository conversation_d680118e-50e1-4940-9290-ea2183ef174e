<template>
  <div class="mark-tag-tree">
    <div class="mark-tag" v-for="item in tagList" :key="item.key">
      <el-dropdown 
        :hide-on-click="false" 
        v-if="item.options && item.options.length > 0" 
        style="width: 100%"
        placement="bottom-end"
         size="small"
      >
        <RadioTag :text="item.name" :hasDropdown="true" :active="item.value === 1" />
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item v-for="(subItem, subIdx) in item.options" :key="subIdx">
            <el-checkbox v-model="subItem.value" :true-label="1" :false-label="0" @change="handleSetTagType(item)">{{subItem.name}}</el-checkbox>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <RadioTag v-else :text="item.name" :hasDropdown="false" :active="item.value === 1" @click="handleClickSingleTag(item)"/>
    </div>
  </div>
</template>
<script>
import RadioTag from '@/components/platform/RadioTag'
export default {
  name: 'MarkTag',
  data() {
    return {}
  },
  props: {
    tagList: {
      type: Array,
      default() {
        return []
      }
    }
  },
  components: {
    RadioTag
  },
  methods: {
    contradiction(group) {
      // 不管是取消还是勾选，都要排除互斥选项
      for (const item of this.tagList) {
        // 如果不同时，需要取消其他项的勾选        
        if (item.group !== group) {
          item.value = 0
          item.options = item.options.map((subItem) => {
            return {
              ...subItem,
              value: 0
            }
          })
        }
      }
    },
    getTagType(status) {
      return status === 1 ? '' : 'info'
    },
    handleSetTagType(item) {
      // checkbox控制子选项
      // 根据子选项所有值来判断
      this.contradiction(item.group)
      item.value = (item.options && item.options.some((item) => item.value === 1)) ? 1 : 0
    },
    handleClickSingleTag(item) {
      // 单选取反
      item.value = (!item.value) ? 1 : 0
      this.contradiction(item.group)
    }
  }
}
</script>
<style lang="stylus" scoped>
.mark-tag-tree
  display: flex;
  flex-wrap: wrap;
  align-content: space-between;
  width: 100%;
  .mark-tag
    margin-right 20px
    display inline-flex
    margin-top 5px
    margin-bottom 5px
    font-size 15px
    font-weight bold

  .mark-tag__label
    cursor pointer
    outline none
    width 100%
    text-align center
</style>
