<template>
  <div class="mark-tag-tree">
    <!-- 1.两级的先放前面 -->
    <div class="mark-tag-content" style="margin-left: 100px;">
      <div class="mark-tag" v-for="item in tagOneLayerArr" :key="item.key">
        <RadioTag
          :text="item.name"
          :hasDropdown="false"
          :active="item.value === 1"
          :disabled="checkTagDisabled(item)"
          @click="handleClickSingleTag(item)"
        />
      </div>
      <!-- 2.其他的放后面 -->
    </div>
    <div class="mark-tag-content">
      <div class="mark-tag-first" v-for="item in tagOtherLayerArr" :key="item.key">
        <span class="mark-tag-label">{{item.name}}：</span>
        <div class="mark-tag-second"  v-for="subItem in item.options" :key="subItem.key">
          <span class="mark-tag-second-text">{{subItem.name}} <img class="mark-tag-img" :src="MarkTagImg" /> </span>
          <template v-if="subItem.options && subItem.options.length > 0">
            <div class="mark-tag-wrap" v-for="subSubItem in subItem.options" :key="subSubItem.key">
              <RadioTag
                :text="subSubItem.name"
                :hasDropdown="false"
                :active="subSubItem.value === 1"
                :disabled="checkTagDisabled(subSubItem)"
                @click="handleClickSingleTag(subSubItem)"
              />
            </div>
          </template>
          <template v-else>
            <div class="mark-tag-wrap">
              <RadioTag
                :text="subItem.name"
                :hasDropdown="false"
                :active="subItem.value === 1"
                :disabled="checkTagDisabled(subItem)"
                @click="handleClickSingleTag(subItem)"
              />
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import RadioTag from '@/components/platform/RadioTag'
import MarkTagImg from '@/assets/mark-tag-icon.png'
import { contradiction } from '@/pages/workbench/archive/wash/util'
import { cloneDeep } from 'lodash-es'

export default {
  name: 'MarkTag',
  data() {
    return {
      MarkTagImg
    }
  },
  props: {
    tagList: {
      type: Array,
      default() {
        return []
      }
    }
  },
  components: {
    RadioTag
  },
  computed: {
    tagOneLayerArr() {
      return this.tagList.filter(item => !item.with_options)
    },
    tagOtherLayerArr() {
      return this.tagList.filter(item => item.with_options)
    }
  },
  methods: {
    checkTagDisabled(item) {
      return !!item?.extra_data?.disabled_on_classify
    },
    handleContradiction(tagList, tag) {
      if (tag.value === 1) {
        const newTagList = contradiction(tagList, tag)
        return newTagList
      } else {
        return tagList
      }
    },
    findTagInTree(tree, item) {
      // 层序遍历
      const que = []
      if (tree !== null) que.push(tree)
      let selected = null
      while (que.length !== 0) {
        let size = que.length
        for (let i = 0; i < size; i++) {
            let node = que[0]
            que.shift()
            if (node.key === item.key) {
              selected = node
              break
            }
            for (let j = 0; j < node.options.length ; j++) {
                if (node.options[j]) que.push(node.options[j])
            }
        }
        if (selected) break
      }
      return selected
    },
    handleClickSingleTag(item) {
      if (this.checkTagDisabled(item)) return
      // 单选取反
      let newTagList = cloneDeep(this.tagList)
      const selectedTag = this.findTagInTree({
        options: newTagList
      }, item)
      selectedTag.value = (!item.value) ? 1 : 0
      newTagList = this.handleContradiction(newTagList, selectedTag)
      this.$emit('updateTagList', {
        newTagList,
        selectedTag
      })
    }
  }
}
</script>
<style lang="stylus" scoped>
.mark-tag-tree
  display flex
  flex-wrap wrap
  align-content space-between
  width 100%
  span
    text-align right
    vertical-align middle
    float left
    font-size 14px
    color var(--text-color)
    line-height 40px
    box-sizing border-box
  .mark-tag-img
    width 24px
    height 24px
    vertical-align middle
  .mark-tag-wrap
    height 40px
    display inline-flex
    align-items center
    margin-right 5px
  .mark-tag-content
    display flex
    flex-wrap wrap
    align-content space-between
    width 100%
  .mark-tag-label
    width 100px
    font-weight 700
    text-align left
    position absolute
    left 0
    top 0
  .mark-tag-first
    display flex
    align-content flex-start
    flex-wrap wrap
    // display inline-block
    // white-space wrap
    width 100%
    padding-left 100px
    position relative
  .mark-tag-second
    margin-right 20px
  .mark-tag-second-text
    color var(--warning-color)
  .mark-tag
    margin-right 20px
    display inline-flex
    margin-top 5px
    margin-bottom 5px
    font-size 15px
    font-weight bold
  .mark-tag__label
    cursor pointer
    outline none
    width 100%
    text-align center
</style>
