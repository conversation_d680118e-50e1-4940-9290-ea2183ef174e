<template>
  <div class="comp-user-info">
    <div style="display:flex;">
      <!-- 用户组 -->
      <em
        v-for="group in groups(userInfo)"
        :key="group.tag"
        :style="{
          'color': `rgba(${group.font_color})`,
          'background': `rgba(${group.bg_color})`,
          'font-size': '8px',
          'height': '23px'}"
        :title="group.group_note">
        {{ group.short_tag }}
      </em>
      <!-- 用户认证 -->
      <span
        v-if="userInfo.official && userInfo.official.role !== 0"
        class="verifyIcon"
        :class="{
          individual: INDIVIDUAL_LIST.includes(userInfo.official.role),
          enterprise: ENTERPRISE_LIST.includes(userInfo.official.role)
        }">
      </span>
      <!-- 用户名 -->
      <el-tooltip placement="bottom">
        <p slot="content">{{userInfo.mid}}</p>
        <a :href="`//space.bilibili.com/${userInfo.mid}`" class="hyperlink user-select" target="_blank">
          <span>{{ userInfo.publisher_name }}</span>
        </a>
      </el-tooltip>
      <!-- up主标识 -->
      <span v-if="userInfo && userInfo.metas && userInfo.metas.is_up === true"><em style="color: var(--red)">（up主）</em></span>
    </div>
    <!-- 粉丝数 -->
    <p>
      粉丝数：
      <em
        :class="{
          'blue-fans-font': userInfo.fan_count >= 10000 && userInfo.fan_count < 100000,
          'orange-fans-font': userInfo.fan_count>= 100000 && userInfo.fan_count < 1000000,
          'red-fans-font': userInfo.fan_count >= 1000000
        }">
        {{userInfo.fan_count || 0}}
      </em>
    </p>
  </div>
</template>
<script>
import { INDIVIDUAL_LIST, ENTERPRISE_LIST } from '@/pages/workbench/constants'
export default {
  name: 'UserInfo',
  data() {
    return {
      INDIVIDUAL_LIST,
      ENTERPRISE_LIST
    }
  },
  props: {
    resource: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  computed: {
    userInfo() {
      const {
        user_info: userInfoRes = {},
        metas
      } = this.resource
      const {
        official,
        name,
        mid,
        follower: fanCount
      } = userInfoRes
      return {
        official,
        publisher_name: name,
        mid,
        fan_count: fanCount,
        metas
      }
    }
  },
  methods: {
    groups(resource) {
      const userGroup = resource.user_group || []
      if (userGroup) {
        return userGroup.filter(group => group)
      }
      return userGroup
    }
  }
}
</script>
<style lang="stylus" scoped>
.comp-user-info
  .verifyIcon
    display inline-block
    vertical-align bottom
    margin-top 2px
    width 18px
    height 18px
    background-image url('~@/assets/user-auth.png')
  .individual
    background-position -39px -82px
  .enterprise
    background-position -4px -81px
   .blue-fans-font
    color var(--blue)
    font-weight bold
  .red-fans-font
    color var(--red)
    font-weight bold
  .orange-fans-font
    color var(--orange)
    font-weight bold
  .selected-row
    background-color var(--blue-light-2)
  .hyperlink
    color var(--link-color)
    cursor pointer
    font-weight 400
  .user-select
    user-select all
    -moz-user-select all
    -webkit-user-select all
    -ms-user-select all
</style>
