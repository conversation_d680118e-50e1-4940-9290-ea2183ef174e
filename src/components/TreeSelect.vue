<template>
  <div class="tree-select">
    <el-popover
      placement="bottom-start"
      width="700"
      :disabled="disabled"
      v-model="visible"
      :visible-arrow="false">
        <el-row class="result-row" style="border: 1px solid #DCDFE6;padding: 8px;margin-bottom: 12px;overflow-y: hidden">
          <el-col :span="2">
            <span class="header-label" style="color: var(--label-color);line-height: 30px;font-size: 14px">已选:</span>
          </el-col>
          <el-col :span="22" style="line-height: 32px">
            <el-tag
              :size="size"
              v-for="(op, index) in selectedOptionsTags"
              :key="index"
              closable
              @close="removeOption(op)"
              style="margin-right: 8px;"
            >
              {{op.name}}
            </el-tag>
          </el-col>
        </el-row>
        <el-tree
          v-if="options"
          ref="tree"
          class="tree-select-tree"
          :default-expand-all="true"
          :props="defaultProps"
          :data="options"
          :default-expanded-keys="defaultExpandedKeys"
          :check-on-click-node="true"
          :check-strictly="!multipleSelect"
          :default-checked-keys="defaultCheckedKeys"
          node-key="id"
          show-checkbox
          @check-change="assignChange"
          style="max-height: 500px;overflow: auto;">
        </el-tree>
        <el-input
          slot="reference"
          :value="optionsString"
          readonly
          :placeholder="placeholder"
          :size="size"
          :style="{'width': width}"
          :disabled="disabled">
          <i class="el-icon-circle-close" slot="suffix" @click.stop="deletedAll"></i>
        </el-input>
    </el-popover>
  </div>
</template>

<script>
export default {
  name: 'TreeSelect',
  model: {
    name: 'selectedOptions',
    event: 'change'
  },
  props: {
    selectedOptions: {
      type: String,
      default() {
        return ''
      }
    },
    placeholder: {
      type: String,
      default() {
        return '请选择'
      }
    },
    width: {
      type: String,
      default() {
        return '220px'
      }
    },
    options: {
      required: true,
      type: Array,
      default() {
        return []
      }
    },
    size: {
      type: String,
      default: 'small'
    },
    multipleSelect: {
      type: Boolean,
      default: true
    },
    disabled: {
      required: false,
      type: Boolean,
      default() {
        return false
      }
    },
    disableTypeId: {
      required: false,
      type: Boolean,
      default() {
        return false
      }
    }
  },
  watch: {
    selectedOptionsTags(nextVal, oldVal) {
      if (nextVal !== oldVal) {
        if (!this.multipleSelect) {
          this.visible = false
        }
      }
    },
    selectedOptions(nextVal, oldVal) {
      if (nextVal !== oldVal) {
        this.initDefaultSelected()
      }
    },
    options(nextVal, oldVal) {
      if (nextVal !== oldVal) {
        this.initDefaultSelected()
      }
    }
  },
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'name',
        disabled: (data) => {
          if (this.disableTypeId && data.disabled) return true
          if (this.multipleSelect) {
            return data.children && !data.children.length
          } else {
            if (data.children && data.children.length) {
              return true
            }
          }
        }
      },
      defaultCheckedKeys: [],
      selectedOptionsTags: [],
      visible: false
    }
  },
  computed: {
    optionsString() {
      return this.selectedOptionsTags.map(item => item.name).join(',')
    },
    defaultExpandedKeys() {
      let keys = []
      this.selectedOptionsTags.map(item => item.pid).forEach(id => {
        if (keys.findIndex(k => k === id) < 0) {
          keys.push(id)
        }
      })
      return keys
    }
  },
  methods: {
    assignChange(...args) {
      // 如果是多选
      if (this.multipleSelect) {
        this.multipleSelectFn(...args)
      } else {
        this.singleSelectFn(...args)
      }
    },
    singleSelectFn(data, checked, indeterminate) {
      if (checked) {
        this.$refs.tree.setCheckedKeys([data.id])
        this.selectedOptionsTags = this.$refs.tree.getCheckedNodes(true)
        this.arcTypeIds = `${data.id}`
        this.$emit('change', this.arcTypeIds)
      }
    },
    multipleSelectFn(data, checked, indeterminate) {
      this.selectedOptionsTags = this.$refs.tree.getCheckedNodes(true)
      this.arcTypeIds = (this.selectedOptionsTags.map(op => op.id) || []).join(',')
      this.$emit('change', this.arcTypeIds)
    },
    removeOption(op) {
      const index = this.selectedOptionsTags.findIndex(o => o.id === op.id)
      if (index > -1) {
        this.selectedOptionsTags.splice(index, 1)
        this.$refs.tree.setChecked(op.id, false)
        this.arcTypeIds = (this.selectedOptionsTags.map(op => op.id) || []).join(',')
        this.$emit('change', this.arcTypeIds)
      }
    },
    deletedAll() {
      this.selectedOptionsTags.forEach(op => {
        this.$refs.tree.setChecked(op.id, false)
      })
      this.selectedOptionsTags = []
    },
    initDefaultSelected() {
      const pids = (this.options || []).map(o => o.id) || []
      if (this.selectedOptions) {
        this.defaultCheckedKeys = this.selectedOptions.split(',').filter(id => {
          if (pids.findIndex(p => p === id) < 0) {
            return id
          }
        })
        this.$nextTick(() => {
          this.selectedOptionsTags = this.$refs.tree.getCheckedNodes(true)
        })
      }
    }
  },
  mounted() {
    this.initDefaultSelected()
  }
}
</script>

<style lang="stylus" scoped>
.tree-select-tree
  >>>.el-tree-node__children
    white-space normal
    font-size 0
    .el-tree-node
      display inline-block
</style>
