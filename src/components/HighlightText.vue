<template>
  <span :key="componentKey">{{ value }}</span>
</template>

<script>
import keywordHighlightMixin from '@/mixins/keyword-highlight'

export default {
  props: {
    value: String
  },
  watch: {
    value() {
      // 检测到变化，需要强制重新渲染，否则会导致高亮关键词重复生效
      this.componentKey += 1
    },
    keywordList() {
      this.componentKey += 1
    }
  },
  data() {
    return {
      componentKey: 0
    }
  },
  mixins: [keywordHighlightMixin]
}
</script>
