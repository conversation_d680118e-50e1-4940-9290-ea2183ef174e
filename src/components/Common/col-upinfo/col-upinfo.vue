<template>
<!-- 发布人信息 -->
<div :class="['col-upinfo', { 'horizontal': horizontal }]">
  <!-- 第一行 -->
  <div class="col-upinfo-main" style="display:flex;flex-wrap: wrap">
    <!-- 1. 用户组 -->
    <em
      v-if="showGroupShortTag"
      v-for="(group, idx) in groups"
      :key="idx"
      :style="{
        'padding': '0px 2px',
        'color': `rgba(${group.font_color})`,
        'background': `rgba(${group.bg_color})`,
        'font-size': '8px',
        'height': '23px'}"
      :title="group.group_note">
      {{ group.short_tag }}
    </em>
    <!-- 2. 用户认证 -->
    <span
      v-if="showOfficial && user_info.official && user_info.official.role !== 0"
      class="verifyIcon"
      :class="{
        individual: INDIVIDUAL_LIST.includes(user_info.official.role),
        enterprise: ENTERPRISE_LIST.includes(user_info.official.role)
      }">
    </span>
    <!-- 3. 用户名 -->
    <el-tooltip placement="bottom" v-if="showName">
      <p slot="content">{{user_info.mid}}</p>
      <a
        :href="`https://space.bilibili.com/${user_info.mid}`"
        class="hyperlink user-select" target="_blank"
        style="min-width: 10px"
      >
        <span v-track="{ event: 'click-user-name', value: trackInfo}">{{ user_info.name }}</span>
      </a>
    </el-tooltip>
  </div>

  <!-- 第二行 粉丝数 -->
  <p v-if="user_info && Object.keys(user_info).length>0 && showFans" class="col-upinfo-fans">
    粉丝数：
    <em
      :class="{
        'blue-fans-font': user_info.follower >= 10000 && user_info.follower < 100000,
        'orange-fans-font': user_info.follower>= 100000 && user_info.follower < 1000000,
        'red-fans-font': user_info.follower >= 1000000
      }">
      {{user_info.follower || 0}}
    </em>
  </p>

 <!-- 第三行 -->
  <div v-if="user_info.face && showAvatar">
    <el-image
    :src="userPic"
    style="width: 56px; height: 56px"
    :preview-src-list="[userPic]"
    v-track="{ event: 'click-user-face', value: trackInfo }"
    ></el-image>
  </div>

  <!-- 第四行 -->
  <div v-if="showTags">
    <!-- 1. up主标识（是则红字，否则不显示） -->
    <span v-if="isUp" class="mr-4">
      <em class="warn-value">UP主</em>
    </span>
    <!-- 2. 未成年0红字，成年1默认颜色，未知2不显示 -->
    <span v-if="(user_info.adult=== 0 || user_info.adult=== 1) && showAdult" class="mr-4">
      <em :class="{ 'warn-value': user_info.adult === 0 }">
        {{ USER_UNDERAGE_TIP_MAP[user_info.adult] }}
      </em>
    </span>
    <!-- 3. 历史封禁显示黄色，否则不显示 -->
    <span v-if="user_info.is_forbidden_before === USER_FORBIDDEN_BEFORE.EVER">
      <em style="color:var(--warning-color)">[历史封禁]</em>
    </span>
    <!-- 4. 是否是大 R 主播 -->
    <span v-if="isR">
      <em class="tag-look">大R</em>
    </span>
    <!-- 5. 是否是高收入主播 -->
    <span v-if="isHighIncome">
      <em class="tag-look">高收入主播</em>
    </span>
    <!-- 6. 高价值up -->
    <span v-if="high_value_up && high_value_up.show_name">
      <em class="tag-look">{{ high_value_up.show_name }}</em>
    </span>
    <!-- 6. 是否是高粘性用户 FIXME:高价值上线后可以去掉 -->
    <span v-else-if="isHighSticky">
      <em class="tag-look">高粘用户</em>
    </span>
  </div>

  <!-- 第五行 -->
  <div>
    <!-- 4. 若为封设备封禁的账号，则显示黄色，否则不显示 -->
    <span v-if="user_info.is_same_device_forbidden === USER_FORBIDDEN_BEFORE.EVER">
      <em style="color:var(--warning-color)">[同设备封禁]</em>
    </span>
  </div>
</div>
</template>
<script>
/**
 * @component
 * @assetTitle 表格-用户信息组件
 * @assetDescription 用户组件（显示up主，小闪电，粉丝数，是否未成年，是否up主）
 * @assetImportName Upinfo
 * @assetTag 工作台列表配置组件
 */
 import replaceBfsImagesUrl from '@/utils/replaceBfsImagesUrl'
import {
  INDIVIDUAL_LIST,
  ENTERPRISE_LIST
} from '@/pages/workbench/constants'
import {
  USER_UNDERAGE_TIP_MAP,
  USER_FORBIDDEN_BEFORE
} from '@/utils/constant'
export default {
  props: {
    showGroupShortTag: {
      type: Boolean,
      default: true
    },
    // 用户组信息
    user_group: {
      type: [Object, Array],
      default: () => ({})
    },
    // 用户信息
    user_info: {
      type: Object,
      default: () => ({})
    },
    // 是否是up主
    isUp: {
      type: Boolean,
      default: false
    },
    // 是否展示未成年标识
    showAdult: {
      type: Boolean,
      default: true
    },
    // 是否是大 R 主播
    isR: {
      type: Boolean,
      default: false
    },
    // 是否是高收入主播
    isHighIncome: {
      type: Boolean,
      default: false
    },
    // 是否是高收入主播
    isHighSticky: {
      type: Boolean,
      default: false
    },
    // 是否水平展示，默认否
    horizontal: {
      type: Boolean,
      default: false
    },
    // 埋点上报参数
    trackOptions: {
      type: Object,
      default: () => ({})
    },
    // 行数据
    data: {
      type: Object,
      default: () => ({})
    },
    // 是否展示主播头像
    showAvatar: {
      type: Boolean,
      default: true
    },
    // 是否展示粉丝数
    showFans: {
      type: Boolean,
      default: true
    },
    // 是否展示用户标识
    showTags: {
      type: Boolean,
      default: true
    },
    // 是否展示用户名
    showName: {
      type: Boolean,
      default: true
    },
    // 是否展示用户认证
    showOfficial: {
      type: Boolean,
      default: true
    },
    // 高价值up
    high_value_up: Object
  },
  data() {
    return {
      INDIVIDUAL_LIST,
      ENTERPRISE_LIST,
      USER_UNDERAGE_TIP_MAP,
      USER_FORBIDDEN_BEFORE
    }
  },
  computed: {
    groups() {
      if (typeof this.user_group !== 'object' || !Object.keys(this.user_group).length) {
        return []
      }
      return Object.keys(this.user_group).map(k => (this.user_group[k]))
    },
    trackInfo() {
      return {
        todo_id: this.$route.query.todo_id,
        oid: this.data?.resource?.oid,
        is_task: this.$route.query.is_task,
        task_id: this.data?.task?.id,
        ...this.trackOptions
      }
    },
    userPic() {
      return replaceBfsImagesUrl(this.user_info.face)
    }
    
  }
}
</script>
<style lang="stylus" scoped>
@import "~@/styl/action-color.styl"
.col-upinfo {
  .verifyIcon {
    display: inline-block
    vertical-align: bottom
    margin-top: 2px
    width: 22px
    height: 22px
    background-image: url('~@/assets/user-auth.png')
  }

  .individual {
    background-position: -39px -82px
  }
  .enterprise {
    background-position: -4px -81px
  }
  .hyperlink {
    color: var(--link-color)
    cursor: pointer
    font-weight: 400
  }
  .user-select {
    user-select: all
    -moz-user-select: all;
    -webkit-user-select: all;
    -ms-user-select: all;
  }
  .blue-fans-font {
    color var(--blue)
    font-weight bold
  }
  .red-fans-font {
    color var(--red)
    font-weight bold
  }
  .orange-fans-font {
    color var(--orange)
    font-weight bold
  }
  .warn-value {
    color var(--error-color)
  }
  .tag-look {
    font-weight bold
    font-size 12px
    color var(--error-color)
    background-color var(--pink-1)
    padding 4px
    margin-right 4px
    border-radius 4px
    line-height 30px
    border none
    display inline
  }

  &.horizontal {
    display: flex;
    flex-direction: row;
    .col-upinfo-main {
      margin-right 5px
    }
    .col-upinfo-fans {
      &:before {
        content: '('
      }
      &:after {
        content: ')'
      }
    }
  }
}
</style>
