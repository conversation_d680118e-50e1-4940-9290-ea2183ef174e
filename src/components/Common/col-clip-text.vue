<template>
<div class="col-clip-text">
  <p v-if="!needClip">{{text}}</p>
  <el-tooltip
    v-else
    popper-class="popper-max-width"
    placement="bottom">
    <p slot="content">{{text}}</p>
    <p>{{clipText}}</p>
  </el-tooltip>
</div>
</template>
<script>
export default {
  props: {
    text: {
      type: String,
      default: ''
    },
    limitNumber: {
      type: Number,
      default: 5
    }
  },
  computed: {
    needClip() {
      const arr = this.text.split('/')
      return arr.length > 4
    },
    clipText() {
      const arr = this.text.split('/')
      return `${arr.slice(0, 4).join('/')}...`
    }
  }
}
</script>
<style lang="stylus" scoped>
.col-clip-text
  p
    word-break break-all
    white-space normal
</style>
