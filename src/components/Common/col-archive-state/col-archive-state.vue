<template>
  <el-tag
    style="border: none"
    v-if="isTag"
    :class="getColorMap(mappingText)">
    {{ mappingText }}
  </el-tag>
  <p v-else :class="getColorMap(mappingText)" style="background-color: initial !important;">
    {{ mappingText }}
  </p>
</template>
<script>
/**
 * @component
 * @assetTitle 表格-稿件状态组件
 * @assetDescription 工作台列表中展示稿件状态
 * @assetImportName ArchiveState
 * @assetTag 工作台列表配置组件
 */
import { STATES } from '@/utils/constant'
export default {
  name: 'ColArchiveState',
  data() {
    return {

    }
  },
  props: {
    // 是否是el-tag形式展示
    isTag: {
      type: Boolean,
      default: false
    },
    // 稿件状态文案, 支持稿件状态值 和 纯文本两种
    text: {
      type: [String, Number],
      default: ''
    }
  },
  computed: {
    mappingText() {
      return STATES[this.text] || this.text
    }
  },
  methods: {
    getColorMap(text) {
      if (['开放浏览', '橙色通过'].indexOf(text) !== -1) {
        return 'action-color-green'
      } else if (['待审', '修复待审', '等待转码', '分发中'].indexOf(text) !== -1) {
        return 'action-color-blue'
      } else {
        return 'action-color-red'
      }
    }
  }
}
</script>
<style lang="stylus" scoped>
@import "~@/styl/action-color.styl"
</style>
