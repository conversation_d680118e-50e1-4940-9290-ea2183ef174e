<template>
  <el-dialog
    class="suppress-dialog"
    title="提交"
    :visible.sync="visible"
    width="30%"
    center
    :before-close="() => $emit('close')">
    <el-checkbox 
      v-model="checked"
      :true-label="1" 
      :false-label="0"
      :disabled="!perms.XCODE2">
      是否确定重新压制视频 ？
    </el-checkbox>
    <p style="margin-top: 10px;">注：该功能用于转自类型修改时去除原用户水印</p>
    <span slot="footer" class="dialog-footer">
      <el-button @click="$emit('close')">取 消</el-button>
      <el-button type="primary" @click="$emit('confirm', checked)">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { mapState } from 'vuex'
export default {
  name: 'Suppress',
  data() {
    return {
      checked: 0
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapState({
      perms: state => state.user.perms
    })
  },
  methods: {
  }
}
</script>
