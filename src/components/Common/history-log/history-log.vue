<template>
<div class="history-log">
  <template v-if="history && history.length > 0">
    <p
      v-for="(item, index) in formatHistory(history)"
      :key="index"
      v-html="item"
    ></p>
  </template>
  <template v-else>
    <p> 暂无 </p>
  </template>
</div>
</template>
<script>
import { COLOR_MAP } from '@/utils/constant.js'

export default {
  props: {
    // 日志列表
    history: Array
  },
  methods: {
    formatHistory(history) {
      const arr = (history && history.map(item => {
        Object.entries(COLOR_MAP).forEach(([str, color]) => {
          const regex = new RegExp(`\\[${str}\\]`, 'gi')
          item = item.replace(regex, `[<em style="color: ${color}">${str}</em>]`)
        })
        return item
      })) || []

      try {
        const bracketArr = arr.map(item => {
          const bracketReg = /{([^}]+)}/g
          return item.replace(bracketReg, (target, insideBracket) => {
            return `[<em style="color: var(--blue)">${insideBracket}</em>]`
          })
        })
        return bracketArr
      } catch (err) {
        console.error(err)
        return arr
      }
    }
  }
}
</script>

<style lang="stylus" scoped>
.history-log {
  overflow auto
}
p {
  color: var(--text-color)
  // font-size: 12px
  line-height: normal
}
</style>
