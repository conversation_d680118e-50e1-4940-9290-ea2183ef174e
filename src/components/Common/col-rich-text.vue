<template>
<div class="col-rich-text">
  <p v-if="config.action_params && !config.action_params.clip" v-html="getOriginContent(row)" style="font-weight: bold;display: inline-block;white-space: pre-wrap;line-height: 22px"></p>
  <el-tooltip
    v-else
    popper-class="popper-max-width"
    placement="bottom">
    <p slot="content" v-html="getOriginContent(row)" style="font-weight: bold;white-space:pre-wrap;"></p>
    <p v-html="mappingKV(config.prop, row)" style="font-weight: bold;display: inline-block"></p>
  </el-tooltip>
</div>
</template>
<script>
import { mappingKV } from '@/pages/workbench/common.js'
export default {
  props: {
    config: {
      type: Object,
      default: {}
    },
    row: {
      type: Object,
      default: {}
    }
  },
  methods: {
    mappingKV(key, data) {
      return mappingKV(key, data)
    },
    getOriginContent(data) {
      if (this.config?.action === 'washText') {
        return mappingKV(this.config.prop, data)
      }
      return mappingKV('rsc_info.origin_content', data) || mappingKV('resource.origin_content', data)
    }
  }
}
</script>
