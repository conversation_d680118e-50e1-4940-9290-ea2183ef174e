<template>
  <div class="link_container">
    <a 
      @click.stop 
      :href="linkUrl" 
      class="col-link hyperlink user-select" 
      style="color:var(--link-color)"
      :target="target"
      v-if="linkText"
    >
      <span>{{ linkText }}</span>
      <i v-if="icon" :class="icon"></i>
    </a>
    
    <span v-else>无</span>
  </div>
</template>
<script>
/**
 * @component
 * @assetTitle 表格-链接文本组件
 * @assetDescription 展示链接和文本
 * @assetImportName Link
 * @assetTag 工作台列表配置组件
 */
export default {
  props: {
    // 链接地址
    linkUrl: {
      type: String,
      default: ''
    },
    // a标签target值，默认新标签页打开
    target: {
      type: String,
      default: '_blank'
    },
    // 链接文案
    linkText: {
      type: String | Number
    },
    // 跳转icon
    icon: {
      type: String
    }

  }
}
</script>
