<template>
  <div>
    <NewTooltip>
      <img
        slot="content"
        loading="lazy"
        :src="cutImage(cover)"
        width="170px"
        height="110px"
      />
      <span v-if="cover" style="color: var(--red)">[图片]</span>
    </NewTooltip>
    <span>{{ title }}</span>
    <span v-if="allLabel" style="color: var(--red)">{{ allLabel }}</span>
  </div>
</template>

<script>
/**
 * @component
 * @assetTitle 表格-稿件标题组件
 * @assetDescription 工作台列表展示稿件标题，带图片，带属性/禁止项
 * @assetImportName ArchiveTitle
 * @assetTag 工作台列表配置组件
 */
import NewTooltip from '@/components/element-update/Tooltip'
import { cutImage } from '@/plugins/bfsImage'
import { uniq } from 'lodash-es'

// 列表展示的稿件属性映射
// 注意：映射值工作台和稿件列表不一致
const LIST_ATTR_MAP = {
  16: '跳转',
  17: '影视',
  22: '付费',
  24: '合作'
}

// 列表展示的稿件禁止项映射
const LIST_FLOW_MAP = {
  49: '排行',
  50: '动态',
  53: '搜索',
  55: '推荐'
}

// 列表展示的稿件禁止项映射
const LIST_FORBID_MAP = {
  norank: '排行',
  noindex: '动态',
  nosearch: '搜索',
  norecommend: '推荐'
}

export default {
  components: {
    NewTooltip
  },
  props: {
    // 稿件标题
    title: String,
    // 稿件封面url地址
    cover: String,
    // 稿件属性位
    attrs: {
      type: Array,
      default() {
        return []
      }
    },
    // 稿件属性位文案映射
    attrMapping: Object,
    // 稿件禁止项
    forbid: [Array, Object],
    // 稿件禁止项文案映射
    forbidMapping: Object
  },
  computed: {
    // 兼容历史禁止项混在稿件属性中的场景，做合并去重处理
    allLabel() {
      return uniq([...this.attrLabels, ...this.forbidLabels])
      .map(label => `[${label}]`)
      .join('')
    },
    attrLabels() {
      let labels = []

      this.attrs.forEach(attr => {
        const attrMapping = this.attrMapping || LIST_ATTR_MAP
        const attrLabel = attrMapping[attr]

        if (attrLabel) {
          labels.push(attrLabel)
        }
      })

      return labels
    },
    forbidLabels() {
      let labels = []
      // 兼容两种数据格式
      // array
      if (Array.isArray(this.forbid)) {
        this.forbid.forEach(item => {
          const forbidMapping = this.forbidMapping || LIST_FLOW_MAP
          const forbidLabel = forbidMapping[item.meal_id]

          if (item.state === 1 && forbidLabel) {
            labels.push(forbidLabel)
          }
        })
      } else if (this.forbid) { // object
        Object.keys(this.forbid).forEach(key => {
          const forbidMapping = this.forbidMapping || LIST_FORBID_MAP
          const forbidLabel = forbidMapping[key]

          if (this.forbid[key] && forbidLabel) {
            labels.push(forbidLabel)
          }
        })
      }

      return labels
    }
  },
  methods: {
    cutImage(cover) {
      return cutImage(cover, 160, 100)
    }
  }
}
</script>
