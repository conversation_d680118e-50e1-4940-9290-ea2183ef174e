<template>
  <div class="col-button">
    <template v-for="(oper, index) in auditSingle" >
      <el-button
        v-if="!oper.hasTags"
        :key="index"
        :type="showType(oper)"
        plain
        size="small"
        @click.stop="clickedOper(oper)">
        {{ oper.hot_key ? `${oper.name}(${oper.hot_key})` : `${oper.name}` }}
      </el-button>
      <template v-else>
        <el-button
          v-for="tag in getReasonTags(oper.name)"
          :key="tag.name"
          :type="showType(oper)"
          plain
          size="small"
          @click.stop="clickedOper(oper, tag)">
          {{tag.name}}
        </el-button>
      </template>
    </template>
  </div>
</template>
<script>
import { mapState, mapActions } from 'vuex'
import { OPER_BUTTON_TYPES } from '@/pages/workbench/constants'
import notify from '@/lib/notify'
import { historyMixin } from '@/mixins/workbench'
export default {
  mixins: [historyMixin],
  props: {
    config: {
      type: Object,
      default: {}
    },
    row: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
    }
  },
  computed: {
    ...mapState('todoDetail', [
      'auditSingle',
      'businessId',
      'todoType',
      'todoId',
      'dataReady',
      'enumData'
    ])
  },
  methods: {
    ...mapActions('todoDetail', [
      'getTask'
    ]),
    getReasonTags(name) {
      const reasonTags = this.enumData?.reasonTags?.options || []
      const reasonOptions = (reasonTags.find(item => item.name === name) || {}).options
      return reasonOptions || []
    },
    showType(oper) {
      return OPER_BUTTON_TYPES[oper.name] || oper.show_type
    },
    async clickedOper(oper = {}, tag) {
      if (!this.dataReady) {
        notify.warning('请稍后再试~')
        return
      }
      const row = this.row
      if (oper.action === 'log') {
        this.getLog(oper, row)
        return true
      }
      // 弹窗
      if (oper.action === 'dialog') {
        this.$emit('openDialog', oper, row, tag)
        return true
      }
      // 直接提交
      if (oper.action === 'submit') {
        const params = {
          resource_result: {}
        }
        this.submit(params, oper, row)
        return true
      }

      if (oper.action === 'confirm') {
        const params = {
          resource_result: {}
        }
        if (tag) {
          params.note = `tag:${tag?.name || ''};`
        }
        this.confirm(params, oper, row, tag)
        return true
      }
    },
    async getLog(oper, row) {
      const { item_id: itemId } = row.resource
      const { businessId, todoId, todoType } = this
      await this.getHistory({
        item_id: itemId,
        business_id: businessId,
        todo_id: todoId,
        todo_type: todoType
      }).then((history) => {
        this.history = history
        this.$emit('openDialog', oper, history)
      })
    },
    confirm(data, oper, row, tag) {
      const name = oper.name
      this.$confirm(`此操作将${name}当前选中审核内容, ${tag?.name ? `驳回TAG为${tag?.name}, ` : ''}是否继续?`, name, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.submit(data, oper, row)
      }).catch((e) => {})
    },
    submit(data, oper, row) {
      this.$emit('submit', data, oper, row)
    }
  }
}
</script>
