<template>
  <div class="col-custom-text">
    <div :style="config.style"  v-html="getCustomHtml()"></div>
  </div>
</template>
<script>
import { evalExpression } from '@/utils/index'
import * as $utils from './col-utils'

export default {
  props: {
    config: {
      type: Object,
      default: {}
    },
    row: {
      type: Object,
      default: {}
    }
  },
  methods: {
    getCustomHtml() {
      return evalExpression('string', this.config.html, {
        ...(this.row || {}),
        $utils
      })
    }
  }
}
</script>
