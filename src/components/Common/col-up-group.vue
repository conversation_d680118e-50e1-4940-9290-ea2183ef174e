<template>
<!-- 发布人信息 -->
<div class="col-upinfo">
  <div style="display:flex;">
    <!-- 用户组 -->
    <span
      v-for="group in groups"
      :title="group.group_name"
      :key="group.id"
      :class="colorClass[group.group_id]"
    >{{group.short_tag || group.group_tag}}</span>
    <!-- 用户认证 -->
    <span
      v-if="user_info.official && user_info.official.role !== 0"
      class="verifyIcon"
      :class="{
        individual: INDIVIDUAL_LIST.includes(user_info.official.role),
        enterprise: ENTERPRISE_LIST.includes(user_info.official.role)
      }">
    </span>
    <!-- 用户名 -->
    <el-tooltip placement="bottom">
      <p slot="content">{{user_info.mid}}</p>
      <a :href="`https://space.bilibili.com/${user_info.mid}`" class="hyperlink user-select" target="_blank">
        <span>{{ user_info.name }}</span>
      </a>
    </el-tooltip>
    <!-- up主标识 -->
    <span v-if="isUp"><em style="color: var(--red)">（up主）</em></span>
  </div>
  <!-- 粉丝数 -->
  <p v-if="user_info">
    粉丝数：
    <em
      :class="{
        'blue-fans-font': user_info.follower >= 10000 && user_info.follower < 100000,
        'orange-fans-font': user_info.follower>= 100000 && user_info.follower < 1000000,
        'red-fans-font': user_info.follower >= 1000000
      }">
      {{user_info.follower || 0}}
    </em>
  </p>
</div>
</template>
<script>
import { 
  INDIVIDUAL_LIST, 
  ENTERPRISE_LIST
} from '@/pages/workbench/constants'
export default {
  props: {
    user_group: {
      type: Object,
      default: () => ({})
    },
    user_group_array: {
      type: Array,
      default: () => {
        return undefined
      }
    },
    user_info: {
      type: Object,
      default: () => ({})
    },
    isUp: {
      type: Boolean,
      default: false
    },
    show_group_ids: {
      type: Array, 
      default() {
        return []
      }
    }
  },
  data() {
    return {
      INDIVIDUAL_LIST,
      ENTERPRISE_LIST
    }
  },
  computed: {
    groups() {
      if (this.user_group_array) {
        return this.user_group_array.filter(group => {
          if (this.show_group_ids.length > 0) {
            return group && this.show_group_ids.some(id => +id === +group.group_id)
          } else {
            return true
          }
        })
      }
      if (typeof this.user_group !== 'object' || !Object.keys(this.user_group).length) {
        return []
      }
      return Object.keys(this.user_group).map(k => (this.user_group[k]))
    },
    colorClass() {
      return {
        1: ['group-icon', 'priority'],
        2: ['group-icon', 'danger'],
        3: ['group-icon', 'pgc'],
        5: ['group-icon', 'politic']
      }
    }
  }
}
</script>
<style lang="stylus" scoped>
@import "~@/styl/action-color.styl"
.col-upinfo {
  text-align left
  .verifyIcon {
    display: inline-block
    vertical-align: bottom
    margin-top: 2px
    width: 22px
    height: 22px
    background-image: url('~@/assets/user-auth.png')
  }

  .individual {
    background-position: -39px -82px
  }
  .enterprise {
    background-position: -4px -81px
  }
  .group-icon {
    display: inline-block;
  }
  .priority {
    color: var(--success-color)
    background-color: mix(lighten(#67C23A, 20%), white, 50%);
    background-color: rgba(lighten(#67C23A, 20%), 0.5);
  }
  .danger {
    color: var(--error-color);
    background-color: mix(darken(#EE5037, 10%), white, 30%);
    background-color: rgba(darken(#EE5037, 10%), 0.3);
  }
  .pgc {
    color: var(--blue);
    background-color: mix(darken(#409EFF, 10%), white, 30%);
    background-color: rgba(darken(#409EFF, 10%), 0.3);
  }
  .politic {
    color: var(--orange);
    background-color: mix(darken(#E6A23C, 10%), white, 30%);
    background-color: rgba(darken(#E6A23C, 10%), 0.3);
  }
  .blue-fans-font {
    color var(--blue)
    font-weight bold
  }
  .red-fans-font {
    color var(--red)
    font-weight bold
  }
  .orange-fans-font {
    color var(--orange)
    font-weight bold
  }
}
</style>
