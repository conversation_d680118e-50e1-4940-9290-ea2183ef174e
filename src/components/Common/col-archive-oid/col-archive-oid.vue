<template>
  <div>
    <a :href="getAvLink"  class="col-type-text" target="_blank">{{aid}}</a>
    <br>
    <em class="col-type-text">(</em><a :href="getBvLink" class="col-type-text" target="_blank">{{bvid}}</a><em class="col-type-text">)</em>
  </div>
</template>
<script>
/**
 * @component
 * @assetTitle 表格-稿件oid组件
 * @assetDescription 工作台列表中展示稿件oid链接
 * @assetImportName ArchiveOid
 * @assetTag 工作台列表配置组件
 */
import { genHost } from '@/api/utils'
export default {
  name: 'ColTypeId',
  data() {
    return {
    }
  },
  props: {
    // 稿件aid
    aid: {
      type: [String, Number],
      default: '' 
    },
    // 稿件bvid
    bvid: {
      type: [String, Number],
      default: '' 
    },
    // 是否跳转前台地址，默认是
    forward: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    getAvLink() {
      return this.forward ? `//www.bilibili.com/video/av${this.aid}` : `${genHost()}/aegis/#/audit/tasks/detail/11?business_id=11&oid=${this.aid}&list_type=00`
    },
    getBvLink() {
      return this.forward ? `//www.bilibili.com/video/${this.bvid}` : `${genHost()}/aegis/#/audit/tasks/detail/11?business_id=11&oid=${this.aid}&list_type=00`
    }
  }
}
</script>
<style lang="stylus" scoped>
.col-type-text
  color var(--link-color)
  cursor pointer
  user-select all
</style>
