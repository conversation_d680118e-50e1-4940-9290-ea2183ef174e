<template>
  <div class="trigger-group">
    <Popover
      v-if="aiRiskList.length !== 0"
      placement="right"
      trigger="hover"
      :popover="popover"
      :popoverConfig="popoverConfig"
    >
      <div
        v-for="risk in aiRiskList"
        :key="risk.cid"
      >
        <div class="font-bold">{{ `P${risk.pOrder}` }}</div>
        <div
          v-for="(tag, index) in risk.riskList"
          :key="index"
        >
          <span>{{ `${tag.type} ` }}</span>
          <span
            v-for="(time, index) in tag.time"
            :key="index"
            class="hyperlink"
          >
            <span @click="openDialog(time, risk)">{{ `${time}` }}</span>
            <span v-if="index !== tag.time.length - 1">，</span>
          </span>
        </div>
      </div>
    </Popover>
    <div
      v-else-if="aiResult === -1 || !auditFinish"
      class="font-bold"
      style="color: var(--red)"
    >
      AI处理超时
    </div>
  </div>
</template>
<script>
import Popover from './Popover'

export default {
  components: {
    Popover
  },
  props: {
    // 直接展示还是可气泡展开
    popover: {
      type: Boolean,
      default: false
    },
    popoverConfig: {
      type: Object,
      default: {}
    },
    aiResult: {
      type: Number,
      default: 0
    },
    auditFinish: {
      tyoe: Boolean,
      default: true
    },
    aiRiskList: {
      type: Array,
      default: () => []
    },
    // 弹窗的名字 即弹窗组件 ref 的名字 通过这个名字打开弹窗
    dialogName: {
      type: String,
      default: ''
    },
    // 需要传递给弹窗组件的参数
    dialogProps: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    openDialog(linkText, risk) {
      const extraDialogProps = {}
      if (this.dialogName === 'iframeVideoPlayer') {
        extraDialogProps.seekTimeString = linkText
        extraDialogProps.seekPause = false
      }
      this.$emit(
        'openDialog',
        { reference: this.dialogName },
        '_data',
        {
          aid: this.dialogProps.oid,
          cid: risk.cid,
          pOrder: risk.pOrder,
          showDetail: this.dialogProps.showDetail,
          ...extraDialogProps
        }
      )
    }
  }
}
</script>
<style lang="stylus" scoped>
.hyperlink
  color var(--link-color)
  cursor pointer
  font-weight 400
.trigger-group
  .hyperlink
    padding 4px 0
    display inline-block
    white-space nowrap
</style>
