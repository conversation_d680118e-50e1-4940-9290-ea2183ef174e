import evalExp from '@/v2/utils/evalExp.js'

export default function getProps(row, col) {
  const { typeConfig, dialog_props } = col
  const { ai_alert = '', ai_result = '', audit_finish = '', popover = false, popover_config = {} } = typeConfig
  const dialogProps = {}
  for (const [propName, propPath] of Object.entries(dialog_props)) {
    dialogProps[propName] = evalExp('string', propPath, row) || propPath
  }
  const riskList = evalExp('array', ai_alert, row) || []
  const aiResult = evalExp('number', ai_result, row) || 0
  const auditFinish = evalExp('boolean', audit_finish, row) || true
  return {
    dialogName: col.dialog_name,
    aiResult,
    auditFinish,
    aiRiskList: riskList.map(risk => ({
      pOrder: risk.page,
      cid: risk.cid,
      riskList: risk.aitag?.map(tag => ({
        type: tag.detail ? `${tag.type}：${tag.detail}` : tag.type,
        time: tag.time
      })) || []
    })),
    dialogProps,
    popover,
    popoverConfig: popover_config
  }
}
