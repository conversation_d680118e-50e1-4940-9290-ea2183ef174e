<template>
  <div class="break-word">
    <div
      ref="content"
      :style="multiLineTruncation"
    >
      <slot></slot>
    </div>
    <el-popover
      :width="parseInt(popoverConfig.width, 10) || 600"
      :trigger="popoverConfig.trigger || 'hover'"
      :offset="popoverConfig.offset || 0"
      :placement="popoverConfig.placement || 'right'"
      v-if="showPopover"
      v-model="isVisible"
    >
      <div class="flex-r">
        <i
          class="el-icon-close"
          style="cursor: pointer"
          @click="closePopover"
        ></i>
      </div>

      <div
        slot="reference"
      >
        <slot name="reference">
          <div 
            class="font-bold"
            style="color: var(--blue)"
            :style="{
              cursor: popoverConfig.trigger === 'click' ? 'pointer' : ''
            }"
          >查看全部</div>
        </slot>
      </div>
      <div
        class="overflow-auto break-word p-4"
        :style="`max-height: ${parseInt(popoverConfig.maxPopoverHeight, 10) || 450}px;`"
      >
        <slot></slot>
      </div>
    </el-popover>
  </div>
</template>
<script>
export default {
  props: {
    popover: {
      type: Boolean,
      default: false
    },
    popoverConfig: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      showPopover: false,
      isVisible: false
    }
  },
  computed: {
    multiLineTruncation() {
      if (this.popover) {
        return `
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: ${parseInt(this.popoverConfig.lineNumber, 10) || 10};
          overflow: hidden;
        `
      }
      return ''
    }
  },
  mounted() {
    this.getShowPopover()
  },
  updated() {
    this.getShowPopover()
  },
  methods: {
    getShowPopover() {
      this.showPopover =
        this.popover ? (this.$refs.content?.scrollHeight > this.$refs.content?.clientHeight) : false
    },
    closePopover() {
      this.isVisible = false
    }
  }
}
</script>
<style lang="stylus" scoped>
.overflow-auto 
  overflow auto

.break-word 
  word-break break-word

</style>
