<template>
<div
  class="col-text"
  v-highlight-config="areaName"
  :style="{ color: textColor }"
  :key="showText"
>
  {{ showText }}
</div>
</template>
<script>
/**
 * @component
 * @assetTitle 表格-text
 * @assetDescription 纯文本组件
 * @assetImportName Text
 * @assetTag 工作台列表配置组件
 */
export default {
  props: {
    // 文本
    text: {
      type: [String, Number],
      default: ''
    },
    // 高亮area，用于敏感词高亮匹配
    areaName: [String, Object],
    // 文本颜色
    textColor: {
      type: String,
      default: ''
    },
    // 兜底文案
    placeholderText: {
      type: String,
      default: ''
    }
  },
  computed: {
    showText() {
      return this.text || this.placeholderText
    }
  }
}
</script>
