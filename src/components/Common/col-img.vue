<template>
<div class="col-img">
  <img :src="imgSrc" :style="imgStyle" @click="preview"/>
  <p v-if="annotation">{{annotation}}</p>
</div>
</template>
<script>
export default {
  props: {
    imgSrc: {
      type: String,
      default: ''
    },
    imgStyle: Object,
    annotation: {
      type: String,
      default: ''
    }
  },
  methods: {
    preview() {
      window.open(this.imgSrc, '_blank')
    }
  }
}
</script>

<style scoped lang="stylus">
.col-img
  img
    width 80px
    height 80px
</style>
