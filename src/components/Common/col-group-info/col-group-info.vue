<template>
  <div style="display:flex;">
    <GroupInfo
      :group="{
        memberNumber,
        id: groupId,
        name: groupName
      }"
    />
  </div>
</template>

<script>
/**
 * @component
 * @assetTitle 表格-成员组件
 * @assetDescription 展示组名，组员数
 * @assetImportName GroupInfo
 * @assetTag 工作台列表配置组件
 */
import GroupInfo from '@/components/Workbench/GroupInfo'

export default {
  components: { GroupInfo },
  props: {
    // 群成员数
    memberNumber: {
      type: [Number, String],
      default: 0
    },
    // 群id
    groupId: {
      type: [Number, String],
      default: ''
    },
    // 群名
    groupName: {
      type: [Number, String],
      default: ''
    }
  }
}
</script>
