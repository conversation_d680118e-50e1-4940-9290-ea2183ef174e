<template>
  <el-dialog
    title="提取信息"
    :visible="visible"
    :modal="modal"
    @close="$emit('update:visible', false)"
    class="extra-info-dialog"
    width="80%"
  >
    <div class="extra-info-topbar">
      <i class="el-icon-document-copy"
        id="extra-copy"
        data-clipboard-action="copy"
        data-clipboard-target="#extra-info-list"
      >
      </i>
    </div>
    <ul id="extra-info-list">
      <li v-for="(item, index) in formattedInfoList" :key="index">{{ item.name }}：{{ item.value }}</li>
    </ul>
    <template v-if="multipleReason && perms.REASON_PIC_READ">
      <div>截图：</div>
      <SnapshotViewer :multipleReason="multipleReason" showThumbnail />
    </template>
  </el-dialog>
</template>

<script>
import { mapState } from 'vuex'
import Clipboard from '@bilibili-studio/clipboard'
import notify from '@/lib/notify'
import SnapshotViewer from '@/v2/biz-components/archive/SnapshotViewer'

export default {
  components: {
    SnapshotViewer
  },
  props: {
    visible: Boolean,
    extraInfoList: Array,
    modal: {
      type: Boolean,
      default: true
    },
    multipleReason: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    ...mapState({
      adorderIndustries: (state) => state.common.adorderConfigs,
      perms: (state) => state.user.perms
    }),
    formattedInfoList() {
      return this.extraInfoList.map(e => {
        if (!e.hasOwnProperty('industryId')) return e
        const industryName = this.getAdorderIndustryName(e.industryId)
        return { name: e.name, value: industryName || '无' }
      }).filter(e => !!e)
    }
  },
  data() {
    return {
      clipboard: null
    }
  },
  mounted() {
    this.initClipboard()
  },
  beforeDestroy() {
    this.clipboard?.destroy()
  },
  methods: {
    initClipboard() {
      this.clipboard = new Clipboard('#extra-copy')

      this.clipboard.on('success', () => {
        notify.success('复制成功')
      })

      this.clipboard.on('error', () => {
        notify.error('复制失败，请重试')
      })
    },
    getAdorderIndustryName(industryId) {
      if (!industryId) return ''
      return this.adorderIndustries.find(e => e.type === 0 && e.id === industryId)?.name || ''
    }
  }
}
</script>

<style lang="stylus" scoped>
.extra-info-dialog {
  .extra-info-topbar {
    margin-top -20px
    display flex
    justify-content flex-end
    #extra-copy {
      font-size 20px
    }
  }
  #extra-info-list {
    li {
      line-height 20px
      white-space pre-wrap
    }
  }
}
</style>
