<template>
  <div class="content-sider" :class="{ small: isCollapse }">
    <!-- 图标 -->
    <div
      class="top-logo animated jello"
      :class="{
        'top-logo-uat': showUatLogo,
        'top-logo-pre': showPreLogo
      }"
      :style="{
        'box-shadow': !isCollapse
          ? '0px 12px 8px -14px rgba(32,33,36,.28)'
          : 'none'
      }"
      @click="handleGoIndex"
    >
      <span
        v-if="showTextLogo"
        class="logo-env-text"
        @click="goHome"
      >
        {{ env.toUpperCase() }}
      </span>
      <SvgIcon
        v-else
        class="logo-img"
        name="logo"
        title="平台首页"
        @click="goHome"
      />
      <span v-if="!isCollapse" class="logo-text">
        AEGIS
      </span>
    </div>
    <!-- 用户信息 -->
    <UserAvatar
      style="margin-top: 10px"
      :collapse="isCollapse"
      :uid="uid"
      :uname="username"
      :nickname="nickname"
    />
    <!-- 工作状态切换组件 -->
    <WorkStatusSelect :isCollapse="isCollapse" @changeCollapse="changeCollapse"/>
    <el-divider></el-divider>
    <div class="list">
      <el-menu
        class="el-menu-vertical first-menu"
        background-color="var(--bg-color)"
        text-color="var(--text-color)"
        active-text-color="var(--primary-color)"
        :router="false"
        :default-active="activeIndex"
        :collapse-transition="false"
        :unique-opened="true"
        :collapse="isCollapse"
      >
        <template v-for="(menu, firstIndex) in authMenu">
          <el-submenu
            v-if="menu.sub"
            :key="firstIndex"
            :index="menu.path || menu.title"
            :class="[
              {
                'collpase-active':
                  isCollapse &&
                  (collapseIndex === menu.path || collapseIndex === menu.title)
              }
            ]"
          >
            <template slot="title">
              <i><SvgIcon class="menu-svg-icon" :name="menu.icon" :title="menu.title" /></i>
              <span slot="title">{{ menu.title }}</span>
            </template>

            <div
              v-for="(secondMenu, secondIndex) in menu.sub"
              :key="secondIndex"
            >
              <el-submenu
                :index="secondMenu.path || secondMenu.title"
                v-if="secondMenu.sub && secondMenu.sub.length"
              >
                <template slot="title">{{ secondMenu.title }}</template>
                <div
                  v-for="(thirdMenu, thirdIndex) in secondMenu.sub"
                  :key="thirdIndex"
                >
                  <el-submenu
                    :index="thirdMenu.path || thirdMenu.title"
                    v-if="thirdMenu.sub && thirdMenu.sub.length"
                  >
                    <template slot="title">{{ thirdMenu.title }}</template>
                    <el-menu-item
                      v-for="(fourthMenu, fourthIndex) in thirdMenu.sub"
                      :index="fourthMenu.path"
                      :key="fourthIndex"
                      @click="nextPage(fourthMenu.path)"
                    >
                      <span slot="title">{{ fourthMenu.title }}</span>
                    </el-menu-item>
                  </el-submenu>
                  <el-menu-item
                    v-else
                    :index="thirdMenu.path"
                    @click="nextPage(thirdMenu.path)"
                  >
                    <span slot="title">{{ thirdMenu.title }}</span>
                  </el-menu-item>
                </div>
              </el-submenu>
              <el-menu-item
                v-else
                :index="secondMenu.path"
                @click="nextPage(secondMenu.path)"
              >
                {{ secondMenu.title }}
              </el-menu-item>
            </div>
          </el-submenu>
          <el-menu-item
            v-else-if="menu.path && !menu.sub"
            :key="firstIndex"
            :index="menu.path || menu.title"
            @click="nextPage(menu.path)"
          >
             <i><SvgIcon class="menu-svg-icon" :name="menu.icon" :title="menu.title" /></i>
            <span slot="title">{{ menu.title }}</span>
          </el-menu-item>
        </template>
      </el-menu>
    </div>
    <div
      class="bottom"
      :class="[
        {
          collapse: !isCollapse
        }
      ]"
    >
      <i
        @click="changeCollapse"
        :class="{
          'el-icon-s-fold open': !isCollapse,
          'el-icon-s-unfold close': isCollapse
        }"
        class="icon collapse-icon"
      ></i>
    </div>
  </div>
</template>
<script>
import { mapState, mapGetters, mapActions } from 'vuex'
import { recurive as genAuthMenu } from '@/utils/menu'
import { openNewPage } from '@/utils/index'
import SvgIcon from '@/v2/pure-components/Icon/SvgIcon.vue'
import UserAvatar from '@/v2/biz-components/goo/UserAvatar.vue'
import WorkStatusSelect from '@/global-components/work-status-select/index'

export default {
  components: {
    SvgIcon,
    UserAvatar,
    WorkStatusSelect
  },
  data() {
    return {
      isCollapse: false,
      env: '',
      proxyEnv: ''
    }
  },
  created() {
    this.isCollapse = this.collapseMenu
    this.env = this.getEnv()
    this.proxyEnv = this.getProxyEnv()
  },
  computed: {
    ...mapGetters({
      getEnv: 'env/getEnv',
      getProxyEnv: 'env/getProxyEnv'
    }),
    ...mapState(['sidebarData']),
    ...mapState({
      uid: (state) => state.user.uid,
      username: (state) => state.user.username,
      nickname: (state) => state.user.nickname,
      collapseMenu: state => state.menu.collapseMenu
    }),
    activeIndex() {
      let index = this.$route.path.split('/') || []
      let res = ''
      if (index.length > 3) {
        res = `/${index.slice(1).join('/')}`
      } else {
        res = this.$route.path === '/' ? null : this.$route.path
      }
      // 如果是收起状态直接返回父级的title
      return res
    },
    collapseIndex() {
      if (this.isCollapse && this.activeIndex) {
        let selected = null
        const getRootFromSub = function (node, root, path) {
          if (selected) return
          if (node.path === path) {
            selected = root
            return
          }
          // 3. 确定单层递归逻辑
          if (node.sub) {
            for (let i = 0; i < node.sub.length; i++) {
              getRootFromSub(
                node.sub[i],
                node.level === 1 ? node.sub[i] : root,
                path
              )
            }
          }
        }
        getRootFromSub(
          {
            level: 1,
            sub: this.authSubMenu
          },
          undefined,
          this.activeIndex
        )
        return selected ? (selected.title || selected.path) : ''
      } else {
        return this.activeIndex
      }
    },
    authMenu() {
      return genAuthMenu(this.sidebarData, this.uid)
    },
    authSubMenu() {
      return this.authMenu.filter((menu) => menu.sub)
    },
    showUatLogo() {
      return this.env === 'uat' && this.proxyEnv === 'uat' // 前端和后端都是 uat 才是真 uat 环境
    },
    showPreLogo() {
      return this.env === 'pre' || this.proxyEnv === 'pre' // 前端和后端有一个是 pre 就算是 pre 环境
    },
    showTextLogo() {
      return this.showUatLogo || this.showPreLogo
    }
  },
  watch: {
    isCollapse: {
      handler() {
        this.$store.dispatch('menu/collapseMenu', this.isCollapse)
      }
    }
  },
  methods: {
    ...mapActions({
      saveWorkStatus: 'user/saveWorkStatus'
    }),
    changeCollapse() {
      this.isCollapse = !this.isCollapse
    },
    goHome() {
      this.$router.push('/')
    },
    nextPage(path) {
      // 知识库新窗口打开
      if (path === '/v3/audit-tools/library' || path === '/v3/audit-tools/library/list' || path === '/v3/order') {
        openNewPage(this.$router, path, {})
      } else {
        this.$router.push(path)
      }
    },
    handleGoIndex() {
      this.$router.push('/')
    }
  }
}
</script>
<style lang="stylus" scoped>
.content-sider
  width 236px
  height 100vh
  overflow-x hidden
  display flex
  flex-direction column
  >>>.el-divider
    margin 10px auto
    width 189px
  &.small
    width 64px
  .top-logo
    height 72px
    display flex
    justify-content center
    align-items center
    box-sizing border-box
    text-align center
    margin-bottom 1px
    background var(--primary-color)
  .top-logo-uat
    background var(--uat-primary-color)
  .top-logo-pre
    background var(--pre-primary-color)
  .logo-text
    color var(--white)
    font-weight bold
    font-size 30px
    line-height 40px
    margin-left 10px

  .logo-env-text
    margin-left 0
    vertical-align middle
    cursor pointer
    color var(--white)
    font-weight bolder
    font-size 20px
  .logo-img
    height 40px
    width 40px
    vertical-align middle
    cursor pointer
    color var(--white)
  // 二级菜单需要灰色
  .menu-svg-icon
    font-size 16px
    margin-right 4px
    text-align center
    width 1em
    height 1em
    vertical-align -0.2em
    fill currentColor
    overflow hidden
  >>>.el-submenu
    background var(--bg-color)
  // 初级菜单白色
  .first-menu
    >li, > li >>> >div
      background var(--white) !important
  // hover状态
  >>>.el-menu-item:hover, >>>.el-submenu__title:hover
    color var(--primary-color) !important
    background-color var(--blue-light-2) !important
    .icon, i
      color var(--blue) !important
  // 收起时，只标蓝父级
  .collpase-active
    background-color var(--blue-light-2) !important
    color var(--primary-color) !important
    &:before
      content ''
      width 5px
      height 100%
      background var(--blue-light-1)
      position absolute
      left 0
      z-index 10
    >>>i, >>>.el-submenu__title
      background-color var(--blue-light-2) !important
      color var(--primary-color) !important
  // active状态
  >>>.el-menu-item
    &.is-active
      background-color var(--blue-light-2) !important
      color var(--primary-color) !important
      &:before
        content ''
        width 5px
        height 100%
        background var(--blue-light-1)
        position absolute
        left 0
  >>>.el-icon-menu:hover
    cursor pointer
  .menu-icon
    margin-top -4px
    padding-right 4px
  >>>.el-menu
    border-right 0px
  .bottom
    position absolute
    display flex
    width 100%
    background var(--content-bg-color)
    bottom 0
    padding-bottom 20px
    justify-content center
    box-sizing border-box
    &.collapse
      justify-content right
      padding-right 15px
    .collapse-icon
      color var(--text-color-light-1)
      cursor pointer
      font-size 24px
      &:hover
        color var(--primary-color)
  .close
    left 20px
  .open
    left 202px
  .list
    padding-bottom 50px
    flex 1
    overflow-y auto
.workStatus
  display flex
  justify-content space-around
  .workSelect
    width 200px
    ::v-deep .el-input__inner
      width 200px
  .previewStatus
    display flex
    justify-content flex-start
    align-items center
    cursor pointer
    font-size 12px
    margin-top 10px
    .statusIcon
      width 10px
      height 10px
      border-radius 10px
      background-color var(--green)
      margin-right 5px
    .inte_class
      background-color var(--pre-primary-color)
    .break_class
      background-color var(--orange)
</style>
