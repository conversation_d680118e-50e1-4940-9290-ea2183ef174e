<template>
  <micro-app
    v-if="appName"
    :name="appName"
    :url="url"
    :data="{ cpmName: name, cpmProps: $attrs, cmpListeners: $listeners, userInfo, env }"
    @created="handleCreate"
    @beforemount="handleBeforeMount"
    @mounted="handleMount"
    @unmount="handleUnmount"
    @error="handleError"
    @datachange="handleDataChange"
  />
</template>
<script>
import { getUrl } from '@/pages/v3/config'
import { mapState } from 'vuex'

export default {
  props: {
    name: String,
    appName: String
  },
  data() {
    return {
      url: getUrl()
    }
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.user,
      env: (state) => state.env
    })
  },
  methods: {
    handleCreate() {
      // eslint-disable-next-line
      console.log('子应用创建了' + this.appName)
    },
    handleBeforeMount() {
      // console.log('主：子应用即将被渲染')
    },
    handleMount() {
      // console.log('主：子应用已经渲染完成')
    },
    handleUnmount() {
      // eslint-disable-next-line
      console.log('子应用卸载了' + this.appName)
    },
    handleError() {
      // console.log('主：子应用加载出错了')
    },
    handleDataChange() {}
  }
}
</script>
<style lang="stylus" scoped></style>
