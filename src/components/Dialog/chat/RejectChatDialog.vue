<template>
  <div class="reject-dialog">
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" :close-on-click-modal="true">
      <el-form
      ref="reject"
      label-width="100px"
      @submit.stop.prevent.native
      :rules="rejectRules"
      :model="reject">
        <el-form-item v-if="dialogTitle === '批量驳回'">
          <p>确认批量驳回选中资源？</p>
        </el-form-item>
        <el-form-item class="custom" label="备注" type="flex">
          <el-input v-model="reject.note" placeholder="备注" size="small" style="width: calc(100% - 100px)"></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button @click="dialogVisible = false" size="medium">取 消</el-button>
        <el-button type="primary" size="medium" @click="beforeCommit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  props: {
    detail: Object,
    businessName: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      dialogTitle: '',
      dialogVisible: false,
      rejectTypes: [],
      rejectForm: {
        note: ''
      },
      reject: {},
      rejectRules: {
      },
      op: {},
      row: {},
      role: ''
    }
  },

  created() {
  },

  mounted() {

  },

  methods: {
    resetFields() {
      this.$refs['reject'].resetFields()
    },
    openRejectMediaDialog(op, row = {}) {
      this.dialogVisible = true
      this.op = op
      this.dialogTitle = this.op.ch_name
      this.reject = {...this.rejectForm}
      this.row = row
      this.$nextTick(() => {
        this.$refs['reject'].resetFields()
      })
    },
    beforeCommit() {
      this.$refs['reject'].validate(valid => {
        if (!valid) {
          return false
        }
        this.dialogTitle === '批量驳回'
        ? this.$emit('confirmOps', this.op, this.reject, this.rejectTypes) : this.commit()
        this.dialogVisible = false
      })
    }
  }
}
</script>

<style lang="stylus">
.reject-dialog {
  .custom {
    .el-form-item__content {
      width: calc(100% - 50px)
      margin-left: 50px
    }
  }
}
</style>
