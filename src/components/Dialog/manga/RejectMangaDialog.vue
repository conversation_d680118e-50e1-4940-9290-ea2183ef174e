<template>
  <div class="reject-dialog">
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" :close-on-click-modal="true" :modal-append-to-body="false" :append-to-body="true">
      <el-form
      ref="reject"
      label-width="100px"
      @submit.stop.prevent.native
      :rules="rejectRules"
      :model="reject">
        <el-form-item v-if="dialogTitle === '批量驳回'">
          <p>确认批量驳回选中资源？</p>
        </el-form-item>
        <el-form-item label="理由" prop="reasonType">
          <el-select v-model="reject.reasonType" size="small" @change="changeReasonType" style="width: calc(100% - 50px)">
            <el-option v-for="item in rejectTypes" :key="item.id" :value="item.id" :label="item.reason"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="custom" prop="rejectReason" label="驳回理由" type="flex">
          <el-input autofocus v-model="reject.rejectReason" size="small" style="width: calc(100% - 100px)"></el-input>
        </el-form-item>
        <el-form-item class="custom" label="备注" type="flex">
          <el-input v-model="reject.note" placeholder="备注" size="small" style="width: calc(100% - 100px)"></el-input>
        </el-form-item>
        <el-form-item label="是否通知用户" prop="notify">
          <el-radio-group v-model="reject.notify">
            <el-radio :label="1">通知</el-radio>
            <el-radio :label="0">不通知</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button @click="dialogVisible = false" size="medium">取 消</el-button>
        <el-button type="primary" size="medium" @click="beforeCommit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  props: {
    detail: Object,
    roles: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },

  data() {
    const validateEvent = (rule, value, callback) => {
      if (!value) {
        callback(new Error('理由必填'))
      } else {
        callback()
      }
    }
    return {
      dialogTitle: '',
      dialogVisible: false,
      rejectTypes: [{
        id: 164,
        reason: '发布时政内容'
      }, {
        id: 163,
        reason: '发布不适宜内容'
      }, {
        id: 162,
        reason: '发布低俗信息'
      }, {
        id: 161,
        reason: '发布色情信息'
      }, {
        id: 160,
        reason: '发布人身攻击信息'
      }, {
        id: 159,
        reason: '发布垃圾广告信息'
      }, {
        id: 158,
        reason: '发布违禁相关信息'
      }, {
        id: 157,
        reason: '发布赌博诈骗信息'
      }, {
        id: 229,
        reason: '【 】内容因涉及人身攻击信息，建议删除或修改'
      }, {
        id: 224,
        reason: '标题中存在不适宜内容，请您修改标题'
      }, {
        id: 214,
        reason: '正文的第（）张图涉及不良低俗内容，请您修改'
      }, {
        id: 215,
        reason: '【 】内容因涉及垃圾广告规信息，建议删除或修改'
      }, {
        id: 216,
        reason: '【 【】内容因涉及不适宜信息，建议删除或修改'
      }, {
        id: 217,
        reason: '【 】内容因涉及不良低俗信息，建议删除或修改'
      }, {
        id: 218,
        reason: '【 】内容因涉及违禁违规信息，建议删除或修改'
      }, {
        id: 219,
        reason: 'TAG因涉及违禁内容，请您修改'
      }, {
        id: 220,
        reason: 'TAG因涉及不良低俗内容，请您修改'
      }, {
        id: 221,
        reason: 'TAG因涉及淫秽色情内容，请您修改'
      }, {
        id: 222,
        reason: 'TAG因涉及人身攻击内容，请您修改'
      }, {
        id: 223,
        reason: 'TAG因涉及不适宜内容，请您修改'
      }, {
        id: 225,
        reason: '正文的第（）张图涉及人身攻击内容，请您修改'
      }, {
        id: 226,
        reason: '正文的第（）张图涉及不适宜内容，请您修改'
      }, {
        id: 227,
        reason: '正文的第（）张图涉及违禁内容，请您修改'
      }, {
        id: 228,
        reason: '正文的第（）张图涉及淫秽色情内容，请您修改'
      }, {
        id: 175,
        reason: '其他问题'
      }, {
        id: 174,
        reason: '发布赌博诈骗内容'
      }, {
        id: 168,
        reason: '发布血腥暴力内容'
      }, {
        id: 167,
        reason: '发布青少年不良咨讯'
      }, {
        id: 166,
        reason: '发布广告行销内容'
      }, {
        id: 165,
        reason: '发布作品不相关内容'
      }, {
        id: 169,
        reason: '发布引战信息'
      }, {
        id: 170,
        reason: '发布抄袭内容'
      }, {
        id: 171,
        reason: '发布恶意剧透内容'
      }, {
        id: 173,
        reason: '发布有害、危害行为内容'
      }, {
        id: 172,
        reason: '发布侵权内容'
      }, {
        id: 203,
        reason: '发布作品不相关内容'
      }, {
        id: 204,
        reason: '发布广告行销内容'
      }, {
        id: 205,
        reason: '发布青少年不良咨讯'
      }, {
        id: 206,
        reason: '发布血腥暴力内容'
      }, {
        id: 207,
        reason: '发布引战信息'
      }, {
        id: 208,
        reason: '发布抄袭内容'
      }, {
        id: 209,
        reason: '发布恶意剧透内容'
      }, {
        id: 210,
        reason: '发布侵权内容'
      }, {
        id: 211,
        reason: '发布有害、危害行为内容'
      }, {
        id: 212,
        reason: '发布赌博诈骗内容'
      }, {
        id: 213,
        reason: '其他问题'
      }],
      rejectForm: {
        reasonType: '',
        rejectReason: '',
        note: '',
        notify: 1
      },
      reject: {},
      rejectRules: {
        reasonType: [
          {
            required: true,
            message: '理由必选',
            trigger: 'blur'
          }
        ],
        rejectReason: [
          {
            required: true,
            validator: validateEvent,
            trigger: 'blur'
          }
        ]
      },
      op: {},
      row: {}
    }
  },

  methods: {
    resetFields() {
      this.$refs['reject'].resetFields()
    },
    openRejectDialog(op, row = {}) {
      this.dialogVisible = true
      this.op = op
      this.dialogTitle = this.op.ch_name
      this.reject = {...this.rejectForm}
      this.reject.reasonType = this.rejectTypes[0].id
      this.reject.rejectReason = this.rejectTypes[0].reason
      this.row = row
      this.$nextTick(() => {
        this.$refs['reject'].resetFields()
      })
    },
    beforeCommit() {
      this.$refs['reject'].validate(valid => {
        if (!valid) {
          return false
        }
        // 1.textResourceList.vue的confirmOps
        this.$emit('confirmOps', this.op, this.reject, this.rejectTypes)
        this.dialogVisible = false
      })
    },
    changeReasonType() {
      this.reject.rejectReason = (this.rejectTypes.find(t => t.id === this.reject.reasonType) || {}).reason
    }
  }
}
</script>

<style lang="stylus">
.reject-dialog {
  .custom {
    .el-form-item__content {
      width: calc(100% - 50px)
      margin-left: 50px
    }
  }
}
</style>
