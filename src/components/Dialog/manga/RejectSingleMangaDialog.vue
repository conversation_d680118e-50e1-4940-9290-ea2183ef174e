<template>
  <div class="reject-dialog">
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" :close-on-click-modal="true" :modal-append-to-body="false" :append-to-body="true">
      <el-form
      ref="reject"
      label-width="100px"
      @submit.stop.prevent.native
      :rules="rejectRules"
      :model="reject">
        <el-form-item label="用户角色" prop="role" v-if="isComic">
          <el-select v-model="role" size="small" @change="getReason">
            <el-option v-for="(val, key) in roles" :key="key" :value="key" :label="val"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="理由" prop="reasonType">
          <el-select v-model="reject.reasonType" size="small" @change="changeReasonType" style="width: calc(100% - 50px)">
            <el-option v-for="item in rejectTypes" :key="item.id" :value="item.id" :label="item.reason"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="custom" prop="rejectReason" label="驳回理由" type="flex">
          <el-input autofocus v-model="reject.rejectReason" size="small" style="width: calc(100% - 100px)"></el-input>
        </el-form-item>
        <el-form-item class="custom" label="备注" type="flex">
          <el-input v-model="reject.note" placeholder="备注" size="small" style="width: calc(100% - 100px)"></el-input>
        </el-form-item>
        <el-form-item label="是否通知用户" prop="notify" v-if="!isComic && !isNetease && !isConsumption">
          <el-radio-group v-model="reject.notify">
            <el-radio :label="1">通知</el-radio>
            <el-radio :label="0">不通知</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button @click="dialogVisible = false" size="medium">取 消</el-button>
        <el-button type="primary" size="medium" @click="beforeCommit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { resourceApi } from '@/api/index'
import $notify from '@/lib/notify'

export default {
  props: {
    detail: Object,
    roles: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },

  data() {
    const validateEvent = (rule, value, callback) => {
      if (!value) {
        callback(new Error('理由必填'))
      } else {
        callback()
      }
    }
    return {
      dialogTitle: '',
      dialogVisible: false,
      rejectTypes: [],
      rejectForm: {
        reasonType: '',
        rejectReason: '',
        note: '',
        notify: 1
      },
      reject: {},
      rejectRules: {
        reasonType: [
          {
            required: true,
            message: '理由必选',
            trigger: 'blur'
          }
        ],
        rejectReason: [
          {
            required: true,
            validator: validateEvent,
            trigger: 'blur'
          }
        ]
      },
      op: {},
      row: {},
      isDynamic: false,
      isDanmu: false,
      isComic: false,
      isNetease: false,
      isUgc: false,
      isIV: false,
      isConsumption: false,
      role: ''
    }
  },

  methods: {
    resetFields() {
      this.$refs['reject'].resetFields()
    },
    openRejectDialog(op, row = {}) {
      this.dialogVisible = true
      this.op = op
      this.dialogTitle = this.op.ch_name
      this.reject = {...this.rejectForm}
      this.role = Object.keys(this.roles)[0]
      this.getReason(this.role)
      this.row = row
      this.$nextTick(() => {
        this.$refs['reject'].resetFields()
      })
    },
    beforeCommit() {
      this.$refs['reject'].validate(valid => {
        if (!valid) {
          return false
        }
        this.commit()
      })
    },
    commit() {
      let [extraData, canCommit] = [{}, false]
      const { note, rejectReason, reasonType, notify } = this.reject
      let row = this.row || {}
      extraData.notify = notify
      let result = {
        note,
        reject_reason: rejectReason,
        reason_id: +reasonType || 0
      }
      let ids
      canCommit = row.business_id && row.oid && row.rid && row.flow_id
      ids = {
        business_id: +row.business_id,
        flow_id: +row.flow_id,
        oid: row.oid.val[0].text,
        rid: +row.rid,
        task_id: (row.task_id && row.task_id.val && row.task_id.val.length && +row.task_id.val[0].text) || 0
      }
      extraData.rsc_count = -1
      if (canCommit) {
        const params = {
          ...ids,
          resource_result: result,
          forbid_params: {},
          binds: this.op.bind_id_list,
          extra_data: !Object.keys(extraData).length ? undefined : extraData
        }
        resourceApi.submitResource(params).then(res => {
          if (res.code === 0 || res.code === 92015 || res.code === 92029) {
            // 92015 任务已被操作或者删除 92029 审核任务超时 refresh
            res.code === 0
            ? (!res.tips && $notify.success(`成功${this.dialogTitle}`)) : res.code === 92015
            ? $notify.warning(res.message) : $notify.error(res.message)
            this.$emit('getResourceList')
          } else {
            $notify.error(res.message)
          }
          this.dialogVisible = false
        }).catch(_ => {})
      }
    },
    getReason(val = undefined) {
      this.rejectTypes = []
      // NOTE:更改，改成拿跟操作项绑定的理由
      const { extra } = this.op
      let apiUrl = ''
      try {
        apiUrl = (JSON.parse(extra) || {}).url
      } catch (error) {
        console.error('extra字符串转换失败')
      }
      apiUrl && this.$ajax.get(apiUrl).then((res) => {
        const { code, data: result } = res
        if (code !== 0) {
          $notify.error('审核理由请求错误')
        } else {
          const reasons = result.data || []
          reasons.forEach(d => {
            if (this.isConsumption) {
              if (d.cate_id === 164) {
                this.rejectTypes.push({
                    reason: d.description,
                    id: d.id
                })
              }
            } else {
              this.rejectTypes.push({
                  reason: d.description,
                  id: d.id
              })
            }
          })
          this.rejectTypes.push({
            reason: '自定义',
            id: '自定义'
          })
          if (this.rejectTypes.length) {
            this.reject.reasonType = this.rejectTypes[0].id
            this.reject.rejectReason = this.rejectTypes[0].reason
          }
        }
      })
    },
    changeReasonType() {
      this.reject.rejectReason = (this.rejectTypes.find(t => t.id === this.reject.reasonType) || {}).reason
    }
  }
}
</script>

<style lang="stylus">
.reject-dialog {
  .custom {
    .el-form-item__content {
      width: calc(100% - 50px)
      margin-left: 50px
    }
  }
}
</style>
