<template>
  <div class="reject-dialog">
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" :close-on-click-modal="true">
      <el-form
      ref="reject"
      label-width="100px"
      @submit.stop.prevent.native
      :rules="isCaption ? null : rejectRules"
      :model="reject">
        <el-form-item v-if="dialogTitle === '批量驳回'">
          <p>确认批量驳回选中资源？</p>
        </el-form-item>
        <el-form-item label="理由" prop="reasonType" v-if="!isCaption">
          <el-select v-model="reject.reasonType" size="small" @change="changeReasonType" style="width: calc(100% - 50px)">
            <el-option v-for="item in rejectTypes" :key="item.id" :value="item.id" :label="item.reason"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="custom" prop="rejectReason" label="驳回理由" type="flex" v-if="!isCaption">
          <el-input autofocus v-model="reject.rejectReason" :disabled="customReasonDisabled" size="small" style="width: calc(100% - 100px)"></el-input>
        </el-form-item>
        <el-form-item class="custom" prop="note" label="备注" type="flex" v-if="!isBgm && !isMaterial && !isTV && !isBwikiImage" :rules="isCaption ? noteRequiredRules : null">
          <el-input v-model="reject.note" placeholder="备注" size="small" style="width: calc(100% - 100px)"></el-input>
        </el-form-item>
        <el-form-item label="是否通知用户" prop="notify" v-if="!isComic && !isNetease && !isConsumption && !isTopic && !isBgm && !isIntl && !isMaterial && !isTV && !isCaption && !isStampCollection && !isBwikiImage">
          <el-radio-group v-model="reject.notify">
            <el-radio :label="1">通知</el-radio>
            <el-radio :label="0">不通知</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button @click="dialogVisible = false" size="medium">取 消</el-button>
        <el-button type="primary" size="medium" @click="beforeCommit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import { reasonApi, resourceApi } from '@/api/index'
import notification from '@/lib/notify'
import { setCurrentResource } from '../../pages/contentAudit/common.js'
import { UAT_TOPIC_REJECT_REASON, PROD_TOPIC_REJECT_REASON, TOPIC_SECOND_REJECT_REASON, UAT_TOPIC_FLOW_ID, PROD_TOPIC_FLOW_ID, CUSTOM_REASON_BIZ_AUTH_MAP } from '@/utils/constant'
import { TV_AUDIT_REJECT_REASON, IM_AUDIT_REJECT_REASON, BWIKI_IMAGE_REJECT_REASON } from '@/pages/workbench/dialogConfig/constants'

const COMIC_REJECT_REASONS = [{
  id: 17,
  reason: '内容涉及低俗'
}, {
  id: 18,
  reason: '内容涉及低俗（胸部、臀部特写）'
}, {
  id: 19,
  reason: '内容涉及低俗（构图视角存在低俗导向、人物行为姿势不雅）'
}, {
  id: 20,
  reason: '内容涉及淫秽色情'
}, {
  id: 21,
  reason: '内容涉及赌博、诈骗、欺诈'
}, {
  id: 22,
  reason: '内容涉及暴力'
}, {
  id: 23,
  reason: '内容涉及血腥'
}, {
  id: 24,
  reason: '内容涉及同性亲密行为'
}, {
  id: 25,
  reason: '内容涉及辱骂性言论'
}, {
  id: 26,
  reason: '内容涉及违禁内容'
}, {
  id: 27,
  reason: '内容涉及违禁内容（侮辱与恶搞国歌、红歌、军歌、国旗、国徽）'
}, {
  id: 28,
  reason: '内容涉及违禁内容（泄露个人隐私）'
}, {
  id: 29,
  reason: '内容涉及违禁内容（存在以暴制暴内容、存在校园暴力内容）'
}, {
  id: 30,
  reason: '内容涉及违禁内容（地图有误）'
}, {
  id: 31,
  reason: '内容涉及违禁品制作、贩卖等相关内容'
}, {
  id: 32,
  reason: '内容导向、价值观存在偏差'
}, {
  id: 33,
  reason: '内容涉及不适宜词'
}, {
  id: 87,
  reason: '付费状态错误'
}, {
  id: 86,
  reason: '图片内容有缺失'
}, {
  id: 85,
  reason: '图片内容与上一话不符'
}, {
  id: 84,
  reason: '图片内容与漫画无关'
}, {
  id: 83,
  reason: '标题有错字'
}, {
  id: 88,
  reason: '其他（自定义）'
}]

const INTL_ARCHIVE_REASONS = [
  {
    id: 10001,
    reason: 'ตามหลักเกณฑ์ของแพลตฟอร์มชุมชน  วิดีโอที่คุณอัปโหลดมี ข้อผิดพลาด ไม่เหมาะสมกับการเผยแพร่บนแพลตฟอร์ม ดังนั้นวิดีโอดังกล่าวถูกปฏิเสธ'
  },
  {
    id: 10002,
    reason: 'ตามหลักเกณฑ์ของแพลตฟอร์มชุมชน  วิดีโอที่คุณอัปโหลดมี ข้อมูลการก่อการร้ายและเนื้อหาที่รุนแรง ไม่เหมาะสมกับการเผยแพร่บนแพลตฟอร์ม ดังนั้นวิดีโอดังกล่าวถูกปฏิเสธ'
  },
  {
    id: 10003,
    reason: 'ตามหลักเกณฑ์ของแพลตฟอร์มชุมชน  วิดีโอที่คุณอัปโหลดมี ข้อมูลลามกอนาจาร ไม่เหมาะสมกับการเผยแพร่บนแพลตฟอร์ม ดังนั้นวิดีโอดังกล่าวถูกปฏิเสธ'
  },
  {
    id: 10004,
    reason: 'ตามหลักเกณฑ์ของแพลตฟอร์มชุมชน วิดีโอที่คุณอัปโหลดมีข้อมูล รุนแรงและนองเลือด ไม่เหมาะสมกับการเผยแพร่บนแพลตฟอร์ม ดังนั้นวิดีโอดังกล่าวถูกปฏิเสธ'
  },
  {
    id: 10005,
    reason: 'ตามหลักเกณฑ์ของแพลตฟอร์มชุมชน วิดีโอที่คุณอัปโหลดมีข้อมูล เป็นอันตรายต่อเด็ก จะไม่เหมาะสมกับการเผยแพร่บนแพลตฟอร์ม ดังนั้นวิดีโอดังกล่าวถูกปฏิเสธ'
  },
  {
    id: 10006,
    reason: 'ตามหลักเกณฑ์ของแพลตฟอร์มชุมชน วิดีโอที่คุณอัปโหลดมีข้อมูล เกี่ยวกับการซื้อขายหรือการขายสินค้าควบคุมจะไม่เหมาะสมกับการเผยแพร่บนแพลตฟอร์ม ดังนั้นวิดีโอดังกล่าวถูกปฏิเสธ'
  },
  {
    id: 10007,
    reason: 'ตามหลักเกณฑ์ของแพลตฟอร์มชุมชน วิดีโอที่คุณอัปโหลดมีข้อมูลที่มี เนื้อหาความเกลียดชังหรือคำพูดรุนแรง ไม่เหมาะสมกับการเผยแพร่บนแพลตฟอร์ม ดังนั้นวิดีโอดังกล่าวถูกปฏิเสธ'
  },
  {
    id: 10008,
    reason: 'ตามหลักเกณฑ์ของแพลตฟอร์มชุมชน  วิดีโอที่คุณอัปโหลดถูกลบเนื่องจากโดนร้องเรียนว่ามี เนื้อหาที่ละเมิดลิขสิทธิ์ผู้อื่น  หากคุณมีคำถามใด ๆ คุณสามารถ คอมเพลน ได้ภายใน 2 วัน'
  },
  {
    id: 10009,
    reason: 'วิดีโอที่คุณอัปโหลดมีเนื้อหาเกี่ยวกับโฆษณา ซึ่งไม่เหมาะสมต่อการเผยแพร่บนแพลตฟอร์ม  ดังนั้นวิ'
  }
]

const MATERIAL_REJECT_REASONS = [{
  id: 1,
  reason: '违禁'
}, {
  id: 2,
  reason: '色情'
}, {
  id: 3,
  reason: '低俗'
}, {
  id: 4,
  reason: '人身攻击'
}, {
  id: 5,
  reason: '血腥暴力'
}, {
  id: 6,
  reason: '赌博诈骗'
}, {
  id: 7,
  reason: '不适宜'
}, {
  id: 8,
  reason: '违反运营规则'
}]

export default {
  props: {
    detail: Object,
    businessName: {
      type: String,
      default: ''
    },
    roles: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },

  data() {
    const validateEvent = (rule, value, callback) => {
      if (!value) {
        callback(new Error('理由必填'))
      } else {
        callback()
      }
    }
    return {
      dialogTitle: '',
      dialogVisible: false,
      rejectTypes: [],
      rejectForm: {
        reasonType: '',
        rejectReason: '',
        note: '',
        notify: 1
      },
      reject: {
      },
      rejectRules: {
        reasonType: [
          {
            required: true,
            message: '理由必选',
            trigger: 'blur'
          }
        ],
        rejectReason: [
          {
            required: true,
            validator: validateEvent,
            trigger: 'blur'
          }
        ]
      },
      noteRequiredRules: [{
        required: true,
        message: '备注必填',
        trigger: 'blur'
      }],
      op: {},
      row: {},
      isDynamic: false,
      isDanmu: false,
      isComic: false,
      isNetease: false,
      isUgc: false,
      isIV: false,
      isConsumption: false,
      isManga: false,
      isTopic: false,
      isBgm: false,
      isIntl: false,
      isMaterial: false,
      isTV: false,
      isIM: false,
      isCaption: false,
      isStampCollection: false,
      isBwikiImage: false,
      role: ''
    }
  },

  computed: {
    ...mapState({
      perms: state => state.user.perms
    }),
    flowId() {
      return +this.$route.query.flow_id || 0
    },
    ...mapGetters({
      getEnv: 'env/getEnv'
    }),
    businessId() {
      return +this.$route.query.business_id
    },
    customReasonDisabled() {
      const auth = CUSTOM_REASON_BIZ_AUTH_MAP[this.businessId]

      if (auth) {
        return !this.perms[auth]
      }

      return this.isIntl
    }
  },

  watch: {
    'businessName': {
      handler() {
        setCurrentResource(this)
      },
      immediate: true
    }
  },
  methods: {
    resetFields() {
      this.$refs['reject'].resetFields()
    },
    openRejectDialog(op, row = {}) {
      this.dialogVisible = true
      this.op = op
      this.dialogTitle = this.op.ch_name
      this.reject = {...this.rejectForm}
      // 写死的理由
      if (this.isComic || this.isIntl || this.isMaterial || this.isTV || this.isIM || this.isBwikiImage) {
        this.initStaticReason()
      } else {
        this.role = Object.keys(this.roles)[0]
        // NOTE: 话题走本地配置
        if (this.isTopic) this.getTopicReason()
        else this.getReason(this.role)
      }
      this.row = row
      // 为了清除校验结果
      this.$nextTick(() => {
        this.$refs['reject'].clearValidate()
      })
    },
    initStaticReason() {
      if (this.isComic) {
        this.rejectTypes = COMIC_REJECT_REASONS
      } else if (this.isIntl) {
        this.rejectTypes = INTL_ARCHIVE_REASONS
      } else if (this.isMaterial) {
        this.rejectTypes = MATERIAL_REJECT_REASONS
      } else if (this.isTV) {
        this.rejectTypes = TV_AUDIT_REJECT_REASON
      } else if (this.isIM) {
        this.rejectTypes = IM_AUDIT_REJECT_REASON
      } else if (this.isBwikiImage) {
        this.rejectTypes = BWIKI_IMAGE_REJECT_REASON
      }
      this.reject.reasonType = this.rejectTypes[0].id
      this.reject.rejectReason = this.rejectTypes[0].reason
    },
    beforeCommit() {
      this.$refs['reject'].validate(valid => {
        if (!valid) {
          return false
        }

        this.dialogTitle === '批量驳回'
        ? this.$emit('confirmOps', this.op, this.reject, this.rejectTypes) : this.commit()
      })
    },
    commit() {
      let [extraData, canCommit] = [{}, false]
      let detail = this.detail
      let resource = detail.resource || {}
      const { note, rejectReason, reasonType, notify } = this.reject
      extraData.notify = notify
      let result = {
        note,
        reject_reason: rejectReason,
        reason_id: +reasonType || 0,
        attribute_list: {
          no_comment: resource.attribute_list && resource.attribute_list.no_comment,
          no_forward: resource.attribute_list && resource.attribute_list.no_forward
        }
      }
      let ids = {}
      if (this.isDanmu || this.isManga || this.isMaterial || this.isIM || this.isStampCollection || this.isBwikiImage) {
        let row = this.row
        canCommit = row && row.business_id && row.oid && row.rid && row.flow_id
        ids = {
          business_id: +row.business_id,
          flow_id: +row.flow_id,
          oid: row.oid.val[0].text,
          rid: +row.rid,
          task_id: (row.task_id && row.task_id.val && row.task_id.val.length && +row.task_id.val[0].text) || 0
        }
        extraData.rsc_count = -1
      } else {
        canCommit = detail.resource && detail.resource.business_id && resource.oid && detail.flow && detail.flow.rid && detail.flow.flow_id
        ids = {
          business_id: detail.resource.business_id,
          flow_id: detail.flow.flow_id,
          oid: resource.oid,
          rid: detail.flow.rid,
          task_id: (detail.task && detail.task.id) || 0
        }
      }
      if (canCommit) {
        resourceApi.submitResource({
          ...ids,
          resource_result: result,
          forbid_params: {},
          binds: this.op.bind_id_list,
          extra_data: !Object.keys(extraData).length ? undefined : extraData
        }).then(res => {
          if (res.code === 0 || res.code === 92015 || res.code === 92029) {
            // 92015 任务已被操作或者删除 92029 审核任务超时 refresh
            res.code === 0
            ? (!res.tips && notification.success(`成功${this.dialogTitle}`)) : res.code === 92015
            ? notification.warning(res.message) : notification.error(res.message)
            this.$emit('getResourceList')
          } else {
            notification.error(res.message)
          }
          this.dialogVisible = false
        }).catch(_ => {})
      }
    },
    getBid() {
      let bid = null
      const env = this.getEnv()
      if (this.isComic) {
        bid = 18
      } else if (this.isUgc) {
        bid = 48
      } else if (this.isIV) {
        bid = 69
      } else if (this.isConsumption) {
        // uat 73
        bid = 73
      } else if (this.isManga) {
        bid = 92
      } else if (this.isBgm) {
        bid = 110
      } else if (this.businessId && this.businessId === 72) {
        bid = env === 'uat' ? 62 : 48 // 合集打卡的驳回理由与合集业务保持一致
      } else {
        // dynamic
        bid = 16
      }
      return bid
    },
    getReason(val = undefined) {
      this.rejectTypes = []
      reasonApi.getReasonList({
        bid: this.getBid(),
        role_id: !('' + val) ? +val : val,
        state: 1,
        sort: 'asc',
        ps: 9999
      }).then(res => {
        if (!res.data.data) {
          return
        }
        (res.data.data || []).forEach(d => {
          if (this.isConsumption) {
            if (d.cate_id === 164) {
              this.rejectTypes.push({
                  reason: d.description,
                  id: d.id
              })
            }
          } else {
            this.rejectTypes.push({
                reason: d.description,
                id: d.id
            })
          }
        })
        this.rejectTypes.push({
          reason: '自定义',
          id: '自定义'
        })
        if (this.rejectTypes.length) {
          this.reject.reasonType = this.rejectTypes[0].id
          this.reject.rejectReason = this.rejectTypes[0].reason
        }
      }).catch(_ => {})
    },
    resetSelectedReason() {
      this.reject.reasonType = ''
      this.reject.rejectReason = ''
    },
    getTopicReason() {
      this.rejectTypes = []
      const env = this.getEnv()
      const FLOW_ID_COMPARE = env === 'uat' ? UAT_TOPIC_FLOW_ID : PROD_TOPIC_FLOW_ID
      const isSecondAudit = this.flowId === FLOW_ID_COMPARE
      this.rejectTypes = isSecondAudit ? TOPIC_SECOND_REJECT_REASON : env === 'uat' ? UAT_TOPIC_REJECT_REASON : PROD_TOPIC_REJECT_REASON
      if (this.rejectTypes.length) {
        this.reject.reasonType = this.rejectTypes[0].id
        this.reject.rejectReason = this.rejectTypes[0].reason
      }
    },
    changeReasonType() {
      this.reject.rejectReason = (this.rejectTypes.find(t => t.id === this.reject.reasonType) || {}).reason
    }
  }
}
</script>

<style lang="stylus">
.reject-dialog {
  .custom {
    .el-form-item__content {
      width: calc(100% - 50px)
      margin-left: 50px
    }
  }
}
</style>
