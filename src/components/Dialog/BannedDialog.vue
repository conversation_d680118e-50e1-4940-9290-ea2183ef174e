<template>
  <div class="banned-dialog">
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" :close-on-click-modal="true">
      <el-form
      ref="banned"
      @submit.stop.prevent.native
      inline
      class="banned-form"
      :rules="bannedRules"
      :model="banned">
        <span v-if="isComment && dialogTitle === '批量驳回'" class="banned-info" style="font-weight: bold; font-size: 16px">确认批量驳回选中评论？</span>
        <span class="banned-info" v-if="dialogTitle.indexOf('批量') < 0">
          当前帐号封禁状态：<em>{{ bannedStatus }}</em>；
          封禁次数：<em>{{ bannedCount }}</em>
        </span>
        <el-form-item label="是否通知用户" prop="notify">
          <el-radio-group v-model="banned.notify">
            <el-radio :label="1">通知</el-radio>
            <el-radio :label="0">不通知</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" class="banned-item" required>
          <el-input v-model="banned.note" size="small"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button @click="dialogVisible = false" size="medium">取 消</el-button>
        <el-button type="primary" size="medium" @click="beforeCommit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import store from '@/store'
import { mapState } from 'vuex'
import notify from '@/lib/notify'
import { DEFAULT_BANNED_REASONS } from '../../pages/contentAudit/constants'
import { setCurrentResource } from '../../pages/contentAudit/common'
import { resourceApi, blockApi } from '@/api/index'
import { IM_AUDIT_REJECT_REASON } from '@/pages/workbench/dialogConfig/constants'
export default {
  props: {
    detail: Object,
    businessName: {
      type: String,
      default: ''
    }
  },

  data() {
    const validateEvent = (rule, value, callback) => {
      if (this.banned.bannedType === '自定义' && (!+value || +value <= 0)) {
        callback(new Error('封禁天数不合法'))
      } else {
        callback()
      }
    }
    const NOT_COMMENT_REASON_ID = []
    const NOT_DYNAMIC_REASON_ID = [0, 29, 32]
    const NOT_DANMU_REASON_ID = [0, 1, 2, 11, 12, 14, 15, 16, 21, 22, 23, 25, 26, 27, 28, 29, 30, 31, 32]
    const NOT_CHAT_REASON_ID = [0]
    return {
      dialogTitle: '',
      dialogVisible: false,
      bannedTypes: ['7', '15', '30', '永久', '自定义'],
      bannedForm: {
        reason: '',
        bannedType: '7',
        bannedDays: '',
        note: '',
        notify: 1
      },
      banned: {},
      bannedRules: {
        reason: [
          {
            required: true,
            message: '封禁理由必选',
            trigger: 'blur'
          }
        ],
        bannedDays: [
          {
            required: true,
            validator: validateEvent,
            trigger: 'blur'
          }
        ]
      },
      op: {},
      status: '',
      bannedStates: [{
        text: '未封禁',
        value: 0
      }, {
        text: '永久封禁',
        value: 1
      }, {
        text: '限时封禁',
        value: 2
      }],
      bannedCount: '',
      row: {},
      isDynamic: false,
      isDanmu: false,
      isComic: false,
      isComment: false,
      isChatroom: false,
      isIM: false,
      NOT_COMMENT_REASON_ID,
      NOT_DYNAMIC_REASON_ID,
      NOT_DANMU_REASON_ID,
      NOT_CHAT_REASON_ID
    }
  },

  computed: {
    ...mapState({
      checkedPics: state => state.pictures.checkedPics
    }),
    bannedStatus() {
      return (this.bannedStates.filter(i => { return i.value === this.status })[0] || {}).text
    },
    bannedReasons() {
      if (this.isIM) {
        return IM_AUDIT_REJECT_REASON.map(e => {
          return {
            label: e.reason,
            value: e.id
          }
        })
      }
      const ids = (() => {
        switch (true) {
          case this.isDanmu:
            return this.NOT_DANMU_REASON_ID
          case this.isDynamic:
            return this.NOT_DYNAMIC_REASON_ID
          case this.isComment:
            return this.NOT_COMMENT_REASON_ID
          case this.isChatroom:
            return this.NOT_CHAT_REASON_ID
          default:
            return []
        }
      })()
      const result = []
      DEFAULT_BANNED_REASONS.forEach(item => {
        const index = ids.findIndex(id => {
          return id === item.value
        })
        if (index === -1) result.push(item)
      })
      return result
    },
    fromTableDetail() {
      return this.isDanmu || this.isComment || this.isChatroom || this.isIM
    }
  },

  created() {

  },

  mounted() {
  },

  watch: {
    businessName: {
      handler() {
        setCurrentResource(this)
      },
      immediate: true
    }
  },

  methods: {
    openBannedDialog(op, row = {}) {
      this.dialogVisible = true
      this.op = op
      this.dialogTitle = this.op.ch_name
      this.banned = { ...this.bannedForm }
      this.row = row
      this.dialogTitle.indexOf('批量') === -1 && this.getForbidInfo()
      this.$nextTick(() => {
        this.$refs.banned.resetFields()
        if (this.isComment) {
          this.banned.reason = 0
          this.banned.notify = 0
        }
      })
    },
    beforeCommit() {
      this.$refs.banned.validate(valid => {
        if (!valid) {
          return false
        }
        this.dialogTitle.indexOf('批量') > -1
        ? this.$emit('confirmOps', this.op, this.banned, this.bannedReasons)
: this.commit()
      })
    },
    setExtraData(extraData) {
      if (this.isComic) {
        extraData.reason_extend = Array.isArray(this.checkedPics) ? this.checkedPics.join(',') : ''
        extraData.rsc_count = this.checkedPics.length
      } else if (this.isComment) {
        extraData.rsc_addit_int = 0
      } else if (this.isDanmu) {
        extraData.rsc_count = -1
      }
    },
    commit() {
      const detail = this.detail
      const resource = detail.resource || {}
      const banned = this.banned
      let [extraData, canCommit] = [{}, false, {}]
      const result = {
        note: banned.note,
        reject_reason: '',
        reason_id: +banned.reason || 0,
        attribute_list: {
          no_comment: resource.attribute_list && resource.attribute_list.no_comment,
          no_forward: resource.attribute_list && resource.attribute_list.no_forward
        }
      }
      let ids = {}
      if (this.fromTableDetail) {
        const row = this.row
        canCommit = row && row.business_id && row.oid && row.rid && row.flow_id
        ids = {
          business_id: +row.business_id,
          flow_id: +row.flow_id,
          oid: row.oid.val[0].text,
          rid: +row.rid,
          task_id: (row.task_id && row.task_id.val && row.task_id.val.length && +row.task_id.val[0].text) || 0
        }
      } else {
        canCommit = detail.resource && detail.resource.business_id && resource.oid && detail.flow && detail.flow.rid && detail.flow.flow_id
        ids = {
          business_id: detail.resource.business_id,
          flow_id: detail.flow.flow_id,
          oid: resource.oid,
          rid: detail.flow.rid,
          task_id: (detail.task && detail.task.id) || 0
        }
      }
      this.setExtraData(extraData)
      if (canCommit) {
        resourceApi.submitResource({
          ...ids,
          resource_result: result,
          binds: this.op.bind_id_list,
          extra_data: !Object.keys(extraData).length ? undefined : extraData
        }).then(res => {
          if (res.code === 0 || res.code === 92015 || res.code === 92029) {
            // 92015 任务已被操作或者删除 92029 审核任务超时 refresh
            res.code === 0
            ? (!res.tips && notify.success(`成功${this.dialogTitle}`))
: res.code === 92015
            ? notify.warning(res.message)
: notify.error(res.message)
            this.$emit('getResourceList')
          } else {
            notify.error(res.message)
          }
          this.dialogVisible = false
        }).catch(_ => {})
      }
    },
    getForbidInfo() {
      blockApi.getForbidInfo({
         mids: this.fromTableDetail ? this.row.mid.val[0].text : (this.detail.resource && this.detail.resource.mid)
      }).then(res => {
        if (res.data && res.data.length) {
          this.status = res.data[0].block_status
          this.bannedCount = res.data[0].block_count
        }
      }).catch(_ => {})
    },
    clearItemValidate(item) {
      if (item === 'time' && this.banned.bannedType !== '自定义') {
        this.$refs.bannedTimeRange.clearValidate()
      }
      if (item === 'reason' && this.banned.reason) {
        this.$refs.bannedReason.clearValidate()
      }
    }
  }
}
</script>

<style lang="stylus">
.banned-dialog {
  .banned-form {
    .banned-info {
      display: block
      padding: 0px 0px 22px
      em {
        color: var(--text-color-light-1)
      }
    }
    .el-form-item {
      margin-bottom: 12px
    }
    .banned-item {
      width: 100%
      .el-form-item__content {
        width: calc(100% - 80px)
      }
      .day {
        margin-left: 8px
        margin-right: 8px
        width: calc(100% - 200px)
      }
    }
  }
}
</style>
