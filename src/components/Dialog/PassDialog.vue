<template>
  <div class="pass-dialog">
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" :close-on-click-modal="true">
      <el-form
      ref="pass"
      @submit.stop.prevent.native
      :model="pass">
        <p class="pass" style="margin-bottom: 18px">此操作将通过当前审核内容, 是否继续?</p>
        <el-form-item class="custom" prop="note" label="备注" type="flex">
          <el-input v-model="pass.note" autofocus placeholder="备注" size="small" style="width: calc(100% - 100px)"></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button @click="dialogVisible = false" size="medium">取 消</el-button>
        <el-button type="primary" size="medium" @click="commit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { resourceApi } from '@/api/index'
import notify from '@/lib/notify'
import { setCurrentResource } from '../../pages/contentAudit/common.js'

export default {
  props: {
    detail: Object,
    businessName: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      dialogTitle: '',
      dialogVisible: false,
      currentBindId: '',
      pass: {},
      passForm: {
        note: ''
      },
      row: {},
      isDynamic: false,
      isDanmu: false,
      isComic: false
    }
  },

  created() {
  },

  mounted() {
  },

  watch: {
    'businessName': {
      handler() {
        setCurrentResource(this)
      },
      immediate: true
    }
  },

  methods: {
    openPassDialog(op, row = {}) {
      this.dialogVisible = true
      this.currentBindId = op.bind_id_list
      this.dialogTitle = op.ch_name
      this.pass = {...this.passForm}
      this.row = row
      this.$nextTick(() => {
        this.$refs['pass'].resetFields()
      })
    },
    commit() {
      let detail = this.detail
      let resource = detail.resource || {}
      let canCommit = false
      let extraData = { notify: null }
      let result = {
        note: this.pass.note,
        reject_reason: '',
        reason_id: 0,
        attribute_list: {
          no_comment: resource.attribute_list && resource.attribute_list.no_comment,
          no_forward: resource.attribute_list && resource.attribute_list.no_forward
        }
      }
      let ids = {}
      if (this.isDanmu) {
        let row = this.row
        canCommit = row && row.business_id && row.oid && row.rid && row.flow_id
        ids = {
          business_id: +row.business_id,
          flow_id: +row.flow_id,
          oid: row.oid.val[0].text,
          rid: +row.rid,
          task_id: (row.task_id && row.task_id.val && row.task_id.val.length && +row.task_id.val[0].text) || 0
        }
        extraData.rsc_count = -1
      } else {
        canCommit = detail.resource && detail.resource.business_id && resource.oid && detail.flow && detail.flow.rid && detail.flow.flow_id
        ids = {
          business_id: detail.resource.business_id,
          flow_id: detail.flow.flow_id,
          oid: resource.oid,
          rid: detail.flow.rid,
          task_id: (detail.task && detail.task.id) || 0
        }
      }
      if (canCommit) {
        resourceApi.submitResource({
          ...ids,
          resource_result: result,
          forbid_params: {},
          binds: this.currentBindId,
          extra_data: !Object.keys(extraData).length ? undefined : extraData
        }).then(res => {
          if (res.code === 0 || res.code === 92015 || res.code === 92029) {
            // 92015 任务已被操作或者删除 92029 审核任务超时 refresh
            res.code === 0
            ? (!res.tips && notify.success(`成功${this.dialogTitle}`)) : res.code === 92015
            ? notify.warning(res.message) : notify.error(res.message)
            this.$emit('getResourceList')
          } else {
            notify.error(res.message)
          }
          this.dialogVisible = false
        }).catch(_ => {})
      }
    }
  }
}
</script>

<style lang="stylus">
.pass-dialog {
  .custom {
    .el-form-item__content {
      width: calc(100% - 50px)
      margin-left: 50px
    }
  }
}
</style>
