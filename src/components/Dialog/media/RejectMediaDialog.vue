<template>
  <div class="media-reject-dialog">
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" :close-on-click-modal="true">
      <el-form
      ref="reject"
      label-width="100px"
      @submit.stop.prevent.native
      :rules="rejectRules"
      :model="reject">
        <el-form-item v-if="dialogTitle.indexOf('批量') > -1">
          <p>将执行驳回当前选中数据</p>
        </el-form-item>
        <el-form-item label="是否删除播单" prop="isDel">
          <el-radio-group v-model="reject.isDel">
            <el-radio :label="2">不删除</el-radio>
            <el-radio :label="1">删除</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核tag" prop="reasonTagId" required>
          <TagGroup
            v-model="reject.reasonTagId"
            :tagList="reasonTags"
            @input="onChangeTagId"
          />
        </el-form-item>
        <el-form-item label="理由" prop="reason" required>
          <ReasonFiller
            ref="reasonFiller"
            v-model="reject.reason"
            :editable="perms.AEGIS_REASON_INPUT_ARCHIVE"
            :reasonTemplate="reject.reasonTemplate"
          />
        </el-form-item>
        <el-form-item label="重置项目" prop="checkList" required>
          <el-checkbox-group v-model="reject.checkList">
            <el-checkbox v-for="(value, key) in checkBoxList" :key="key" :label="parseInt(key, 10)">{{value}}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="是否通知用户" prop="notify">
          <el-radio-group v-model="reject.notify">
            <el-radio :label="1">通知</el-radio>
            <el-radio :label="0">不通知</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button @click="dialogVisible = false" size="medium">取 消</el-button>
        <el-button type="primary" size="medium" :disabled="isDisabled" @click="beforeCommit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import http from '@/lib/http'
import notify from '@/lib/notify'
import ReasonFiller from '@/v2/pure-components/ReasonFiller'
import TagGroup from '@/v2/pure-components/TagGroup'
import { workbenchApi } from '@/api/index'

export default {
  components: {
    ReasonFiller,
    TagGroup
  },

  props: {
    businessId: Number,
    multipleSelection: {
      type: Array,
      default: () => {
        return []
      }
    }
  },

  data() {
    return {
      dialogTitle: '',
      dialogVisible: false,
      reasonTags: [],
      rejectForm: {
        reasonTagId: '',
        reason: '',
        note: '',
        checkList: [],
        isDel: 2,
        notify: 1
      },
      reject: {},
      rejectRules: {
        reasonTagId: [
          {
            required: true,
            message: '理由必选',
            trigger: 'blur'
          }
        ],
        reason: [
          { required: true, message: '理由必填', trigger: 'blur' },
          { required: true, message: '理由必填', trigger: 'change' }
        ],
        checkList: [
          { required: true, message: '重置项目必选', trigger: 'blur' },
          { required: true, message: '重置项目必选', trigger: 'change' }
        ]
      },
      op: {},
      checkBoxList: {
        1: '重置标题',
        2: '清空简介',
        3: '重置封面'
      },
      checkBoxMap: {
        '标题': 1,
        '简介': 2,
        '封面': 3
      }
    }
  },

  computed: {
    ...mapState({
      perms: (state) => state.user.perms
    }),
    isDisabled() {
      return this.reject.checkList && !this.reject.checkList.length
    }
  },

  watch: {
  },

  created() {
    this.$EventBus.$on('reason-content-selection', this.handleReset)
  },

  mounted() {
  },

  beforeDestroy() {
    this.$EventBus.$off('reason-content-selection', this.handleReset)
  },

  methods: {
    resetFields() {
      this.$refs['reject'].resetFields()
    },
    openRejectMediaDialog(op) {
      this.op = op
      this.dialogTitle = this.op.ch_name
      this.reject = {...this.rejectForm}
      this.getReasonTag()
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['reject'].resetFields()
      })
    },
    /**
     * public
     * return string 有值代表不通过
     */
     validateReason() {
      const reasonFiller = this.$refs.reasonFiller
      if (reasonFiller) {
        const { data } = reasonFiller.validate()
        if (data?.length) {
          return data.map((e) => e.message).join('；')
        }
      }
      return ''
    },
    beforeCommit() {
      this.$refs['reject'].validate(valid => {
        if (!valid) {
          return false
        }
        this.commit()
      })
    },
    commit() {
      const reasonErr = this.validateReason()
      if (reasonErr) {
        return notify.error(reasonErr)
      }
      let multSelection = this.multipleSelection
      let reject = this.reject
      let extraData = {
        is_pass: 0,
        is_del: +reject.isDel,
        operation: reject.checkList.join(',')
      }
      let auditInfo = {
        note: reject.note,
        reject_reason: reject.reason,
        notify: +reject.notify,
        reason_id: +reject.reasonTagId || 0
      }
      const path = `/x/admin/aegis/engine/resource/batchsubmit`
      http.post(path, JSON.stringify({
        business_id: this.businessId,
        rids: (multSelection.map(d => { return d.rid }) || []).join(','),
        binds: this.op.bind_id_list,
        extra_data: extraData,
        ...auditInfo
      }), true).then(res => {
        if (res.code === 0 || res.code === 92015 || res.code === 92029) {
          // 92015 任务已被操作或者删除 92029 审核任务超时 refresh
          res.code === 0
          ? (!res.tips && notify.success(`成功${this.dialogTitle}`)) : res.code === 92015
          ? notify.warning(res.message) : notify.error(res.message)
          this.$emit('getResourceList')
        } else {
          notify.error(res.message)
        }
        this.dialogVisible = false
      })
    },
    async getReasonTag() {
      try {
        const res = await workbenchApi.getReasonTags({
          business_id: this.businessId
        })
        this.reasonTags = (res.data?.options || []).map(el => ({
          tag_id: el.id,
          ...el
        }))
      } catch (err) {
        console.error(err)
      }
    },
    handleReset(payload) {
      const newCheckList = payload.map(keyword => {
        if (this.checkBoxMap[keyword]) return this.checkBoxMap[keyword]
        else return undefined
      }).filter(e => !!e)
      this.reject.checkList = newCheckList
    },
    onChangeTagId(tagId) {
      if (!tagId) {
        this.reject.reasonTemplate = ''
        this.reject.reason = ''
      }
      const tagObj = this.reasonTags.find(e => e.tag_id === tagId)
      this.reject.reasonTemplate = tagObj?.extra_data?.reason || ''
    }
  }
}
</script>

<style lang="stylus">
.media-reject-dialog {
  .custom {
    .el-form-item__content {
      width: calc(100% - 50px)
      margin-left: 50px
    }
  }
  .banned-info {
    display: block
    padding: 0px 0px 22px
    em {
      color: var(--text-color-light-1)
    }
  }
  .banned-item {
    width: 100%
    .el-form-item__content {
      width: calc(100% - 78px)
    }
    .day {
      margin-left: 8px
      margin-right: 8px
      width: calc(100% - 200px)
    }
  }
}
</style>
