<template>
  <div class="operation-log-dialog">
    <el-dialog class="dialog" :modal-append-to-body="false" :append-to-body="true" title="日志" :visible.sync="dialogTableVisible">
      <p v-for="(item, index) in log" :key="index" style="line-height: 2" v-html="item"></p>
      <p v-if="!log.length" style="line-height: 2"> 暂无 </p>
    </el-dialog>
  </div>
</template>

<script>
import { setHistoryColor } from '../../pages/contentAudit/common'
export default {
  props: {
    operationLog: Array
  },

  data() {
    return {
      dialogTableVisible: false,
      log: []
    }
  },
  watch: {
    operationLog(val) {
      if (val) {
        this.log = setHistoryColor(val)
      }
    }
  }
}
</script>

<style lang="stylus">
.operation-log-dialog {
  .dialog > p {
    color: var(--text-color)
    font-size: 14px
  }
}
</style>
