<template>
  <div class="lock-dialog">
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" :close-on-click-modal="true">
      <el-form
      ref="lock"
      label-width="100px"
      @submit.stop.prevent.native
      :rules="lockRules"
      :model="lock">
        <el-form-item>
          <p>确定{{ dialogTitle }}选中的资源</p>
        </el-form-item>
        <el-form-item label="理由" prop="reasonType">
          <el-select v-model="lock.reasonType" size="small" @change="changeReasonType" style="width: calc(100% - 50px)">
            <el-option v-for="item in lockTypes" :key="item.id" :value="item.id" :label="item.reason"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="custom" prop="lockReason" label="锁定理由" type="flex">
          <el-input autofocus v-model="lock.lockReason" size="small" style="width: calc(100% - 100px)"></el-input>
        </el-form-item>
        <el-form-item label="是否通知用户" prop="notify" v-if="isDrama && !isMaterial">
          <el-radio-group v-model="lock.notify">
            <el-radio :label="1">通知</el-radio>
            <el-radio :label="0">不通知</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button @click="dialogVisible = false" size="medium">取 消</el-button>
        <el-button type="primary" size="medium" @click="beforeCommit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { resourceApi, reasonApi } from '@/api/index'
import notify from '@/lib/notify'
import { setCurrentResource } from '../../pages/contentAudit/common.js'

const MATERIAL_REJECT_REASONS = [{
  id: 1,
  reason: '违禁'
}, {
  id: 2,
  reason: '色情'
}, {
  id: 3,
  reason: '低俗'
}, {
  id: 4,
  reason: '人身攻击'
}, {
  id: 5,
  reason: '血腥暴力'
}, {
  id: 6,
  reason: '赌博诈骗'
}, {
  id: 7,
  reason: '不适宜'
}, {
  id: 8,
  reason: '违反运营规则'
}]

export default {
  props: {
    detail: Object,
    businessName: {
      type: String,
      default: ''
    }
  },

  data() {
    const validateEvent = (rule, value, callback) => {
      if (!value) {
        callback(new Error('理由必填'))
      } else {
        callback()
      }
    }
    return {
      dialogTitle: '',
      dialogVisible: false,
      lockTypes: [],
      lockForm: {
        reasonType: '',
        lockReason: '',
        note: '',
        notify: 1
      },
      lock: {},
      lockRules: {
        reasonType: [
          {
            required: true,
            message: '理由必选',
            trigger: 'blur'
          }
        ],
        lockReason: [
          {
            required: true,
            validator: validateEvent,
            trigger: 'blur'
          }
        ]
      },
      op: {},
      isDrama: false,
      isConsumption: false,
      isMaterial: false
    }
  },

  computed: {
    ...mapState({
    })
  },

  watch: {
    'businessName': {
      handler() {
        setCurrentResource(this)
      },
      immediate: true
    }
  },

  methods: {
    resetFields() {
      this.$refs['lock'].resetFields()
    },
    openLockDialog(op, row = {}) {
      this.dialogVisible = true
      this.op = op
      this.row = row
      this.dialogTitle = this.op.ch_name
      this.lock = {...this.lockForm}
      // 单话理由写死
      if (this.isMaterial) {
        this.initStaticReason()
      } else {
        this.getReason()
      }
      this.$nextTick(() => {
        this.$refs['lock'].resetFields()
      })
    },
    initStaticReason() {
      if (this.isMaterial) {
        this.lockTypes = MATERIAL_REJECT_REASONS
      }
      this.lock.reasonType = this.lockTypes[0].id
      this.lock.lockReason = this.lockTypes[0].reason
    },
    beforeCommit() {
      this.$refs['lock'].validate(valid => {
        if (!valid) {
          return false
        }
        this.dialogTitle === '批量锁定'
        ? this.$emit('confirmOps', this.op, this.lock, this.lockTypes) : this.commit()
      })
    },
    commit() {
      let detail = this.detail
      const { resource, flow } = detail
      const { lockReason, reasonType } = this.lock
      let canCommit = false
      let ids = {}
      let extraData = {}
      let result = {
        note: '',
        reject_reason: lockReason,
        reason_id: +reasonType || 0
      }
      if (this.isMaterial) {
        let row = this.row
        canCommit = row && row.business_id && row.oid && row.rid && row.flow_id
        ids = {
          business_id: +row.business_id,
          flow_id: +row.flow_id,
          oid: row.oid.val[0].text,
          rid: +row.rid,
          task_id: (row.task_id && row.task_id.val && row.task_id.val.length && +row.task_id.val[0].text) || 0
        }
        extraData.rsc_count = -1
      } else {
        canCommit = resource && resource.business_id && resource.oid && flow && flow.rid && flow.flow_id
        ids = {
          business_id: detail.resource.business_id,
          flow_id: detail.flow.flow_id,
          oid: resource.oid,
          rid: detail.flow.rid,
          task_id: (detail.task && detail.task.id) || 0
        }
      }
      if (canCommit) {
        resourceApi.submitResource({
          ...ids,
          resource_result: result,
          forbid_params: {},
          binds: this.op.bind_id_list,
          extra_data: extraData
        }).then(res => {
          if (res.code === 0) {
            !res.tips && notify.success('成功锁定')
            this.$emit('getResourceList')
          } else {
            notify.error(res.message)
          }
          this.dialogVisible = false
        }).catch(_ => {})
      }
    },
    getReason() {
      this.lockTypes = []
      reasonApi.getReasonList({
        bid: this.isDrama ? 48 : 73,
        state: 1,
        sort: 'asc',
        ps: 9999
      }).then(res => {
        if (!res.data.data) {
          return
        }
        (res.data.data || []).forEach(d => {
          if (this.isConsumption) {
            if (d.cate_id === 165) {
              this.lockTypes.push({
                  reason: d.description,
                  id: d.id
              })
            }
          } else {
            if (d.sec_id === 19) {
              this.lockTypes.push({
                  reason: d.description,
                  id: d.id
              })
            }
          }
        })
        if (this.lockTypes.length) {
          this.lock.reasonType = this.lockTypes[0].id
          this.lock.lockReason = this.lockTypes[0].reason
        }
      }).catch(_ => {})
    },
    changeReasonType() {
      this.lock.lockReason = (this.lockTypes.find(t => t.id === this.lock.reasonType) || {}).reason
    }
  }
}
</script>

<style lang="stylus">
.lock-dialog {
  .custom {
    .el-form-item__content {
      width: calc(100% - 50px)
      margin-left: 50px
    }
  }
}
</style>
