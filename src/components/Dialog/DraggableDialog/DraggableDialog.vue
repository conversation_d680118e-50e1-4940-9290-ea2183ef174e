<template>
  <Draggable
    v-if="visible"
    :width="width"
    :height="height"
    :zIndex="zIndex"
    limitClient
    class="draggable-dialog"
    :rememberPosition="rememberPosition"
    :fixPosition="fixPosition"
  >
    <div class="draggable-dialog-header" slot="dragTrigger">
      <span>{{ title }}</span>
      <el-button icon="el-icon-close" type="text" class="draggable-dialog-close-btn" @click="closeDialog"></el-button>
    </div>
    <div class="draggable-dialog-body" slot="noDragTrigger">
      <slot></slot>
    </div>
  </Draggable>
</template>

<script>
/**
 * @component
 * @assetTitle 可拖拽对话框
 * @assetDescription 可拖拽的弹出式对话框
 * @assetImportName DraggableDialog
 * @assetTag 通用组件
 */
import Draggable from '@/components/package/Draggable'

export default {
  components: {
    Draggable
  },
  props: {
    // 对话框的标题
    title: String,
    // 对话框的宽度
    width: String,
    // 对话框的高度
    height: String,
    // 对话框的 zIndex
    zIndex: {
      type: Number,
      default: 100
    },
    // 是否展示对话框，支持 .sync 修饰符
    visible: Boolean,
    // 是否记住上次用户拖拽到的位置
    rememberPosition: String,
    /**
     * 弹窗的初始位置
     * @params fixPosition.left {number} 距离屏幕左侧的距离
     * @params fixPosition.top {number} 距离屏幕顶部的距离
     */
    fixPosition: {
      type: Object,
      default() {
        return { left: 0, top: 0 }
      }
    }
  },
  mounted() {
    document.addEventListener('keydown', this.dialogEscHandler)
  },
  beforeDestroy() {
    document.removeEventListener('keydown', this.dialogEscHandler)
  },
  methods: {
    closeDialog() {
      // 弹窗的关闭事件
      this.$emit('update:visible', false)
    },
    dialogEscHandler(event) {
      if (this.visible && event.keyCode === 27) {
        this.closeDialog()
      }
    }
  }
}
</script>

<style lang="stylus">
.draggable-dialog {
  background: var(--content-bg-color);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  .draggable-dialog-header {
    font-size: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    border-bottom: 1px solid var(--border-color-light-2);
    box-sizing: border-box;
    .draggable-dialog-close-btn {
      font-size: 16px;
      color: var(--text-color-dark-2);
    }
  }
  .draggable-dialog-body {
    padding: 20px;
  }
}
</style>
