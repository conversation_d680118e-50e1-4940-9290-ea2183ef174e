<template>
  <audio controls ref="audioDom" id="mng-music" mediaGroup preload="auto" style="width:100%;">
    <source ref="audioSource" src="" type="audio/mpeg"/>
  </audio>
</template>

<script>
export default {
  data() {
    return {
      audioURL: '',
      currentFile: ''
    }
  },
  methods: {
    getAudio() {
      if (window.HTMLAudioElement) {
        return this.$refs.audioDom
      }
      return null
    },
    loadAudio(audioURL) {
      const audioSource = this.$refs.audioSource
      const audioDom = this.getAudio()
      audioSource.src = audioURL
      audioDom.src = audioURL
      const mime = audioSource.type
      return audioDom && !!audioDom.canPlayType(mime)
    },
    playAudio() {
      const oAudio = this.getAudio()
      if (!oAudio) return
      if (oAudio.paused) {
        oAudio.play()
      } else {
        oAudio.pause()
      }
    }
  },
  mounted() {
    // audio组件mounted
    const ad = this.$refs.audioDom
    ad.controls = false
    ad.autoplay = false
    ad.volume = 0.2
    ad.currentTime = 0
    ad.playbackRate = 1
    this.$emit('music-ready', this)
  },
  props: {

  },
  model: {
    prop: 'self'
  }
}
</script>
