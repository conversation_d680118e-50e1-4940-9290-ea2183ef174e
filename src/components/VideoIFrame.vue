<template>
  <iframe
    v-if="visible"
    allowfullscreen="true"
    :src="videoSrc"
    frameborder="0">
  </iframe>
</template>

<script>
import { isNil } from 'lodash-es'
import { mapGetters, mapState } from 'vuex'
import { fillTemplate } from '@/v2/utils'

// 接入info参考: https://info.bilibili.co/pages/viewpage.action?pageId=12869379
const iframePlayerUrlMap = {
  uat: '//uat-manager.bilibili.co/docs/player/innerplayer.html',
  /* eslint-disable */
  pre: fillTemplate(
    '//${this.a}.${this.b}.${this.c}/docs/player/innerplayer.html',
    {
      a: 'manager',
      b: 'bilibili',
      c: 'co'
    }
  ),
  prod: '//manager.bilibili.co/docs/player/innerplayer.html'
}
const defaultExtra = {
  as_wide: 1,
  inner: 1,
  danmaku: 0,
  stableController: 1,
  hideInteractiveHistory: 1,
  crossDomain: 1
}

export default {
  name: 'VideoIFrame',
  props: {
    cid: {
      required: true
    },
    aid: {
      required: true
    },
    visible: {
      type: Boolean,
      default: true
    },
    extra: Object,
    seek: Number, // 定位时间点
    seekPause: { // 定位是否暂停播放
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      rawDomain: '',
      player: ''
    }
  },
  created() {
    this.rawDomain = document.domain
    document.domain = 'bilibili.co'
    window.Html5IframeInitialized = () => {
      const { player } = window
      this.player = player

      if (player) {
        this.$emit('videoLoad')
        if (this.seek) {
          player.seek(this.seek, this.seekPause)
        }
      }
      this.$emit('init')
    }
    this.$EventBus.$on('seek-time', this.goSeekTime)
  },
  beforeDestroy() {
    document.domain = this.rawDomain
    window.Html5IframeInitialized = () => {}
    this.$EventBus.$off('seek-time', this.goSeekTime)
  },
  computed: {
    ...mapGetters({
      getProxyEnv: 'env/getProxyEnv'
    }),
    ...mapState({
      mode: 'env/mode'
    }),
    videoSrc() {
      const query = {
        cid: this.cid,
        aid: this.aid,
        ...defaultExtra,
        ...(this.extra || {})
      }
      const queryStr = Object.keys(query)
        .map(key => !isNil(query[key]) ? `${key}=${query[key]}` : '')
        .filter(e => e)
        .join('&')

      const isLocalDev = this.mode === 'development'
      if (isLocalDev) {
        const env = this.getProxyEnv()
        return `${iframePlayerUrlMap[env]}?${queryStr}`
      }
      return `${iframePlayerUrlMap.prod}?${queryStr}`
    }
  },
  methods: {
    goSeekTime(time) {
      this.player.seek(time)
    }
  }
}
</script>
