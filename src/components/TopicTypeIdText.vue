<template>
  <p class="topic-typeid-text">
    <template v-if="value">
      <span
        v-for="(data, idx) in formatData"
        :key="idx"
        style="margin-right: 10px"
        >{{ data }};</span
      >
    </template>
    <span v-else>
      不限制
    </span>
  </p>
</template>
<script>
import { mapState, mapActions } from 'vuex'
export default {
  name: 'TopicTypeIdText',
  data() {
    return {}
  },
  props: {
    value: {
      type: String,
      default: ''
    }
  },
  computed: {
    ...mapState({
      arctypes: (state) => state.arctype.arctypes
    }),
    formatData() {
      const typeIdArr = this.value.split(',')
      return typeIdArr.map((typeId) => {
        return this.preOrderTraversal(
          {
            children: this.arctypes
          },
          typeId
        )
      })
    }
  },
  methods: {
    ...mapActions({
      getArctype: 'arctype/getArctype'
    }),
    // n叉树找路径
    preOrderTraversal(root, searchId) {
      const st = []
      if (root !== null) st.push(root)
      let result = ''
      let pathSt = []
      pathSt.push(root.name || '')
      while (st.length !== 0) {
        let node = st[st.length - 1]
        let path = pathSt[pathSt.length - 1]
        pathSt.pop()
        st.pop()
        // 获取路径
        // 放入
        if (!node.children) {
          if (+searchId === +node.id) {
            result = path
            break
          }
        } else {
          for (let i = node.children.length - 1; i >= 0; i--) {
            st.push(node.children[i])
            pathSt.push(path + `${path ? '-' : ''}` + node.children[i].name)
          }
        }
      }
      return result
    }
  },
  async mounted() {
    await this.getArctype()
  }
}
</script>
<style lang="stylus" scoped></style>
