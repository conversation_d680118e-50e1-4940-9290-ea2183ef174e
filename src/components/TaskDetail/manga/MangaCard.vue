<template>
  <div class="manga-detail-form">
    <el-card class="box-card" header="漫画详情">
      <el-form ref="formDetail" label-width="50px" class="form">
        <el-form-item label="标题" class="form-item" v-if="mangaInfo.title">
          <p>{{mangaInfo.title}}</p>
        </el-form-item>
        <el-form-item label="内容" class="form-item">
          <quill-editor 
            class="custom-quill-editor" 
            ref="myQuillEditor" 
            v-model="mangaInfo.content" 
            :options="{
              modules: {
                toolbar: false
              }
            }"
            :style="mangaCard.style"
          ></quill-editor>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'
import { quillEditor } from 'vue-quill-editor'
import { mangaApi } from '@/api/index'

export default {
  name: 'ComicDetailForm',
  data () {
    return {
      mangaInfo: {
        content: '',
        title: ''
      }
    }
  },
  props: {
    detail: {
      type: Object,
      required: true,
      default: () => {
        return {}
      }
    },
    mangaCard: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  components: {
    quillEditor
  },
  watch: {
    detail: {
      handler(nextVal) {
        if (nextVal !== {}) { 
          this.fetchData(nextVal) 
        }
      },
      immediate: true
    }
  },
  methods: {
    fetchData(detail) {
      const resourceId = detail && detail.resource && detail.resource.oid
      if (resourceId) {
        mangaApi.getMangaInfo({
          resource_id: resourceId
        }).then((res) => {
          const data = res.data
          this.mangaInfo = {
            title: data && data.resource && data.resource.extra1s,
            content: data && data.resource_text && data.resource_text.content
          }
        }).catch(_ => {})
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      const editor = this.$refs.myQuillEditor && this.$refs.myQuillEditor.quill
      editor && editor.enable(false)
    })
  }
}
</script>

<style lang="stylus" scoped>
.manga-detail-form
  .box-card 
    margin 0
  .custom-quill-editor
    height 400px
</style>
