<template>
<div class="cooperation-info-card">
	<el-card class="box-card">
		<div slot="header" class="clearfix">
			<span>合作信息</span>
		</div>
		<el-row type="flex" class="batch-row">
			<el-button type="success" size="small" @click="openDialog(null, '批量通过')" :disabled="isDisabled">批量通过</el-button>
			<el-button type="danger" size="small" @click="openDialog(null, '批量驳回')"  :disabled="isDisabled">批量驳回</el-button>
			<div class="diff-msg" v-if="diff_msg">{{diff_msg}}</div>
		</el-row>
		<el-table
		:data="tableData"
		stripe
		border
		height="240"
		style="width: 100%; margin-top: 18px">
			<el-table-column
				v-for="(item, index) in COLUMNS"
				:key="index"
				:label="item.label"
				:prop="item.prop"
				:width="item.width"
				align="center">
				<template v-slot="scope">
					<el-button v-if="item.prop === 'apply_staff_mid'" type="text" @click="goStaffZone(scope.row.apply_staff_mid)">{{ scope.row.apply_staff_mid }}</el-button>
					<div v-else-if="item.prop === 'ops'">
						<el-button type="success" size="mini" @click="submit(scope.row, '通过')" v-if="scope.row.apply_state === -1 || scope.row.apply_state === -2 || scope.row.apply_state === 0">通过</el-button>
						<el-button type="danger" size="mini" @click="openDialog(scope.row, '驳回')" v-if="scope.row.apply_state === -1 || scope.row.apply_state === -2 || scope.row.apply_state === 0">驳回</el-button>
						<el-button type="info" size="mini" @click="getStaffLog(scope.row.id)">日志</el-button>
					</div>
					<div v-else-if="item.prop === 'staff_name'">
						<p
							v-if="scope.row.user_info && scope.row.user_info.official && scope.row.user_info.official.role !== 0"
							class="verifyIcon"
							:class="{
								individual: INDIVIDUAL_LIST.includes(scope.row.user_info.official.role),
								enterprise: ENTERPRISE_LIST.includes(scope.row.user_info.official.role)
							}">
						</p>
						<p>{{scope.row.staff_name}}</p>
					</div>
					<p v-else>{{scope.row[item.prop]}}</p>
				</template>
			</el-table-column>
		</el-table>
	</el-card>

	<el-dialog :title="dialogTitle" :visible.sync="dialogVisible" :close-on-click-modal="true" :append-to-body="true">
      <el-form
      ref="reject"
      label-width="100px"
      @submit.stop.prevent.native
      :rules="rejectRules"
      :model="reject">
        <el-form-item label="理由" prop="reasonType">
          <el-select v-model="reject.reasonType" size="small" @change="changeReasonType" style="width: calc(100% - 50px)">
            <el-option v-for="item in rejectTypes" :key="item.id" :value="item.id" :label="item.reason"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="custom" prop="rejectReason" label="驳回理由" type="flex">
          <el-input autofocus v-model="reject.rejectReason" size="small" style="width: calc(100% - 100px)"></el-input>
        </el-form-item>
        <el-form-item label="是否通知用户" prop="notify">
          <el-radio-group v-model="reject.notify">
            <el-radio :label="1">通知</el-radio>
            <el-radio :label="0">不通知</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button @click="dialogVisible = false" size="medium">取 消</el-button>
        <el-button type="primary" size="medium" @click="beforeSubmit">确 定</el-button>
      </div>
    </el-dialog>

		<OperationLogDialog ref="operationLog" :operationLog="operationLog"></OperationLogDialog>
</div>
</template>

<script>
import { createNamespacedHelpers } from 'vuex'
import { staffApi, reasonApi } from '@/api/index'
import OperationLogDialog from '@/components/Dialog/operationLogDialog'
import { INDIVIDUAL_LIST, ENTERPRISE_LIST } from '@/pages/workbench/constants'

const moduleName = 'env'
const { mapGetters } = createNamespacedHelpers(moduleName)

// const STATES_MAP = {
// 	'-2': '驳回',
// 	'-1': '待审',
// 	'0': '已通过',
// 	'1': 'staff同意',
// 	'2': 'staff拒绝',
// 	'3': 'up主删除',
// 	'4': 'staff无视'
// }

const COLUMNS = [{
	label: '顺序',
	prop: 'apply_order',
	width: '60'
}, {
	label: '职能类型',
	prop: 'apply_title'
}, {
	label: 'mid',
	prop: 'apply_staff_mid'
}, {
	label: '昵称',
	prop: 'staff_name'
}, {
	label: '合作申请状态',
	prop: 'state_label'
}, {
	label: '操作',
	prop: 'ops',
	width: '220'
}]

export default {
	components: {
		OperationLogDialog
	},
	props: {
		detail: {
			type: Object,
			default: () => {
				return {}
			}
		}
	},

	data() {
		const validateEvent = (rule, value, callback) => {
      if (!value) {
        callback(new Error('理由必填'))
      } else {
        callback()
      }
    }
		return {
			tableData: [],
			diff_msg: '',
			dialogVisible: false,
			dialogTitle: '',
			rejectTypes: [],
      rejectForm: {
        reasonType: '',
        rejectReason: '',
        notify: 1
      },
      reject: {},
      rejectRules: {
        reasonType: [
          {
            required: true,
            message: '理由必选',
            trigger: 'blur'
          }
        ],
        rejectReason: [
          {
            required: true,
            validator: validateEvent,
            trigger: 'blur'
          }
        ]
			},
			currentRow: {},
			COLUMNS,
			operationLog: [],
			ENTERPRISE_LIST,
      INDIVIDUAL_LIST
		}
	},

	watch: {
		'detail.resource': {
			handler(val) {
				if (val) {
					this.getApplyList()
				}
			},
			immediate: true
		}
	},

	computed: {
		isDisabled() {
			return this.tableData.findIndex(d => d.apply_state === -1) < 0
		},
		...mapGetters(['env'])
	},

	methods: {
		getApplyList() {
			staffApi.getApplyList({
				aid: this.detail.resource.oid,
				state: '-2,-1,0,1,2,3,4'
			}).then(res => {
				if (!res.data) {
					return
				}
				/* eslint-disable */
				this.tableData = ((res.data.staff_applys || []).map(row => {
					const { id, apply_state, apply_aid, apply_order, apply_title, apply_staff_mid, staff_name, state_label, user_info } = row
					return {
						id,
						apply_state,
						apply_aid,
						apply_order,
						apply_title,
						apply_staff_mid,
						staff_name,
						state_label,
						user_info
					}
				})) || []
				/* eslint-enable */
        this.diff_msg = res.data.diff_msg
        this.$emit('getStaffTableData', this.tableData)
			}).catch(_ => {})
		},
		openDialog(row, title) {
			this.currentRow = row
			this.dialogTitle = title
			this.reject = {...this.rejectForm}
			if (title === '批量通过') {
				this.$confirm(`此操作将批量通过当前选中审核内容, 是否继续?`, title, {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					this.submit(null)
				}).catch(() => {})
			} else {
				this.dialogVisible = true
				this.getReason()
				this.$nextTick(() => {
					this.$refs['reject'].resetFields()
				})
			}
		},
		beforeSubmit() {
			this.$refs['reject'].validate(valid => {
        if (!valid) {
          return false
        }
        this.submit()
      })
		},
		submit(row, title) {
			let ids = ''
			const isPass = this.dialogTitle.indexOf('驳回') < 0 || title
			if (row || this.currentRow) {
				ids += (row && row.id) || this.currentRow.id
			} else {
				let arr = []
				this.tableData.forEach(d => {
					if (d.apply_state === -1) {
						arr.push(d.id)
					}
				})
				ids = arr.join(',')
			}
			staffApi.editApplyState({
				ids,
				state: isPass ? 0 : -2,
				is_send: isPass ? 1 : +this.reject.notify,
				reject_reason: this.reject.rejectReason || ''
			}).then(res => {
				this.dialogVisible = false
				this.getApplyList()
			}).catch(_ => {})
		},
		goStaffZone(mid) {
			window.open(`https://space.bilibili.com/${mid}`)
		},
		getReason() {
			this.rejectTypes = []
			// online 69 uat 74
			reasonApi.getReasonList({
				// 线上或者pre预发的话，是69， uat是74
				bid: (this.env().pre || this.env().prod) ? 69 : 74,
        state: 1,
        sort: 'asc',
        ps: 9999
      }).then(res => {
        if (!res.data.data) {
          return
        }
        this.rejectTypes = res.data.data.map(d => {
          return {
            reason: d.description,
            id: d.id
          }
        })
        this.rejectTypes.push({
          reason: '自定义',
          id: '自定义'
        })
        if (this.rejectTypes.length) {
          this.reject.reasonType = this.rejectTypes[0].id
          this.reject.rejectReason = this.rejectTypes[0].reason
        }
      }).catch(_ => {})
		},
		changeReasonType() {
      this.reject.rejectReason = (this.rejectTypes.find(t => t.id === this.reject.reasonType) || {}).reason
		},
		getStaffLog(id) {
			staffApi.getStaffLog({
				apply_id: id
			}).then(res => {
				this.operationLog = res.data || []
				this.$refs.operationLog.dialogTableVisible = true
			})
		}
	}

}
</script>
<style lang="stylus">
.cooperation-info-card {
	height 280px
	.batch-row {
		.el-button {
			align-self flex-end
		}
		.diff-msg {
			height 20px
			background #FEF0F0
			color var(--error-color)
			font-size 12px
			padding 9px
			overflow auto
			margin-left 10px
			line-height 20px
		}
	}
	.verifyIcon {
    display: inline-block
    vertical-align: bottom
    margin-top: 2px
    width: 18px
    height: 18px
    background-image: url('~@/assets/user-auth.png')
  }
  .individual {
    background-position: -39px -82px
  }
  .enterprise {
    background-position: -4px -81px
  }
}
</style>
