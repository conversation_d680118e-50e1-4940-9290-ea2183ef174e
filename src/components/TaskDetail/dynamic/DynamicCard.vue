<template>
  <div class="dynamic-card">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>审核内容</span>
      </div>
      <div class="hit-msg" v-if="hitReason">命中：{{ hitReason }}</div>
      <div class="feature-group-info" v-if="featureGroupInfo">
        内容特征库：
        <TextareaDiv :rows="2" style="flex: 1">{{ featureGroupInfo }}</TextareaDiv>
      </div>
      <div class="feature-group-info" v-if="filterRemark && perms && perms.AEGIS_COMMUNITY_DYNAMIC_FILTER_REMARK">
        敏感词备注：{{filterRemark}}
      </div>
      <DynamicCard ref="dynamic" style="height: 100%" :resource="computedResource" />
    </el-card>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import DynamicCard from '../../DynamicCard'
import { mappingKV } from '@/pages/workbench/common.js'
import TextareaDiv from '@/components/element-update/TextareaDiv.vue'
import { getValueByKeyPath } from '@/v2/utils'

export default {
  components: {
    DynamicCard,
    TextareaDiv
  },
  props: {
    resource: {
      type: Object,
      default: () => {
        return {}
      }
    },
    card: {
      type: Object,
      default: () => {
        return {}
      }
    },
    detail: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    ...mapState({
      perms: (state) => state.user.perms
    }),
    businessId() {
      return +this.$route.query?.business_id
    },
    computedResource() {
      if (this.card?.source) {
        return mappingKV(this.card.source, this.detail)?.resource || {}
      }
      return this.resource
    },
    hitReason() {
      if (this.computedResource?.metas) {
        const { hit, reason } = this.computedResource.metas
        const reasons = []

        if (hit) {
          reasons.push(hit)
        }

        if (reason) {
          reasons.push(reason)
        }

        if (this.card.hasOwnProperty('hidden_hit') && this.card?.hidden_hit) {
          return !this.card?.hidden_hit
        }

        return reasons.join('；')
      }
      return ''
    },
    featureGroupInfo() {
      if (this.computedResource?.feature_group_info_v2) {
        let remarks = []
        const { feature_group_info_v2 } = this.computedResource
        for (const key in feature_group_info_v2) {
          remarks = remarks.concat((feature_group_info_v2[key] || []).map(item => `${item.remark}`))
        }
        return remarks.join('\n')
      }
      if (this.computedResource?.feature_group_info?.length > 0) {
        return this.computedResource.feature_group_info
          .map(item => `${item.remark}`)
          .join('\n')
      }

      return ''
    },
    filterRemark() {
      return this.computedResource?.metas?.oper_filter_remark || ''
    }
  },
  watch: {
    computedResource(val) {
      const rawSnapshot = this.card?.source ? mappingKV(this.card.source, this.detail) : false
      const snapshot = rawSnapshot?.dynamicCardData
      let hasValidSnapshot = false
      if (snapshot) {
        // FIXME: 等旧版动态快照下线后可以删掉
        hasValidSnapshot = !!snapshot?.item?.basic && !!snapshot?.item?.id_str && !!snapshot?.item?.modules
        if (!hasValidSnapshot) console.error('Snapshot is invalid. Fetch realtime dyn data')
      }
      if (hasValidSnapshot) {
        const showOpusView = rawSnapshot?.resource?.extra2 === 2 && rawSnapshot?.resource?.metas?.tribee_id > 0 && this.businessId === 18 // extra2 含义：来源，2 含义 图文相簿
        this.getDynamicCardDetail({ snapshot, showOpusView })
      } else {
        const pathToOid = this.card.oid_key
        const oid = pathToOid ? getValueByKeyPath(val, pathToOid) : val.oid
        const showOpusView = val.extra2 === 2 && val?.metas?.tribee_id > 0 && this.businessId === 18 // extra2 含义：来源，2 含义 图文相簿
        if (oid) {
          this.getDynamicCardDetail({ oid, showOpusView })
        } else {
          this.$refs.dynamic.cardDetail = null
        }
      }
    }
  },

  methods: {
    getDynamicCardDetail(params) {
      this.$refs.dynamic.getCardDetail(params)
    }
  }
}
</script>
<style lang="stylus" scoped>
.dynamic-card {
  height: calc(100% - 2px)
  .el-card {
    margin-bottom: 0px
    height: 100%
  }
  .hit-msg {
    height 20px
    background #FEF0F0
    color var(--error-color)
    font-size 12px
    padding 9px
    overflow auto
    margin-left 10px
    line-height 20px
  }
  .feature-group-info {
    display flex
    padding 9px 9px 9px 14px
  }
}
</style>
