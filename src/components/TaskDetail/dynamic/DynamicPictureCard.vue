<template>
  <div class="dynamic-picture-card" :key="cardKey">
    <el-card class="box-card" v-track.impression="{ event: 'show-dynamic-ai-tip', value: { todo_id: $route.query.todo_id, oid: resource.oid, aiTipInfo}}">
      <div slot="header" class="clearfix">
        <span>风险图片</span>
      </div>
      <PictureBox ref="pictureBox" :pictures="pictures" :checkedData="checkedData" :showImagesBox="showImagesBox" @preview-ocr-img="onPreviewOcrImg" />
    </el-card>
    <el-image-viewer
      v-if="previewUrls.length > 0"
      :on-close="onCloseImageViewer"
      :url-list="previewUrls"
    />
  </div>
</template>

<script>
import PictureBox from '@/components/PictureBox'
import { isObject } from '@/utils/type'
import { mapState } from 'vuex'
import store from '@/store'
import { mappingKV } from '@/pages/workbench/common.js'
import drawRect from '@/v2/utils/imageRect.js'
import { dynamicApi } from '@/api'
import { replaceBfsImagesUrl } from '@/utils'

const DYNAMIC_ID = 18
export default {
  components: {
    'el-image-viewer': () => import('element-ui/packages/image/src/image-viewer'),
    PictureBox
  },
  props: {
    resource: {
      type: Object,
      default: () => {
        return {}
      }
    },
    card: {
      type: Object,
      default: () => {
        return {}
      }
    },
    detail: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    ...mapState({
      dynamicCardData: state => state.dynamicPic.dynamicCardData
    }),
    computedResource() {
      if (this.card?.source) {
        return mappingKV(this.card.source, this.detail)?.resource || {}
      }
      return this.resource
    },
    isQa() {
      return this.card?.source === 'snapshotDetail'
    },
    showImagesBox() {
      return this.card?.show_image_box || false
    },
    ocrLocations() {
      if (this.$route.path === '/workbench/todo-config/detail' && +this.$route.query.business_id === DYNAMIC_ID) {
        return this.computedResource?.picture_info?.ocr_locations || {}
      }
      return this.computedResource?.metas?.ocr_locations || {}
    }
  },
  data() {
    return {
      cardKey: 0,
      pictures: [],
      checkedData: [],
      aiTipInfo: [],
      previewUrls: []
    }
  },
  watch: {
    computedResource(val) {
      if (val) {
        // 获取下一个任务清空vuex pictures/chekcedPics state
        store.dispatch('pictures/resetChecked')
        this.pictures = []
      }
    },
    pictures(val) {
      if (val && this.computedResource) {
        let picIndexObj = this.computedResource.picture_info?.dyn_ai_risk_pic || {}
        if (typeof picIndexObj === 'object' && !Object.keys(picIndexObj)?.length) {
          picIndexObj = this.computedResource.metas?.dynAiRiskPic || {}
        }
        // dynAiRiskPic可能是string 可能是obj
        if (isObject(picIndexObj)) {
          this.checkedData = Object.keys(picIndexObj).map(i => this.pictures[i] && this.pictures[i].index) || []
        } else {
          this.checkedData = []
        }
        store.dispatch('pictures/checkedPictures', this.checkedData)
      }
    },
    async dynamicCardData (val) {
      this.cardKey += 1
      if (val) {
        let pics = val?.pictures || []
        if (this.isQa && pics.length) pics = await this.fetchInternalUrls(pics)
        let dynAiRiskPicReminder, libraryIdListMap
        let indexToHiddenLayerMap
        if (this.$route.path === '/workbench/todo-config/detail' && +this.$route.query.business_id === DYNAMIC_ID) {
          dynAiRiskPicReminder = this.computedResource?.picture_info?.reminder || {}
          indexToHiddenLayerMap = this.computedResource?.picture_info?.double_pics || {}
          libraryIdListMap = this.computedResource?.picture_info?.black_zsk_id || {}
        } else {
          dynAiRiskPicReminder = this.computedResource?.metas?.dynAiRiskPicReminder || {}
          indexToHiddenLayerMap = this.computedResource?.metas?.double_pics || {}
          libraryIdListMap = this.computedResource?.metas?.black_zsk_id || {}
        }
        if (!pics?.length) return
        // ai提示埋点数据
        this.aiTipInfo = pics.map((_p, i) => {
          return {
            index: i,
            tips: dynAiRiskPicReminder[i] || [],
            tiplibraryIds: libraryIdListMap[i] || [],
            tipPictures: indexToHiddenLayerMap[i] || [],
            ocrLocations: this.ocrLocations[i] || {}
          }
        })
        this.pictures = pics.map((p, i) => {
          const maxSrc = replaceBfsImagesUrl(p.url)
          const minSrc = this.canUseBfsCut(p.url) ? `${maxSrc}@170w_133h.webp` : p.url
          const ocrPreviewUrl = this.canUseBfsCut(p.url) ? `${maxSrc}@680w_532h_1e.webp` : p.url
          const tipPictures = []
          if (this.ocrLocations[i]) tipPictures.push({ type: 'ocr', url: ocrPreviewUrl, coordinates: this.ocrLocations[i] })
          if (indexToHiddenLayerMap[i]) {
            indexToHiddenLayerMap[i].forEach(hiddenLayerUrl => {
              tipPictures.push({
                type: 'hiddenLayer',
                url: replaceBfsImagesUrl(hiddenLayerUrl)
              })
            })
          }
          return {
            index: i,
            minSrc,
            maxSrc,
            tips: (dynAiRiskPicReminder[i] || []).join('、'),
            tipLibraryIds: libraryIdListMap[i] || [],
            tipPictures
          }
        })
      }
    }
  },
  methods: {
    async fetchInternalUrls(oldPics) {
      const oldUrls = oldPics.map(e => e.url)
      try {
        const res = await dynamicApi.getInternalImages({
          dyn_id: this.computedResource.oid,
          urls: oldUrls
        })
        const { urls: newUrls } = res.data
        return oldPics.map((e, i) => ({
          ...e,
          url: newUrls[i]
        }))
      } catch (e) {
        console.error(e)
      }
    },
    canUseBfsCut(url) {
      return !url.includes('soft_delete_backup') // 被软删除的图片不支持 bfs 裁切
    },
    async onPreviewOcrImg(imageIndex) {
      const maxSrc = this.pictures[imageIndex].maxSrc
      const coordinates = this.ocrLocations[imageIndex]
      const hiResUrl = this.canUseBfsCut(maxSrc) ? `${maxSrc}@1052w.webp` : maxSrc
      const dataurl = await drawRect(hiResUrl, coordinates, { strokeStyle: '#FF0000', lineWidth: 3 })
      this.previewUrls = [dataurl]
    },
    onCloseImageViewer() {
      this.previewUrls = []
    }
  }
}
</script>
<style lang="stylus" scoped>
.dynamic-picture-card {
  height: 100%
  .el-card {
    margin-bottom: 0px
    height: 100%
  }
  .pics-wrapper {
    width: 100%
    margin: 12px 0px 0px
  }
}
</style>
