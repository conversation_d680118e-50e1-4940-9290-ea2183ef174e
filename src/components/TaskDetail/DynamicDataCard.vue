<template>
  <div class="dynamic-data-card" :style="{'height': `${(dynamicDataCard.style && dynamicDataCard.style.height) || 'unset'}`}">
    <el-card class="box-card dynamic-data-info" v-if="dynamicDataCard.action === 'showtime' && $route.path.indexOf('gardener') < 0 ? show() : 1">
      <div slot="header" class="clearfix">
        <span>{{dynamicDataCard.title}}</span>
      </div>
      <el-form inline @submit.stop.prevent.native :label-width="(dynamicDataCard.style && dynamicDataCard.style.width) || ''" class="dynamic-data-form">
        <el-row v-for="(row, index) in dynamicDataCard.data" :key="index" :style="dynamicDataCard.style && dynamicDataCard.style[index]">
          <el-form-item
            v-for="(item, i) in row"
            :key="i"
            :label="`${item.label}：`"
            :class="{
							'text-area': item.type === 'textarea' || item.type === 'hit' ,
							'tip': item.type === 'textarea' || item.type === 'hit',
							'tag-item': item.type === 'tag' ,
							'multiple-img': item.type === 'multipleImg'
						}">

            <span class="value" v-if="item.type === 'extra'">{{ mappingKV(item.key) }}</span>

            <el-input
            v-else-if="item.type === 'textarea'"
            type="textarea"
            :rows="item.action && item.action === 'rows' ? item.action_params.rows : 2"
            :placeholder="mappingKV(item.key)"
            disabled>
            </el-input>

            <span class="value" v-else-if="item.type === 'datetime'">{{  mappingKV(item.key)}}</span>

            <div v-else-if="item.type === 'img'">
              <img
                :src="mappingKV(item.key)"
                class="cover"
                :style="{
                  'height': `${item.style.height}px`,
                  'width': `${item.style.width}px`
                }"
                @click="showPreivewImage(item.key, item.label, i)"
                v-if="mappingKV(item.key) && mappingKV(item.key).indexOf('transparent') < 0"
              />
              <div
                v-else
                class="cover nothing"
                :style="{
                  'height': `${item.style.height}px`,
                  'width': `${item.style.width}px`,
                  'line-height': `${item.style.height}px`
                }"
              >
                无预览
              </div>
              <ViewerBox
                :imgArray="imgObj[i]"
                :options="options"
                :viewerID="`coverViewBoxImages${i}`"
                @close="onClose"
              />
            </div>

            <div  v-else-if="item.type === 'multipleImg'&& mappingKV(item.key)">
              <div v-for="(pic, imgIndex) in imgs(item.key)" :key="imgIndex" style="display: inline-block">
                <img
                  :src="pic"
                  class="cover"
                  :style="{
                    'height': `${item.style.height}px`,
                    'width': `${item.style.width}px`
                  }"
                  @click="showMultiplePreivewImage(item.key, item.label, imgIndex, i)"
                  v-if="pic && pic.indexOf('transparent') < 0"
                />
                <div
                  v-else
                  class="cover nothing"
                  :style="{
                    'height': `${item.style.height}px`,
                    'width': `${item.style.width}px`,
                    'line-height': `${item.style.height}px`
                  }"
                >
                  无预览
                </div>
              </div>
              <ViewerBox
                :imgArray="multiImg[i]"
                :options="multiImgOptions[i]"
                :viewerID="`multipleCoverViewBoxImages${i}`"
                @close="onCloseMultiImg"
              />
            </div>

            <div
              v-else-if="item.type === 'multipleImg' && (!mappingKV(item.key) || mappingKV(item.key).indexOf('transparent') >= 0)"
              class="cover nothing"
              :style="{
                'display': 'inline-block',
                'height': `${item.style.height}px`,
                'width': `${item.style.width}px`,
                'line-height': `${item.style.height}px`
              }"
            >
              无预览
            </div>

						<div class="tags" v-else-if="item.type === 'tag'">
              <el-tag type="info" size="mini" v-for="(tag, index) in (Array.isArray(mappingKV(item.key)) ? mappingKV(item.key) : mappingKV(item.key).split(','))" :key="index">{{tag}}</el-tag>
            </div>

						<el-button  class="link-btn" type="text" v-else-if="item.type === 'link'" @click="goToNewPage(item.key, item.action, item.action_params)">{{mappingKV(item.key)}}</el-button>

            <p v-else-if="item.type === 'hit'" v-html="getHitContent(item.key)" class="hit el-textarea__inner"></p>
            <div v-if="item.type === 'text'">
              <span class="value" v-if="item.action === 'kv'">{{ handleValueChange(item) }}</span>
              <span class="value" v-else>{{ mappingKV(item.key) }}</span>
            </div>
          </el-form-item>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>
<script>
import { datetime } from '@/lib/formattime'
import { formatRegExp, escapeText } from '../../pages/contentAudit/common'
import { transformKey } from './common.js'
import ViewerBox from '@/components/ViewerBox'
import fetch from '@/lib/fetch'
import { genHost } from '@/api/utils'

export default {
  components: {
    ViewerBox
  },
  props: {
    dynamicDataCard: {
      type: Object,
      default: () => {
        return {}
      }
    },
    detail: {
      type: Object,
      default: () => {
        return {}
      }
    },
    extra: {
      type: Array,
      default: () => {
        return []
      }
    }
  },

  watch: {
    detail(val) {
      if (Object.keys(val).length) {
        this.fetchData()
      }
    }
  },

  data() {
    return {
      options: {
        initialViewIndex: 0,
        keyboard: true
      },
      imgObj: {},
      multiImg: {},
      multiImgOptions: {},
      data: {}
    }
  },
  mounted() {
  },

  methods: {
    fetchData() {
      const action = this.dynamicDataCard.action_params || {}
      const params = {}
      for (let key in action.params) {
        params[key] = this.getValue(action.params[key].value)
      }
      fetch({
        url: action.url,
        method: action.method.toLowerCase(),
        data: params
      }).then((res) => {
         this.data = res.data || {}
      }).catch(_ => {})
    },
    getValue(key) {
      return transformKey(key, this.detail)
    },
    mappingKV(key) {
      let val = null
      key.split('.').forEach((k, i) => {
        if (k) {
          if (!i) {
            val = this.data[k] === 0 ? 0 : (this.data[k] || '')
          } else {
            val = val[k] === 0 ? 0 : (val[k] || '')
          }
        }
      })
      return val
    },
    handleValueChange(item) {
      const key = +this.mappingKV(item.key)
      return item.action_params.enums[key]
    },
    extraValue(k) {
      let val = '暂无'
      const key = this.extraKey(k)
      if (this.detail.resource) {
        this.extra.forEach(i => {
          if (i.key === key) {
            val = (
              (i.enum.filter(e => {
                return e.value === this.detail.resource[key]
              })[0]) || {}
            ).label || '暂无'
          }
        })
      }
      return val
    },
    extraKey(val) {
      const extra = this.extra.filter(e => e.name === val)[0] || {}
      return extra.key
    },
    timeVal(label) {
      if (!this.detail.resource) {
        return '暂无'
      }
      const key = this.extraKey(label)
      return datetime(+this.detail.resource[key] * 1000)
    },
    getHitContent(key) {
      let hit = this.detail.hit || []
      let content = escapeText(this.getValue(key))
      if (hit) {
        hit.forEach(key => {
          let newStr = formatRegExp(key)
          let regexp = new RegExp(newStr, 'g')
          content = content.replace(regexp, `<em style="color:var(--red)">${key}</em>`)
        })
      }
      return content
    },
    showPreivewImage(key, label, vIndex) {
      this.imgObj = {}
      this.imgObj[vIndex] = [{
        src: this.mappingKV(key),
        name: label
      }]
    },
    showMultiplePreivewImage(key, label, index, vIndex) {
      const imgs = this.imgs(key) || []
      this.multiImgOptions[vIndex] = {
        initialViewIndex: index,
        keyboard: true
      }
      this.multiImg = {}
      this.multiImg[vIndex] = imgs.map(pic => {
        return {
          src: pic,
          name: label
        }
      })
    },
    onClose() {
      this.imgObj = {}
    },
    onCloseMultiImg() {
      this.multiImg = {}
    },
    show() {
      if (this.dynamicDataCard.action_params) {
        /* eslint-disable */
        const fn = new Function(this.dynamicDataCard.action_params.expression)
        return fn()
        /* eslint-enable */
      }
      return true
    },
		imgs(key) {
			return (this.mappingKV(key).split(',')) || []
		},
		goToNewPage(key, action, params) {
			const val = this.mappingKV(key)
			if (action === 'aid') {
        window.open(`${genHost()}/aegis/#/audit/tasks/detail/11?business_id=11&oid=${val}&list_type=00`)
			} else if (action === 'mid') {
        window.open(`https://space.bilibili.com/${val}`)
      } else if (action === 'custom') {
        const url = params.url.replace(/\$\{(.*?)\}/, val)
        window.open(url)
			} else {
        window.open(val)
      }
		}
  }
}
</script>
<style lang="stylus">
  .dynamic-data-card {
    .dynamic-data-info {
      height: 100%
      .el-card_body {
        height: calc(100% - 40px)
        overflow: auto
      }
    }
    .dynamic-data-form {
    }
    .cover {
      display: block
      margin-right: 8px
      margin-bottom 8px
    }
    .nothing {
      border: 1px solid #fbfbfb
      text-align: center
      font-size: 20px
      color: var(--text-color-light-2)
      font-weight: bold
      border-radiu: 4px
    }
    .text-area {
      display: flex
    }
		.tag-item {
      display: flex
			width: 100%
			.el-form-item__content {
				width 100%
			}
    }
		.multiple-img {
			display: flex
			width: 100%
			.el-form-item__content {
				display flex
				width 85%
				flex-wrap wrap
			}
		}
		.tags {
			width: calc(100% - 60px)
			height: 30px
			border: 1px solid var(--border-color-light-1)
			padding: 4px 0px 8px 13px
			border-radius: 5px
			overflow: auto
			line-height: 30px
			.el-tag {
				margin-right: 8px
			}
		}
    .link-btn {
      padding 8px !important
    }
  }
</style>
