<template>
  <div class="history-card"  :style="{'height': `${(historyCard.style && historyCard.style.height) || `unset`}`}">
    <el-card class="box-card history" :style="{'height': `${(historyCard.style && historyCard.style.height) || `200px`}`}">
      <div slot="header" class="clearfix">
        <span>审核历史</span>
      </div>
      <div>
        <p v-for="(item, index) in getHistory()" :key="index" v-html="item"></p>
      </div>
    </el-card>
  </div>
</template>
<script>
import { setHistoryColor } from '../../pages/contentAudit/common'

export default {
  props: {
    history: {
      type: Array,
      default: () => {
        return []
      }
    },
    historyCard: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {

    }
  },
  methods: {
    getHistory() {
      return setHistoryColor(this.history)
    }
  }
}
</script>
<style lang="stylus">
.history-card {
  .history {
    overflow: hidden
    .el-card__body {
      height: calc(100% - 70px)
      max-height: 220px
      overflow: auto
      div {
        color: var(--text-color-light-1)
        font-size: 14px
        p {
          padding: 0px 0px 8px
        }
      }
    }
  }
}
</style>
