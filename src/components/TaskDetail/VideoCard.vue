<template>
  <div class="video-card">
    <PlayerBox
      ref="playBox"
      :list="list"
      :hideList="card.hideList"
      :player="card.player"
      :url="url"
    ></PlayerBox>
  </div>
</template>
<script>
import PlayerBox from '../PlayerBox'
import { transformKey } from './common'
import { cardApi } from '@/api/index'
import fetch from '@/lib/fetch'
export default {
  props: {
    card: {
      type: Object,
      default: () => {
        return {}
      }
    },
    detail: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  components: {
    PlayerBox
  },

  data() {
    return {
      list: [],
      url: '',
	  rawDomain: ''
    }
  },
  watch: {
    'detail.resource': {
      handler(val) {
        if (this.card.action === 'direct') {
          this.url = this.getValue(this.card.key)
          return
        }
        if (this.card.player !== 'origin') {
          this.getPlayList()
        } else {
          this.fetchData()
        }
      },
      deep: true
    }
  },
  methods: {
    fetchData() {
      const action = this.card.action_params || {}
      const params = {}
      let canRequset = true
      for (const key in action.params) {
        params[key] = this.getValue(action.params[key].value)
        if (params[key] === '' || params[key] === undefined) {
          canRequset = false
        }
      }
      const method = action.method ? action.method.toLowerCase() : 'get'
      if (canRequset) {
        fetch({
          url: action.url,
          method,
          data: params
        })
          .then((res) => {
            this.url = transformKey(action.output_key, res.data) || ''
          })
          .catch((_) => {})
      }
    },
    getValue(key) {
      return transformKey(key, this.detail)
    },
    getPlayList() {
      const aid = transformKey(this.card.key, this.detail)
      if (!aid) {
        return
      }
      this.list = []
      cardApi
        .getVideoArchive({ aid })
        .then((res) => {
          ;(res.data.videos || []).forEach((v) => {
            const item = {
              aid: v.aid,
              cid: v.cid,
              title: v.title
            }
            this.list.push(item)
          })
          this.$refs.playBox.autoPlayFirst()
        })
        .catch((_) => {})
    }
  }
}
</script>
<style lang="stylus">
.video-card {
	height: 390px
	padding-top: 10px
}
</style>
