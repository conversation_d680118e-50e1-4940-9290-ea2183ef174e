  <template>
    <div class="advanced">
      <el-tabs v-model="curTab">
        <el-tab-pane label="高级操作" name="common">
          <el-form label-position="left" label-width="120px" size="small" :disabled="disabled">
            <el-form-item label="ip限制：">
              <IpLimit v-model="attrForm.policy_id" :policies="policies"></IpLimit>
            </el-form-item>
            <el-form-item label="高级禁止：">
              <AttrTable
                v-model="attrForm"
                :disabled="disabled"
              />
            </el-form-item>
            <el-form-item label="稿件属性：">
              <AttrList v-model="attrForm" class="attribute-comp-detail"/>
            </el-form-item>
            <el-form-item label="充电设置：">
              <Charge :aid="aid" :chargeState="chargeState"/>
            </el-form-item>
            <el-form-item label="稿件归属：">
              <Author :aid="aid" :mid="mid"/>
            </el-form-item>
            <el-form-item label="阅读权限：">
              <Read :access="access" :aid="aid"/>
            </el-form-item>
            <el-form-item label="用户组：">
              <UserGroup :stat="stat" />
            </el-form-item>
            <el-form-item label="其他设置：">
              <Others :attrForm="attrForm" />
            </el-form-item>
            <el-form-item label="OGV稿件跳转：">
              <OgvRedirect :aid="aid" :j.sync="attrForm.j" :redirectUrl.sync="attrForm.redirect_url" :disabled="disabled"/>
            </el-form-item>
            <el-form-item label="盗版稿件跳转：">
              <PiracyRedirect :aid="aid" :disabled="disabled"/>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane v-if="perms.ARC_UNRECOM_VIEW" label="推荐黑名单" name="rec_black">
          <div class="wrapper">
            <BlackList :aid="aid" :disabled="disabled" />
          </div>
        </el-tab-pane>
        <el-tab-pane label="合作信息管理" name="coop">
          <div class="wrapper">
            <CoopTab :aid="aid" :stat="stat" :disabled="disabled"/>
          </div>
        </el-tab-pane>
        <el-tab-pane label="争议标识管理" name="argue">
          <div class="wrapper">
            <ArgueTab :aid="aid" :stat="stat" :disabled="disabled"/>
          </div>
        </el-tab-pane>
      </el-tabs>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import IpLimit from '@/components/Ip/IpLimit'
import AttrTable from '@/v2/biz-components/archive/AttrTable.vue'
import AttrList from '@/v2/biz-components/archive/AttrList.vue'
import Charge from '@/components/Advanced/Charge'
import Author from '@/components/Advanced/Author'
import Read from '@/components/Advanced/Read'
import Others from '@/components/Advanced/Others'
import BlackList from '@/components/Advanced/BlackList'
import CoopTab from '@/components/Advanced/CoopTab'
import ArgueTab from '@/components/Advanced/ArgueTab'
import UserGroup from '@/components/TaskDetail/archive/UserGroup'
import PiracyRedirect from '@/components/Advanced/PiracyRedirect'
import OgvRedirect from '@/components/Advanced/OgvRedirect'
export default {
  components: {
    IpLimit,
    AttrTable,
    AttrList,
    Charge,
    Author,
    Read,
    Others,
    BlackList,
    CoopTab,
    ArgueTab,
    UserGroup,
    PiracyRedirect,
    OgvRedirect
  },
  props: {
    value: {
      type: Object,
      required: true
    },
    aid: {
      type: [String, Number],
      required: true
    },
    stat: {
      type: Object,
      default() {
        return {}
      }
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  provide() {
    return {
      perms: this.perms
    }
  },
  computed: {
    ...mapState({
      perms: state => state.user.perms
    }),
    mid() {
      return this.stat.mid || ''
    },
    attrForm: {
      get() {
        return this.value
      },
      set(newForm) {
        this.$emit('input', newForm)
      }
    }
  },
  data() {
    return {
      curTab: 'common',
      policies: [],
      access: '',
      chargeState: ''
    }
  },
  watch: {
    'stat.policies': {
      handler(val) {
        this.policies = val || []
      },
      immediate: true
    },
    'stat.access': {
      handler(val) {
        this.access = val
      },
      immediate: true
    },
    'stat.chargeState': {
      handler(val) {
        this.chargeState = val
      },
      immediate: true
    }
  }
}
</script>

<style lang="stylus">
.advanced
  .el-form-item
    margin-bottom 15px !important

</style>
<style lang="stylus" scoped>
.advanced
  padding 20px
  height calc(100% - 60px)
  width calc(100% - 20px)
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1)
  border-radius: 2px
  box-sizing: border-box
  background: var(--content-bg-color)
  overflow auto
  .wrapper
    background-color var(--content-bg-color)
    border-radius 3px
    margin-bottom 5px
    // box-shadow 0 1px 1px 0 rgba(0, 0, 0, .1)
    box-shadow initial !important
    padding 10px 0
    width calc(100% - 20px)
  .input
    display inline-block
    width 300px
    vertical-align middle
  .jumpCheckbox
    vertical-align middle
    line-height 35px
</style>
