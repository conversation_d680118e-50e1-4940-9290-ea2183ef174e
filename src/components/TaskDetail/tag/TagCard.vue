<template>
  <div class="tag-card">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>可选标签</span>
        <span>（{{selectedTags.length}} / {{tags.length}}）</span>
      </div>
      <div class="content">
        <TagElement v-for="(item) in tags" :key="item.label" :label="item.label" @click="handleClick(item)" :active="item.selected"></TagElement>
      </div>
    </el-card>
  </div>
</template>
<script>
import TagElement from './TagElement.vue'
import {createNamespacedHelpers} from 'vuex'
import notify from '@/lib/notify'

const moduleName = 'tags'
const { mapActions, mapState, mapGetters } = createNamespacedHelpers(moduleName)

export default {
  name: 'tag-card',
  components: {
    TagElement
  },
  props: {
    resource: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    ...mapState(['tags']),
    ...mapGetters(['getSelectedTags']),
    selectedTags() {
      return this.getSelectedTags()
    }
  },
  watch: {
    // resource: {
    resource: async function(nextVal) {
      let tagsArr = []
      if (nextVal && nextVal.metas && nextVal.metas && nextVal.metas.tag) {
        const tagsLabelArr = nextVal.metas.tag.split(',')
        tagsLabelArr.map((item) => {
          tagsArr.push({
            label: item,
            selected: false
          })
          return item
        })
        await this.fetchSelectedTag(+nextVal.oid).then((res) => {
          // 默认选中的tag
          const tags = (res && res.data && res.data.audit_tag) || ''
          const selectedTagsLabelArr = tags !== '' ? tags.split(',') : []
          selectedTagsLabelArr.map((item) => {
            const idx = tagsArr.findIndex((subItem) => subItem.label === item)
            if (!!~idx) {
              tagsArr[idx].selected = true
            }
          })
        }).catch((e) => {
          // -404是没有这条数据，即这个稿件未审核，那不用弹错误提示
          if (e.code !== -404) {
            notify.error(e.message || '获取审核tag失败', 1500)
          }
        })
        this.setTags(tagsArr)
      } else {
        this.setTags([])
      }
    }
  },
  destroyed() {
    this.setTags([])
  },
  data() {
    return {
      tagsArr: []
    }
  },
  methods: {
    ...mapActions([
      'setTags',
      'selectTags'
    ]),
    handleClick(label) {
      this.selectTags(label)
    },
    fetchSelectedTag(cid) {
      const url = '/x/internal/archive_category/view'
      return this.$ajax.get(url, {
        params: {
          cid: cid
        },
        silent: true
      })
    }
  }
}
</script>
<style lang="stylus">
.tag-card
  height 100%
  overflow hidden
  .box-card
    height 100%
    box-sizing border-box
</style>
