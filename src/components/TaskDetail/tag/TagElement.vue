<template>
  <div class="tag-element" @click="$emit('click')" :class="[{
    'active': active
  }]">
    {{label}}
  </div>
</template>
<script>
export default {
  name: 'tag-element',
  props: {
    label: {
      type: String,
      default: 'TAG'
    },
    active: {
      type: Boolean,
      default: false
    }
  }
}
</script>
<style lang="stylus" scoped>
.tag-element
  padding 20px 35px
  display flex
  color var(--text-color-dark-2)
  border 1px solid #e4e4e4
  box-sizing border-box
  border-radius 4px
  font-size 14px
  float left
  margin-right 20px
  margin-bottom 20px
  cursor pointer
  &.active
    background #3489FF
    border 1px solid #3489FF
    color var(--text-color-reverse)
</style>
