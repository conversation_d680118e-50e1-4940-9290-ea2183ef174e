<template>
  <div class="empty-detail">
    <div class="main">
      <p class="tip animated swing">{{tip}}</p>
      <div v-if="!noOperation">
        <el-button type="primary" @click="getTask" :disabled="disabled">刷新</el-button>
        <el-button type="primary" @click="quitOrDelay" plain :disabled="disabled">退出</el-button>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {

    }
  },
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    noOperation: {
      type: Boolean,
      default: false
    },
    tip: {
      type: String,
      default: '任务清空了~'
    }
  },
  methods: {
    getTask() {
      this.$emit('getTask')
    },
    quitOrDelay() {
      this.$emit('quitOrDelay', true)
    }
  }
}
</script>
<style lang="stylus" scoped>
.empty-detail {
  position: relative
  z-index: 1
  height: 100vh
  display: flex
  justify-content:center
  align-items:center
  background-image: url('~@/assets/no_task_page.png')
  background-color: var(--white)
  background-position: center
  background-repeat: no-repeat
  background-size: contain
  .main {
    margin-top: 260px
    .tip {
      text-align: center
      font-size: 24px
      font-weight: 600
      color: var(--primary-color)
    }
    div {
      margin-top: 24px
    }
  }
}
</style>
