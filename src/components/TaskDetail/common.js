export function firstUpperCase(char) {
  let arr = char.split('')
  let firstChar = arr[0].toUpperCase()
  arr.shift()
  return `${firstChar}${arr.join('')}`
}
export function transformKey(key, obj) {
  let val = null
  if (key) {
    key.split('.').forEach((k, i) => {
      if (k) {
        if (!i) {
          val = obj[k] === 0 ? 0 : (obj[k] || '')
        } else {
          val = val[k] === 0 ? 0 : (val[k] || '')
        }
      }
    })
    return val
  }
  return ''
}
