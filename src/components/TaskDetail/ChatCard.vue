<template>
  <div class="chat-card" :style="{'height': `${(cardInfo.style && cardInfo.style.height) || 'unset'}`}">
    <el-card class="box-card chat-info">
      <div slot="header" class="clearfix">
        <span>{{cardInfo.title}}</span>
      </div>
        <IM
          :msgs="msgs"
          :noMore="noMore"
          @load-more="getNextPageMsg"
        />
    </el-card>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import IM from '@/components/IM/IM'
import { chatApi } from '@/api'

export default {
  name: 'ChatCard',
  inheritAttrs: false,
  components: {
    IM
  },
  props: {
    cardInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
    detail: {
      type: Object,
      default: () => {
        return {}
      }
    },
    extra: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      sessionId: '',
      sessionType: '',
      msgs: [],
      nextOffset: -1,
      inited: true
    }
  },
  watch: {
    detail(nextVal) {
      if (nextVal.resource && nextVal.resource.oid) {
        // 赋值参数
        this.sessionId = nextVal.resource.oid
        const metas = nextVal.resource.metas || {}
        this.sessionType = metas.receiver_type || 1
        this.getFirstMsg()
      }
    }
  },
  computed: {
    ...mapGetters({
      getEnv: 'env/getEnv'
    }),
    noMore() {
      return !this.inited && this.nextOffset === 0
    }
  },
  methods: {
    async fetchAuditMsg(otherParams = {}) {
      const params = {
        session_id: this.sessionId,
        session_type: this.sessionType,
        size: 20,
        ...otherParams
      }
      let data = {}
      await chatApi.getAuditMsg(params).then((res) => {
        data = res.data
      }).catch(_ => {})

      const { msgs, next_offset: nextOffset, _gt_: gt } = data
      const msgData = (msgs && msgs.map((msg) => {
        return {
          ...msg,
          content: JSON.parse(msg.content),
          face: msg.sender_face,
          uname: msg.sender_name
        }
      })) || []
      return {
        msgs: msgData,
        nextOffset: Object.keys(otherParams).length > 0 ? 0 : nextOffset,
        gt
      }
    },
    // 重置数据
    reset() {
      this.msgs = []
      this.nextOffset = 0
    },
    // 获取首页数据
    async getFirstMsg(params) {
      this.reset()
      const { msgs, nextOffset } = await this.fetchAuditMsg(params)
      this.msgs = msgs
      this.nextOffset = nextOffset
      this.inited = false
    },
    // 获取下一页数据
    async getNextPageMsg(params) {
      // 如果还有数据
      if (!this.noMore) {
        const { msgs, nextOffset } = await this.fetchAuditMsg({
          offset: this.nextOffset
        })
        this.msgs = this.msgs.concat(msgs)
        this.nextOffset = nextOffset
      }
    }
  },
  created() {
  }
}
</script>
<style lang="stylus" scoped>
.chat-card
  height 100% !important
  .chat-info
    height 100%
    .el-card_body
      height calc(100% - 40px)
      overflow auto
</style>
