<template>
  <div class="task-detail-template">
    <el-row type="flex" justify="space-between" class="base-row">
      <div class="base-div">
        <div class="default-base-info">
          <span
            class="task-info"
            v-if="+$route.query.type === 0 || +$route.query.type === 1"
          >
            任务ID：<em>暂无</em>
          </span>
          <span
            class="task-info"
            v-if="page.header"
          >
            {{page.header.label}}：<em>暂无</em>
          </span>
          <span
            class="task-info"
            v-if="+$route.query.type === 0 || +$route.query.type === 1"
          >
            任务耗时：<em>未开始</em>
          </span>
          <span
            class="task-info delay-num"
            v-if="+$route.query.type === 0 || +$route.query.type === 1"
          >
            已延迟任务数：<em>0个</em>
          </span>

          <el-button
            plain
            type="info"
            class="delay-btn"
            v-if="+$route.query.type === 0 || +$route.query.type === 1"
          >
            延迟
          </el-button>
          <el-button
            plain
            type="info"
            v-if="page.use_reset_btn"
            :class="{'reset-btn': $route.query.type === 1}"
          >
            重置操作
          </el-button>
          <el-button
            plain
            type="info"
            v-if="page.use_track_btn"
            class="track"
          >
            信息追踪
          </el-button>
        </div>
      </div>
      <el-button type="info" plain>退出</el-button>
    </el-row>

    <el-row type="flex" class="detail-row " v-if="page.content && page.content[0].columns">
       <el-col class="detail-col" :span="+page.content[0].style.width || 12">
        <div v-for="(card, key) in page.content[0].columns.data[0]" :key="key">
          <component
            :ref="card.type"
            :is="getCurrentComponent(card.type)"
            :normalCard="card"
            :historyCard="card"
            :operationCard="card"
            :dynamicDataCard="card"
            :useForbidBtn="card.use_forbid_btn"
            :mangaCard="card"
          ></component>
        </div>
       </el-col>

       <el-col class="detail-col" :span="+page.content[1].style.width || 12" v-if="page.layout === 'layout1'">
         <div v-for="(card, key) in page.content[1].columns.data[0]" :key="key" style="height: 100%">
            <component
              :ref="card.type"
              :is="getCurrentComponent(card.type)"
              :normalCard="card"
              :historyCard="card"
              :operationCard="card"
              :dynamicDataCard="card"
              :mangaCard="card"
              :useForbidBtn="card.use_forbid_btn"
            ></component>
         </div>
       </el-col>

       <el-col v-else class="detail-col" :span="+page.content[1].style.width || 12">
         <el-row
          type="flex"
          class="right-top-row"
          v-for="(row, key) in page.content[1].rows.data"
          :key="key"
          :style="page.content[1].rows.style[key] || {
            height: '100%',
            width: '100%'
          }">
            <el-col
              v-for="(card, subKey) in row"
              :key="subKey"
              :style="(page.content[1].rows.style[key].child && page.content[1].rows.style[key].child[subKey]) || {
                height: '100%',
                width: '100%'
              }">
             <component
              :ref="card.type"
              :is="getCurrentComponent(card.type)"
              :normalCard="card"
              :historyCard="card"
              :operationCard="card"
						  :videoCard="card"
              :pictureCard="card"
              :dynamicDataCard="card"
              :tagCard="card"
              :mangaCard="card"
              :useForbidBtn="card.use_forbid_btn"
            ></component>
           </el-col>
         </el-row>
       </el-col>
    </el-row>
  </div>
</template>
<script>
import HistoryCard from './HistoryCard'
import OperationsCard from './OperationsCard'
import NormalCard from './NormalCard'
import DynamicCard from './dynamic/DynamicCard'
import MangaCard from './manga/MangaCard'
import ComicCard from './comic/ComicCard'
import WikiCard from './comic/WikiCard'
import PicturesCard from './comic/PicturesCard'
import CooperationInfoCard from './staff/CooperationInfoCard'
import DynamicDataCard from './DynamicDataCard'
import VideoCard from './VideoCard'
import TagCard from './tag/TagCard.vue'
import MediaCard from './media/MediaCard.vue'

export default {
  props: {
    pageData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  components: {
    HistoryCard,
    OperationsCard,
    DynamicCard,
    NormalCard,
    ComicCard,
    WikiCard,
		PicturesCard,
		CooperationInfoCard,
    VideoCard,
    TagCard,
    MediaCard,
    MangaCard
  },
  data () {
    return {
    }
  },

  computed: {
    page() {
      return this.pageData
    }
  },

  created() {
  },

  watch: {
  },

  mounted() {
  },

  methods: {
    getCurrentComponent(type) {
      let component = null
      switch (type) {
        case 'operation':
          component = OperationsCard
          break
        case 'history':
          component = HistoryCard
          break
        case 'comic':
          component = ComicCard
          break
        case 'wiki':
          component = WikiCard
          break
        case 'dynamic':
          component = DynamicCard
          break
        case 'comic_picture_box':
          component = PicturesCard
					break
				case 'staff_cooperation':
					component = CooperationInfoCard
					break
				case 'video':
					component = VideoCard
          break
        case 'dynamic_data':
					component = DynamicDataCard
          break
        case 'tag_card':
          component = TagCard
          break
        case 'manga':
          component = MangaCard
          break
        case 'media':
          component = MediaCard
          break
        default:
          component = NormalCard
          break
      }
      return component
    }
    // calRightRowHeight(index) {
    //   let height = 0
    //   this.pageData1.content[1].data.forEach((d, i) => {
    //     if (i <= index) {
    //       height += d.style.height
    //     }
    //   })
    //   if (index === 0) {
    //     return height
    //   } else {
    //     return `calc(100% - ${height})`
    //   }
    // }
  }
}
</script>
<style lang="stylus">
.task-detail-template {
  height: calc(100vh - 40px) !important
  width: 100%
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1)
  border-radius: 2px
  box-sizing: border-box
  background: var(--content-bg-color)
  .base-row {
    height: 60px
    padding: 10px 18px
    font-size: 15px
    z-index: 20
    lin-height: 3
    box-shadow: 0 2px 4px 0 rgba(0,0,0,.1)
    .base-div {
      display:flex
      .default-base-info {
        line-height: 2.6
        .el-button {
          vertical-align: top
        }
        .task-info {
          color: var(--text-color-dark-1)
          margin-right: 10px
          em {
            color: var(--text-color)
            font-weight: bold
          }
        }
        .delay-num > em {
          color: var(--red)
        }
      }
    }
  }
  .el-card {
    color: var(--text-color)
    box-shadow: none
    margin-bottom: 8px
    margin-right: 8px
    position: relative
    .el-card__header {
      padding: 12px
      font-size: 15px
      font-weight: 500
    }
    .el-card__body {
      font-size: 14px
      padding: 12px
      height: calc(100% - 70px)
      overflow: auto
    }
    .el-form > .el-form-item {
      margin-bottom 0px
    }
    .el-form > .el-row > .el-form-item {
      margin-bottom 0px
    }
  }
  .detail-row {
    height: calc(100% - 60px)
    padding: 18px 10px 18px 18px
    overflow: auto
    z-index: 10
    .detail-col {
      height: 100%
      overflow: auto
      &::-webkit-scrollbar {
        display: none
      }
    }
  }
  .text-area {
    width: 100%
    margin-bottom: 18px !important
  }
  .tip {
    .el-form-item__content {
      width: calc(100% - 97px)
    }
  }
  .value {
    color: var(--grey-light-1)
  }
  .blue-fans-font {
    color: var(--blue)
    font-weight: bold
  }
  .red-fans-font {
    color: var(--red)
    font-weight: bold
  }
	.hit {
    height: 70px
    overflow: auto
    background: var(--bg-color)
    border-color: var(--border-color-light-1)
    color: var(--text-color-light-2)
    cursor: not-allowed
  }
  .track > span > a {
    color: var(--text-color)
  }
  .right-top-row {
    margin-bottom: 10px
    &:last-child {
      margin-bottom: 0px
    }
  }
  a {
    color: var(--link-color)
    font-weight: bold
  }
}
</style>
