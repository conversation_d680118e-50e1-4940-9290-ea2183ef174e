<template>
  <div class="empty-detail">
    <div class="main">
      <p class="tip animated swing">加载数据有误~</p>
      <div>
        <el-button type="primary" @click="refresh">刷新</el-button>
        <el-button type="primary" @click="goBack" plain>返回</el-button>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {

    }
  },
  methods: {
    refresh() {
      window.location.reload(true)
    },
    goBack() {
      this.$emit('back')
    }
  }
}
</script>
<style lang="stylus" scoped>
.empty-detail {
  height: 100vh
  display: flex
  justify-content:center
  align-items:center
  background-image: url('~@/assets/no_task_page.png')
  background-position: center
  background-repeat: no-repeat
  background-size: contain
  .main {
    margin-top: 260px
    .tip {
      text-align: center
      font-size: 24px
      font-weight: 600
      color: var(--primary-color)
    }
    div {
      margin-top: 24px
    }
  }
}
</style>
