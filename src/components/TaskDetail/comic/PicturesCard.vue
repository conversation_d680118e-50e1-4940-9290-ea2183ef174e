<template>
  <div class="pictures-card" :style="{'height': `${pictureCard.style && pictureCard.style.height}`}">
    <el-card class="box-card extra-info" style="height: 100%;margin-bottom: 0px">
      <div slot="header" class="clearfix extra-header">
        <span class="extra-info-span">其它信息</span>
        <div>
          <span class="checked-num">已选择  <em>{{checkedPicsLength + ' / '}}</em><em>{{ pictures.length }}</em></span>
          <el-button size="small" type="primary" @click="changeChecked">{{ checkedBtn ? '全选' : '取消' }}</el-button>
        </div>
      </div>
      <div class="pics-wrapper">
        <PictureBox ref="pictureBox" :pictures="pictures" @getCheckedPicsLength="getCheckedPicsLength"></PictureBox>
      </div>
    </el-card>
  </div>
</template>
<script>
import store from '@/store'
import { Indexer } from '@bilibili-firebird/lib.indexer'
import PictureBox from '@/components/PictureBox'
import fetch from '@/lib/fetch'

export default {
  components: {
    PictureBox
  },
  props: {
		pictureCard: {
			type: Object,
			default: () => {
        return {}
      }
		},
    detail: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      checkedPicsLength: 0,
      checkedBtn: true,
      action: {},
      pictures: [],
      checkedText: '全选'
    }
  },
  watch: {
    detail() {
      this.getComicPics()
    }
  },
  methods: {
    getComicPics() {
      const resource = this.detail.resource
      if (resource) {
        /* eslint-disable */
        const { epid, comic_id, index } = resource.metas
        const indexInfo = {
          epid,
          comic_id,
          index
        }
        /* eslint-enable */
        this.$nextTick(() => {
          const action = (this.detail.actions || []).filter(a => a.name.indexOf('其他信息') > -1)[0] || {}
          this.getIndexToken(action, indexInfo, true)
        })
      }
    },
    getIndexToken(action, indexInfo, isInit) {
      const method = action.method && action.method.toLowerCase()
      this.action = action
      if (method) {
        return fetch({
          url: action.url,
          method: method.toLowerCase(),
          data: {
            urls: isInit ? `[${JSON.stringify(indexInfo.index)}]` : indexInfo
          }
        }).then((res) => {
          if (res.data) {
            if (isInit) {
              this.getIndex(res.data[0], indexInfo)
            }
            return Promise.resolve(res)
          } else {
            return Promise.resolve({data: []})
          }
        }).catch(_ => {})
      }
    },
    getIndex(data, indexInfo) {
      const url = `${data.url}?token=${data.token}`
      this.$ajax.get(url, {
        responseType: 'arraybuffer',
        handle: true
      }).then(res => {
        if (res) {
          const uint8Array = new Uint8Array(res)
          const indexer = new Indexer({
            seasonId: +indexInfo.comic_id,
            episodeId: +indexInfo.epid,
            indexData: uint8Array
          })
          indexer.decode().then(() => {
            let obj = JSON.parse(indexer.decodedData) || {}
            const jsonPic = `${JSON.stringify(obj.pics)}`
            let newSmallPicTokens = obj.pics.map(i => { return i + '@170w_133h.webp' })
            const jsonSmallPic = `${JSON.stringify(newSmallPicTokens)}`
            this.getIndexToken(this.action, jsonSmallPic, false).then(res => {
              let array = [];
              (res.data || []).forEach((d, i) => {
                array.push({
                  minSrc: `${d.url}?token=${d.token}`,
                  index: i
                })
              })
              this.pictures = [...array]
              this.getIndexToken(this.action, jsonPic, false).then(res => {
                this.setPicturs(res.data)
              })
            })
          })
        }
      })
    },
    setPicturs(data) {
      (data || []).forEach((d, i) => {
        this.$set(this.pictures, i, {
          ...this.pictures[i],
          maxSrc: `${d.url}?token=${d.token}`
        })
      })
      store.dispatch('pictures/allPictures', this.pictures)
      this.preLoadPictures(this.pictures)
    },
    preLoadPictures(pics) {
      pics.forEach(p => {
        let Img = new Image()
        Img.src = p.maxSrc
      })
    },
    changeChecked() {
      this.checkedBtn = !this.checkedBtn
      this.checkedAll()
    },
    checkedAll(isNewTask = false) {
      isNewTask
      ? this.$refs.pictureBox.checkedAll(isNewTask)
      : this.$refs.pictureBox.checkedAll(this.checkedBtn)
    },
    getCheckedPicsLength (checkedPics) {
      this.checkedPicsLength = checkedPics.length
    }
  }
}
</script>
<style lang="stylus">
.pictures-card {
  height: 100%
  .extra-info {
    .el-card__body {
      height: calc(100% - 56px)
      overflow: auto
      box-sizing: border-box
    }
    .pics-wrapper {
      width: 100%
      margin: 12px 0px 0px
    }
  }
  .extra-header {
    display: flex
    justify-content: space-between
    .extra-info-span {
      line-height: 2
    }
  }
  .checked-num {
    display: inline-block
    margin-right: 8px
    font-size: 14px
    color: var(--text-color-dark-1)
    em {
      font-weight: bold
    }
  }
}
</style>
