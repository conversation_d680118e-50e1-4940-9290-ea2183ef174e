<template>
  <div class="wiki-card">
    <el-card class="box-card wiki-info">
      <div slot="header" class="clearfix">
        <span>关联百科</span>
      </div>
      <el-form inline @submit.stop.prevent.native>
        <el-form-item label="百科ID：">
          <span class="value">{{ wikiInfo.info_id || '无' }}</span>
        </el-form-item>
        <el-form-item label="原名：">
          <span class="value">{{  wikiInfo.o_name || '无' }}</span>
        </el-form-item>
        <el-form-item label="百科创建人：">
          <span class="value">{{  wikiInfo.cuser || '无' }}</span>
        </el-form-item>
        <el-form-item label="中文名">
          <span class="value">{{  wikiInfo.c_name || '无' }}</span>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>
<script>
import fetch from '@/lib/fetch'
export default {
  props: {
    detail: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      wikiInfo: {}
    }
  },
  watch: {
    detail(val) {
      val.actions && this.getWikiInfo()
    }
  },
  methods: {
    getWikiInfo() {
      const action = this.detail.actions.filter(a => { return a.name.indexOf('关联百科') > -1 })[0] || {}
      fetch({
        url: action.url,
        method: action.method.toLowerCase(),
        data: {
          oid: this.detail.resource.oid
        }
      }).then((res) => {
        this.wikiInfo = res.data
      }).catch(_ => {})
    }
  }
}
</script>
<style lang="stylus">
  .wiki-card {
    span {
      font-size: 14px
      color: var(--text-color)
      em {
          color: var(--grey-light-1)
        }
    }
  }
</style>
