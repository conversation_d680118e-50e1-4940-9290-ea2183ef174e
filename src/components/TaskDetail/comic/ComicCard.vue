<template>
  <div class="comic-card">
    <el-card class="box-card comic-info">
      <div slot="header" class="clearfix">
        <span>关联漫画作品</span>
      </div>
      <div class="comic-info">
        <span>漫画ID：<em>{{ comicData && comicData.cid }}</em></span>
        <span>漫画标题：<em>{{ comicData && comicData.title}}</em></span>
      </div>
      <el-table
      :data="tableDataShow"
      stripe
      border
      height="202"
      style="width: 100%;margin-top: 8px">
        <el-table-column
          align="center"
          prop="id">
          <template v-slot="scope">
            <router-link
            target="_blank"
            :to="{path:`/audit/list/picture?oid=${scope.row.id}&businessId=${$route.query.business_id}&isResource=${1}`}">
              {{scope.row.id}}
            </router-link>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="title">
        </el-table-column>
      </el-table>

      <Pagination
      class="pagination"
      :pager="pager"
      @getList="getList"
      :computedPager="true"
      :tableData="comicData && comicData.ep_list"
      showSize="small">
      </Pagination>
    </el-card>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination.vue'
import fetch from '@/lib/fetch'

export default {
  components: {
    Pagination
  },
  props: {
    detail: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      comicData: [],
      pager: {
        ps: 10,
        pn: 1,
        total: 0
      }
    }
  },
  computed: {
    tableDataShow() {
      if (this.comicData && this.comicData.ep_list) {
        return [...this.comicData.ep_list].splice((this.pager.pn - 1) * this.pager.ps, this.pager.ps)
      }
      return []
    }
  },
  watch: {
    detail(val) {
      val.actions && this.getRelatedComic()
    }
  },
  methods: {
    getRelatedComic() {
      const action = this.detail.actions.filter(a => { return a.name.indexOf('关联漫画') > -1 })[0] || {}
      fetch({
        url: action.url,
        method: action.method.toLowerCase(),
        data: {
          oid: this.detail.resource.oid
        }
      }).then((res) => {
        this.comicData = res.data || []
      }).catch(_ => {})
    },
    getList() {
      this.$emit('getTask')
    }
  }
}
</script>
<style lang="stylus">
.comic-card {
  .comic-info {
    span {
      font-size: 14px
      color: var(--text-color)
      em {
          color: var(--grey-light-1)
        }
    }
    .pagination {
      margin-top: 8px
      display: flex
      justify-content: center
    }
  }
}
</style>
