<template>
  <div class="bgm-card">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>其它音频信息</span>
      </div>
      <audio-box @ready="auditBoxReady" @error="resourceError"></audio-box>
      <el-row class="info" type="flex">
        <el-col :span="12" class="left-info">
          <el-row class="info-row">
            <p><label>音频分类：</label>{{type}}</p>
          </el-row>
          <el-row class="info-row">
            <p><label>歌手：</label>{{songInfo.singer || noData}}</p>
            <p><label>编曲：</label>{{songInfo.arrangement || noData}}</p>
            <p><label>混音/后期：</label>{{songInfo.audio_mixing || noData}}</p>
          </el-row>
          <el-row class="info-row">
            <p><label>作曲：</label>{{songInfo.composition || noData}}</p>
            <p><label>本家作者：</label>{{creatorInfo.origin_author || noData}}</p>
            <p><label>原曲名：</label>{{creatorInfo.origin_song_name || noData}}</p>
          </el-row>
          <el-row class="info-row">
            <p><label>作词：</label>{{songInfo.author || noData}}</p>
            <p><label>本家地址：</label>{{creatorInfo.origin_url || noData}}</p>
          </el-row>
          <el-row class="cover-row">
            <span>音乐封面：</span>
            <div class="cover nothing" v-if="!cover">无预览</div>
            <img v-else :src="cover" class="cover" @click="showPreivewImage"/>
            <ViewerBox :imgArray="imgArray" :options="options" viewerID="coverViewBoxImages" @close="onClose"/>
          </el-row>
        </el-col>
        <el-col :span="12" class="right-info">
          <p>歌词信息：</p>
          <el-input type="textarea" placeholder="暂无" v-model="lyric" maxlength="30" :rows="23"></el-input>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>
<script>
import AudioBox from '@/components/AudioBox.vue'
import notify from '@/lib/notify.js'
import ViewerBox from '@/components/ViewerBox'
import { bgmApi } from '@/api/index'
import axios from 'axios'

export default {
  components: {
    AudioBox,
    ViewerBox
  },
  props: {
    resource: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      auditBoxObj: {},
      creatorInfo: {},
      songInfo: {},
      noData: '暂无',
      cover: '',
      options: {
        initialViewIndex: 0,
        keyboard: true
      },
      imgArray: [],
      oid: '',
      playUrl: '',
      creativeType: [],
      styleType: [],
      langType: [],
      lyric: ''
    }
  },
  watch: {
    resource(val) {
      if (val) {
        const metas = val.metas || {}
        this.creatorInfo = (metas.creator_info && JSON.parse(metas.creator_info)) || {}
        this.songInfo = (metas.song_info && JSON.parse(metas.song_info)) || {}
        this.cover = val.extra2s
        this.oid = val.oid
        this.getLyric(val.extra3s)
        this.getBgmResource()
      }
    }
  },
  computed: {
    type() {
      const cType = (this.creativeType.find(t => t.id === this.resource.extra3) || {}).name
      const lType = (this.langType.find(t => t.id === this.resource.extra2) || {}).name
      const sType = (this.styleType.find(t => t.id === this.resource.extra1) || {}).name
      return `${cType || ''} ${lType || ''} ${sType || ''}`
    }
  },
  created() {
    this.getBgmStyle()
  },
  methods: {
    getLyric(path) {
      if (!path) return
      axios.get(path).then(res => {
        this.lyric = res.data || ''
      })
    },
    getBgmResource() {
      if (!this.oid) {
        return
      }
      bgmApi.getBgmResource({
        audio_id: this.oid
      }).then(res => {
        const data = res.data
        if (data) {
          if (!data.play_url) {
            return notify.error('获取歌曲播放地址错误')
          }
          this.playUrl = data.play_url || ''
          this.auditBoxObj.loadList([
            {
              src: this.playUrl
            }
          ])
        }
      })
    },
    getBgmStyle() {
      bgmApi.getBgmStyle().then(res => {
        const data = res.data || {}
        this.creativeType = data.creative_type || []
        this.langType = data.lang_type || []
        this.styleType = data.style_type || []
      })
    },
    auditBoxReady(auditBox) {
      this.auditBoxObj = auditBox
    },
    resourceError() {
      notify.error('读取远程音频文件失败')
    },
    showPreivewImage() {
      this.imgArray = [{
        src: this.cover,
        name: '封面'
      }]
    },
    onClose() {
			this.imgArray = []
    }
  }
}
</script>
<style lang="stylus" scoped>
.bgm-card {
  height: 100%
  .info {
    margin-top: 50px
    font-size: 16px
    .left-info {
      margin-right 10px
    }
    .right-info {
      p {
        color var(--text-color-light-1)
        padding-bottom 10px
      }
    }
    .info-row {
      display flex
      p {
        padding-bottom: 50px
      }
      p > label {
        padding-left 50px
        color var(--label-color)
      }
    }
  }
  .cover-row {
    span {
      padding-left 50px
      color var(--text-color-light-1)
    }
  }
  .cover {
    height: 134px
    width: 234px
    display inline-block
  }
  .nothing {
    border: 1px solid #fbfbfb
    text-align: center
    line-height: 132px
    font-size: 20px
    color: var(--text-color-light-2)
    font-weight: bold
    border-radius: 4px
  }
}
</style>
