<template>
  <div class="task-detail-template">
    <el-row
      type="flex"
      justify="space-between"
      class="base-row"
      v-if="!isBlank"
    >
      <div class="base-div">
        <div class="default-base-info">
          <span class="task-info" v-if="isTask">
            任务ID：<em>{{ task.id }}</em>
          </span>
          <span class="task-info" v-if="page.header">
            {{ page.header.label }}：<em>{{ getValue(page.header.key) }}</em>
          </span>
          <span class="task-info" v-if="isTask">
            任务耗时：<em>{{ result || '未开始' }}</em>
          </span>
          <span class="task-info delay-num" v-if="isTask">
            已延迟任务数：<em>{{ (delayTasks || 0) + '个' }}</em>
          </span>

          <el-button
            plain
            type="info"
            v-if="!isDelay && isTask"
            class="delay-btn"
            @click="quitOrDelay(false)"
          >
            延迟
          </el-button>
          <el-button
            plain
            type="info"
            v-if="page.use_reset_btn"
            :class="{ 'reset-btn': this.isDelay }"
            @click="reset"
          >
            重置操作
          </el-button>
          <el-button plain type="info" v-if="page.use_track_btn" class="track">
            <router-link
              target="_blank"
              :to="{
                path: `/v3/audit/track?oid=${
                  trackQuery.resource.oid
                }&id=${(trackQuery.task && trackQuery.task.id) ||
                  '暂无'}&business_id=${trackQuery.resource.business_id}`
              }"
            >
              信息追踪
            </router-link>
          </el-button>
        </div>
      </div>
      <el-button type="info" plain @click="quitOrDelay(true)">退出</el-button>
    </el-row>

    <el-row
      type="flex"
      class="detail-row "
      v-if="
        !isBlank &&
          Object.values(page).length &&
          page.content &&
          page.content[0].columns
      "
    >
      <el-col class="detail-col" :span="+page.content[0].style.width || 12">
        <div v-for="(card, key) in page.content[0].columns.data[0]" :key="key">
          <component
            :ref="card.type"
            :is="getCurrentComponent(card.type)"
            :ops="ops"
            :extra="extra"
            :detail="detail"
            :normalCard="card"
            :cardInfo="card"
            :historyCard="card"
            :operationCard="card"
            :dynamicDataCard="card"
            :mangaCard="card"
            :resource="resource"
            :codeTimer="codeTimer"
            :history="detail.historys"
            :useForbidBtn="card.use_forbid_btn"
            :staffTableData="staffTableData"
            @quitOrDelay="quitOrDelay"
            @getTask="getDelayOrOrderList"
          ></component>
        </div>
      </el-col>

      <el-col
        class="detail-col"
        :span="+page.content[1].style.width || 12"
        v-if="page.layout === 'layout1'"
      >
        <div
          v-for="(card, key) in page.content[1].columns.data[0]"
          :key="key"
          style="height: 100%"
        >
          <component
            :ref="card.type"
            :is="getCurrentComponent(card.type)"
            :ops="ops"
            :extra="extra"
            :detail="detail"
            :cardInfo="card"
            :normalCard="card"
            :historyCard="card"
            :operationCard="card"
            :dynamicDataCard="card"
            :mangaCard="card"
            :resource="resource"
            :codeTimer="codeTimer"
            :history="detail.historys"
            :staffTableData="staffTableData"
            :useForbidBtn="card.use_forbid_btn"
            @quitOrDelay="quitOrDelay"
            @getTask="getDelayOrOrderList"
          ></component>
        </div>
      </el-col>

      <el-col
        v-else
        class="detail-col"
        :span="+page.content[1].style.width || 12"
      >
        <el-row
          type="flex"
          class="right-top-row"
          v-for="(row, key) in page.content[1].rows.data"
          :key="key"
          :style="
            page.content[1].rows.style[key] || {
              height: '100%',
              width: '100%'
            }
          "
        >
          <el-col
            v-for="(card, subKey) in row"
            :key="subKey"
            :style="
              (page.content[1].rows.style[key].child &&
                page.content[1].rows.style[key].child[subKey]) || {
                height: '100%',
                width: '100%'
              }
            "
          >
            <component
              :ref="card.type"
              :is="getCurrentComponent(card.type)"
              :ops="ops"
              :extra="extra"
              :detail="detail"
              :cardInfo="card"
              :normalCard="card"
              :historyCard="card"
              :operationCard="card"
              :dynamicDataCard="card"
              :pictureCard="card"
              :card="card"
              :resource="resource"
              :codeTimer="codeTimer"
              :history="detail.historys"
              :staffTableData="staffTableData"
              :useForbidBtn="card.use_forbid_btn"
              :mangaCard="card"
              @quitOrDelay="quitOrDelay"
              @getStaffTableData="getStaffTableData"
              @getTask="getDelayOrOrderList"
            ></component>
          </el-col>
        </el-row>
      </el-col>
    </el-row>

    <TaskQuitDialog
      v-if="$route.query.isTask"
      ref="taskQuitDialog"
      @getTask="getDelayOrOrderList"
      @goBack="goBack"
      :tableData="tableData"
      :task="task"
    >
    </TaskQuitDialog>

    <EmptyDetail
      v-if="isBlank"
      @quitOrDelay="quitOrDelay"
      @getTask="getTask"
    ></EmptyDetail>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { hhmmssS } from '@/lib/formattime'
import { taskApi, resourceApi, templateApi } from '@/api/index'
import { transformKey } from './common.js'
import { goTaskList, getExtra } from '../../pages/contentAudit/common'
import TaskQuitDialog from '../../pages/contentAudit/task/TaskQuitDialog'
import EmptyDetail from './EmptyDetail'
import HistoryCard from './HistoryCard'
import OperationsCard from './OperationsCard'
import NormalCard from './NormalCard'
import DynamicCard from './dynamic/DynamicCard'
import DynamicPictureCard from './dynamic/DynamicPictureCard'
import MangaCard from './manga/MangaCard'
import ComicCard from './comic/ComicCard'
import WikiCard from './comic/WikiCard'
import PicturesCard from './comic/PicturesCard'
import CooperationInfoCard from './staff/CooperationInfoCard'
import VideoCard from './VideoCard'
import DynamicDataCard from './DynamicDataCard'
import ChatCard from './ChatCard'
import TagCard from './tag/TagCard.vue'
import MediaCard from './media/MediaCard.vue'
import BgmCard from './bgm/BgmCard.vue'
import ChapterCard from '@/components/TaskDetail/chapter/ChapterCard'
import { getBackUrl } from '@/utils/index'

export default {
  components: {
    HistoryCard,
    OperationsCard,
    DynamicCard,
    NormalCard,
    EmptyDetail,
    ComicCard,
    WikiCard,
    PicturesCard,
    CooperationInfoCard,
    VideoCard,
    TaskQuitDialog,
    TagCard,
    MediaCard,
    MangaCard,
    BgmCard,
    ChatCard,
    ChapterCard
  },
  data() {
    return {
      delayTasks: '',
      codeTimer: null,
      result: '',
      ops: {},
      detail: {},
      task: {},
      isBlank: false,
      tableData: [],
      query: {
        resource: {},
        task: {}
      },
      extra: [],
      business_id: null,
      flow_id: null,
      isDelay: 0,
      isTask: 1,
      resource: {},
      page: {},
      staffTableData: [],
      userSpecialGroup: [1, 2, 5, 98],
      userName: {
        1: '优质用户',
        2: '高危用户',
        5: '时政UP主',
        98: '图文高危用户'
      }
    }
  },

  computed: {
    ...mapState({}),
    trackQuery() {
      return this.detail.resource ? this.detail : this.query
    }
  },

  created() {
    let query = this.$route.query
    this.business_id = +query.business_id || 0
    this.flow_id = +query.flow_id || 0
    this.isDelay = +query.is_delay
    this.isTask = +query.isTask
    this.getPageInfo()
  },

  watch: {},

  mounted() {},

  destroyed() {
    clearInterval(this.codeTimer)
  },

  methods: {
    getPageInfo() {
      templateApi
        .getPageDetail({
          business_id: +this.business_id
        })
        .then(res => {
          if (res.data) {
            this.page = JSON.parse(res.data.content)
            this.$nextTick(() => {
              this.extra = getExtra(this.extra, this.business_id)
              this.getDelayOrOrderList()
            })
          }
        })
        .catch(_ => {})
    },
    getDelayOrOrderList() {
      this.reset()
      // 非延迟任务清单 : (延迟任务清单 || 审核任务详情)
      return !this.isDelay && this.isTask
        ? this.getTask()
        : this.getTaskDetail()
    },
    getTask() {
      this.isBlank = false
      taskApi
        .getTaskNext({
          business_id: this.business_id,
          flow_id: this.flow_id
        })
        .then(res => {
          let data = res.data
          clearInterval(this.codeTimer)
          if (!data) {
            return
          }
          if (!data.single_dispatch || !data.single_dispatch.length) {
            this.isBlank = true
            return
          }
          // 清空单话图片勾选
          this.$refs.comic_picture_box &&
            this.$refs.comic_picture_box[0].checkedAll(true)
          this.splitData(data.single_dispatch[0])
        })
        .catch(_ => {})
    },
    getTaskDetail() {
      this.detail = {}
      let [page, oid, id] = ['', '', '']
      if (this.$route.query.isResource) {
        page = 'resource'
        oid = this.$route.query.oid
      } else {
        page = 'task'
        id = this.$route.query.task_id
      }
      const apiFn =
        page === 'resource' ? resourceApi.getResourceInfo : taskApi.getTaskInfo
      apiFn({
        business_id: this.business_id,
        flow_id: this.flow_id,
        oid,
        task_id: id
      }).then(res => {
        let result = res.data
        if (!result) {
          clearInterval(this.codeTimer)
          this.isBlank = true
          return
        }

        const data = result.single_dispatch ? result.single_dispatch[0] : result
        this.splitData(data)
      })
    },
    splitData(data = {}) {
      let task = data.task || {}
      let attribute = data.resource.attribute_list || {}
      this.setAttribute(attribute)

      if (this.isDelay) {
        this.result = '暂无'
      } else if (!this.isDelay && this.isTask) {
        this.consume(data.task.gtime)
      }
      if (data.user_group) {
        let userGroupStr = ''
        ;(Object.values(data.user_group) || []).forEach(item => {
          userGroupStr = userGroupStr + item.group_tag + ': ' + item.group_name + '\n'
        })
        data.user_group = userGroupStr
      }
      if (data.user_special) {
        let userSpecialStr = ''
        ;(Object.keys(data.user_special) || []).forEach(key => {
          if (this.userSpecialGroup.find(groupKey => groupKey === +key)) {
            userSpecialStr = userSpecialStr + this.userName[key] + ': ' + data.user_special[key].note + '\n'
          }
        })
        data.user_special = userSpecialStr
      }

      this.detail = {
        ...data
      }
      this.task = task
      // 如果是动态则获取卡片信息
      this.resource = data.resource || {}
      // 任务数据概览
      this.setTaskNumber(data)
      // 审核操作按钮(下掉封禁)
      if (data.flow?.operations) {
        const new_operations = data.flow.operations.filter(item => item.ch_name !== '封禁')
        data.flow.operations = new_operations
      }
      this.ops = data.flow
    },
    setTaskNumber(data) {
      this.delayTasks = data.undo_stat && data.undo_stat.delay_count
      // 退出表格数据
      this.tableData = data.undo_stat ? [data.undo_stat] : []
    },
    setAttribute(attribute) {
      if (attribute.no_comment) {
        this.$refs.operation[0].forbidList.push('2')
        this.$refs.operation[0].attributeList.no_comment = 1
      }
      if (attribute.no_forward) {
        this.$refs.operation[0].forbidList.push('1')
        this.$refs.operation[0].attributeList.no_forward = 1
      }
    },
    consume(gtime) {
      let time = null
      let currentTime = null
      this.codeTimer = setInterval(() => {
        currentTime = Math.round(new Date().getTime() / 1000)
        time = currentTime - gtime
        this.result = hhmmssS(time)
      }, 1000)
    },
    quitOrDelay(isQuit) {
      if (this.isTask) {
        this.$nextTick(() => {
          this.$refs.taskQuitDialog.quitOrDelay(isQuit)
        })
      } else {
        if (this.$route.query.businessType === 'text') {
          const back = this.$route.query.back || ''
          const url = getBackUrl(back)
          if (url) {
            window.location.href = url
          } else {
            this.$router.go(-1)
          }
        } else {
          this.$router.push({
            path: `/audit/list/${this.$route.query.businessType}`,
            query: {
              businessId: this.business_id,
              isResource: 1
            }
          })
        }
      }
    },
    goBack() {
      clearInterval(this.codeTimer)
      goTaskList(this.$route.query, this.$router)
    },
    getCurrentComponent(type) {
      let component = null
      switch (type) {
        case 'operation':
          component = OperationsCard
          break
        case 'history':
          component = HistoryCard
          break
        case 'comic':
          component = ComicCard
          break
        case 'wiki':
          component = WikiCard
          break
        case 'dynamic':
          component = DynamicCard
          break
        case 'dynamic_picture':
          component = DynamicPictureCard
          break
        case 'comic_picture_box':
          component = PicturesCard
          break
        case 'staff_cooperation':
          component = CooperationInfoCard
          break
        case 'video':
          component = VideoCard
          break
        case 'dynamic_data':
          component = DynamicDataCard
          break
        case 'tag_card':
          component = TagCard
          break
        case 'media':
          component = MediaCard
          break
        case 'manga':
          component = MangaCard
          break
        case 'bgm':
          component = BgmCard
          break
        case 'chat':
          component = ChatCard
          break
        case 'chapter':
          component = ChapterCard
          break
        default:
          component = NormalCard
          break
      }
      return component
    },
    reset() {
      this.$refs.operation &&
        this.$refs.operation[0] &&
        this.$refs.operation[0].resetChecked()
    },
    getValue(key) {
      return transformKey(key, this.detail)
    },
    getStaffTableData(data) {
      this.staffTableData = data || []
    }
    // calRightRowHeight(index) {
    //   let height = 0
    //   this.pageData1.content[1].data.forEach((d, i) => {
    //     if (i <= index) {
    //       height += d.style.height
    //     }
    //   })
    //   if (index === 0) {
    //     return height
    //   } else {
    //     return `calc(100% - ${height})`
    //   }
    // }
  }
}
</script>

<style lang="stylus">
.task-detail-template {
    height: calc(100vh - 40px) !important
    width: 100%
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1)
    border-radius: 2px
    box-sizing: border-box
    background: var(--content-bg-color)

    .base-row {
        height: 60px
        padding: 10px 18px
        font-size: 15px
        z-index: 20
        line-height: 3
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .1)

        .base-div {
            display:flex
            .default-base-info {
                line-height: 2.6

                .el-button {
                    vertical-align: top
                }

                .task-info {
                    color: var(--text-color-dark-1)
                    margin-right: 10px
                    em {
                        color: var(--text-color)
                        font-weight: bold
                    }
                }

                .delay-num>em {
                    color: var(--red)
                }
            }
        }
    }

    .el-card {
        color: var(--text-color)
        box-shadow: none
        margin-bottom: 8px
        margin-right: 8px
        position: relative

        .el-card__header {
            padding: 12px
            font-size: 15px
            font-weight: 500
        }

        .el-card__body {
            font-size: 14px
            padding: 12px
            height: calc(100% - 70px)
            overflow: auto
        }

        .el-form>.el-form-item {
            margin-bottom 0px
        }

        .el-form>.el-row>.el-form-item {
            margin-bottom 0px
        }
    }

    .detail-row {
        height: calc(100% - 60px)
        padding: 18px 10px 18px 18px
        overflow: auto
        z-index: 10

        .detail-col {
            height: 100%
            overflow: auto

            &::-webkit-scrollbar {
                display: none
            }
        }
    }

    .text-area {
        width: 100%
        margin-bottom: 18px !important
    }

    .tip {
        .el-form-item__content {
            width: calc(100% - 97px)
        }
    }

    .value {
        color: var(--grey-light-1)
    }

    .blue-fans-font {
        color: var(--blue)
        font-weight: bold
    }

    .red-fans-font {
        color: var(--red)
        font-weight: bold
    }

    .orange-fans-font {
        color: var(--orange)
        font-weight: bold
    }

    .hit {
        height: 70px
        overflow: auto
        background: var(--bg-color)
        border-color: var(--border-color-light-1)
        color: var(--text-color-light-2)
        cursor: not-allowed
    }

    .track>span>a {
        color: var(--text-color)
    }

    .right-top-row {
        margin-bottom: 10px
        height: 100%

        &:last-child {
            margin-bottom: 0px
        }
    }

    a {
        color: var(--link-color)
        font-weight: bold
    }

    .el-form-item__label {
        line-height 30px
    }

    .el-form-item__content {
        line-height 30px
    }
}
</style>
