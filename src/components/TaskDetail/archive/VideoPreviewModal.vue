<template>
 <Draggable
  :class="['wrapper', 'video-preview-modal', visible ? 'modal-visible' : 'modal-hidden']"
  :width="draggableWidth"
  :zIndex="100"
  rememberPosition="videoPreview"
  :fixPosition="fixPosition"
  :disabled="disableDraggle"
  :noFixed="noFixed"
  limitClient>
  <div slot="dragTrigger" class="row" style="width: 100%; line-height: 22px; padding-bottom: 5px;">
    <div v-if="!useRawFile" class="row-title">
      <span>P{{pIndex}}</span><span class="title" :title="title">{{title}}</span>
    </div>
    <div v-else>
      <span class="title">播放源</span>
    </div>
    <i v-if="!disableClose" class="el-icon-circle-close" style="font-size: 22px; cursor: pointer;" @click="close"></i>
    <a v-if="toggle" class="toggle font-14" @click.prevent="toggleVideo">{{ useRawFile ? '切换播放源' : '切换原片' }}</a>
  </div>
  <div slot="noDragTrigger">
    <div class="player-area">
      <VideoTimeExtractor
        v-model="timeExtractorCollapsed"
        :pIndex="pIndex"
        :mediaReady="mediaReady"
        :sliceMap="sliceMap"
        @update:slice-map="relayUpdateSliceMap"
        style="flex: 1 0 auto"
      />
      <!-- flex-basis 必须是100% 默认撑满容器剩余的跨度 -->
      <div style="flex: 1 1 100%">
        <div v-if="hasCopyrightAiTag" class="ai-tag-container p-8">
          <AiTag :list="aiRiskHint.aiTag" :cid="cid" reportBiz="archive" />
        </div>
        <div v-if="hasCommunityAiHint" class="community-tag-container p-8">
          <ClipCommunityAiTag :communityAiTag="communityAiTag" :cid="cid" :extraClass="communityAiTagClassname" />
        </div>
        <NanoModulePlayer
          class="player-iframe"
          ref="nanoPlayer"
          :aid="useRawFile ? undefined : aid"
          :cid="useRawFile ? undefined : cid"
          :src="useRawFile ? videosrc : undefined"
          :seek="seek"
          :seekPause="seekPause"
          :videoPlaylist="videoPlaylist"
          :fetchSubtitle="fetchSubtitle"
          :filename="filename"
          :hasTopLeftover="hasCommunityAiHint"
          @nano-loaded-data="handleMediaLoaded"
          @toggle-fullscreen="toggleFullscreen"
        >
        </NanoModulePlayer>
      </div>
      <VideoInfoExtractor v-if="showExtractor" v-model="collapsed" :cid="cid" :mediaReady="mediaReady" style="flex: 1 0 auto" />
    </div>
  </div>
 </Draggable>
</template>

<script>
import AiTag from '@/components/ArchiveTask/AiTag'
import NanoModulePlayer from '@/components/package/VideoPlayer/NanoModulePlayer'
import Draggable from '@/components/package/Draggable'
import VideoInfoExtractor from '@/v2/biz-components/archive/VideoInfoExtractor'
import VideoTimeExtractor from '@/v2/biz-components/archive/VideoTimeExtractor'
import ClipCommunityAiTag from '@/components/TaskDetail/archive/ClipCommunityAiTag'
import { archiveApi } from '@/v2/api'

export default {
  name: 'video-preview-modal',
  components: {
    AiTag,
    ClipCommunityAiTag,
    Draggable,
    NanoModulePlayer,
    VideoInfoExtractor,
    VideoTimeExtractor
  },
  props: {
    shouldFetchClipAiResult: {
      type: Boolean,
      default: false
    },
    preferToUseRawFile: {
      type: Boolean,
      default: false
    },
    visible: {
      type: Boolean,
      default: false
    },
    pIndex: {
      type: Number,
      default: 0
    },
    aiRiskHint: {
      type: Object,
      default: () => {}
    },
    title: {
      required: true
    },
    aid: {
      required: true
    },
    cid: {
      required: true
    },
    filename: {
      type: String
    },
    fetchSubtitle: {
      type: Boolean,
      default: false
    },
    videosrc: {
      required: true
    },
    // 是否允许切换
    toggle: {
      required: false,
      default: false
    },
    disableDraggle: {
      type: Boolean,
      default: false
    },
    noFixed: {
      type: Boolean,
      default: false
    },
    disableClose: {
      type: Boolean,
      default: false
    },
    seek: Number,
    seekPause: {
      type: Boolean,
      default: true
    },
    showExtractor: {
      type: Boolean,
      default: false
    },
    // 打回理由播放器时间节点 { key: pIndex, value: [ { sTime, eTime } ] }
    sliceMap: {
      type: Object,
      default: () => {}
    },
    videoPlaylist: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    draggableWidth() {
      if (!this.collapsed && !this.timeExtractorCollapsed) return '1212px'
      if (!this.collapsed || !this.timeExtractorCollapsed) return '936px'
      return '660px'
    },
    hasCopyrightAiTag() {
      return this.aiRiskHint?.aiTag && Array.isArray(this.aiRiskHint.aiTag) && this.aiRiskHint.aiTag.length > 0
    },
    hasCommunityAiHint() {
      return this.shouldFetchClipAiResult && Array.isArray(this.communityAiTag) && this.communityAiTag.length
    }
  },
  data() {
    return {
      useRawFile: false, // 是否播放原文件
      moveable: {
        draggable: true,
        throttleDrag: 0
      },
      frame: {
        translate: [0, 0]
      },
      frameStart: {
        x: 0,
        y: 0
      },
      targetDom: null,
      fixPosition: {
        top: '50px',
        right: '20px'
      },
      collapsed: true,
      mediaReady: false,
      timeExtractorCollapsed: true,
      communityAiTag: [],
      cacheCommunityAiTagCid: undefined,
      communityAiTagClassname: ''
    }
  },
  watch: {
    videosrc: {
      handler() {
        this.init()
      },
      immediate: true
    },
    visible(newVal) {
      if (newVal === false) {
        this.$refs?.nanoPlayer?.pauseVideo()
        if (window.player) window.player.pause()
      } else {
        if (this.shouldFetchClipAiResult) this.fetchCommunityAiHint()
      }
    }
  },
  methods: {
    init() {
      if (!this.videosrc) return
      this.useRawFile = this.preferToUseRawFile
    },
    close() {
      this.$emit('close')
    },
    toggleVideo() {
      this.useRawFile = !this.useRawFile
    },
    handleMediaLoaded() {
     this.mediaReady = true
    },
    relayUpdateSliceMap(payload) {
      this.$emit('update:slice-map', payload)
    },
    toggleFullscreen() {
      this.communityAiTagClassname = this.communityAiTagClassname ? '' : 'fullscreen'
    },
    async fetchCommunityAiHint() {
      if (this.cid === this.cacheCommunityAiTagCid) return
      try {
        const res = await archiveApi.getCommunityAiTag({ cid: this.cid })
        this.communityAiTag = res?.data?.ai_tag || []
        this.cacheCommunityAiTagCid = this.cid
      } catch (e) {
        console.error(e)
      }
    }
  }
}
</script>
<style lang="stylus" scoped>
.modal-visible
  visibility visible
.modal-hidden
  visibility hidden
  >>> #aegis-nano-container .bpx-player-progress
    visibility hidden
.video-preview-modal
  background var(--content-bg-color)
  border 10px solid var(--border-color-light-2)
  color var(--text-color-dark-1)
  width 100%
  .ai-tag-container
    background-color var(--bg-orange)
  .community-tag-container
    background-color var(--warning-bg-color)
  .player-area
    display flex
    flex-direction row
    align-items stretch
  .row
    display flex
    position relative
    align-items baseline
    height 21px
    justify-content space-between
  .row-title
    display flex
    position relative
    align-items center
    font-size 14px
  .title
    display inline-block
    margin-left 5px
    margin-right 20px
    max-width 120px
    white-space nowrap
    text-overflow ellipsis
    overflow hidden
    vertical-align middle
    font-size 12px
    color var(--text-color-dark-1)
    font-weight 700
  .toggle
    position absolute
    right 45px
    cursor pointer
  .close-btn
    position absolute
    right 0
    padding 0 5px
    font-size 18px
    line-height 1
  .player-iframe
    width 100%
    height 500px
  .info-label
    font-weight 300
</style>
