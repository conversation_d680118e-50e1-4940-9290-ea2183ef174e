<template>
  <div class="argue argue-block">
    争议标签：
    <el-select
      v-model="argueRole"
      placeholder="角色"
      size="small"
      style="width: 80px;
      margin-right: 5px;"
      :disabled="disabledSelect"
      @change="argueRoleChange">
      <el-option
        v-for="role in argueData.roles"
        :key="role.name"
        :label="role.name"
        :value="role.rid">
      </el-option>
    </el-select>
    <AgTooltip
      :disabled="disabled" 
      placement="top" 
    >
      <el-select
      :disabled="disabledSelect"
        v-model="currentArgueTag"
        placeholder="选择分类"
        no-data-text="分类为空"
        size="small"
        style="margin-right: 10px;width: 100px;"
        @visible-change="(val) => dropdownVisible = val"
        @change="updateArgueTag">
        <el-option
          v-for="argue in argueTagList"
          :key="argue.name"
          :label="argue.name"
          :value="argue.tag_id">
        </el-option>
      </el-select>
      <div slot="content">
        {{tooltipContent}}
      </div>
    </AgTooltip>
  </div>
</template>
<script>
import AgTooltip from '@/components/element-update/Tooltip'
export default {
  name: 'argue',
  inheritAttrs: false,
  components: {
    AgTooltip
  },
  data() {
    return {
      argueRole: '',
      currentArgueTag: null,
      disabled: false,
      dropdownVisible: false,
      argueData: {},
      tagId: ''
    }
  },
  props: {
    aid: {
      type: [Number, String]
    },
    stat: {
      type: Object,
      default() {
        return {}
      }
    },
    form: {
      type: Object,
      default() {
        return {}
      }
    },
    disabledSelect: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    argueTagList() {
      if (!this.argueRole) return []
      return this.argueData.tagRoleMap[this.argueRole]
    },
    tooltipContent() {
      this.disabled = true
      if (this.currentArgueTag) {
        const idx = this.argueTagList.findIndex((item) => item.tag_id === this.currentArgueTag)
        if (idx > -1) {
          if (this.argueTagList[idx].description && !this.dropdownVisible) this.disabled = false
          return this.argueTagList[idx].description || ''
        }
      } else {
        return ''
      }
    }
  },
  watch: {
    'stat.argueData': {
      handler(val) {
        this.argueData = val
      },
      immediate: true
    },
    'form.tag_id': {
      handler(val) {
        this.tagId = val
      },
      immediate: true
    },
    aid: {
      handler() {
        // 两种情况，
        // 1.页面加载info数据，初始化选中
        // 2.切换高级设置切回来，保留选中状态
        const argueData = this.argueData
        this.argueRole = argueData.roleId
        if (this.tagId && this.argueRole) {
          this.currentArgueTag = argueData.tagMap[this.tagId] && argueData.tagMap[this.tagId].tag_id
        }

        // 解决办法：增加兜底。用第二个选项的值去找第一个选项应该选什么
        const tagKeys = Object.keys(argueData.tagMap || {})
        let sameKey = ''
        tagKeys.map(item => {
          if (+argueData.tagMap[item].tag_id === +this.tagId) {
            sameKey = item
          }
        })
        if (sameKey !== '') {
          const keyName = argueData.tagMap[sameKey].rname
          const idx = argueData.roles.findIndex(item => item.name === keyName)
          if (idx > -1) {
            this.argueRole = argueData.roles[idx].rid
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    argueRoleChange(role) {
      if (role) {
        this.currentArgueTag = null
        this.updateArgueTag(null)
      }
    },
    updateArgueTag(tag) {
      if (!tag) {
        this.form.tag_id = ''
        return
      }
      this.form.tag_id = tag
    }
  }
}
</script>
<style lang="stylus" scoped>
.argue-block
  font-size 14px
  line-height 32px
</style>
