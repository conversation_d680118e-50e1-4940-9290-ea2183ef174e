<template>
  <div class="archive-info">
    <AgFormItem label="标题：" labelWidth="45px" :highlight="highlight.title" >
      <CopyrightHighlight
        v-model="form.title"
        :keywordData="titleKeywordData"
        style="font-size: 14px;"
        :disabled="disabled"
      />
    </AgFormItem>
    <AgFormItem label="分区：" labelWidth="45px" type="flex-wrap">
      <AgFormItem :highlight="highlight.typeid" :showSlot="showSlot" style="margin-bottom: 0;">
        <div style="margin-right: 5px;">
          <Category
            v-if="arctypes && arctypes.length > 0"
            placeholder="分区选择"
            v-model="form.typeid"
            :multiple="false"
            :child-only="true"
            :options="arctypes"
            style="width:120px"
            :disabled="disabled"
          />
        </div>
      </AgFormItem>
      <div style="display: flex">
        <div class="archive-col" style="width: 100%; display: flex; align-items: center">
          <div class="col-title">类型：</div>
          <el-radio-group 
            v-model="form.copyright" 
            size="small" 
            style="margin-right: 20px;"
            :disabled="disabled"
            @change="handleCopyrightChange"
          > 
            <el-radio :label="1" style="margin-right: 5px;">自制</el-radio>
            <el-radio :label="2" style="margin-right: 5px;">转载</el-radio>
            <el-radio :label="0" disabled>未知</el-radio>
          </el-radio-group>
        </div>
      </div>
      <div>
        作者：
        <NewTooltip maxWidth="200">
          <template slot="content">{{userInfo.sign ? `个签：${userInfo.sign}` : '暂无数据'}}</template>
          <UserIcon :official="userInfo.official" class="up-icon"></UserIcon>
          <span class="up-name" :title="`UP主：${userInfo.name}`"><a style="color: var(--link-color);" title="名称" target='_blank' :href="`//space.bilibili.com/${userInfo.mid}`">{{userInfo.name}}</a></span>&nbsp;({{userInfo.mid}})
        </NewTooltip>
      </div>
      <div class="archive-info__adult" v-if="userCard">
        <span>类型：<span :class="{ 'warn-value': userCard.adult === 0 }">{{ USER_UNDERAGE_TIP_MAP[userCard.adult] }}</span></span>
      </div>
      <div class="archive-info__adult" v-if="profession">
        <span>资质：<span style="color: var(--red);">{{ profession }}</span></span>
      </div>
      <div class="archive-info__fans">
        <span class="archive-info__fans-label">粉丝数：<UserFans :fans="userInfo.follower"/></span>
      </div>
    </AgFormItem>
    <AgFormItem label="发布：" labelWidth="45px" type="flex-wrap">
      <el-date-picker
        v-model="form.ptime"
        :disabled="!perms.PUBTIME || disabled"
        value-format="yyyy-MM-dd HH:mm:ss"
        type="datetime"
        size="small"
        placeholder="选择日期"
        style="margin-right:5px;">
      </el-date-picker>
      <el-checkbox 
        v-model="form.delay"
        size="small"
        style="margin-right: 5px;"
        :true-label="1"
        :false-label="0"
        :disabled="!perms.DELAY || disabled">
        启用定时
      </el-checkbox>
      <el-date-picker
        size="small"
        v-model="form.dtime"
        :disabled="!perms.DELAY || disabled"
        type="datetime"
        value-format="yyyy-MM-dd HH:mm:ss"
        style="margin-right: 5px;"
        placeholder="选择日期">
      </el-date-picker>

      <div>转载来源：</div>
      <el-input style="flex: 1;" v-model="form.source" placeholder="" size="small" :disabled="disabled"></el-input>
    </AgFormItem>
    <AgFormItem label="标签：" labelWidth="45px" type="flex">
      <el-col :span="22">
        <SelectHighlight
          v-model="form.tag"
          multiple
          style="width: 95%;"
          filterable
          allow-create
          default-first-option
          placeholder="请选择TAG"
          size="small"
          :disabled="disabled"
          :keywordList="sensitiveKeyword.tag"
          @keydown.enter.native.stop>
          <el-option
            v-for="item in availableTags"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </SelectHighlight>
      </el-col>
      <el-col :span="2">
        <el-button type="primary"  @click="saveTags" style="float: right;" size="small" :disabled="disableSaveTagInPGC" data-qa-disabled="1">保存</el-button>
      </el-col>
    </AgFormItem>
    <AgFormItem label="简介：" labelWidth="45px" type="flex" :highlight="highlight.content">
      <RichInput
        :uid="userInfo.mid"
        v-model="form.content"
        type="textarea"
        :rows="5"
        placeholder=""
        :disabled="disabled"
        :keywordList="sensitiveKeyword.desc"
      ></RichInput>
    </AgFormItem>
    <AgFormItem label="动态：" labelWidth="45px" type="flex" :highlight="highlight.dynamic">
      <RichInput
        type="textarea"
        style="flex: 1;"
        :uid="userInfo.mid"
        :rows="5"
        placeholder=""
        :disabled="disabled"
        v-model="form.dynamic"
        :keywordList="sensitiveKeyword.dynamic"
      >
      </RichInput>
    </AgFormItem>
    <AgFormItem label="频道：" labelWidth="45px" type="flex" v-if="stat && stat.channels && stat.channels.length > 0">
      <Channels :channels="stat.channels" ></Channels>
    </AgFormItem>
  </div>
</template>
<script>
import Category from '@/components/TreeSelect/Category.vue'
import Channels from './Channels.vue'
import { mapState } from 'vuex'
import { detailApi } from '@/api/index'
import notify from '@/lib/notify'
import CopyrightHighlight from './CopyrightHighlight'
import AgFormItem from '@/components/element-update/FormItem'
import UserIcon from './UserIcon.vue'
import UserFans from './UserFans.vue'
import RichInput from '@/components/element-update/RichInput.vue'
import NewTooltip from '@/components/element-update/Tooltip.vue'
import { 
  USER_UNDERAGE_TIP_MAP
} from '@/utils/constant'
import SelectHighlight from '@/components/element-update/SelectHighlight.vue'

export default {
  name: 'archive-info',
  inheritAttrs: false,
  data() {
    return {
      upFrom: '',
      availableTags: [],
      highlight: {},
      userInfo: {},
      showSlot: true,
      USER_UNDERAGE_TIP_MAP
    }
  },
  components: {
    Channels,
    CopyrightHighlight,
    AgFormItem,
    UserIcon,
    UserFans,
    Category,
    RichInput,
    SelectHighlight,
    NewTooltip
  },
  props: {
    form: {
      type: Object,
      default() {
        return { 
        }
      }
    },
    stat: {
      type: Object,
      default() {
        return {

        }
      }
    },
    aid: {
      type: [Number, String],
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapState({
      perms: state => state.user.perms,
      arctypes: state => state.arctype.arctypes
    }),
    isPGC() {
      return this.stat.arcAttributes.is_pgc === 1
    },
    disableSaveTagInPGC() {
      return this.disabled || this.isPGC
    },
    userCard() {
      return this.stat.user_card
    },
    profession() {
      // FIXME: 这个文件可以删除了
      const profession = this.stat?.userInfo?.profile?.profession || {}
      return profession.name || ''
    },
    titleKeywordData() {
      const copyrightKeywords = this.stat?.copyrightKeywords?.archive || []
      const sensitiveKeywords = this.stat?.archiveFilter?.title || []

      return [...copyrightKeywords, ...sensitiveKeywords]
    },
    // 敏感词
    sensitiveKeyword() {
      return this.stat.archiveFilter || {}
    }
  },
  watch: {
    'stat.archiveData': {
      handler(val) {
				if (val) {
          this.upFrom = val && val.up_from
				}
      },
      immediate: true
    },
    'stat.allTags': {
      handler(val) {
				if (val) {
          this.availableTags = (val && val.slice()) || []
				}
      },
      immediate: true
    },
    'stat.highlight': {
      handler(val) {
				if (val) {
          this.highlight = (val && val.archive) || {}
				}
      },
      immediate: true
    },
    'stat.userInfo': {
      handler(val) {
        this.userInfo = val || {}
      },
      immediate: true
    }
  },
  methods: {
    // 自制改成转载，需要去掉勾选“禁止转载”
    handleCopyrightChange() {
      if (this.form.copyright === 2) {
        this.form.no_reprint = 0
      }
    },
    saveTags() {
      if (this.disableSaveTagInPGC) return 
      const form = this.form
      // NOTE::频道回查列表 0 做特殊处理
      const review = this.$route.query.review || ''
      const params = {
        aid: this.aid,
        tags: form.tag.slice().join(',')
      }
      if (review === '0') {
        params.channel_review = '1'
      }
      detailApi.saveTags(params).then(() => {
        notify.success('tag保存成功')
        if (review === '0') this.$emit('back')
      }).catch((e) => {
        if (e.code === 21069) this.$emit('back')
      })
    },
    keyDownHandler(event) {
      const keyCode = event.keyCode
      if (event.target.nodeName === 'TEXTAREA') {
        return
      }
      // q键盘提交tag，防止q快捷键input输入框
      if (event.target.nodeName === 'INPUT') {
        return
      }
      if (keyCode === 81 && !this.sGua_pendingSubmit) {
        this.saveTags()
      }
    },
    registerKeyHandler() {
      document.addEventListener('keydown', this.keyDownHandler)
      this.$once('hook:beforeDestroy', () => {
        document.removeEventListener('keydown', this.keyDownHandler)
      })
    },
    handleOpenCover() {
      const url = this.stat.coverInfo && this.stat.coverInfo.fullCover
      url && window.open(url, '_blank')
    }
  },
  mounted() {
    this.registerKeyHandler()
  }
}
</script>
<style lang="stylus">
.archive-info
  .el-tag--info
    background var(--blue) !important
    color var(--text-color-reverse) !important
  .el-tag__close
    color var(--blue-dark-1) !important
    font-size 14px !important
    margin-right 5px !important
    background: var(--blue) !important
  .el-radio__label
    padding-left 2px !important
  .el-checkbox__label
    padding-left 2px
  .up-icon
    vertical-align text-top
</style>
<style lang="stylus" scoped>
.archive-info
  &__fans
    margin-left auto
    &-label
      font-weight 400
  &__adult
    margin-left 20px
  .el-form-item
    margin-bottom 10px !important
  .warn-value
    color var(--error-color)
</style>
