<template>
  <div class="business-labels">
    <el-tag
      v-for="(label, index) in labels"
      :key="index"
      :class="['label', vertical ? 'vertical' : '']"
      :type="LABEL_MAP[label].type"
    >
      {{ LABEL_MAP[label].name }}
    </el-tag>
    <template v-if="flyLabel">
      <el-tooltip placement="top">
        <div slot="content">
          <div><b>个人起飞：</b>{{ mapToText(flyLabel.personalFly) }}</div>
          <div><b>内容起飞：</b>{{ mapToText(flyLabel.contentFly) }}</div>
          <div><b>商业起飞：</b>{{ mapToText(flyLabel.businessFly) }}</div>
        </div>
        <el-tag class="label" type="danger">起飞</el-tag>
      </el-tooltip>
    </template>
  </div>
</template>

<script>
export default {
  props: {
    value: {
      type: Object,
      default() {
        return {}
      }
    },
    vertical: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      LABEL_MAP: Object.freeze({
        staff: {
          name: '联合投稿',
          type: 'success'
        },
        porder: {
          name: '私单稿件',
          type: 'success'
        },
        adorder: {
          name: '花火商单',
          type: 'danger'
        },
        order: {
          name: '绿洲商单',
          type: 'success'
        },
        interactive: {
          name: '互动视频',
          type: 'warning'
        },
        isSponsored: {
          name: '充电专属',
          type: 'warning'
        },
        ugcpay: {
          name: '付费稿件',
          type: 'info'
        },
        playlet: {
          name: '微短剧',
          type: 'warning'
        },
        pugvpay: {
          name: '课堂稿件',
          type: 'warning'
        }
      })
    }
  },
  computed: {
    labels() {
      return Object.entries(this.value || {})
        .filter(([k, val]) => val === true && this.LABEL_MAP[k] !== undefined)
        .map(([key]) => key)
    },
    flyLabel() {
      const { flyOrder } = this.value
      if (!flyOrder || !flyOrder?.fly) return false
      return flyOrder
    }
  },
  methods: {
    mapToText(val) {
      return !!val ? '是' : '否'
    }
  }
}
</script>

<style lang="stylus" scoped>
.business-labels
  display inline-flex
  flex-wrap wrap
  .label
    margin-left 5px
    margin-top 5px
</style>
