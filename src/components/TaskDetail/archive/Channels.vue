<template>
  <div class="channel-wrapper">
    <ul>
      <li class="channel" v-for="(channel, index) in channels" :key="index">
        <AgTooltip
          placement="top-start"
        >
          <span>{{ channel._hitNames }}</span>
          <div slot="content">
            {{channel._hitRules}}
          </div>
        </AgTooltip>
      </li>
    </ul>
  </div>
</template>

<script>
import AgTooltip from '@/components/element-update/Tooltip'
export default {
  props: {
    channels: {
      type: Array,
      required: true,
      default() {
        return []
      }
    }
  },
  components: {
    AgTooltip
  }
}
</script>

<style lang="stylus" scoped>
.channel-wrapper
  margin-bottom 0
.channel
  display inline-block
  color var(--warning-color)
  border 1px solid  var(--warning-color)
  border-radius 3px
  padding 2px 5px
  margin-right 5px
  line-height 1.5
  font-size 14px
</style>
