<template>
  <div class="warnings-new" v-if="!nowarning">
    <div class="wn__card">
      <!-- 推广信息 -->
      <ol v-for="(item, idx) in promotePos" :key="idx" class="wn__ol">
        <span class="wn__color1">推广位置 {{item.name}}</span>
        <span>推广时间 {{item._time}}</span>
        <span :class="item._online ? 'wn__color2' : 'wn__color1'">推广状态 {{item._state}}</span>
      </ol>
      <!-- 用户警告 -->
      <ol v-for="(item, idx) in userWarning" :key="idx" class="wn__ol">
        <span>{{item}}</span>
      </ol>
    </div>
  </div>
</template>
<script>
// import * as CONSTANT from '@/utils/constant.js'
export default {
  name: 'warnings-new',
  inheritAttrs: false,
  props: {
    promotePos: {
      type: Array,
      default() {
        return []
      }
    },
    userWarning: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
    }
  },
  computed: {
    nowarning() {
      const noUserWarn = this.userWarning && this.userWarning.length === 0
      const noProto = this.promotePos && this.promotePos.length === 0
      return noUserWarn && noProto
    }
  }
}
</script>
<style lang="stylus" scoped>
.warnings-new{
  margin-bottom 10px
  height 100px
  color var(--text-color)
  box-shadow none
  padding 12px
  position relative
  background-color var(--warning-bg-color)
  border-color var(--warning-border-color)
  overflow auto
  font-size 14px !important
  line-height 21px !important
  .wn__color1{
    color var(--blue)
  }
  .wn__color2{
    color var(--green)
  }
  .wn__card{
    width 100%
  }
}
</style>
