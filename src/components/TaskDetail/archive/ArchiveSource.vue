<template>
  <div class="archive-source">
    <AgFormItem label="投稿来源：" labelWidth="75px" align="right">
      <span style="margin-right: 15px;">{{upFromText[upFrom]}}</span>
    </AgFormItem>
  </div>
</template>
<script>
import AgFormItem from '@/components/element-update/FormItem'
import { mapState } from 'vuex'
export default {
  name: 'archive-source',
  inheritAttrs: false,
  data() {
    return {
      upFrom: ''
    }
  },
  components: {
    AgFormItem
  },
  props: {
    stat: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  computed: {
    ...mapState({
      COMMON: state => state.common.COMMON
    }),
    upFromText() {
      return (this.COMMON && this.COMMON.up_from) || {}
    }
  },
  watch: {
    'stat.archiveData': {
      handler(val) {
				if (val) {
          this.upFrom = val && val.up_from
				}
      },
      immediate: true
    }
  }
}
</script>
