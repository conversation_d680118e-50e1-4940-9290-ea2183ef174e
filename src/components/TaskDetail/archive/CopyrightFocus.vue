<template>
  <div @click.prevent="onFocus" class="highlight-container">
    <HighlightText
      :class="[
        highlightWrapperClass,
        'highlight-wrapper',
        {
          'highlight-input': isInput,
          'highlight-hover': showOverSize,
          'highlight-hover__bottom': showOverSize && placement === 'bottom',
          'hightlght-input-disabled': disabled
        },
      ]"
      :style="{
        width: showOverSize ? this.width : '100%',
        height: showOverSize ? this.height : 'auto'
      }"
      ref="hlwRef"
      v-show="!isFocus"
      v-if="value"
      :value="text"
      :keywordList="keywordData"
      highlightClassName="keyword-highlight-mark-pink"
      @mouseenter.native="handleMouseEnter"
      @mouseleave.native="handleMouseLeave"
    >
    </HighlightText>
    <template v-if="isInput">
      <AgOverlayTextarea
        type="textarea"
        ref="textarea"
        :rows="1"
        style="width: 200px;"
        size="small"
        placeholder=""
        :placement="placement"
        v-show="textarea & isFocus"
        v-model="text"
        :width="width"
        :height="height"
        @input="inputText"
        @blur="onBlur"
        :disabled="disabled"
      />
      <el-input v-model="text" v-show="isFocus && !textarea" @input="inputText" @blur="onBlur" ref="input" :disabled="disabled"></el-input>
    </template>
  </div>
</template>
<script>
import HighlightText from '@/components/HighlightText.vue'
import { debounce } from 'lodash-es'
import AgOverlayTextarea from '@/components/element-update/OverlayTextarea'

export default {
  name: 'CopyrightFocus',
  components: {
    HighlightText,
    AgOverlayTextarea
  },
  data() {
    return {
      text: this.value,
      isFocus: false,
      focusEl: null,
      isHovering: false,
      overLine: false
    }
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    keywordData: {
      type: Array
    },
    placement: {
      type: String
    },
    highlightWrapperClass: {
      type: String
    },
    isInput: {
      type: Boolean,
      default: true
    },
    textarea: {
      type: Boolean,
      default: false
    },
    isHover: {
      type: Boolean,
      default: true
    },
    width: {
      type: String,
      default: '400px'
    },
    height: {
      type: String,
      default: '200px'
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    showOverSize() {
      return this.overLine && this.isHovering
    }
  },
  watch: {
    isFocus: {
      handler(newVal) {
        this.$emit('focus', newVal)
      }
    }
  },
  mounted() {
    const type = this.textarea ? 'textarea' : 'input'
    const inputRef = this.$refs[type]
    this.focusEl = inputRef && inputRef.$refs[type]
    this.reJudgeOverLine()
  },
  methods: {
    reJudgeOverLine() {
      this.$nextTick(() => {
        const hlwEl = this.$refs.hlwRef?.$el

        if (hlwEl) {
          this.overLine = hlwEl.clientHeight !== hlwEl.scrollHeight
        } else {
          this.overLine = true
        }
      })
    },
    handleMouseEnter() {
      this.isHovering = true
    },
    handleMouseLeave() {
      this.isHovering = false
    },
    onFocus() {
      if (this.disabled) {
        return
      }
      if (!this.isInput) return
      this.isFocus = true
      this.$nextTick(() => {
        this.focusEl && this.focusEl.focus()
      })
    },
    inputText: debounce(function inputChange() {
      if (!this.isInput) return
      this.$emit('input', this.text)
    }, 300),
    onBlur() {
      this.isFocus = false
    }
  }
}
</script>
<style lang="stylus" scoped>
.highlight-container
  display flex
  position relative
  font-size inherit
.highlight-wrapper
  display inline-block
  flex 1
  width 100%
  height 100%
  user-select auto
  word-break break-all
  overflow auto
.highlight-hover
  outline 0
  position absolute
  bottom 0
  left 0
  z-index 2
  overflow auto
  resize none
  height 7em
  &__bottom
    top 0
    bottom initial
.highlight-input
  display inline-block
  border-radius 3px
  border 1px solid #cacaca
  padding-left 15px
  background var(--content-bg-color)
  color var(--text-color)

  min-height 32.9886px
  padding 5px 15px
  line-height 1.5
  box-sizing border-box
  border-radius 4px
  transition border-color .2s cubic-bezier(.645,.045,.355,1)
.hightlght-input-disabled
  background-color: var(--bg-color);
  border-color: var(--border-color-light-1);
  color: var(--text-color-light-2);
  cursor: not-allowed
.highlight-item
  border-right 1px solid var(--border-color-reverse)
.prepend,
.append
  display flex
  align-items center
  font-size inherit
  background-color var(--bg-color)
  color var(--text-color-light-1)
  vertical-align middle
  position relative
  border 1px solid var(--border-color)
  padding 0 5px
  line-height 1.4
  white-space nowrap
  [class^='select_selected']
    background transparent
    border none !important
  [class^='Select_selectWrapper']
    flex 1
    margin 0 -5px
  [class^='select_active']
    border none
  [class^='select_focused']
    border none
</style>
