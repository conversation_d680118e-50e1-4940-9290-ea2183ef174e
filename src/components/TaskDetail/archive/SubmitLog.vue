<template>
  <!-- 稿件历史 -->
  <div class="submit-log">
    <AgCard class="sl__card">
      <!-- header -->
      <div slot="header" class="clearfix sl__ol" style="font-size: 12px;">
        <a :href="submitLink" target="_blank" class="sl__title">
          投稿记录
        </a>
        <!-- 总投稿 -->
        <span>总投稿：{{userStats.total}}</span>
        <!-- 通过数量 -->
        <span>通过数量：{{userStats.pass}}</span>
        <!-- 发布待审数量 -->
        <span>发布待审数量：{{userStats[-1]}}</span>
        <!-- 修复待审数量 -->
        <span>修复待审数量：{{userStats[-6]}}</span>
        <!-- 锁定数量 -->
        <span>锁定数量：{{userStats[-4]}}</span>
        <!-- 打回数量 -->
        <span>打回数量：{{userStats[-2]}}</span>
        <!-- 打回比例 -->
        <span>打回比例：{{userStats.percentage}}%</span>
      </div>
      <!-- body -->
      <div class="sl__body">
        <div class="sl__inner">
          <!-- 每行内容 -->
          <ol v-for="(item, idx) in userSubmitHistory" :key="idx" class="sl__ol">
            <!-- 创建时间 -->
            <span>{{item.ctime}}</span>
            <!-- 分区 -->
            <span>【{{arctypeMap[item.typeid]}}】</span>
            <!-- 视频标题 -->
            <a :href="videoLink(item.aid)"
              target="_blank">
              {{item.title}}
            </a>
            <!-- 稿件状态 -->
            <span v-show="item.state !== -999" :style="{
              color: getColor(item.state)
            }">({{STATES[item.state]}})</span>
          </ol>
        </div>
      </div>
    </AgCard>
  </div>
</template>
<script>
import AgCard from '@/components/element-update/Card.vue'
import { STATES } from '@/utils/constant'
import { mapState } from 'vuex'
import { genHost } from '@/api/utils'
export default {
  name: 'archive-history',
  data() {
    return {
      STATES
    }
  },
  props: {
    userStats: {
      type: Object,
      default() {
        return {}
      }
    },
    userSubmitHistory: {
      type: Array,
      default() {
        return []
      }
    },
    upFrom: {
      type: [String, Number],
      default: '-5'
    },
    mid: {
      type: [String, Number]
    }
  },
  components: {
    AgCard
  },
  computed: {
    ...mapState({
      arctypeMap: state => state.arctype.arctypeMap
    }),
    submitLink() {
      return `${genHost()}/aegis/#/archive/list/00?mids=${this.mid}&new_search=1`
    }
  },
  methods: {
    getColor(item) {
      let color = 'var(--blue)'
      if (item >= 0) {
        color = 'var(--green)'
      } else if (item < 0 && item !== -999 && item !== -1) {
        color = 'var(--red)'
      }
      return color
    },
    videoLink(aid) {
      return `${genHost()}/aegis/#/audit/tasks/detail/11?business_id=11&oid=${aid}&list_type=00`
    }
  }
}
</script>
<style lang="stylus" scoped>
.submit-log{
  .sl{
    &__card{
      height 130px
      padding-right 10px
    }
    &__title{
      font-size 12px
      margin-right 5px
      color var(--link-color)
    }
    &__body{
      overflow-y auto
      font-size 12px
      line-height 18px
      box-sizing border-box
      padding 8px
      height 100%
    }
    &__ol{
      span{
        margin-right 5px
      }
      a{
        color var(--link-color)
      }
    }
  }
}
</style>
