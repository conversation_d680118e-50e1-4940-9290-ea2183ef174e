<template>
  <div class="audit-reason" v-if="stateStr === '-2' || stateStr === '-4'">
    <div class="cate-row">
      <el-radio-group
        v-if="dirty"
        size="small"
        @change="switchCate"
        v-model="cateLabel"
        :disabled="disabled"
        data-cy="audit-reason-select-reason-cate"
      >
        <el-radio-button
          v-for="radio in reasonCates"
          :key="radio.label"
          :label="radio.label"
        ></el-radio-button>
      </el-radio-group>
    </div>
    <div class="content-row">
      <div class="select-row" v-if="dirty">
        <NewSelect
          v-model="selectedReason"
          size="small"
          filterable
          default-first-option
          placeholder="审核理由分类"
          @change="reasonChange"
          :disabled="disabled"
          injectClass="custom-select-height"
          data-cy="audit-reason-select-reason-type"
        >
          <el-option
            v-for="reason in actionReasons"
            :key="reason.id"
            :label="reason.reason"
            :value="reason.id"
          >
          </el-option>
        </NewSelect>
      </div>
      <div class="forward-row" v-if="stateStr === '-4'">
        <span>撞车跳转：</span>
        <el-input
          v-model="lockForward"
          class="input"
          size="small"
          :controls="false"
          :disabled="disabled"
          data-cy="audit-reason-input-forward"
        >
        </el-input>
      </div>
      <div class="reason-row">
        <AgTextarea
          type="textarea"
          :rows="1"
          size="small"
          placeholder="请输入内容"
          v-model="reasonText"
          :disabled="disabled"
          data-cy="audit-reason-textarea-reason"
        />
      </div>
    </div>
  </div>
</template>
<script>
import NewSelect from '@/components/element-update/Select.vue'
import AgTextarea from '@/components/element-update/Textarea.vue'
import { reasonFormat } from '@/utils'
import { archiveApi } from '@/api'
import { PROD_REASON_FLOW, UAT_REASON_FLOW } from '@/utils/constant'
import { changeHttpProtocol } from '@/utils/index'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      selectedCateIndex: null,
      reasonTextFocus: false,
      options: [],
      initState: '',
      initReason: '',
      cateLabel: '',

      reasonCates: [], // 操作分类
      selectedReason: null // 打回/锁定 对应操作
    }
  },
  props: {
    state: {
      type: [String, Number],
      default: 0
    },
    flowId: {
      type: [String, Number],
      default: 0
    },
    listType: {
      type: String,
      default: ''
    },
    reason: {
      type: String,
      default: ''
    },
    forward: {
      type: [String, Number],
      default: ''
    },
    initHideSelect: {
      type: Boolean,
      default: true
    },
    selectedAction: {
      type: Object,
      default() {
        return {}
      }
    },
    historyState: {
      type: [Number, String],
      default: null
    },
    dirty: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  components: {
    NewSelect,
    AgTextarea
  },
  computed: {
    stateStr() {
      return this.state.toString()
    },
    reasonText: {
      get() {
        return this.reason
      },
      set(newReason) {
        this.$emit('update-reason', newReason)
      }
    },
    lockForward: {
      get() {
        return this.forward
      },
      set(newForward) {
        this.$emit('update-forward', newForward)
      }
    },
    actionReasons() {
      return (
        (this.reasonCates &&
          this.reasonCates[this.selectedCateIndex] &&
          this.reasonCates[this.selectedCateIndex].data) ||
        []
      )
    },
    ...mapGetters({
      getEnv: 'env/getEnv'
    })
  },
  created() {
    this.initState = this.state.toString()
    this.initReason = this.reason.toString()
  },
  watch: {
    selectedAction: {
      handler(nextVal) {
        this.actionChange(nextVal)
      }
    },
    reasonCates: {
      handler(nextVal) {
        if (nextVal && nextVal.length > 0) {
          this.cateLabel = nextVal && nextVal[0] && nextVal[0].label
          this.switchCate(this.cateLabel)
        }
      },
      immediate: true
    }
  },
  methods: {
    getCateLabel(cate) {
      return cate.label ? cate.label.name || cate.label : '其他'
    },
    switchCate(label) {
      if (!label) return
      const selectedCateIndex = this.reasonCates.findIndex(
        (item) => item.label === label
      )
      if (selectedCateIndex > -1) {
        this.selectedCateIndex = selectedCateIndex
        if (this.actionReasons.length === 0) {
          this.$emit('clear-reason')
          return
        }
        const newReason = this.actionReasons[0]
        this.selectedReason = newReason.id
        this.$emit('update-reason', newReason.reason, newReason.id)
      }
    },
    actionChange(option) {
      // 1.不是打回/锁定，则直接返回
      const { stateStr } = this
      if (stateStr !== '-2' && stateStr !== '-4') return
      // 2.如果是花火走本地拼接参数，请求理由结果
      const env = this.getEnv()
      const NOW_REASON_FLOW = env === 'uat' ? UAT_REASON_FLOW : PROD_REASON_FLOW
      if (['60', '61', '62', '83', '80', '85', '86', '87'].find((item) => +item === +this.listType)) {
        let matchKey = 'default'
        for (let key in NOW_REASON_FLOW) {
          const listTypeArr = NOW_REASON_FLOW[key].listType
          if (
            listTypeArr.find((listTypeName) => listTypeName === this.listType)
          ) {
            matchKey = key
            break
          }
        }
        const reasonObj = NOW_REASON_FLOW[matchKey]
        archiveApi.getReason(reasonObj[stateStr]).then((res) => {
          this.reasonProcess(res.data)
        })
      } else {
        // 否则走api
        const { extra } = option
        let apiUrl = ''
        try {
          apiUrl = (JSON.parse(extra) || {}).url
        } catch (error) {
          console.error('extra字符串转换失败')
        }
        apiUrl = changeHttpProtocol(apiUrl)
        apiUrl &&
          this.$ajax.get(apiUrl).then((res) => {
            this.reasonProcess(res.data)
          })
      }
    },
    reasonProcess(data) {
      const { stateStr } = this
      let result = reasonFormat(data)
      const initState = this.initState
      const initReason = this.initReason
      // 稿件，匹配审核操作历史最近一条状态
      const isHistoryState =
        this.historyState !== null && this.historyState.toString() === stateStr
      this.reasonCates = result.map((item, index) => {
        item._index = index // btnGroup 判断选中用
        return item
      })
      this.selectedCateIndex = 0

      const actionReasons =
        (this.reasonCates &&
          this.reasonCates[this.selectedCateIndex] &&
          this.reasonCates[this.selectedCateIndex].data) ||
        []
      let newReason
      let newReasonId
      if (actionReasons.length === 0) {
        newReason =
          (initState === stateStr || isHistoryState) && initReason
            ? initReason
            : ''
        this.selectedReason = null
        newReasonId = ''
      } else {
        newReason =
          (initState === stateStr || isHistoryState) && initReason
            ? initReason
            : actionReasons[0].reason
        this.selectedReason = actionReasons[0].id
        newReasonId = this.selectedReason
      }
      this.$emit('update-reason', newReason, newReasonId)
    },
    reasonChange(id) {
      let name = ''
      const idx = this.actionReasons.findIndex((item) => item.id === id)
      if (idx > -1) {
        name = this.actionReasons[idx].reason
      }
      this.$emit('update-reason', name, id)
    }
  }
}
</script>
<style lang="stylus" scoped>
.audit-reason
  .cate-row
    height 35px
    margin-bottom 5px
  .reason-row
    display flex
  .content-row
    display flex
    align-items center
    .select-row
      margin-right 10px
    .forward-row
      display inline-flex
      margin-right 10px
      align-items center
      .el-input
        flex 1
    .reason-row
      flex 1
.reason-text-container
  flex 1
  vertical-align top
.with-type-select
  margin-left 80px
.forward-group
  width 230px
.reason-text
  width 100%
  height 2.5em
  resize none
.reason-focus
  position absolute
  bottom 0
  left 0
  z-index 2
  height 5em
</style>
