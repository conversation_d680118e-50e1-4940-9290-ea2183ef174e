<template>
  <div class="audit-operation">
    <AgFormItem label="操作：" labelWidth="45px">
      <AgSelect
        v-model="form.state"
        size="small"
        @change="stateChange"
        :disabled="disabled"
        style="width:120px;margin-right: 10px;"
        data-cy="audit-oper-select-oper"
      >
        <el-option
          value=""
          label="选择操作">
        </el-option>
        <el-option
          v-for="option in actionList"
          :key="option.bind_id"
          :label="option.name"
          :value="option.value">
        </el-option>
      </AgSelect>
      <el-checkbox
        :true-label="1"
        :false-label="0"
        v-model="form.norank"
        label="禁排行"
        size="mini"
        :disabled="!perms.NO_RANK || disabled"
        data-cy="audit-oper-norank"
      >
        禁排行
      </el-checkbox>
      <template v-for="(field, idx) in ATTR_FIELDS">
        <el-checkbox
          :key="idx"
          v-model="form[field]"
          :true-label="1"
          :false-label="0"
          size="mini"
          :disabled="!perms[PERM_MAP[field]] || disabled"
          :data-cy="`audit-oper-${field}`"
        >
          禁{{ATTR_FIELD_TEXT[field]}}
        </el-checkbox>
      </template>
      <el-checkbox
        v-model="form.hot_down"
        :true-label="1"
        :false-label="0"
        :disabled="!perms.HOT_DOWN"
        size="mini"
        data-cy="audit-oper-hotdown"
      >
        热门降权
      </el-checkbox>
      <el-button
        @click="doubleLimit"
        style="margin-left: 10px;"
        size="mini"
        type="primary"
        plain
        :disabled="!perms.NO_RANK || !perms.NO_HOT"
        >双限</el-button
      >
      <el-button
        @click="forthLimit"
        size="mini"
        type="primary"
        plain
        :disabled="!perms.NO_RANK || !perms.NO_HOT || !perms.NO_INDEX || !perms.NO_RECOMMEND"
        >四限</el-button
      >
      <div style="margin: 0 10px;display: inline-block;">
        <el-checkbox
          v-model="form.sendnotify"
          :true-label="1"
          :false-label="0"
          :disabled="disabled"
          label="通知"
          data-cy="audit-oper-notify"
        >
          通知
        </el-checkbox>
      </div>
      <el-button type="primary" size="normal" @click="hanldePreview" style="padding: .5em 0;margin-right: 5px; width: 60px" data-cy="audit-oper-preview">预览</el-button>
    </AgFormItem>
    <AgFormItem :showSlot="showSlot">
      <AuditReason
        :state="form.state"
        :flowId="stat.flowId"
        :dirty="dirty"
        :historyState="stat.arcHistroyState"
        :reason="form.reject_reason"
        :selectedAction="selectedAction"
        :listType="listType"
        @update-reason="updateReason"
        @clear-reason="updateReason"
        :disabled="disabled"
      />
    </AgFormItem>
    <AgFormItem label="备注：" labelWidth="45px" type="flex">
      <!-- 分两种 -->
      <template v-if="isUserNote">
        <AgDivideNote
          v-if="isOldAuditNote"
          :tags="auditTags"
          :options="stat.noteOptions"
          :aid="aid"
          :disabled="disabled"
        />
        <AgNoteTag
          v-else
          :tags="auditTags"
          :options="stat.noteOptions"
          :aid="aid"
          @add-note="handleAddNote"
          @delete-note="handleDeleteNote"
          :disabled="disabled"
        />
      </template>
      <!-- 没有角色的 -->
      <template v-else>
        <AgSelect
          v-model="note"
          style="width: 160px; margin-right: 5px;"
          placeholder="选择快捷文本"
          size="small"
          @change="changeNote"
          :disabled="disabled"
          data-cy="audit-oper-select-note"
        >
          <el-option
            v-for="reason in REASONS"
            :key="reason"
            :label="reason"
            :value="reason">
          </el-option>
        </AgSelect>
        <AgTextarea
          type="textarea"
          :rows="1"
          size="small"
          placeholder="填写描述"
          v-model="form.note"
          :disabled="disabled"
          data-cy="audit-oper-textarea-note"
        />
      </template>
    </AgFormItem>
  </div>
</template>
<script>
import { UAT_RECHECK_FLOW_ID, PROD_RECHECK_FLOW_ID, PERM_MAP, ATTR_FIELDS, ATTR_FIELD_TEXT, REASONS } from '@/utils/constant'
import AuditReason from './AuditReason.vue'
import Argue from './Argue.vue'
import { mapGetters, mapState } from 'vuex'
import AgSelect from '@/components/element-update/Select.vue'
import AgFormItem from '@/components/element-update/FormItem.vue'
import AgTextarea from '@/components/element-update/Textarea.vue'
import AgNoteTag from '@/components/element-update/NoteTag.vue'
import AgDivideNote from '@/components/element-update/DivideNote.vue'
import notify from '@/lib/notify'

export default {
  name: 'audit-operation',
  inheritAttrs: false,
  data() {
    return {
      ATTR_FIELDS,
      ATTR_FIELD_TEXT,
      PERM_MAP,
      REASONS,
      actionList: [],
      selectedAction: {},
      dirty: false,
      historyState: '',
      review: undefined,
      listType: '',
      showSlot: true,
      auditTags: [],
      allTags: [],
      note: ''
    }
  },
  components: {
    Argue,
    AuditReason,
    AgSelect,
    AgFormItem,
    AgTextarea,
    AgNoteTag,
    AgDivideNote
  },
  props: {
    stat: {
      type: Object,
      default() {
        return {}
      }
    },
    form: {
      type: Object,
      default() {
        return {}
      }
    },
    bvid: {
      type: Number / String,
      default: ''
    },
    aid: {
      type: [Number, String],
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    // 兼容快照是否使用老版本备注组件
    isOldAuditNote: Boolean
  },
  computed: {
    ...mapState({
      perms: state => state.user.perms
    }),
    ...mapGetters({
      getEnv: 'env/getEnv'
    }),
    isUserNote() {
      return this.stat.noteOptions && this.stat.noteOptions.length > 0
    }
  },
  watch: {
    'stat.actionList': {
      handler(val) {
        this.actionList = val
      },
      immediate: true
    },
    'stat.arcHistroyState': {
      handler(val) {
        this.historyState = val
      },
      immediate: true
    },
    'stat.auditTags': {
      handler(val) {
        this.auditTags = val
      },
      immediate: true
    },
    'stat.allTags': {
      handler(val) {
        this.allTags = val
      },
      immediate: true
    },
    aid: {
      handler() {
        this.note = ''
      },
      immediate: true
    }
  },
  methods: {
    doubleLimit() {
      this.form.nohot = 1
      this.form.norank = 1
    },
    forthLimit() {
      this.form.nohot = 1
      this.form.norank = 1
      this.form.noindex = 1
      this.form.norecommend = 1
    },
    handleAddNote(note) {
      const { value, label, remark, pre_text: preText } = note
      const noteTags = this.auditTags
      const existNoteTag = noteTags.find(tag => tag.tag_id === value && tag.pre_text === preText)
      if (existNoteTag && existNoteTag.canDelete) {
        notify.warning('已经存在该备注tag，只修改了备注')
        existNoteTag.remark = remark
        existNoteTag.tag_name = `${preText}【${remark}】`
      } else if (existNoteTag) {
        notify.warning('已经存在该备注tag，且无权修改')
      } else {
        this.auditTags.unshift({
          tag_id: value,
          tag_name: label,
          remark: remark,
          pre_text: preText,
          hitState: false,
          first_init: false,
          canDelete: true
        })
      }
    },
    handleHitLastNote(hit) {
      if (this.auditTags && this.auditTags.length > 0) {
        this.auditTags[this.auditTags.length - 1].hitState = hit
      }
    },
    handleDeleteLastNote() {
      this.auditTags.pop()
    },
    handleDeleteNote(note, idx) {
      this.auditTags.splice(idx, 1)
    },
    getQuery() {
      const query = this.$route.query
      this.listType = query.list_type
      this.review = query.review
    },
    toSetBindId(state) {
      const idx = this.actionList.findIndex((item) => item.value === state)
      if (idx > -1) {
        this.form.bind_id = this.actionList[idx].bind_id
      }
    },
    judgeRow(flowId) {
      const env = this.getEnv()
      const FLOW_ID_ARR = env === 'uat' ? UAT_RECHECK_FLOW_ID : PROD_RECHECK_FLOW_ID
      return FLOW_ID_ARR.some((item) => item === flowId)
    },
    stateChange(newState) {
      this.dirty = true
      const form = this.form
      const state = newState.toString()
      const flowId = +this.stat.flowId
      if (
          // 全部稿件列表
          this.listType === '00' ||
          // 待回查稿件  30 40 90
          (this.review !== '' && this.review >= 0) ||
          this.judgeRow(flowId)
        ) {
        // 系统通知联动 打回锁定
        if (state === '-2' || state === '-4') {
          form.sendnotify = 1
        } else {
          form.sendnotify = 0
        }
      }
      this.toSetBindId(state)
      this.fetchReason(newState)
    },
    fetchReason(newState) {
      const idx = this.actionList.findIndex((item) => item.value === newState)
      if (idx > -1) {
        const api = this.actionList[idx].extra || ''
        if (api) {
          this.selectedAction = this.actionList[idx]
        }
      }
    },
    updateReason(reason = '', reasonId = '') {
      const newId = reasonId || this.form.reject_reason_id
      this.form.reject_reason = reason
      this.form.reject_reason_id = newId
    },
    hanldePreview() {
      window.open(`//www.bilibili.com/video/${this.bvid}`, '_blank')
    },
    changeNote(selected) {
      var form = this.form
      if (form.note.length > 0) {
        form.note += '\n'
      }
      form.note += selected
    }
  },
  mounted() {
    this.getQuery()
  }
}
</script>
<style lang="stylus">
.audit-operation
  .el-checkbox__label
    font-size 13px !important
    padding-left 1px !important
  .el-checkbox
    margin-right 2px !important
</style>
