<template>
  <div class="user-group">
    <AgFormItem type="flex" :showSlot="showSlot">
      <el-select
        v-bind:value="form.group_ids"
        @change="(val) => form.group_ids = val"
        default-first-option
        placeholder="请选择用户组"
        size="small"
        :disabled="disabled || !perms.USERGROUP_ADD"
        style="margin-right: 10px;min-width: 100px;">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
      <AgTextarea
        type="textarea"
        :rows="1"
        style="margin-right: 10px;min-width:500px;max-width:800px;"
        size="small"
        placeholder="用户备注"
        v-model="form.note"
        @input="handleInput"
        :disabled="disabled || !perms.USERGROUP_ADD"
      />
      <el-button
        type="primary"
        size="small"
        :disabled="disabled || !perms.USERGROUP_ADD"
        @click="handleAddUser">
        添加
      </el-button>
    </AgFormItem>
  </div>
</template>
<script>
import { userGroupApi } from '@/api/index'
import { mapState } from 'vuex'
import notify from '@/lib/notify'
import AgFormItem from '@/components/element-update/FormItem'
import AgTextarea from '@/components/element-update/Textarea'
const SPEICAL_USER_GROUPS = [2, 19]
const SPECIAL_USER_GROUP_NAME = {
  2: '高危用户',
  19: '激励回查白名单'
}
export default {
  name: 'user-group',
  inheritAttrs: false,
  data() {
    return {
      SPEICAL_USER_GROUPS,
      SPECIAL_USER_GROUP_NAME,
      options: [
        {
          value: 2,
          label: '高危用户'
        },
        {
          value: 19,
          label: '激励回查白名单'
        }
      ],
      form: {
        group_ids: 2,
        note: ''
      },
      showSlot: true
    }
  },
  computed: {
    ...mapState({
      perms: state => state.user.perms
    })
  },
  props: {
    stat: {
      type: Object,
      default() {
        return {}
      }
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  components: {
    AgFormItem,
    AgTextarea
  },
  methods: {
    validateBy() {
      if (this.form.note === '') {
        return false
      } else {
        return true
      }
    },
    handleInput(val) {
      this.form.note = val
    },
    handleAddUser() {
      const name = SPECIAL_USER_GROUP_NAME[this.form.group_ids]
      if (this.validateBy()) {
        userGroupApi.addUser({
          group_ids: [+this.form.group_ids],
          mids: [+this.stat.mid],
          remarks: [this.form.note]
        }).then(() => {
          notify.success(`添加${this.stat.mid}为${name}，成功`)
          this.form.note = ''
        })
        .catch(_ => {})
      } else {
        notify.error(`请输入${name || ''}备注`)
      }
    }
  }
}
</script>
