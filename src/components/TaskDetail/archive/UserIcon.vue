<template>
  <div class="user-icon" v-if="(allGroups && allGroups.length > 0) || official.role > 0">
    <span v-if="official.role > 0" :class="{
      ['verify-icon']: true,
      ['individual']: roleInfo.isIndividual,
      ['enterprise']: roleInfo.isEnterprise
    }" :title="roleInfo.title"></span>
    <span
      v-for="group in allGroups"
      :title="group.group_name"
      :key="group.id"
      :class="colorClass[group.group_id]"
    >{{group.group_short_tag || group.group_tag}}</span>
  </div>
</template>

<script>
import { INDIVIDUAL_LIST, ENTERPRISE_LIST } from '@/pages/workbench/constants'
/**
 * @doc https://info.bilibili.co/pages/viewpage.action?pageId=8716486
 * role: 0: 无认证 1:up主认证 2:身份认证 3:企业认证 4: 政府认证 5:媒体认证 6:其他认证 7:垂直领域认证
 */

// 特殊用户组列表
const LIST_USER_TYPE = [1, 2, 3, 5]

export default {
  props: {
    userGroups: {
      type: Array,
      default() {
        return []
      }
    },
    official: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {

    }
  },
  computed: {
    allGroups() {
      return this.userGroups.filter(group => LIST_USER_TYPE.includes(group.group_id)).sort()
    },
    roleInfo() {
      const { role } = this.official
      const isIndividual = INDIVIDUAL_LIST.includes(role)
      const isEnterprise = ENTERPRISE_LIST.includes(role)
      return {
        isIndividual,
        isEnterprise,
        title: (isIndividual && '个人认证') ||
               (isEnterprise && '企业认证') ||
               ''
      }
    },
    colorClass() {
      const baseClass = ['group-icon']
      return {
        1: [...baseClass, 'priority'],
        2: [...baseClass, 'danger'],
        3: [...baseClass, 'pgc'],
        5: [...baseClass, 'politic']
      }
    }
  }
}
</script>

<style lang="stylus" scoped>
.user-icon
  display inline-flex !important
  margin-right 5px
  .type
    margin-right 1px
    font-size 12px
  .group-icon
    display inline-block
  .priority
    color var(--success-color)
    background-color mix(lighten(#67C23A, 20%), white, 50%)
    background-color rgba(lighten(#67C23A, 20%), 0.5)
  .danger
    color var(--red)
    background-color mix(darken(#EE5037, 10%), white, 30%)
    background-color rgba(darken(#EE5037, 10%), 0.3)
  .pgc
    color var(--blue)
    background-color mix(darken(#409EFF, 10%), white, 30%)
    background-color rgba(darken(#409EFF, 10%), 0.3)
  .politic
    color var(--orange)
    background-color mix(darken(#E6A23C, 10%), white, 30%)
    background-color rgba(darken(#E6A23C, 10%), 0.3)
  .verify-icon
    display inline-block
    width 18px
    height 18px
    background-image url('~@/assets/user-auth.png')
  .individual 
    background-position -39px -82px
  .enterprise
    background-position -5px -82px
</style>
