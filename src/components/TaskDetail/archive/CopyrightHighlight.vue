<template>
  <div @click.prevent="onFocus" class="highlight-container">
    <span class="prepend" v-show="!isFocus" v-if="withPrepend">
      <slot name="prepend"></slot>
    </span>
    <HighlightText
      :class="[
        highlightWrapperClass,
        'highlight-wrapper',
        { 'highlight-input': isInput },
        { 'hightlght-input-disabled': disabled }
      ]"
      :style="highlightWrapperStyle"
      v-show="!isFocus"
      :value="text"
      :keywordList="keywordData"
      @mark-done="e=>$emit('mark-done',e)"
      @tippy-show="e=>$emit('tippy-show',e)"
      @click-library-id="e=>$emit('click-library-id', e)"
    >
    </HighlightText>
    <span class="append" v-show="!isFocus" v-if="withAppend">
      <slot name="append"></slot>
    </span>
    <template v-if="isInput">
      <el-input type="textarea" :rows="1" v-model="text" v-show="textarea & isFocus" @blur="onBlur" ref="textarea"></el-input>
      <el-input v-model="text" v-show="isFocus && !textarea && !disabled" @blur="onBlur" ref="input"></el-input>
    </template>
  </div>
</template>
<script>
import HighlightText from '@/components/HighlightText.vue'

export default {
  name: 'HighlightInput',
  components: { HighlightText },
  data() {
    return {
      isFocus: false,
      focusEl: null,
      keywords: [],
      sensitiveStyleMap: {}
    }
  },
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    keywordData: {
      type: Array
    },
    placement: {
      type: String,
      default: 'bottom'
    },
    highlightWrapperClass: {
      type: String
    },
    highlightWrapperStyle: {
      type: Object,
      default() {
        return {}
      }
    },
    isInput: {
      type: Boolean,
      default: true
    },
    textarea: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    withPrepend() {
      return this.$slots.prepend !== undefined
    },
    withAppend() {
      return this.$slots.append !== undefined
    },
    text: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    isFocus: {
      handler(newVal) {
        this.$emit('focus', newVal)
      }
    }
  },
  mounted() {
    const type = this.textarea ? 'textarea' : 'input'
    const inputRef = this.$refs[type]
    this.focusEl = inputRef && inputRef.$refs[type]
  },
  methods: {
    onFocus() {
      if (this.disabled) {
        return
      }
      if (!this.isInput) return
      this.isFocus = true
      this.$nextTick(() => {
        this.focusEl && this.focusEl.focus()
      })
    },
    onBlur() {
      this.isFocus = false
    }
  }
}
</script>
<style lang="stylus" scoped>
.highlight-container
  display flex
  position relative
  font-size inherit
  &>span
    width 100%
.highlight-wrapper
  display inline-block
  flex 1
  width 100%
  height 100%
  user-select auto
  word-break break-all
.highlight-input
  display inline-block
  border-radius 3px
  border 1px solid #cacaca
  padding-left 15px
  background var(--content-bg-color)
  color var(--text-color)

  min-height 32.9886px
  padding 5px 15px
  line-height 1.5
  box-sizing border-box
  border-radius 4px
  transition border-color .2s cubic-bezier(.645,.045,.355,1)
.hightlght-input-disabled
  background-color: var(--bg-color);
  border-color: var(--border-color-light-1);
  color: var(--text-color-light-2);
  cursor: not-allowed;

.highlight-item
  border-right 1px solid var(--border-color-reverse)

.highlight-text
  background var(--warning-color)
  color var(--text-color-reverse)
  &:hover
    background #fb9700
.prepend,
.append
  display flex
  align-items center
  font-size inherit
  background-color var(--bg-color)
  color var(--text-color-light-1)
  vertical-align middle
  position relative
  border 1px solid var(--border-color)
  padding 0 5px
  line-height 1.4
  white-space nowrap
  [class^='select_selected']
    background transparent
    border none !important
  [class^='Select_selectWrapper']
    flex 1
    margin 0 -5px
  [class^='select_active']
    border none
  [class^='select_focused']
    border none
</style>
