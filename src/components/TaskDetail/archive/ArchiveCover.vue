<template>
  <div class="archive-cover">
    <AgFormItem :highlight="highlight.cover" :showSlot="showSlot">
      <div class="archive-cover__wrap" v-if="cutImage(cover)" :class="{'archive-cover__wrap-highlight': highlight.cover}">
        <template v-if="preview">
          <el-image
            style="width: 680px; height: 440px;"
            :src="cutImage(cover)"
            :preview-src-list="[cover]">
          </el-image>
        </template>
        <template v-else>
          <img class="archive-cover__img" :src="cutImage(cover)" alt="封面" @click="handleOpenCover"/>
        </template>
      </div>
      <div class="archive-cover__nodata" v-else :class="{'archive-cover__wrap-highlight': highlight.cover}">
        无预览图
      </div>
      <div class="archive-cover__content">
        <div class="archive-cover__content-text">尺寸: {{size.width}} x {{size.height}}</div>
        <el-input v-model="cover" size="small" class="archive-cover__content-input" :disabled="disabled"/>
        <Upload
          v-show="!disabled"
          class="cover-upload" :options="uploadOptions" @uploaded="handleUpdateCover"/>
      </div>
    </AgFormItem>
  </div>
</template>
<script>
import Upload from '@/components/package/Upload'
import { loadImg } from '@/utils/image'
import AgFormItem from '@/components/element-update/FormItem'
import { cutImage } from '@/plugins/bfsImage'
export default {
  name: 'archive-cover',
  inheritAttrs: false,
  props: {
    stat: {
      type: Object,
      default() {
        return {}
      }
    },
    form: {
      type: Object,
      default() {
        return {}
      }
    },
    disabled: {
      type: Boolean,
      default: false
    },
    preview: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      uploadOptions: {
        buttonName: '上传封面'
      },
      size: {
        width: 100,
        height: 100
      },
      highlight: {},
      showSlot: true
    }
  },
  computed: {
    initCover() {
      return this.stat.coverInfo && this.stat.coverInfo.initCover
    },
    cover: {
      get: function () {
        return this.stat.coverInfo.fullCover
      },
      // setter
      set: function (newValue) {
        this.stat.coverInfo.fullCover = newValue
      }
    }
  },
  watch: {
    'stat.coverInfo.fullCover': {
      handler(newCover) {
        if (!newCover || !newCover.length) {
          this.size = { width: 0, height: 0 }
          return
        }
        setTimeout(async () => {
          this.size = await loadImg(newCover)
        }, 0)
      },
      immediate: true,
      deep: true
    },
    'stat.highlight': {
      handler(val) {
				if (val) {
          this.highlight = (val && val.archive) || {}
				}
      },
      immediate: true
    }
  },
  components: {
    Upload,
    AgFormItem
  },
  methods: {
    handleUpdateCover(newCover) {
      this.stat.coverInfo = Object.assign({}, {
        ...this.stat.coverInfo,
        fullCover: newCover
      })
      this.form.cover = newCover
    },
    handleOpenCover() {
      const url = this.stat.coverInfo && this.stat.coverInfo.fullCover
      url && window.open(url, '_blank')
    },
    cutImage(cover) {
      return cutImage(cover, 240, 150)
    }
  }
}
</script>
<style lang="stylus" scoped>
.archive-cover
  width 100%
  display flex
  flex-direction column
  font-size 14px
  &__wrap{
    display inline-flex
    padding 10px 12.5px
    background-image url('~@/assets/transparent.png')
    background-repeat repeat
    margin-left 5px
    margin-top 5px
    margin-bottom 10px
    &-highlight{
      background-color: var(--green-light-1) !important;
    }
  }
  &__nodata{
    display inline-flex
    border 1px solid var(--grey-light-1)
    color var(--text-color)
    border-radius 2px
    align-items center
    justify-content center
    font-size 16px
    padding 10px
    background var(--content-bg-color)
    width 240px
    height 150px
    box-sizing border-box
    margin-bottom 5px
    margin-left 5px
    margin-top 5px
    &-highlight{
      background-color: var(--green-light-1) !important;
    }
  }
  &__img
    width 240px
    height 150px
    color var(--text-color-reverse)
    background-color var(--grey-light-1)
    cursor pointer
  &__content
    display flex
    &-text
      font-size 14px
      line-height 32px
    &-input
      flex 1
      margin 0 5px
</style>
