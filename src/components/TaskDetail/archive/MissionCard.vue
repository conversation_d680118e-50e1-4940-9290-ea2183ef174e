<template>
  <div class="mission-card">
    <!-- 活动模块 -->
    <div class="mission-box mission" v-if="stat.mission && stat.mission.id !== 0">
      <el-checkbox
        v-model="form.cancel_mission"
        :true-label="1"
        :false-label="0"
        :disabled="!perms.CANCEL_MISSION || disabled">
        取消活动
      </el-checkbox>
      <span
        class="highlight">
        活动ID：{{stat.mission.id}} {{stat.mission.name}}
      </span>
    </div>
    <!-- 付费模块 -->
    <div class="mission-box pay-module">
      <el-checkbox
        v-if="perms.UGC_PAY"
        v-model="form.ugcpay"
        :true-label="1"
        :disabled="disabled"
        :false-label="0">
        是否付费
      </el-checkbox>
      <el-input-number
        v-if="perms.UGC_PAY"
        v-model="form.ugcpay_price"
        style="width: 50px;margin-left: 5px;"
        size="small"
        :min="0"
        :controls="false"
        :disabled="disabled">
      </el-input-number>
      <span v-if="!perms.UGC_PAY" style="line-height: 33px;">无付费权限</span>
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex'
export default {
  name: 'mission-card',
  props: {
    form: {
      type: Object,
      default() {
        return {}
      }
    },
    stat: {
      type: Object,
      default() {
        return {}
      }
    },
    aid: {
      type: [Number, String]
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapState({
      perms: state => state.user.perms
    })
  }
}
</script>
<style lang="stylus" scoped>
.mission-card{
  font-size 14px
  display flex
  flex-wrap wrap
  margin-bottom 5px
  .mission{
    display flex
    align-items center
    .highlight{
      padding 5px
      border-radius 3px
      color var(--primary-color)
      background-color var(--blue-light-2)
    }
  }
  .mission-box{
    margin-left 5px
    &:first-child {
      margin-left 0
    }
  }
}
</style>
