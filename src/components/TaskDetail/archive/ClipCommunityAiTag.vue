<template>
  <div class="community-ai-tag" :class="extraClass">
    <div v-for="(aiTagByModel, i) in communityAiTag" :key="i">
      [{{ aiTagByModel.model_name }}]
      <span v-if="aiTagByModel.video && Array.isArray(aiTagByModel.video) && aiTagByModel.video.length">
        <span class="dimension-level">画面片段</span>
        <span v-for="(tagObj, j) in aiTagByModel.video" :key="`video${j}`">
          <span>{{ tagObj.hint }}</span>
          <span
            v-for="time in tagObj.time.split('-')"
            :key="time"
            @click="handleVideoSeek"
            class="clickable-time"
          >
            {{ time.trim() }}
          </span>
        </span>
      </span>
      <span v-if="aiTagByModel.text && Array.isArray(aiTagByModel.text) && aiTagByModel.text.length">
        <span class="dimension-level">文字片段</span>
        <span v-for="(tagObj, j) in aiTagByModel.text" :key="`text${j}`">
          <span>{{ tagObj.hint }}</span>
          <span
            v-for="time in tagObj.time.split('-')"
            :key="time"
            @click="handleVideoSeek"
            class="clickable-time"
          >
            {{ time.trim() }}
          </span>
        </span>
      </span>
      <span v-if="aiTagByModel.audio && Array.isArray(aiTagByModel.audio) && aiTagByModel.audio.length">
        <span class="dimension-level">音频片段</span>
        <span v-for="(tagObj, j) in aiTagByModel.audio" :key="`audio${j}`">
          <span>{{ tagObj.hint }}</span>
          <span
            v-for="time in tagObj.time.split('-')"
            :key="time"
            @click="handleVideoSeek"
            class="clickable-time"
          >
            {{ time.trim() }}
          </span>
        </span>
      </span>
    </div>
  </div>
</template>
<script>
import moment from 'moment'

export default {
  props: {
    communityAiTag: {
      type: Array,
      default: () => []
    },
    extraClass: {
      type: String,
      validator: (val) => {
        return ['fullscreen', ''].includes(val)
      }
    }
  },
  methods: {
    handleVideoSeek(e) {
      const rawTime = e.target.innerText
      let parsedTime = rawTime.trim()
      if (rawTime.includes('-')) {
        parsedTime = rawTime.split('-')[0].trim()
      }
      this.$EventBus.$emit('seek-time', moment.duration(parsedTime).as('seconds'))
    }
  }
}
</script>
<style lang="stylus" scoped>
.community-ai-tag
  line-height 20px
  max-height 70px
  overflow-y auto

  .dimension-level
    color var(--orange-dark-1)

  .clickable-time
    color var(--blue)
    cursor pointer

  .clickable-time:not(:first-child)::before
    content "-"
    cursor auto
    color var(--black)
  .type-label
    color var(--red)
.fullscreen
  position fixed
  top 0
  left 0
  height 80px
  max-height 80px
  width 100%
  overflow-y auto
  z-index 100000
  background-color var(--warning-bg-color)
</style>
