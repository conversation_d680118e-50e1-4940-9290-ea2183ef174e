<template>
  <!-- 稿件历史 -->
  <div class="archive-history">
    <AgCard class="ah__card">
      <!-- header -->
      <div v-if="!hideHeader" slot="header" class="clearfix" style="font-size: 12px;">
        <span class="ah__title">稿件历史</span>
      </div>
      <!-- body -->
      <div class="ah__body">
        <div class="ah__inner">
          <!-- 每行内容 -->
          <ol v-for="(item, idx) in listData" :key="idx" class="ah__ol">
            <!-- 创建时间 -->
            <span v-show="item.ctime">{{item.ctime}}</span>
            <!-- 用户名 -->
            <span v-show="item.username">{{item.username}}</span>
            <!-- 身份 -->
            <span v-show="perms.SHOW_UID_DEPARTMENT && item.department">[{{item.department}}]</span>
            <!-- 稿件状态 -->
            <span v-show="item.state !== -999" :style="{
              color: getColor(item.state)
            }">({{STATES[item.state]}})</span>
            <!-- 流程 -->
            <span style="color: var(--blue)" v-show="item.flow_name">
              [{{item.flow_name}}]
            </span>
            <!-- 变更 -->
            <span v-show="item.content">
              (变更：{{item.content}})
            </span>
            <!-- 备注 -->
            <span v-show="item.remark">
              (备注：{{ replaceRemark(item.remark) }})
            </span>
          </ol>
        </div>
      </div>
    </AgCard>
  </div>
</template>
<script>
import AgCard from '@/components/element-update/Card.vue'
import { STATES } from '@/utils/constant'
import { mapState } from 'vuex'
export default {
  name: 'archive-history',
  data() {
    return {
      STATES
    }
  },
  props: {
    listData: {
      type: Array,
      default() {
        return []
      }
    },
    upFrom: {
      type: [String, Number],
      default: '-5'
    },
    hideHeader: {
      type: Boolean,
      default: false
    }
  },
  components: {
    AgCard
  },
  computed: {
    ...mapState({
      arctypeMap: state => state.arctype.arctypeMap,
      perms: state => state.user.perms
    })
  },
  methods: {
    replaceRemark(remark) {
      return remark?.replaceAll('自动开放流程终结', '稿件开放流程终结')
    },
    getColor(item) {
      let color = 'var(--blue)'
      if (item >= 0) {
        color = 'var(--green)'
      } else if (item < 0 && item !== -999 && item !== -1) {
        color = 'var(--red)'
      }
      return color
    }
  }
}
</script>
<style lang="stylus" scoped>
.archive-history{
  .ah{
    &__card{
      height 130px
      padding-right 10px
    }
    &__title{
      font-size 12px
    }
    &__body{
      overflow-y auto
      font-size 12px
      line-height 18px
      box-sizing border-box
      padding 8px
      height 100%
    }
    &__ol{
      word-break break-all
      span{
        margin-right 5px
      }
    }
  }
}
</style>
