<template>
  <div class="clip-list" id="clip-list">
    <!-- 超过50p虚拟滚动 -->
    <VirtualScroll
      ref="vsRef"
      :listData="largeVideos"
      v-if="needVS"
      :highlight="highlight"
      :activeClipIndex="activeClipIndex">
      <template v-slot:table-header>
        <div class="table-row">
          <div class="table-cell">
            <div class="custom-th" :style="{width: `${columnWidth['P序'].res}px`}">
              <div class="clip-list__order">
                <AgTooltip
                  placement="top-start"
                  class="item"
                  :maxWidth="300"
                >
                  <span>P序<i class="el-icon-warning" /></span>
                  <div slot="content">按住每列P序进行拖动，可更换分P顺序，实时生效</div>
                </AgTooltip>
              </div>
            </div>
          </div>
          <div class="table-cell">
            <div class="custom-th" :style="{width: `${columnWidth['cid'].res}px`}">
              cid
            </div>
          </div>
          <div class="table-cell">
            <div class="custom-th" :style="{width: `${columnWidth['filename'].res}px`}">
              filename
            </div>
          </div>
          <div class="table-cell">
            <div class="custom-th" :style="{width: `${columnWidth['转码状态'].res}px`}">
              转码状态
            </div>
          </div>
          <div class="table-cell">
            <div class="custom-th" :style="{width: `${columnWidth['类型'].res}px`}">
              类型
            </div>
          </div>
          <div class="table-cell">
            <div class="custom-th" :style="{width: `${columnWidth['分P标题'].res}px`}">
              分P标题
            </div>
          </div>
          <div class="table-cell">
            <div class="custom-th" :style="{width: `${columnWidth['分P简介'].res}px`}">
              分P简介
            </div>
          </div>
          <div class="table-cell">
            <div class="custom-th" :style="{
              width: `${columnWidth['一审结果&建议'].res}px`,
              minWidth: '120px'
            }">
              一审结果&建议
            </div>
          </div>
          <div class="table-cell">
            <div class="custom-th" :style="{width: `${columnWidth['一审时间'].res}px`}">
              一审时间
            </div>
          </div>
          <div class="table-cell">
            <div class="custom-th" :style="{width: `${columnWidth['一审操作人'].res}px`}">
              一审操作人
            </div>
          </div>
          <div class="table-cell">
            <div class="custom-th" :style="{width: `${columnWidth['视频操作'].res}px`}">
              视频操作
            </div>
          </div>
        </div>
      </template>
      <template v-slot:table-body="slotProps">
        <div class="table-cell">
          <div class="custom-td" :style="{width: `${columnWidth['P序'].res}px`}">
            <div class="clip-list__rank my-handle" @click="openPIndex(slotProps.item)" style="color: var(--blue-light-1)">
              P{{slotProps.item.index_order}}
            </div>
          </div>
        </div>
        <div class="table-cell">
          <div class="custom-td" :style="{width: `${columnWidth['cid'].res}px`}">
            {{slotProps.item.cid}}
          </div>
        </div>
        <div class="table-cell table-cell-flex">
          <AgTextover ref="agTextoverRefs">
            <div class="custom-td custom-td__textover" :style="{width: `${columnWidth['filename'].res}px`}">
                {{slotProps.item.filename}}
            </div>
            <div class="custom-td custom-td__textover-all custom-td__textover-content" slot="content">{{slotProps.item.filename}}</div>
          </AgTextover>
        </div>
        <div class="table-cell">
          <div class="custom-td" :style="{width: `${columnWidth['转码状态'].res}px`}">
            <div>
              <a
                v-if="slotProps.item"
                :class="STATE_COLOR[slotProps.item.status]"
                :href="`${videoInfoUrl}${slotProps.item.cid}&aid=${aid}`"
                target="_blank">
                {{STATES[slotProps.item.status]}}
              </a>
              <span>{{formatXcodeState(slotProps.item.xcode)}}</span>
            </div>
          </div>
        </div>
        <div class="table-cell">
          <div class="custom-td" :style="{width: `${columnWidth['类型'].res}px`}">
            <div><el-tag type="info">{{slotProps.item.src_type}}</el-tag></div>
          </div>
        </div>

        <div class="table-cell">
          <div class="custom-td" :style="{width: `${columnWidth['分P标题'].res}px`}">
            <CopyrightFocus
              ref="copyrightFocusRef"
              v-model="slotProps.item.eptitle"
              textarea
              style="width: 100%;height: 32px;"
              :keywordData="copyrightKeywords[slotProps.item.cid]"
              :width="copyrightFocusWidth"
              height="100px"
              :placement="slotProps.isBottom ? 'top-start' : 'bottom'"
              :disabled="disabled"
              >
            </CopyrightFocus>
          </div>
        </div>
        <!--  -->
        <div class="table-cell">
          <div class="custom-td" :style="{width: `${columnWidth['分P简介'].res}px`}">
            <el-input
              class="textarea"
              type="textarea"
              placeholder="请输入内容"
              :rows="1"
              resize="vertical"
              v-model="slotProps.item.description"
            ></el-input>
          </div>
        </div>
        <!-- 一审结果&建议 -->
        <div class="table-cell table-cell-flex">
          <AgTextover ref="agTextoverRefs">
            <div class="custom-td custom-td__textover" :style="{
              width: `${columnWidth['一审结果&建议'].res}px`,
              minWidth: '120px'
            }">
              <span v-if="slotProps.item.tag"><label class="hl-color">一审TAG：</label>{{slotProps.item.tag}}</span>
              <span v-if="slotProps.item._attr"><label class="hl-color">分p限制：</label>{{slotProps.item._attr}}</span>
              <span v-if="slotProps.item.reason"><label class="hl-color">理由：</label>{{slotProps.item.reason}}</span>
              <span v-if="slotProps.item._note"><label class="hl-color">修改建议：</label>{{slotProps.item._note}}</span>
            </div>
           <div class="custom-td custom-td__textover-all custom-td__textover-content" slot="content"><span v-if="slotProps.item.tag"><label class="hl-color">一审TAG：</label>{{slotProps.item.description}}</span><span v-if="slotProps.item._attr"><label class="hl-color">分p限制：</label>{{slotProps.item._attr}}</span><span v-if="slotProps.item.reason"><label class="hl-color">理由：</label>{{slotProps.item.reason}}</span><span v-if="slotProps.item._note"><label class="hl-color">修改建议：</label>{{slotProps.item._note}}</span></div>
          </AgTextover>
        </div>
        <!-- 一审时间 -->
        <div class="table-cell">
          <div class="custom-td" :style="{width: `${columnWidth['一审时间'].res}px`}">
            <div>{{slotProps.item.passtime}}</div>
          </div>
        </div>
        <!-- 一审操作人 -->
        <div class="table-cell">
          <div class="custom-td" :style="{width: `${columnWidth['一审操作人'].res}px`}">
            <div>{{slotProps.item.oname}}</div>
          </div>
        </div>
        <!-- 视频操作 -->
        <div class="table-cell">
          <div class="custom-td" :style="{width: `${columnWidth['视频操作'].res}px`}">
            <div class="clip-list__operation">
              <el-button type="primary" size="small" @click="showClipPreview(slotProps.item)">切片</el-button>
              <el-button v-if="!hidePreviewBtn" type="primary" size="small" @click="showVideoPreview(slotProps.item)">预览</el-button>
              <el-dropdown type="primary" @command="(command) => handleMenu(command, slotProps.item)" trigger="hover" v-if="aid">
                <span class="el-dropdown-link">
                  <i class="el-icon-more"></i>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="showDownload">获取</el-dropdown-item>
                  <el-dropdown-item command="confirmSave" :disabled="pendingRequestLock">保存</el-dropdown-item>
                  <el-dropdown-item command="confirmDelete" :disabled="pendingRequestLock">删除</el-dropdown-item>
                  <el-dropdown-item command="showWeblink">外链</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
        </div>
      </template>
    </VirtualScroll>
    <!-- 不超过普通展示 -->
    <div class="table-div" ref="tableSubDiv" v-else>
      <div class="table-header">
        <div class="table-row">
          <div class="table-cell">
            <div class="custom-th" :style="{width: `${columnWidth['P序'].res}px`}">
              <div class="clip-list__order">
                <AgTooltip
                  placement="top-start"
                  class="item"
                  :maxWidth="300"
                >
                  <span>P序<i class="el-icon-warning" /></span>
                  <div slot="content" v-if="aid">按住每列P序进行拖动，可更换分P顺序，实时生效</div>
                </AgTooltip>
              </div>
            </div>
          </div>
          <div class="table-cell">
            <div class="custom-th" :style="{width: `${columnWidth['cid'].res}px`}">
              cid
            </div>
          </div>
          <div class="table-cell">
            <div class="custom-th" :style="{width: `${columnWidth['filename'].res}px`}">
              filename
            </div>
          </div>
          <div class="table-cell">
            <div class="custom-th" :style="{width: `${columnWidth['转码状态'].res}px`}">
              转码状态
            </div>
          </div>
          <div class="table-cell">
            <div class="custom-th" :style="{width: `${columnWidth['类型'].res}px`}">
              类型
            </div>
          </div>
          <div class="table-cell">
            <div class="custom-th" :style="{width: `${columnWidth['分P标题'].res}px`}">
              分P标题
            </div>
          </div>
          <div class="table-cell">
            <div class="custom-th" :style="{width: `${columnWidth['分P简介'].res}px`}">
              分P简介
            </div>
          </div>
          <div class="table-cell">
            <div class="custom-th" :style="{
              width: `${columnWidth['一审结果&建议'].res}px`,
              minWidth: '120px'
            }">
              一审结果&建议
            </div>
          </div>
          <div class="table-cell">
            <div class="custom-th" :style="{width: `${columnWidth['一审时间'].res}px`}">
              一审时间
            </div>
          </div>
          <div class="table-cell">
            <div class="custom-th" :style="{width: `${columnWidth['一审操作人'].res}px`}">
              一审操作人
            </div>
          </div>
          <div class="table-cell">
            <div class="custom-th" :style="{width: `${columnWidth['视频操作'].res}px`}">
              视频操作
            </div>
          </div>
        </div>
      </div>
      <div class="table-body table-body-overflow">
        <div class="table-row" v-for="(item, idx) in videos" :key="item.id" :class="tableRowClassName(item, idx)">
          <div class="table-cell">
          <div class="custom-td" :style="{width: `${columnWidth['P序'].res}px`}">
            <div class="clip-list__rank my-handle" @click="openPIndex(item)" style="color: var(--blue-light-1)">
              P{{item.index_order}}
            </div>
          </div>
        </div>
        <div class="table-cell">
          <div class="custom-td custom-td__textover" :style="{width: `${columnWidth['cid'].res}px`}">
            {{item.cid}}
          </div>
        </div>
        <div class="table-cell table-cell-flex">
          <AgTextover ref="agTextoverRefs">
            <div class="custom-td custom-td__textover" :style="{width: `${columnWidth['filename'].res}px`}">
              {{item.filename}}
            </div>
            <div class="custom-td custom-td__textover-all custom-td__textover-content" slot="content">{{item.filename}}</div>
          </AgTextover>
        </div>
        <div class="table-cell">
          <div class="custom-td" :style="{width: `${columnWidth['转码状态'].res}px`}">
            <div>
              <a
                v-if="item"
                :class="STATE_COLOR[item.status]"
                :href="`${videoInfoUrl}${item.cid}&aid=${aid}`"
                target="_blank">
                {{STATES[item.status]}}
              </a>
              <span>{{formatXcodeState(item.xcode)}}</span>
            </div>
          </div>
        </div>
        <div class="table-cell">
          <div class="custom-td" :style="{width: `${columnWidth['类型'].res}px`}">
            <div><el-tag type="info">{{item.src_type}}</el-tag></div>
          </div>
        </div>

        <div class="table-cell">
          <div class="custom-td" :style="{width: `${columnWidth['分P标题'].res}px`}">
            <CopyrightFocus
              ref="copyrightFocusRef"
              v-model="item.eptitle"
              textarea
              style="width: 100%;height: 32px;"
              :keywordData="copyrightKeywords[item.cid]"
              :width="copyrightFocusWidth"
              :disabled="disabled"
              height="100px">
            </CopyrightFocus>
          </div>
        </div>
        <!--  -->
        <div class="table-cell">
          <div class="custom-td" :style="{width: `${columnWidth['分P简介'].res}px`}">
            <el-input
              class="textarea"
              type="textarea"
              placeholder="请输入内容"
              :rows="1"
              resize="vertical"
              :disabled="disabled"
              v-model="item.description"
            ></el-input>
          </div>
        </div>
        <!-- 一审结果&建议 -->
        <div class="table-cell table-cell-flex">
          <AgTextover ref="agTextoverRefs">
            <div class="custom-td custom-td__textover" :style="{
              width: `${columnWidth['一审结果&建议'].res}px`,
              minWidth: '120px'
            }">
              <span v-if="item.tag"><label class="hl-color">一审TAG：</label>{{item.tag}}</span>
              <span v-if="item._attr"><label class="hl-color">分p限制：</label>{{item._attr}}</span>
              <span v-if="item.reason"><label class="hl-color">理由：</label>{{item.reason}}</span>
              <span v-if="item._note"><label class="hl-color">修改建议：</label>{{item._note}}</span>
            </div>
            <div class="custom-td custom-td__textover-all custom-td__textover-content" slot="content"><span v-if="item.tag"><label class="hl-color">一审TAG：</label>{{item.tag}}</span><span v-if="item._attr"><label class="hl-color">分p限制：</label>{{item._attr}}</span><span v-if="item.reason"><label class="hl-color">理由：</label>{{item.reason}}</span><span v-if="item._note"><label class="hl-color">修改建议：</label>{{item._note}}</span></div>
          </AgTextover>
        </div>
        <!-- 一审时间 -->
        <div class="table-cell">
          <div class="custom-td" :style="{width: `${columnWidth['一审时间'].res}px`}">
            <div>{{item.passtime}}</div>
          </div>
        </div>
        <!-- 一审操作人 -->
        <div class="table-cell">
          <div class="custom-td" :style="{width: `${columnWidth['一审操作人'].res}px`}">
            <div>{{item.oname}}</div>
          </div>
        </div>
        <!-- 视频操作 -->
        <div class="table-cell">
          <div class="custom-td" :style="{width: `${columnWidth['视频操作'].res}px`}">
            <div class="clip-list__operation">
              <el-button type="primary" size="small" @click="showClipPreview(item)">切片</el-button>
              <el-button v-if="!hidePreviewBtn" type="primary" size="small" @click="showVideoPreview(item)">预览</el-button>
              <el-dropdown v-show="!disabled" type="primary" @command="(command) => handleMenu(command, item)" trigger="hover"  v-if="aid">
                <span class="el-dropdown-link">
                  <i class="el-icon-more"></i>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="showDownload">获取</el-dropdown-item>
                  <el-dropdown-item command="confirmSave" :disabled="pendingRequestLock">保存</el-dropdown-item>
                  <el-dropdown-item command="confirmDelete" :disabled="pendingRequestLock">删除</el-dropdown-item>
                  <el-dropdown-item command="showWeblink">外链</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
        </div>
        </div>
      </div>
    </div>
    <!-- 下载弹窗 -->
    <el-dialog title="获取视频" :visible.sync="showDownloadModal" width="20%" :destroy-on-close="true">
      <el-button type="primary" size="small" @click="handleDownload">下载</el-button>
    </el-dialog>
    <!-- 编辑外链地址弹窗  -->
    <el-dialog title="编辑外链地址" :visible.sync="showWeblinkModal" :destroy-on-close="true">
      <el-form inline>
        <el-row>
          <el-form-item label="weblink">
            <el-input
              placeholder=""
              v-model="weblinkForm.weblink"
            ></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item>
            <el-button type="primary" size="small" @click="saveWeblink">保存</el-button>
          </el-form-item>
        </el-row>
      </el-form>
    </el-dialog>
    <!-- 弹窗 -->
    <!-- 切片弹窗 -->
    <ClipPreviewModal
      ref="cpmodalRef"
      v-if="showClipPreviewModal"
      :clip="clipPreviewData"
      :videos="videos"
      :cid="clipPreviewData.cid"
      :pIndex="clipPreviewData.pIndex"
      :aid="aid"
      @change-clip-index="(newClipIndex) => activeClipIndex = newClipIndex"
      @set-cover="setCover"
      @close="closeClipPreview"
      @modal-opened="clipModalOpened"
      @modal-closed="clipModalClosed">
      <CrashInfo
        :cid="clipPreviewData.cid">
      </CrashInfo>
    </ClipPreviewModal>
    <!-- 视频弹窗 -->
    <VideoPreviewModal
      v-if="videoPreview.pIndex && showVideoPreviewModal"
      class="preview-wrapper"
      :pIndex="videoPreview.pIndex"
      :title="videoPreview.title"
      :aid="aid"
      :cid="videoPreview.cid"
      :videosrc="videoPreview.videoId"
      :toggle="stat.videoToggle"
      :toggleText="stat.toggleText"
      :sliceMap="sliceMap"
      @toggle="handleVideoToggle"
      @close="closeVideoPreview"
      @update:slice-map="handleSliceMapChange"
    />
  </div>
</template>
<script>
import { detailApi, videoApi } from '@/api/index'
import { STATES } from '@/utils/constant'
import ClipPreviewModal from '@/components/TaskDetail/archive/ClipPreviewModal'
import VideoPreviewModal from '@/components/TaskDetail/archive/VideoPreviewModal'
import CrashInfo from '@/components/Archive/CrashInfo'
import notify from '@/lib/notify'
import { mapState } from 'vuex'
import { genHost } from '@/api/utils'
import Sortable from 'sortablejs'
import CopyrightFocus from './CopyrightFocus'
import VirtualScroll from '@/components/VirtualScroll'
import AgTextover from '@/components/element-update/Textover'
import AgTooltip from '@/components/element-update/Tooltip'
import { getAttrAndNote } from '@/utils'
import moment from 'moment'

export default {
  name: 'clip-list',
  inheritAttrs: false,
  data() {
    return {
      downloadUrl: '',
      showDownloadModal: false,
      showWeblinkModal: false,
      showClipPreviewModal: false,
      showVideoPreviewModal: false,
      weblinkForm: { id: '', weblink: '' },
      pendingRequestLock: false,
      STATE_COLOR: {
        '-30': 'blue',
        '-1': 'blue',
        '-2': 'red',
        '-4': 'red',
        '-16': 'red',
        '-100': 'red',
        0: 'green',
        10000: 'green'
      },
      STATES,
      videoPreview: {
        pIndex: '',
        title: '',
        cid: '',
        videoId: ''
      },
      clipPreviewData: {
        pIndex: '',
        cid: '',
        filename: ''
      },
      activeClipIndex: -1,
      copyrightKeywords: {},
      highlight: {},
      videos: [],
      XcodeState: {},
      disabledEpShow: false,
      hasSorted: false,
      columnWidth: {
        P序: {
          width: 60,
          res: 60
        },
        cid: {
          width: 100,
          res: 100
        },
        // 待分配
        filename: {
          flex: 1,
          res: 0
        },
        转码状态: {
          width: 150,
          res: 150
        },
        类型: {
          width: 85,
          res: 85
        },
         // 待分配
        分P标题: {
          flex: 1
        },
        分P简介: {
          flex: 1
        },
         // 待分配
        '一审结果&建议': {
          flex: 1,
          res: 0,
          minWidth: 120
        },
        一审时间: {
          width: 95,
          res: 95
        },
        一审操作人: {
          width: 100,
          res: 100
        },
        视频操作: {
          width: 150,
          res: 150
        }
      },
      copyrightFocusWidth: null,
      needVirtualScrollLimit: 5,
      oldVideoItem: {},
      newVideoItem: {},
      largeVideos: [],
      sortableObj: null,
      unwatch: null,
      sliceMap: {}
    }
  },
  props: {
    stat: {
      type: Object,
      default() {
        return {}
      }
    },
    aid: {
      type: [Number, String],
      default: ''
    },
    bvid: {
      type: [Number, String],
      default: ''
    },
    form: {
      type: Object,
      default() {
        return {}
      }
    },
    disabled: {
      type: Boolean,
      default: false
    },
    hidePreviewBtn: {
      type: Boolean,
      default: false
    }
  },
  components: {
    ClipPreviewModal,
    VideoPreviewModal,
    CrashInfo,
    CopyrightFocus,
    VirtualScroll,
    AgTextover,
    AgTooltip
  },
  watch: {
    COMMON: {
      handler(val) {
        if (val) {
          this.XcodeState = val.xcode_state || {}
        }
      },
      immediate: true
    },
    'stat.copyrightKeywords': {
      handler(val) {
        if (val) {
          this.copyrightKeywords = (val && val.videos) || {}
        }
      },
      immediate: true
    },
    'stat.videos': {
      handler(val) {
        if (val) {
          this.videos = val
        }
      },
      immediate: true
    },
    'stat.highlight': {
      handler(val) {
        if (val) {
          this.highlight = val.videos || []
        }
      },
      immediate: true
    },
    aid: {
      handler(val) {
        this.closeVideoPreview()
        this.closeClipPreview()
      },
      immediate: true
    }
  },
  computed: {
    ...mapState({
      COMMON: state => state.common.COMMON,
      collapseMenu: state => state.menu.collapseMenu
    }),
    videoInfoUrl() {
      return `${genHost()}/aegis/#/archive/archive-video-task/resource/detail?cid=`
    },
    needVS() {
      const { videos, needVirtualScrollLimit } = this
      // videos有变化时，重设largeVideos
      this.setLargeVideos()
      return videos && videos.length >= needVirtualScrollLimit
    }
  },
  methods: {
    resizeLayout() {
      this.$nextTick(() => {
        this.resizeTable()
        this.setLargeVideos()
        this.resizeTextOver()
      })
    },
    setLargeVideos() {
      this.largeVideos = this.videos && this.videos.map((item, idx) => {
        item.idx = item.index_order - 1
        return item
      })
    },
    resizeTable() {
      let tableDivEl = null
      if (this.needVS) {
        tableDivEl = this.$refs.vsRef.$refs.tableSubDiv
      } else {
        tableDivEl = this.$refs.tableSubDiv
      }
      let totalWidth = tableDivEl.clientWidth
      const el = document.getElementById('clip-list')
      if (el) {
        const parentEl = el.parentElement
        totalWidth = parentEl.clientWidth - 16
      }
      let occupyWidth = 0
      const calcCols = []
      let flexCount = 0
      for (const col in this.columnWidth) {
        // 如果没有设置minWidth，即设置的固定宽度
        if (!this.columnWidth[col].flex && !this.columnWidth[col].minWidth) {
          const maxWidth = this.columnWidth[col].width || 0
          this.columnWidth[col].res = this.columnWidth[col].width || 0
          occupyWidth += maxWidth
        } else {
          // 放入计算池
          calcCols.push({
            col,
            flex: this.columnWidth[col].flex || 1,
            minWidth: this.columnWidth[col].minWidth
          })
          flexCount += this.columnWidth[col].flex
        }
      }
      // 分配策略：优先分配minWidth，剩余的宽度再分配给flex
      const leftWidth = totalWidth - occupyWidth
      const onePieceWidth = leftWidth / (flexCount || 1)
      calcCols.map((item) => {
        this.columnWidth[item.col].res = onePieceWidth * (item.flex || 1)
      })
      this.$nextTick(() => {
        if (this.$refs.copyrightFocusRef && this.$refs.copyrightFocusRef[0]) {
          this.copyrightFocusWidth = getComputedStyle(this.$refs.copyrightFocusRef[0].$el).width
        }
      })
    },
    resizeTextOver() {
      const elArr = this.$refs.agTextoverRefs
      if (elArr) {
        elArr.map((item) => {
          item && item.reflow()
        })
      }
    },
    reflowCopyrightFocus() {
      const elArr = this.$refs.copyrightFocusRef
      if (elArr) {
        elArr.map((item) => {
          item && item.reJudegeOverLine()
        })
      }
    },
    openPIndex(data) {
      window.open(data._link, '_blank')
    },
    formatXcodeState(xcode) {
      return this.XcodeState[xcode]
    },
    initSort() {
      const table = document.querySelector('.table-body')
      this.sortableObj = Sortable.create(table, {
        handle: '.my-handle',
        onStart: (data) => {
          // 为虚拟滚动的swap做准备
          if (this.needVS) {
            const idx = data.oldIndex
            const renderList = this.$refs.vsRef.renderList
            const trueIdx = renderList[idx - 1].idx
            this.oldVideoItem = {
              arrIdx: trueIdx,
              data: this.videos[trueIdx]
            }
          }
        },
        onEnd: (data) => {
          if (this.needVS) {
            return this.sortVirtualEnd(data)
          }
          let { newIndex, oldIndex } = data
          if (newIndex === oldIndex) return
          const newList = this.videos
          // 数组的move
          // https://github.com/brownieboy/array.prototype.move/blob/master/src/array-prototype-move.js
          if (newList.length === 0) {
              return newList
          }
          while (oldIndex < 0) {
              oldIndex += newList.length
          }
          while (newIndex < 0) {
              newIndex += newList.length
          }
          if (newIndex >= newList.length) {
              let k = newIndex - newList.length
              while ((k--) + 1) {
                newList.push(undefined)
              }
          }
          newList.splice(newIndex, 0, newList.splice(oldIndex, 1)[0])
          this.listSortChange(newList)
        }
      })
    },
    sortVirtualEnd(data) {
      const { newIndex } = data
      const renderList = this.$refs.vsRef.renderList
      let trueNewIdx = renderList[newIndex - 1].idx
      this.newVideoItem = {
        arrIdx: trueNewIdx,
        data: this.videos[trueNewIdx]
      }
      if (trueNewIdx === this.oldVideoItem.arrIdx) {
        return
      }
      let oldIndex = this.oldVideoItem.arrIdx
      const newList = this.videos
      // 数组的move
      // https://github.com/brownieboy/array.prototype.move/blob/master/src/array-prototype-move.js
      if (newList.length === 0) {
          return newList
      }
      while (oldIndex < 0) {
          oldIndex += newList.length
      }
      while (trueNewIdx < 0) {
          trueNewIdx += newList.length
      }
      if (trueNewIdx >= newList.length) {
          let k = trueNewIdx - newList.length
          while ((k--) + 1) {
            newList.push(undefined)
          }
      }
      newList.splice(trueNewIdx, 0, newList.splice(oldIndex, 1)[0])
      this.listSortChange(newList)
    },
    tableRowClassName(row, idx) {
      const highLightFlag = !!(this.highlight && this.highlight[row.cid])
      const activeFlag = idx === this.activeClipIndex
      return `${highLightFlag ? 'table-row__highlight' : ''} ${activeFlag ? 'table-row__active' : ''}`
    },
    setCover(src) {
      this.form.cover = src
      if (this.stat.coverInfo) {
        this.stat.coverInfo.fullCover = src
      }
    },
    handleVideoToggle() {
      if (/^\d+$/.test(this.videoPreview.videoId.toString())) {
        // 从视频id切换为视频源文件
        this.stat.toggleText = '切换'
        this.videoPreview.videoId = this.stat.sourceFile
      } else {
        // 从视频源文件切换为视频id
        this.stat.toggleText = '切换终稿'
        this.videoPreview.videoId = this.stat.videoId
      }
    },
    handleSliceMapChange({ pIndex, slices }) {
      this.$set(this.sliceMap, pIndex, slices)
    },
    clipModalOpened() {
      document.removeEventListener('keydown', this.keyDownHandler)
    },
    clipModalClosed() {
      document.removeEventListener('keydown', this.keyDownHandler)
      document.addEventListener('keydown', this.keyDownHandler)
    },
    // 显示下载弹窗
    showDownload(data) {
      const { cid } = data
      videoApi.getDownload({
        cid
      }).then(res => {
        this.downloadUrl = res.data && res.data[0] && res.data[0].url
        this.showDownloadModal = true
      }).catch(_ => {})
    },
    // 去下载
    handleDownload() {
      window.open(this.downloadUrl, '_blank')
    },
    // 格式化video
    formatVideos(videos, bvid) {
      return videos.map(item => {
        return {
          // 详细字段描述看cliplist.js文件
          index_order: item.index_order,
          id: item.id,
          cid: item.cid,
          filename: item.filename,
          status: item.status,
          xcode: item.xcode_state,
          src_type: item.src_type,
          eptitle: item.eptitle,
          description: item.description,
          tag: item.tag_desc,
          ...getAttrAndNote(item.attribute, item.note),
          reason: item.reason,
          passtime: item.pass_time ? moment(item.pass_time * 1000).format('YYYY-MM-DD HH:mm:ss') : '',
          oname: item.oname,
          weblink: item.web_link
        }
      })
    },
    async reloadVideoList() {
      await videoApi.getVideos({
        aid: this.aid
      }).then((res) => {
        const {
          videos
        } = res.data || {}
        // 更新数据
        this.stat.videos = this.formatVideos(videos, this.bvid)
        this.$forceUpdate()
      }).catch(_ => {})
    },
    // 去保存
    confirmSave(item) {
      this.$confirm('确认保存修改?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.pendingRequest = true
        const newItem = {
          cid: item.cid,
          aid: this.aid,
          eptitle: item.eptitle,
          description: item.description
        }
        await videoApi.editVideo(newItem).then(() => {
          notify.success('修改成功')
          this.reloadVideoList()
        }).catch(_ => {})
      }).catch(_ => {}).finally(() => {
        this.pendingRequest = false
      })
    },
    // 删除
    confirmDelete(data) {
      const { cid } = data
      this.$confirm('确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.pendingRequest = true
        await videoApi.delVideo({
          cid,
          aid: this.aid
        }).then(() => {
          notify.success('删除成功')
          this.reloadVideoList()
        }).catch(_ => {})
        this.pendingRequest = true
      }).catch(() => {
        this.pendingRequest = false
      })
    },
    showWeblink(item) {
      this.weblinkForm = {
        cid: item.cid,
        aid: this.aid,
        weblink: item.weblink
      }
      this.showWeblinkModal = true
    },
    saveWeblink() {
      const form = { ...this.weblinkForm }
      videoApi.saveWeblink(form).then(() => {
        notify.success('外链保存成功')
        this.reloadVideoList()
        this.showWeblinkModal = false
      }).catch(_ => {})
    },
    handleMenu(command, data) {
      switch (command) {
        case 'showDownload': {
          this.showDownload(data)
          break
        }
        case 'confirmSave': {
          this.confirmSave(data)
          break
        }
        case 'confirmDelete': {
          this.confirmDelete(data)
          break
        }
        case 'showWeblink': {
          this.showWeblink(data)
          break
        }
      }
    },
    // 获取当前分P为第几个
    findPIndexByCid(cid) {
      const videoIndex = this.videos.findIndex(video => video.cid === cid)
      return videoIndex > -1 ? videoIndex + 1 : null // 分p从1开始
    },
    // 显示切片弹窗
    showClipPreview(clip) {
      const { cid, pIndex } = clip
      if (!cid || !(cid.toString().length)) {
        return
      }
      this.closeVideoPreview()
      if (cid === this.clipPreviewData.cid && this.showClipPreviewModal === true) {
        this.closeClipPreview()
        return
      }
      this.clipPreviewData = {
        ...clip,
        pIndex: this.findPIndexByCid(clip.cid)
      }
      this.activeClipIndex = pIndex - 1 // pIndex从1开始
      this.showClipPreviewModal = true
    },
    // 关闭切片弹窗
    closeClipPreview() {
      this.clipPreviewData.cid = ''
      this.clipPreviewData.filename = ''
      this.clipPreviewData.pIndex = ''
      this.activeClipIndex = null
      this.showClipPreviewModal = false
      // 关闭预览弹窗
      this.$refs.cpmodalRef && this.$refs.cpmodalRef.closeFocusModal()
    },
    // 显示视频弹窗
    showVideoPreview(clip) {
      const { eptitle, cid, id } = clip
      this.closeClipPreview()
      const clipInfo = {
        pIndex: this.findPIndexByCid(cid),
        title: eptitle,
        cid,
        videoId: id
      }
      // 显示视频预览
      const newClipIndex = clipInfo.pIndex
      if (this.videoPreview.pIndex === newClipIndex && this.showVideoPreviewModal === true) {
        this.closeVideoPreview()
        return
      }
      // videoId
      this.stat.videoId = clipInfo.videoId
      this.videoPreview = clipInfo
      this.activeClipIndex = newClipIndex - 1 // pIndex从1开始
      this.showVideoPreviewModal = true
    },
    // 关闭视频弹窗
    closeVideoPreview() {
      this.showVideoPreviewModal = false
      this.videoPreview = {}
      this.activeClipIndex = null
    },
    // 添加快捷键
    keyDownHandler(event) {
      const keyCode = event.keyCode
      if (event.target.nodeName === 'TEXTAREA') {
        return
      }
      if (keyCode === 27) { // ESC
        // 关闭切片 预览悬浮窗
        this.closeVideoPreview()
        this.closeClipPreview()
      }
    },
    listSortChange(list) {
      const form = {
        aid: this.aid,
        list_order: list.map((item, index) => {
          return {
            id: item.id,
            index: index + 1
          }
        })
      }
      this.hasSorted = true
      detailApi.setVideoOrder(form).then(async () => {
        notify.success('排序成功', 1500)
        this.hasSorted = true
        await this.reloadVideoList()
        if (this.needVS) {
          this.setLargeVideos()
          this.$refs.vsRef && this.$refs.vsRef.redraw()
          this.$forceUpdate()
        }
      }).catch(async _ => {
        await this.reloadVideoList()
        if (this.needVS) {
          this.setLargeVideos()
          this.$refs.vsRef && this.$refs.vsRef.redraw()
          this.$forceUpdate()
        }
      })
    }
  },
  mounted() {
    this.clipModalClosed()
    this.initSort()
    this.resizeTable()
    this.setLargeVideos()
    this.unwatch = this.$watch('collapseMenu', () => {
      this.resizeLayout()
    })
  },
  beforeDestroy() {
    this.sortableObj && this.sortableObj.destroy()
    this.sortableObj = null
    this.unwatch && this.unwatch()
    document.removeEventListener('keydown', this.keyDownHandler)
  }
}
</script>
<style lang="stylus">
.clip-list
  .popover-dark
    background rgba(104,104,104,0.8)
    border-radius 4px
    color var(--text-color-reverse)
    max-width unset
    word-break break-all
  .hl-color
    color var(--primary-color)
.clip-list__popover
  .hl-color
    color var(--primary-color)
  &>span
    margin-right 5px
</style>
<style lang="stylus" scoped>
.clip-list
  display flex
  width 100%
  &__rank
    font-size 16px
    font-weight bold
    cursor pointer
    margin-right 10px
    color var(--link-color)
    text-align center
  &__operation
    display flex
    justify-content center
    align-items center
  &__srctype
    padding 5px 5px 5px 0
  .blue
    color var(--blue)
    &:hover
      color var(--blue-dark-1)
  .red
    color var(--red)
    &:hover
      color var(--red-dark-1)
  .green
    color var(--green)
    &:hover
      color var(--green-dark-1)
  .result-flex
    height 35px
    padding 5px 10px
    line-height 23px
    box-sizing border-box
    border-width 1px 0px 1px 1px
    max-width 100%
    line-height 20px
    white-space normal
    word-break break-all
    &>span
      margin-right 5px
  &__card-highlight
    background-color var(--green-light-1)
  .el-icon-more
    transform rotage
    transform rotate(90deg)
    font-size 20px
    color var(--link-color)
  .custom-td__textover-content
    display inline-block
</style>
<style lang="stylus">
.clip-list
  .table-div
    display flex
    width 100%
    flex-direction column
    position relative
    .table-row
      display flex
      width 100%
      &__highlight
        .table-cell
          background-color var(--green-light-1)
        .custom-td
          background-color var(--green-light-1)
      &__active
        .table-cell
          background rgb(227, 242, 253) !important
        .custom-td
          background rgb(227, 242, 253) !important
    .table-cell
      vertical-align middle
      background var(--content-bg-color)
      &-flex
        display flex
        align-items center
        justify-content center
    .table-body
      display flex
      width 100%
      flex-direction column
      .table-cell
        border-top 2px solid #f9f9f9
        border-bottom 2px solid #f9f9f9
        box-sizing border-box
    .custom-th
      display inline-block
      -webkit-box-sizing border-box
      box-sizing border-box
      position relative
      vertical-align middle
      padding-left 10px
      padding-right 10px
      width 100%
      background #f9f9f9
      border-right 2px solid var(--border-color-reverse)
      font-size 14px
      font-weight bold
      color var(--text-color)
      padding 0
      line-height 20px
      text-align center
      background #f9f9f9
    .custom-td
      padding 0 10px
      box-sizing border-box
      font-size 14px
      color var(--text-color)
      line-height 20px
      height 100%
      display flex
      align-items center
      justify-content center
      &__textover
        word-break break-all
        overflow hidden
        text-overflow ellipsis
        display -webkit-box
        -webkit-line-clamp 4
        -webkit-box-orient vertical
        &-content
          color var(--text-color-reverse)
      &__textover-all
        word-break break-all
        &-content
          color var(--text-color-reverse)
</style>
