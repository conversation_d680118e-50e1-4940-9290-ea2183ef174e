<template>
  <div class="topic-card" v-if="topic && topic.id">
    <el-checkbox
      :value="cancelTopic"
      :true-label="1"
      :false-label="0"
      :disabled="!perms.CANCEL_TOPIC || disabled"
      @input="val => $emit('update:cancelTopic', val)">
      取消话题
    </el-checkbox>
    <span
      class="highlight">
      话题ID：{{topic.id}} {{topic.name}}
    </span>
  </div>
</template>
<script>
import { mapState } from 'vuex'
export default {
  name: 'TopicCard',
  props: {
    topic: {
      type: Object,
      default() {
        return {}
      }
    },
    cancelTopic: {
      type: Number,
      default: 0
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapState({
      perms: state => state.user.perms
    })
  }
}
</script>
<style lang="stylus" scoped>
.topic-card
  font-size 14px
  display flex
  flex-wrap wrap
  margin-bottom 5px
  align-items center
  .highlight
    padding 5px
    border-radius 3px
    color var(--primary-color)
    background-color var(--blue-light-2)
</style>
