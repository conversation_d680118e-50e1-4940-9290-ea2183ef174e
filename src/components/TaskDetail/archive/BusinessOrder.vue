<template>
  <div class="business-order">
    <el-form inline size="small" :disabled="disabled">
      <!-- 第一行 -->
      <el-row>
        <el-form-item>
          <el-checkbox
            v-bind:value="form.is_porder"
            v-if="!isBusinessOrder"
            :true-label="1"
            :false-label="0"
            :disabled="readOnly || businessAuthDisabled"
            @input="handleChangePorder"
          >
            设为非绿洲商业稿件
          </el-checkbox>
          <span v-else>
            {{
              stat.orderInfo.name || stat.orderInfo.id
                ? `${stat.orderInfo.name}: ${stat.orderInfo.id} `
                : ''
            }}
          </span>
          <el-button v-if="showBizSubmit" style="margin: 10px" type="primary" size="small" @click="submitBiz">商业信息提交</el-button>
        </el-form-item>
      </el-row>
      <el-row v-if="(!isBusinessOrder && active) || isOrder">
        <el-form-item>
          <el-select
            v-if="isOrder"
            v-bind:value="boForm.group_id"
            placeholder="选择流量套餐"
            :disabled="(readOnly && !isBusinessOrder) || businessAuthDisabled"
            @change="(val) => (boForm.group_id = val)"
          >
            <el-option
              v-for="option in options.flowTags"
              :key="option.id"
              :label="option.name"
              :value="option.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-checkbox
            v-bind:value="boForm.porder_notify"
            v-if="!isBusinessOrder && active"
            :true-label="1"
            :false-label="0"
            :disabled="(readOnly && !isBusinessOrder) || businessAuthDisabled"
            @input="(val) => (boForm.porder_notify = val)"
          >
            通知
          </el-checkbox>
        </el-form-item>
      </el-row>
      <template v-if="isOrder">
        <!-- 第二行 -->
        <el-row style="margin-top: 10px">
          <el-form-item label="推广行业" required>
            <!-- v-model="curIndustry" -->
            <el-select
              v-model="curIndustry"
              placeholder="选择推广行业"
              :disabled="readOnly || businessAuthDisabled"
              value-key="id"
              @change="indChange"
            >
              <el-option
                v-for="(option, idx) in industries"
                :key="idx"
                :label="option.name"
                :value="option"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="推广品牌"
            v-if="isMobileGame"
            required
          >
            <el-radio-group
              v-bind:value="boForm.official"
              @input="officialChange"
              :disabled="readOnly || businessAuthDisabled"
            >
              <el-radio :label="1">官方品牌</el-radio>
              <el-radio :label="0">其他品牌</el-radio>
            </el-radio-group>
          </el-form-item>
          <!-- 广告主 -->
          <el-form-item>
            <el-input
              :disabled="readOnly || businessAuthDisabled"
              v-model="boForm.advertiser"
              placeholder="广告主"
            ></el-input>
          </el-form-item>
        </el-row>
        <!-- 第三行 -->
        <el-row style="margin-top: 10px">
          <el-form-item>
            <el-select
              v-if="boForm.official"
              v-model="selectedBrand"
              placeholder="选择官方品牌"
              filterable
              value-key="id"
              :disabled="readOnly || businessAuthDisabled"
              @change="updateBrand"
            >
              <el-option
                v-for="(option, idx) in allBrands"
                :key="idx"
                :label="option.name"
                :value="option"
              >
              </el-option>
            </el-select>
            <el-input
              v-else
              v-model="boForm.brand_name"
              placeholder="推广品牌"
              :disabled="readOnly || businessAuthDisabled"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <div @mouseover="handleShowTip" @mouseleave="handleHideTip">
              <AgTooltip
                :disabled="disabledShow || businessAuthDisabled"
                placement="top"
              >
                <el-select
                  v-model="selectedShowTypes"
                  @change="updateShowType"
                  multiple
                  placeholder="选择推广形式"
                  filterable
                  value-key="id"
                  :disabled="readOnly || businessAuthDisabled"
                  @visible-change="(show) => (showDropdown = show)"
                  @focus="focus = true"
                  @blur="focus = false"
                >
                  <el-option
                    v-for="(option, idx) in showTypes"
                    :key="idx"
                    :label="option.name"
                    :value="option"
                  >
                  </el-option>
                </el-select>
                <div slot="content">
                  {{ showTypeText }}
                </div>
              </AgTooltip>
            </div>
          </el-form-item>
          <!-- 广告主 -->
          <el-form-item>
            <el-input
              v-model="boForm.agent"
              placeholder="代理商"
              :disabled="readOnly || businessAuthDisabled"
            ></el-input>
          </el-form-item>
        </el-row>
      </template>
    </el-form>
  </div>
</template>
<script>
import { businessOrderApi } from '@/api/index'
import { mapState } from 'vuex'
import AgTooltip from '@/components/element-update/Tooltip'
import notify from '@/lib/notify'

export default {
  name: 'business-order',
  inheritAttrs: false,
  data() {
    return {
      options: {
        flowTags: []
      },
      businessConfigs: [],
      // 当前选中的推广行业(怎么配置化)
      curIndustry: null,
      allBrands: [],
      // 当前选中的品牌
      selectedBrand: null,
      // 当前选中的推广形式
      selectedShowTypes: null,
      showTip: false,
      showDropdown: false,
      isOrder: false,
      pool: '',
      readOnly: true,
      business_flow_pool: 2,
      show_type: '',
      industry_id: '',
      brand_id: '',
      active: 0
    }
  },
  props: {
    form: {
      type: Object,
      default() {
        return {
          value: 0,
          group_id: 7,
          porder_notify: 0,
          official: 0,
          brand_name: ''
        }
      }
    },
    stat: {
      type: Object,
      default() {
        return {}
      }
    },
    boForm: {
      type: Object,
      default() {
        return {}
      }
    },
    disabled: {
      type: Boolean,
      default: false
    },
    aid: {
      type: [String, Number],
      required: true
    },
    showBizSubmit: Boolean
  },
  components: {
    AgTooltip
  },
  computed: {
    ...mapState({
      perms: (state) => state.user.perms
    }),
    isBusinessOrder() {
      return [1, 2].includes(this.pool)
    },
    industries() {
      return this.businessConfigs.filter((item) => item.type === 0)
    },
    showTypes() {
      return this.businessConfigs.filter((item) => item.type === 1)
    },
    isMobileGame(id) {
      return this.curIndustry && this.curIndustry.id === 1
    },
    showTypeText() {
      return this.selectedShowTypes
        ? this.selectedShowTypes.map((item) => item.name).join(',')
        : ''
    },
    disabledShow() {
      return !(this.showTip && !this.showDropdown && this.showTypeText)
    },
    businessAuthDisabled() {
      return !this.perms.ARC_PORDER
    }
  },
  watch: {
    'stat.businessInfo': {
      handler(val) {
        this.pool = (val || {}).pool
        this.business_flow_pool = val.business_flow_pool || 2
      },
      immediate: true
    },
    'stat.boReadonly': {
      handler(val) {
        this.readOnly = val
      },
      immediate: true
    },
    'boForm.show_type': {
      handler(val) {
        this.show_type = val
      },
      immediate: true
    },
    'boForm.industry_id': {
      handler(val) {
        this.industry_id = val
      },
      immediate: true
    },
    'boForm.brand_id': {
      handler(val) {
        this.brand_id = val
      },
      immediate: true
    },
    // 切换任务或者刚进来时，2.如果是商单数据，则需要刷新
    aid: {
      handler() {
        this.$nextTick(() => {
          this.clearData()
          const isPorder = this.stat.arcAttributes.is_porder
          this.isOrder = isPorder || this.isBusinessOrder
          this.active = isPorder
          if (this.isOrder) {
            this.init()
          }
        })
      },
      immediate: true
    }
  },
  methods: {
    // 清空本地勾选数据
    clearData() {
      this.options = {
        flowTags: []
      }
      this.businessConfigs = []
      this.curIndustry = null
      this.allBrands = []
      this.selectedBrand = null
      this.selectedShowTypes = null
    },
    // 切换成商单稿件就需要，查询一堆下拉项接口数据
    handleChangePorder(val) {
      this.form.is_porder = val
      this.isOrder = val || this.isBusinessOrder
      this.active = val
      if (val === 1) {
        this.init()
      }
    },
    // 获取流量套餐选项
    getFlowTags(flowPool) {
      businessOrderApi
        .getFlowTags({
          pool: flowPool,
          // 写死的state
          state: 0
        })
        .then((res) => {
          const data = res?.data?.items || []
          this.options.flowTags = data
        })
        .catch((_) => {})
    },
    /**
     * 获取某一业务类别下的商业推广信息配置 0: 私单， 1: 绿洲计划商单， 2: 花火计划商单
     * @param {Number} pool
     */
    async getBusinessOrderConfigs(pool) {
      await businessOrderApi
        .getConfigs({
          pool: this.pool === -1 ? 0 : this.pool,
          // 写死的type。这样返回的数据就包括type为1的推广形式和type为0的推广行业
          type: ''
        })
        .then((res) => {
          const data = res.data || []
          this.businessConfigs = data
        })
        .catch((_) => {})
    },
    async getBrands() {
      await businessOrderApi
        .getBrands()
        .then((res) => {
          const data = (res.data || []).map(item => {
            return {
              id: item.game_base_id,
              name: item.game_name
            }
          })
          this.allBrands = data
        })
        .catch((e) => console.error('获取官方品牌错误'))
    },
    indChange() {
      this.boForm.industry_id = this.curIndustry.id
      // 判断是否是手游
      this.boForm.official = this.isMobileGame ? 1 : 0
      // 如果是官方品牌的，则需要去拿选项列表
      if (this.boForm.official && this.curIndustry) {
        this.getBrands()
      } else {
        this.boForm.brand_name = ''
      }
      this.boForm.brand_id = ''
    },
    officialChange(val) {
      this.boForm.official = val
      if (
        this.boForm.official === 0 &&
        this.isMobileGame
      ) {
        // 手机游戏
        this.boForm.group_id = 21 // 其他手游品牌五限
      }
    },
    updateBrand(brand) {
      if (!brand) return
      const { id, name = '' } = brand
      this.boForm.brand_id = id
      this.boForm.brand_name = name
    },
    updateShowType(showTypes) {
      if (!showTypes || showTypes.length === 0) {
        this.boForm.show_type = ''
      } else {
        this.boForm.show_type = showTypes.map((item) => item.id).join(',')
      }
    },
    handleShowTip(e) {
      e && e.stopPropagation()
      this.showTip = true
    },
    handleHideTip(e) {
      e && e.stopPropagation()
      this.showTip = false
    },
    initShowType(showType) {
      if (showType) {
        // 转成数字
        const typeIds = showType.split(',').map(item => {
          return +item
        })
        this.selectedShowTypes = this.showTypes.filter(
          (type) => typeIds.indexOf(type.id) > -1
        )
      }
    },
    initBrand(industryId, brandId) {
      if (!industryId || industryId === 0) {
        this.curIndustry = null
        this.boForm.official = 0
        return
      }
      const curIndustry = (this.curIndustry = this.industries.find(
        (ind) => ind.id === industryId
      ))
      if (!curIndustry) return
      if (
        this.boForm.official ||
        this.isMobileGame
      ) {
        this.getBrands().then(() => {
          const brands = this.allBrands
          let brand = null
          const newBrandId = parseInt(brandId, 10)
          if (brands) {
            brand = brands.find((brand) => brand.id === newBrandId)
          }
          this.selectedBrand = brand || null
        })
      }
    },
    initIndustrySelected(industryId) {
      const idx = this.industries.findIndex((item) => +item.id === +industryId)
      if (idx > -1) {
        this.curIndustry = this.industries[idx]
      }
    },
    async init() {
      // 1.获取数据
      await this.getBusinessOrderConfigs()
      // 2.获取流量套餐
      const flowPool =
        this.business_flow_pool === -1 ? 2 : this.business_flow_pool
      this.getFlowTags(flowPool)
      // 3.初始化推广形式
      this.initShowType(this.show_type)
      // 4.初始化推广品牌
      // 先做成转化
      this.initBrand(this.industry_id, this.brand_id)
      // 5.初始化选中行业
      this.initIndustrySelected(this.industry_id)
    },
    async submitBiz() {
      try {
        await businessOrderApi.submitBusiness({
          is_porder: this.form.is_porder,
          aid: this.aid,
          industry_id: this.boForm.industry_id,
          brand_id: this.boForm.brand_id,
          brand_name: this.boForm.brand_name,
          official: this.boForm.official,
          show_type: this.boForm.show_type,
          show_front: this.boForm.show_front,
          advertiser: this.boForm.advertiser,
          agent: this.boForm.agent,
          group_id: this.boForm.group_id
        })
        notify.success('商业信息提交成功!')
      } catch (error) {
        notify.error(`商业信息提交失败: ${error.message}`)
      }
    }
  }
}
</script>
<style lang="stylus" scoped>
.business-order
  background var(--content-bg-color)
  >>>.el-form-item 
    margin-bottom 0
</style>
