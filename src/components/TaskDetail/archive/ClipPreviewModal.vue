<template>
  <Draggable
    width="auto"
    :zIndex="100"
    rememberPosition="clipPreview"
    :fixPosition="fixPosition"
    class="clip-preview-modal"
    limitClient>
    <div slot="noDragTrigger">
      <div class="clip-preview-modal__content">
        <ClipViewer
          v-if="clipReady && currentClip.cid"
          ref="smallModalViewer"
          :clip="currentClip"
          :filename="currentFilename"
          :isBigModal="bigModalOptions.isShow"
          :imgWidth="imgWidth"
          :imgHeight="imgHeight"
          :isFocusViewer="focusModalOptions.isShow"
          @show-big-modal="showBigModal"
          @close-big-modal="closeBigModal"
          @show-focus="showFocus"
          @page-change="pageChange"/>

        <el-dialog :visible.sync="focusModalOptions.isShow" :title="focusModalOptions.title" @close="modalClose" append-to-body class="body-focus-modal">
          <p>时间{{focusPicInfo.time}}</p>
          <img :src="focusPicInfo.src" class="focus-img" alt="切片预览" @error="onImgError"/>
        </el-dialog>

        <el-dialog :visible.sync="bigModalOptions.isShow" @close="modalClose" append-to-body width="85%" height="100%" class="big-modal-dialog">
          <ClipViewer
          ref="bigModalViewer"
            :clip="currentClip"
            :filename="currentFilename"
            :isBigModal="bigModalOptions.isShow"
            :imgWidth="imgWidth"
            :imgHeight="imgHeight"
            :isFocusViewer="focusModalOptions.isShow"
            @close-big-modal="closeBigModal"
            @show-focus="showFocus"
            @page-change="pageChange"/>
        </el-dialog>
      </div>
      <slot></slot>
    </div>
  </Draggable>
</template>

<script>
import { cloneDeep } from 'lodash-es'
import moment from 'moment'
import ClipViewer from './ClipViewer'
import { archiveApi } from '@/api/index'
import notify from '@/lib/notify'
import Draggable from '@/components/package/Draggable'
import { report } from '@/utils/index'
import { mapState } from 'vuex'
import clipKeysMixin from '@/mixins/clip-keys.js'

export default {
  name: 'clip-preview-modal',
  mixins: [
    clipKeysMixin
  ],
  props: {
    cid: {
      type: [String, Number]
    },
    pIndex: {
      default: null
    },
    videos: {
      type: Array,
      default() {
        return []
      }
    },
    maxWidth: {
      type: Number,
      default: 800
    },
    maxHeight: {
      type: Number,
      default: 550
    },
    aid: {
      type: [Number, String],
      default: ''
    }
  },
  components: {
    ClipViewer,
    Draggable
  },
  data() {
    return {
      fixPosition: {
        top: '50px',
        right: '20px'
      },

      clipReady: false,
      currentFilename: '',
      currentClip: {},
      focusPicInfo: {
        src: '',
        time: '',
        idx: 0
      },
      // 切片图默认宽高
      imgWidth: 800,
      imgHeight: 450,

      shotStore: {},

      prevType: 'page', // 往前，默认上一页， page, video 切片翻页或切换分p
      nextType: 'page', // 往后

      focusModalOptions: {
        isShow: false,
        isEscClose: true,
        title: '预览图片'
      },
      bigModalOptions: {
        isShow: false,
        width: '85%'
      },
      unwatchCid: null
    }
  },
  created() {
    this.unwatchCid = this.$watch('cid', (val) => {
      if (!val || !val.toString().length) return
      this.getClip(val)
    }, { immediate: true })
  },
  computed: {
    ...mapState({
      username: state => state.user.username,
      uid: state => state.user.uid
    })
  },
  methods: {
    handleClipShift(key) {
      if (!this.focusModalOptions.isShow) return
      const viewerComp = this.bigModalOptions.isShow ? this.$refs.bigModalViewer : this.$refs.smallModalViewer
      let nextIdx = this.focusPicInfo.idx
      let time = ''
      switch (key) {
        case 'pageUp':
        case 'arrowLeft':
          // 1.判断是否需要翻到上一页
          if (this.focusPicInfo.idx - 1 < 0) {
            this.pageChange('prev')
            this.$nextTick(() => {
              nextIdx = this.currentClip.timeList.length - 1
              time = this.currentClip.timeList[nextIdx]
              viewerComp.focusPic(time, nextIdx)
            })
          } else {
            nextIdx = Math.max(this.focusPicInfo.idx - 1, 0)
            time = this.currentClip.timeList[nextIdx]
            viewerComp.focusPic(time, nextIdx)
          }
          break
        case 'pageDown':
        case 'arrowRight':
          // 2.判断是否需要翻到下一页
          if (this.focusPicInfo.idx + 1 > this.currentClip.timeList.length - 1) {
            this.pageChange('next')
            this.$nextTick(() => {
              nextIdx = 0
              time = this.currentClip.timeList[nextIdx]
              viewerComp.focusPic(time, nextIdx)
            })
          } else {
            nextIdx = Math.min(this.focusPicInfo.idx + 1, this.currentClip.timeList.length - 1)
            time = this.currentClip.timeList[nextIdx]
            viewerComp.focusPic(time, nextIdx)
          }
          break
      }
    },
    getClip(cid) {
      if (cid == null) {
        return
      }
      const videos = this.videos
      const shotStore = this.shotStore

      this.clipReady = true

      const cidIndex = videos.findIndex(video => video.cid === cid)
      const cachedShot = shotStore[cid]
      if (cachedShot) {
        this.currentClip = cloneDeep(cachedShot)
        this.currentFilename = cachedShot.filename
        this.$emit('change-clip-index', cachedShot.pIndex - 1)
        this.goPage(1)
        return
      }

      // 请求cid切片
      archiveApi.getVideoshots({cid}).then(
        (res) => {
          const result = res.data || ''
          if (!result) {
            this.currentClip = {}
            return
          }
          result.cid = cid
          result.page = 1
          if (this.pIndex !== null) {
            result.pIndex = cidIndex + 1 // pIndex 1开始
          }
          result.index.shift()
          result.pageCount = Math.ceil(result.index.length / (result.img_x_len * result.img_y_len))
          result.index = result.index.map(time => secondsToTime(time))
          result.filename = this.getFilename(cid)

          this.shotStore[cid] = result
          this.prepareImageSize(result.image[0])
          this.currentClip = cloneDeep(result)
          this.currentFilename = result.filename
          this.$emit('change-clip-index', cidIndex)
          this.goPage(1)
        }
      ).catch((e) => { // 获取失败
        this.currentClip = {}
      })
    },

    getFilename(cid) {
      const video = this.videos.find(v => v.cid.toString() === cid.toString())
      return video ? video.filename : ''
    },

    goPage(pn) {
      const currentClip = this.currentClip
      let page = 1
      if (pn > 0) {
        page = pn
      }
      if (page >= currentClip.pageCount) {
        page = currentClip.pageCount
      }
      const pagesize = currentClip.img_x_len * currentClip.img_y_len
      const indexLen = currentClip.index.length
      let nextSize = indexLen - pagesize * (page - 1)
      nextSize = nextSize > pagesize ? pagesize : nextSize
      const timeList = currentClip.index.slice(
        pagesize * (page - 1),
        pagesize * (page - 1) + nextSize
      )
      if (timeList.length === 0) {
        notify.warning('切片数据缺失')
      }
      currentClip.page = page

      const videos = this.videos
      const videoIndex = videos.findIndex(video => video.cid === currentClip.cid)

      let prev = ''
      let next = ''
      let lastPage = ''
      let nextPage = ''
      const pageCount = currentClip.pageCount

      // 往前
      if (page - 1 > 0) {
        prev = '上一页'
        lastPage = page - 1
        this.prevType = 'page'
      } else if (videoIndex > 0) {
        prev = '前一分P'
        this.prevType = 'video'
      }
      // 往后
      if (page + 1 <= pageCount) {
        next = '下一页'
        nextPage = page + 1
        this.nextType = 'page'
      } else if (videoIndex < videos.length - 1) {
        next = '后一分P'
        this.nextType = 'video'
      }

      const pageInfo = {
        prev,
        next,
        lastPage,
        nextPage,
        pageCount
      }

      currentClip.videoIndex = videoIndex
      currentClip.pageInfo = pageInfo
      currentClip.timeList = timeList
    },
    pageChange(type) {
      const currentClip = this.currentClip
      const actionType = this[`${type}Type`]

      const pageOffset = type === 'next' ? 1 : -1

      if (actionType === 'video') {
        this.getClip(this.videos[currentClip.videoIndex + pageOffset].cid)
      } else {
        this.goPage(currentClip.page + pageOffset)
      }
    },
    onImgError() {
      notify.error('切片获取失败 @bvcflow')
    },
    showBigModal() {
      report('lookBigImage', { username: this.username, aid: this.aid })
      this.bigModalOptions.isShow = true
      this.$emit('modal-opened')
      this.$nextTick(() => {
        const modalMask = document.querySelector('.big-modal-dialog')
        if (modalMask) {
          modalMask.scrollTop = 75
        }
      })
    },
    closeBigModal() {
      this.bigModalOptions.isShow = false
    },

    showFocus(payload) {
      const { src, time, idx = 0 } = payload
      this.focusPicInfo = {
        src,
        time,
        idx
      }
      this.focusModalOptions.isShow = true
      this.$emit('modal-opened')
    },

    closeFocusModal() {
      this.focusModalOptions.isShow = false
    },

    modalClose() {
      this.$emit('modal-closed')
    },

    prepareImageSize(src) {
      const image = new Image()
      image.onload = () => {
        let width = image.width
        let height = image.height

        const ratio = width / height

        const MAX_IMG_WIDTH = this.maxWidth
        const MAX_IMG_HEIGHT = this.maxHeight
        if (width > MAX_IMG_WIDTH) {
          width = MAX_IMG_WIDTH
          height = width / ratio
        }
        if (height > MAX_IMG_HEIGHT) {
          height = MAX_IMG_HEIGHT
          width = height * ratio
        }
        this.imgWidth = width
        this.imgHeight = height
      }
      image.src = src
    }
  },
  beforeDestroy() {
    this.unwatchCid && this.unwatchCid()
  }
}

/**
 * seconds number to hh:mm:ss string
 */
function secondsToTime(seconds) {
  if (seconds === undefined) {
    return '-'
  }
  if (seconds === 0) {
    return '00:00'
  }
  const dur = moment.duration().add(seconds, 's')
  let hours = dur.hours()
  if (hours === 0) {
    hours = ''
  } else {
    hours = padTime(dur.hours())
    if (hours.length !== 0) {
      hours = `${hours}:`
    }
  }

  let minutes = padTime(dur.minutes())
  if (minutes.length !== 0) {
    minutes = `${minutes}:`
  }
  return hours + minutes + padTime(dur.seconds())
}

/**
 * left pad time number with 0
 */
function padTime(time) {
  return time < 10 ? `0${time}` : `${time}`
}
</script>

<style lang="stylus" scoped>
.clip-preview-modal
  background-color var(--border-color-light-2)
  padding 10px
  .drag-wrapper
    background-color var(--border-color-light-2)
    padding 10px
  .clip-preview-modal
    display flex
.action
  text-align center
  display flex
  justify-content center
  align-items center
  margin-top 10px
.focus-img
  min-width 100px
  min-height 80px
  max-width 580px
  max-height 580px
  margin auto
</style>
<style lang="stylus">
.big-modal-dialog
  .el-dialog
    min-height 100%
    margin 20px auto !important
.body-focus-modal
  .el-dialog
    width 600px
  .el-dialog__body
    padding 10px
</style>
