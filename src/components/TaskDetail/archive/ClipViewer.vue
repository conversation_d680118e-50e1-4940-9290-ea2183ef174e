<template>
  <div>
    <div
      v-if="clip.pageInfo"
      :class="{
        'btn-row': true,
        'big-btn-row': isBigModal
      }"
      :style="containerStyle"
    >
      <div class="v-left clip-btn-wrapper">
        <el-button
          type="primary"
          size="small"
          :disabled="clip.pageInfo.prev.length === 0"
          @click.stop="prevPage"
          v-behavior-track="'clip-viewer-prev-page-btn'"
        >
          {{ clip.pageInfo.prev || '上一页' }}
          <span v-if="clip.pageInfo.lastPage">
            {{ clip.pageInfo.lastPage }}
            /
            {{ clip.pageInfo.pageCount }}
          </span>
        </el-button>
      </div>

      <div v-if="!isBigModal" class="clip-btn-wrapper">
        <span v-if="clip.pIndex">P{{ clip.pIndex }}</span>
        <el-button
          type="primary"
          size="small"
          @click.stop="showBig"
          v-behavior-track="'clip-viewer-show-big-img-btn'"
        >
          查看大图
        </el-button>
      </div>
      <div v-if="showPager">
        <el-pagination
          layout="prev, pager, next"
          :page-count="clip.pageCount"
          :current-page="clip.page"
          :hide-on-single-page="true"
          :pager-count="5"
          @current-change="currentChange"
        ></el-pagination>
      </div>

      <div class="v-right clip-btn-wrapper">
        <el-button
          type="primary"
          size="small"
          :disabled="clip.pageInfo.next.length === 0"
          @click.stop="nextPage"
          v-behavior-track="'clip-viewer-next-page-btn'"
        >
          {{ clip.pageInfo.next || '下一页' }}
          <span v-if="clip.pageInfo.nextPage">
            {{ clip.pageInfo.nextPage }}
            /
            {{ clip.pageInfo.pageCount }}
          </span>
        </el-button>
      </div>
    </div>
    <div
      :class="{
        'img-wrapper': !isBigModal,
        'big-img-wrapper': isBigModal
      }"
      :style="containerStyle"
      @click="$emit('click-clip')"
    >
      <img
        :src="videoSlicesImgUrl"
        :class="{ 'clip-img': true, 'big-img': isBigModal }"
        ref="clipImg"
        @error="onPreviewImgError"
        alt="切片预览 @bfs"
      />
      <div
        v-if="clip.timeList.length > 0"
        :class="{ 'time-wrapper': true, 'big-label-wrapper': isBigModal }"
        :style="timeWrapperStyle"
        v-behavior-track="
          `clip-viewer-time-clip-img-area-${isBigModal ? 'big' : 'raw'}`
        "
      >
        <span
          v-for="(time, index) in clip.timeList"
          class="time-img"
          :key="index"
          :style="{
            width: `${100 / clip.img_x_len}%`,
            height: `${100 / clip.img_x_len}%`
          }"
          @click="focusPic(time, index)"
        >
          <time class="time-label">{{ time }}</time>
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import { genBvcUrl, replaceBfsImagesUrl } from '@/utils'
import notify from '@/lib/notify'

export default {
  name: 'clip-viewer',
  data() {
    return {
      imgZoomWidth: 'auto'
    }
  },
  props: {
    clip: {
      type: Object,
      required: true
    },
    content: {
      type: Array,
      default() {
        return []
      }
    },
    filename: {
      type: String,
      default: ''
    },
    isBigModal: {
      type: Boolean,
      required: false
    },
    imgWidth: {
      type: Number,
      required: true
    },
    imgHeight: {
      type: Number,
      required: true
    },
    isTask: {
      type: Boolean,
      default: false
    },
    isFocusViewer: {
      type: Boolean,
      default: false
    },
    showPager: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    timeWrapperStyle() {
      if (this.isBigModal) {
        return {
          width: `${this.imgZoomWidth}px`,
          marginLeft: `-${this.imgZoomWidth / 2}px`
        }
      }
      return {
        width: `${this.imgWidth}px`,
        height: `${this.imgHeight}px`
      }
    },
    containerStyle() {
      if (this.isBigModal) {
        return {
          width: `${this.imgZoomWidth}px`
        }
      }
      return {
        width: `${this.imgWidth}px`
      }
    },
    videoSlicesImgUrl() {
      const rawSlicesImgUrl = this?.clip?.image?.[this.clip?.page - 1]
      return replaceBfsImagesUrl(rawSlicesImgUrl)
    }
  },
  mounted() {
    document.removeEventListener('keydown', this.keyHandler)
    if (this.isBigModal || this.isFocusViewer) {
      this.imgZoomWidth = this.$refs.clipImg.clientWidth
      document.addEventListener('keydown', this.keyHandler)
    }
  },
  beforeDestroy() {
    document.removeEventListener('keydown', this.keyHandler)
  },
  methods: {
    nextPage() {
      this.$emit('page-change', 'next')
    },
    prevPage() {
      this.$emit('page-change', 'prev')
    },
    currentChange(currentPage) {
      this.$emit('page-change-by-page-num', currentPage)
    },
    keyHandler(event) {
      const keyCode = event.keyCode
      // 1.是否在查看某一张图
      // 2.是否在查看大图模式
      if (!this.isFocusViewer && this.isBigModal) {
        event.stopPropagation()
        // 快捷键左右切换查看大图
        if (keyCode === 37) {
          if (this.clip.pageInfo.prev.length === 0) return
          this.prevPage()
          return
        }
        if (keyCode === 39) {
          if (this.clip.pageInfo.next.length === 0) return
          this.nextPage()
          return
        }
        // 快捷键ESC 退出查看大图
        if (keyCode === 27) {
          this.closeBig()
        }
      }
    },

    focusPic(time, idx) {
      const cid = this.clip.cid
      let fileName = ''
      if (!this.isTask) {
        if (this.filename && this.filename.length) {
          fileName = this.filename
        }
      } else {
        if (this.content && Array.isArray(this.content)) {
          fileName = this.content[0].filename
        }
      }

      const url =
        fileName.length === 0
          ? `http://vs12450.acg.tv/get_video_pic.php?cid=${cid}&type=jsonp&pos=${time}`
          : genBvcUrl('panel_videoshot',{
              flowid: fileName,
              pos: time
            })

      this.showFocus({
        time,
        src: url,
        idx
      })

      this.$emit('click-clip-preview', {
        time,
        idx
      })
    },
    showFocus(payload) {
      this.$emit('show-focus', payload)
    },
    showBig() {
      this.$emit('show-big-modal')
    },
    closeBig() {
      this.$emit('close-big-modal')
    },
    onPreviewImgError() {
      notify.warning('切片预览获取失败 @bfs', 1500)
    }
  }
}
</script>
<style lang="stylus" scoped>
.btn-row {
  text-align: center;
  clear: both;
  height: 45px;
  display: flex;
  justify-content: space-between;
  background: var(--content-bg-color);
  padding: 0 10px;
  box-sizing: border-box;
}

.clip-btn-wrapper {
  display: inline-block;
  margin: 5px 0 5px;
}

.big-btn-row {
  height: 40px;

  .clip-btn-wrapper {
    margin: 0;
  }
}

.img-wrapper {
  position: relative;
  background-color: transparent;
}

.big-img-wrapper {
  position: relative;
}

.clip-img {
  display: block;
  max-width: 100%;
  max-height: 521px;

  &:after {
    content: '切片预览图获取失败 @bfs';
    font-size: 16px;
    text-align: center;
    display: block;
    position: absolute;
    padding-top: 20px;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: transparent;
  }
}

.time-wrapper {
  position: absolute;
  left: 0px;
  top: 0px;
}

.time-img {
  float: left;
  cursor: pointer;
}

.big-img {
  display: block;
  width: 100%;
  max-height: 50%;
  margin: 5px auto;
}

.big-label-wrapper {
  left: 50%;
  max-width: none;
  height: 100%;
}

.time-label {
  float: left;
  font-size: 14px;
  padding: 2px 4px;
  line-height: 14px;
  color: var(--text-color-reverse);
  background-color: rgba(0, 0, 0, 0.3);
}
</style>
