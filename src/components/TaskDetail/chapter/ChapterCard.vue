<template>
  <div class="chapter-card">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>分段章节列表</span>
      </div>
      <div class="chapter-list">
        <el-row
          class="chapter-item"
          type="flex"
          v-for="(item, index) in chapters"
          :key="index"
        >
          <el-col :span="6" style="margin-right: 18px">
            时间段：
            <em class="time-range" @click="seek(item)">
              {{ showTimeRange(item) }}
            </em>
          </el-col>
          <el-col :span="18">
            文案：
            <em class="content">{{ item.content }}</em>
          </el-col>
        </el-row>
      </div>
    </el-card>
    <VideoSinglePlayer
      ref="playBox"
      :aid="aid"
      :cid="cid"
      :extraConfig="{
        keyboard: { focused: true, global: false }
      }"
      className="player-iframe"
    ></VideoSinglePlayer>
  </div>
</template>
<script>
import { hhmmssS } from '@/lib/formattime'
import VideoSinglePlayer from '@/components/VideoSinglePlayer'
export default {
  props: {
    detail: {
      type: Object,
      default: () => {}
    }
  },
  components: {
    VideoSinglePlayer
  },
  data() {
    return {
      url: ''
    }
  },
  computed: {
    chapters() {
      if (
        !this.detail ||
        !this.detail.resource ||
        !this.detail.resource.metas
      ) {
        return []
      }
      const data = (this.detail.resource.metas.chapters || []).map((item) => {
        return {
          ...item,
          type: 2
        }
      })
      return data
    },
    aid() {
      return this.detail?.resource?.extra1
    },
    cid() {
      return this.detail?.resource?.extra2
    }
  },
  watch: {},
  created() {
    document.domain = 'bilibili.co'
  },
  methods: {
    showTimeRange(chapter) {
      return `${hhmmssS(chapter.from)} - ${hhmmssS(chapter.to)}`
    },
    seek(item) {
      const from = item.from
      window.nanoPlayer?.seek(from)
    }
  }
}
</script>
<style lang="stylus">
.chapter-card {
  .chapter-list {
    max-height: 280px
    overflow auto
    padding: 6px 6px 0px
    .chapter-item {
      margin-bottom: 18px
      font-size: 14px
      .time-range {
        color: var(--link-color)
        cursor: pointer
      }
      .content {
        color: var(--primary-color)
      }
    }
  }

  .player-iframe {
    width 100%
    height 100%
  }
}
</style>
