<template>
  <div class="general-card" :style="{'height': `${(normalCard.style && normalCard.style.height) || 'unset'}`}">
    <el-card class="box-card general-info" v-if="normalCard.action === 'showtime' && $route.path.indexOf('gardener') < 0 ? show() : 1">
      <div slot="header" class="clearfix">
        <span>{{normalCard.title}}</span>
      </div>
      <el-form inline @submit.stop.prevent.native :label-width="(normalCard.style && normalCard.style.width) || ''" class="general-form">
        <el-row v-for="(row, index) in normalCard.data" :key="index" :style="normalCard.style && normalCard.style[index]">
          <el-form-item
            v-for="(item, i) in row"
            :key="i"
            :label="`${item.label}：`"
            :class="{
							'text-area': item.type === 'textarea' || item.type === 'hit',
							'tip': item.type === 'textarea' || item.type === 'hit',
							'tag-item': item.type === 'tag' ,
							'multiple-img': item.type === 'multipleImg',
              'topic-text': item.type === 'TopicTypeIdText'
						}">

            <span class="value" v-if="item.type === 'extra'">{{ extraValue(item.label) }}</span>

            <span
              :class="{
                'blue-fans-font': detail.user_info.follower >= 10000 && detail.user_info.follower < 100000,
                'orange-fans-font': detail.user_info.follower >= 100000 && detail.user_info.follower < 1000000,
                'red-fans-font': detail.user_info.follower >= 1000000
              }"
              v-else-if="item.type === 'follower' && detail.user_info"
            >
              {{ detail.user_info.follower }}
            </span>

            <el-input
            v-else-if="item.type === 'textarea'"
            type="textarea"
            :rows="item.action && item.action === 'rows' ? item.action_params.rows : 2"
            :placeholder="getValue(item.key)"
            disabled>
            </el-input>

            <span class="value" v-else-if="item.type === 'datetime'">{{  timeVal(item.label) }}</span>

            <span class="value" v-else-if="item.type === 'comic_mark'">{{  mark() }}</span>

            <div v-else-if="item.type === 'img'">
              <div
                  class="cover nothing"
                  v-if="!getValue(item.key) || getValue(item.key).indexOf('transparent') >= 0"
                  :style="{
                    'height': `${item.style.height}px`,
                    'width': `${item.style.width}px`,
                    'line-height': `${item.style.height}px`
                  }"
                >
                  无预览
            </div>
              <div class="img-box-wrap" v-else>
                <img
                  :src="getValue(item.key)"
                  class="cover"
                  :style="{
                    'height': `${item.style.height}px`,
                    'width': `${item.style.width}px`
                  }"
                  @click="showPreivewImage(item.key, item.label, i)"
                />
              </div>
              <ViewerBox
              :imgArray="imgObj[i]"
              :options="options"
              :viewerID="`coverViewBoxImages${i}`"
              @close="onClose"
              />
            </div>

            <div v-else-if="item.type === 'multipleImg' && getValue(item.key)">
              <div v-for="(pic, imgIndex) in imgs(item.key)" :key="imgIndex" style="display: inline-block">
                <div class="img-box-wrap"  v-if="pic && pic.indexOf('transparent') < 0">
                  <img
                    :src="pic"
                    class="cover"
                    :style="{
                      'height': `${item.style.height}px`,
                      'width': `${item.style.width}px`
                    }"
                    @click="showMultiplePreivewImage(imgIndex, item, i)"
                  />
                </div>
                <div
                  v-else
                  class="cover nothing"
                  :style="{
                    'height': `${item.style.height}px`,
                    'width': `${item.style.width}px`,
                    'line-height': `${item.style.height}px`
                  }"
                >
                  无预览
                </div>
              </div>
              <ViewerBox
                :imgArray="multiImg[i]"
                :options="multiImgOptions[i]"
                :viewerID="`multipleCoverViewBoxImages${i}`"
                @close="onCloseMultiImg"
              />
            </div>

            <div
              v-else-if="item.type === 'multipleImg' && !getValue(item.key)"
              class="cover nothing"
              :style="{
                'display': 'inline-block',
                'height': `${item.style.height}px`,
                'width': `${item.style.width}px`,
                'line-height': `${item.style.height}px`
              }"
            >
              无预览
            </div>

						<div class="tags" v-else-if="item.type === 'tag'">
              <el-tag type="info" size="mini" v-for="(tag, index) in getValue(item.key)" :key="index">{{tag}}</el-tag>
            </div>

						<el-button class="link-btn" type="text" v-else-if="item.type === 'link'" @click="goToNewPage(item.key, item.action, item.action_params)">{{getValue(item.key)}}</el-button>

            <p v-else-if="item.type === 'hit'" v-html="getHitContent(item.key)" class="hit el-textarea__inner"></p>

            <p v-else-if="item.type === 'arctype'">{{arctypeMap[getValue(item.key)]}}</p>

            <!-- 话题活动组件 -->
            <TopicTypeIdText v-else-if="item.type === 'TopicTypeIdText'" :value="getValue(item.key)"></TopicTypeIdText>

            <!-- 话题活动组件 -->
            <TopicTypeInfo v-else-if="item.type === 'TopicTypeInfo'" :value="getValue(item.key)"></TopicTypeInfo>

            <div v-if="item.type === 'text'">
              <span class="value" v-if="item.action === 'kv'">{{ handleValueChange(item) }}</span>
              <span class="value" v-else>{{ getValue(item.key) }}</span>
            </div>
          </el-form-item>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>
<script>
import { mapState, mapActions } from 'vuex'
import { datetime } from '@/lib/formattime'
import { formatRegExp, escapeText } from '../../pages/contentAudit/common'
import { transformKey } from './common.js'
import ViewerBox from '@/components/ViewerBox'
import TopicTypeIdText from '@/components/TopicTypeIdText'
import TopicTypeInfo from '@/components/TopicTypeInfo'
import { genHost } from '@/api/utils'

export default {
  components: {
    ViewerBox,
    TopicTypeIdText,
    TopicTypeInfo
  },
  props: {
    normalCard: {
      type: Object,
      default: () => {
        return {}
      }
    },
    detail: {
      type: Object,
      default: () => {
        return {}
      }
    },
    extra: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      options: {
        initialViewIndex: 0,
        keyboard: true
      },
      multiImgOptions: {},
      imgObj: {},
      multiImg: {}
    }
  },
  computed: {
    ...mapState({
      arctypeMap: state => state.arctype.arctypeMap
    })
  },
  created() {
    this.getArctype()
  },
  methods: {
    ...mapActions({
      getArctype: 'arctype/getArctype'
    }),
    getValue(key) {
      return transformKey(key, this.detail)
    },
    handleValueChange(item) {
      const key = +this.getValue(item.key)
      return item.action_params.enums[key]
    },
    extraValue(k) {
      let val = '暂无'
      const key = this.extraKey(k)
      if (this.detail.resource) {
        this.extra.forEach(i => {
          if (i.key === key) {
            val = (
              (i.enum.filter(e => {
                return e.value === this.detail.resource[key]
              })[0]) || {}
            ).label || '暂无'
          }
        })
      }
      return val
    },
    extraKey(val) {
      const extra = this.extra.filter(e => e.name === val)[0] || {}
      return extra.key
    },
    timeVal(label) {
      if (!this.detail.resource) {
        return '暂无'
      }
      const key = this.extraKey(label)
      return datetime(+this.detail.resource[key] * 1000)
    },
    getHitContent(key) {
      let hit = this.detail.hit || []
      let content = escapeText(this.getValue(key))
      if (hit) {
        hit.forEach(key => {
          let newStr = formatRegExp(key)
          let regexp = new RegExp(newStr, 'g')
          content = content.replace(regexp, `<em style="color:var(--red)">${key}</em>`)
        })
      }
      return content
    },
    mark() {
      if (!this.extra || !this.extra.length || !this.detail.resource) {
        return '无'
      }
      const enums = this.extra.find(e => e.key === 'extra2').enum
      return enums.find(e => e.value === this.detail.resource.extra2).label
    },
    showPreivewImage(key, label, vIndex) {
      let temp = []
      this.imgObj = {}
      temp.push({
        src: this.getValue(key),
        name: label
      })
      this.imgObj[vIndex] = [...temp]
    },
    showMultiplePreivewImage(index, item, vIndex) {
      const imgs = this.imgs(item.key) || []
      this.multiImgOptions[vIndex] = {
        initialViewIndex: index,
        keyboard: true
      }
      this.multiImg = {}
      this.multiImg[vIndex] = imgs.map(pic => {
        return {
          src: pic,
          name: item.label
        }
      })
    },
    onClose() {
			this.imgObj = {}
    },
    onCloseMultiImg() {
      this.multiImg = {}
    },
    show() {
      if (this.normalCard.action_params) {
        /* eslint-disable */
        const fn = new Function(this.normalCard.action_params.expression)
        return fn()
        /* eslint-enable */
      }
      return true
    },
		imgs(key) {
      let vals = this.getValue(key)
      if (vals) {
        return vals.split(',')
      }
      return []
		},
		goToNewPage(key, action, params) {
			const val = this.getValue(key)
			if (action === 'aid') {
        window.open(`${genHost()}/aegis/#/audit/tasks/detail/11?business_id=11&oid=${val}&list_type=00`)
			} else if (action === 'mid') {
				window.open(`https://space.bilibili.com/${val}`)
			} else if (action === 'custom') {
        // 前端动态拼接链接
        const url = params.url.replace(/\$\{(.*?)\}/, val)
        window.open(url)
      } else {
        // 服务端返回完整的链接
        window.open(val)
      }
    }
  }
}
</script>
<style lang="stylus">
  .general-card {
    .general-info {
      height: 100%
      .el-card_body {
        height: calc(100% - 40px)
        overflow: auto
      }
    }
    .general-form {
    }
    .cover {
      display: block
      margin-right: 8px
      margin-bottom 8px
    }
    .nothing {
      border: 1px solid #fbfbfb
      text-align: center
      font-size: 20px
      color: var(--text-color-light-2)
      font-weight: bold
      border-radiu: 4px
    }
    .text-area {
      display: flex
    }
		.tag-item {
      display: flex
			width: 100%
			.el-form-item__content {
				width 80%
			}
      margin-bottom: 10px !important
    }
		.multiple-img {
			display: flex
			width: 100%
			.el-form-item__content {
				display flex
				width 85%
				flex-wrap wrap
			}
		}
		.tags {
			width 100%
			height: 30px
			border: 1px solid var(--border-color-light-1)
			padding: 4px 0px 8px 13px
			border-radius: 5px
			overflow: auto
			line-height: 30px
      .el-form-item__label {
        width: 100px
      }
			.el-tag {
				margin-right: 8px
			}
		}
    .link-btn {
      padding 8px 0px !important
    }
    .img-box-wrap {
      display inline-flex
      padding 10px 12.5px
      background-image url('~@/assets/transparent.png')
      background-repeat repeat
      margin 0
      .cover {
        margin 0
      }
    }
  }
</style>
<style lang="stylus" scoped>
.general-card
  .topic-text
    display flex
    width 100%
    margin-bottom: 10px !important
    >>>.el-form-item__content
      width calc(100% - 110px)
</style>
