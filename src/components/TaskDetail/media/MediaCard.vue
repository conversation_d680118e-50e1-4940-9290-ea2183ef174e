<template>
  <div class="media-card">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>播单内容</span>
      </div>
      <el-button
        @click="deleted(null)"
        type="danger"
        size="small"
        :disabled="!multipleSelection.length"
      >
        批量删除
      </el-button>
      <el-table
        border
        ref="multipleTable"
        :data="tableData"
        tooltip-effect="dark"
        style="width: 100%; margin: 20px 0px"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          align="center"
          type="selection"
          width="55"
        ></el-table-column>
        <el-table-column
          v-for="(item, index) in COLUMNS"
          :key="index"
          :label="item.label"
          :prop="item.prop"
          align="center"
        >
          <template v-slot="scope">
            <el-button
              v-if="item.prop === 'op'"
              size="mini"
              type="danger"
              @click="deleted(scope.row)"
            >
              删除
            </el-button>
            <p
              v-else-if="item.prop === 'resource_id'"
              type="text"
              @click="goDetail(scope.row)"
              class="a-cell"
            >
              {{ scope.row.resource_id }}({{ scope.row.bv_id }})
            </p>
            <p
              v-else-if="item.prop === 'title'"
              v-highlight-config="
                card.sensitive_words && card.sensitive_words.title
              "
              type="text"
              @click="goPlayPage(scope.row)"
              class="a-cell"
            >
              {{ scope.row.title }}
            </p>
            <img
              v-else-if="item.prop === 'cover_url'"
              :src="scope.row.cover_url"
              width="200"
              height="100"
              @click="showPreivewImage(scope.row.cover_url)"
            />
            <p v-else>{{ scope.row[item.prop] }}</p>
          </template>
        </el-table-column>
      </el-table>
      <Pagination
        class="pagination"
        :pager="pager"
        @getList="getList"
        :computedPager="false"
        :tableData="tableData"
        showSize="medium"
      ></Pagination>
      <ViewerBox
        :imgArray="imgArray"
        :options="options"
        :viewerID="`coverViewBoxImages`"
        @close="onClose"
      />
    </el-card>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import notify from '@/lib/notify'
import { hhmmssS } from '@/lib/formattime'
import ViewerBox from '@/components/ViewerBox'
import { createNamespacedHelpers, mapState } from 'vuex'
import { genHost } from '@/api/utils'
import { mediaApi } from '@/api/index'
import { sleep } from '@/v2/utils'

const moduleName = 'env'
const { mapGetters } = createNamespacedHelpers(moduleName)

const COLUMNS = [
  {
    label: '序号',
    prop: 'index'
  },
  {
    label: '稿件ID',
    prop: 'resource_id'
  },
  {
    label: '封面',
    prop: 'cover_url'
  },
  {
    label: '内容',
    prop: 'title'
  },
  {
    label: '时长',
    prop: 'duration'
  },
  {
    label: '状态',
    prop: 'is_normal'
  },
  {
    label: '操作',
    prop: 'op'
  }
]
export default {
  components: {
    Pagination,
    ViewerBox
  },
  props: {
    detail: {
      type: Object,
      default: () => {
        return {}
      }
    },
    // 工作台卡片配置
    card: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      tableData: [],
      multipleSelection: [],
      pager: {
        ps: 10,
        pn: 1,
        total: 0
      },
      mediaState: {
        1: '公开',
        0: '非公开'
      },
      options: {
        initialViewIndex: 0,
        keyboard: true
      },
      imgArray: [],
      COLUMNS,
      env: ''
    }
  },

  watch: {
    'detail.resource': {
      handler(val) {
        if (val) {
          this.getList()
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.env = this.getEnv()
  },
  computed: {
    ...mapGetters(['getEnv']),
    ...mapState({
      isRedBlue: (state) => state.todoDetail.isRedblue
    })
  },
  methods: {
    getList() {
      this.tableData = []
      const resource = (this.detail && this.detail.resource) || {}
      mediaApi
        .getResourceList({
          media_id: +resource.oid,
          mid: +resource.mid,
          page_num: this.pager.pn,
          page_size: this.pager.ps
        })
        .then((res) => {
          if (!res.data) return
          ;(res.data.list || []).forEach((d, i) => {
            d.index = i + 1
            d.duration = hhmmssS(d.duration)
            d.is_normal = this.mediaState[d.is_normal]
          })
          this.tableData = res.data.list || []
          this.pager.total = res.data.total
          this.$nextTick(() => {
            this.$EventBus.$emit('sensitiveWordChange')
          })
        })
        .catch((_) => {})
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    deleted(row) {
      const opName = row ? '删除' : '批量删除'
      this.$confirm(`此操作将${opName}当前选中审核内容, 是否继续?`, opName, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.submit(row)
        })
        .catch(() => {})
    },
    async submit(row) {
      let resources = ''
      if (row) {
        resources = `${row.resource_id}:${row.type}`
      } else {
        resources = this.multipleSelection
          .map((item) => `${item.resource_id}:${item.type}`)
          .join(',')
      }
      if (this.isRedBlue) {
        await sleep(200)
        notify.success('删除成功')
      } else {
        try {
          const res = await mediaApi.deleteResource({
            media_id: this.detail && this.detail.resource && +this.detail.resource.oid,
            mid: this.detail && this.detail.resource && +this.detail.resource.mid,
            resources
          })
          if (res.code === 0) {
            notify.success('删除成功')
          } else {
            notify.error(res.message)
          }
        } catch (error) {
          console.error(error)
        }
      }

      const resourceIdList = row
        ? [row.resource_id]
        : this.multipleSelection.map(e => e.resource_id)
      this.removeTableItems(resourceIdList)
      this.$forceUpdate()
      this.$nextTick(() => this.$EventBus.$emit('sensitiveWordChange'))
    },
    removeTableItems(resourceIdList) {
      this.tableData = this.tableData.filter(e => !resourceIdList.includes(e.resource_id))
    },
    showPreivewImage(url) {
      this.imgArray = [
        {
          src: url,
          name: '封面'
        }
      ]
    },
    onClose() {
      this.imgArray = []
    },
    goDetail(row) {
      window.open(
        `${genHost()}/aegis/#/audit/tasks/detail/11?business_id=11&oid=${
          row.resource_id
        }&list_type=00`
      )
    },
    goPlayPage(row) {
      window.open(`https://www.bilibili.com/video/av${row.resource_id}`)
    }
  }
}
</script>
<style lang="stylus">
.media-card {
  .a-cell {
    cursor: pointer;
    color: var(--link-color);
  }
}
</style>
