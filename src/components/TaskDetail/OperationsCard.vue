<template>
  <div class="operations-card">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>审核操作</span>
      </div>
      <el-form inline @submit.stop.prevent.native>
        <el-form-item v-if="Object.values(ops).length">
          <el-button
          v-for="op in ops.operations"
          :key="op.bind_id_list"
          :type="op.ch_name === '通过' ? 'success': op.ch_name === '驳回' ? 'warning' : (op.ch_name === '封禁' || op.ch_name === '锁定') ? 'danger' : 'primary'"
          size="small"
          @click="openDialog(op)">
            {{ `${op.ch_name}${(noHotkey && op.ch_name !== '通过') || !KEY_MAP[op.ch_name] ? '' : `(${KEY_MAP[op.ch_name]})`}` }}
          </el-button>
        </el-form-item>
        <el-form-item v-else>
          <el-button
          v-for="(op, index) in ((operationCard.action_params && operationCard.action_params.buttons) || [])"
          :key="index"
          :type="op.label === '通过' ? 'success': op.label === '驳回' ? 'warning' : (op.label === '封禁' || op.label === '锁定') ? 'danger' : 'primary'"
          size="small">
            {{ `${op.label}(${op.key})` }}
          </el-button>
        </el-form-item>
        <el-row v-if="showForbid">
          <el-form-item label="禁止项：">
            <el-checkbox-group v-model="forbidList" @change="handleCheckedChange">
              <el-checkbox label="1">禁止转发</el-checkbox>
              <el-checkbox label="2">禁止评论</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-row>
      </el-form>
    </el-card>

     <component
      v-for="(item, index) in comps"
      :key="index"
      :ref="item.ref"
      :is="item.name"
      @getTask="getTask"
      @goBack="goBack"
      :codeTimer="codeTimer"
      :attributeList="attributeList"
      :roles="detail.access_roles"
      :detail="detail">
    </component>
  </div>
</template>
<script>
import { KEY_MAP } from '../../pages/contentAudit/constants'
import { firstUpperCase } from './common'
import notify from '@/lib/notify'
import Vue from 'vue'
import { mapGetters, mapState } from 'vuex'
import { taskApi, resourceApi } from '@/api/index'
import { getBackUrl } from '@/utils/index'
import { DYNAMIC_BIZ } from '@/utils/constant.js'

const NAME_MAP = {
  '通过': 'pass',
  '驳回': 'reject',
  '封禁': 'banned',
	'锁定': 'lock',
	'提交': ''
}
const CODE_KEY_MAP = [83, 68, 71, 70]
const TASK_TYPE_MAP = {
  '1': 'video',
  '2': 'voice',
  '3': 'picture',
  '4': 'text'
}

export default {
  components: {
  },

  props: {
    operationCard: {
      type: Object,
      default: () => {
        return {}
      }
    },
    ops: {
      type: Object,
      default: () => {
        return {}
      }
    },
    useForbidBtn: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    codeTimer: {
      type: Number,
      default: null
    },
    detail: {
      type: Object,
      default: () => {
        return {}
      }
		},
		staffTableData: {
			type: Array,
			default: () => {
				return []
			}
		}
  },
  data() {
    return {
      forbidList: [],
      attributeList: {},
      KEY_MAP,
      NAME_MAP,
			comps: [],
			query: {}
    }
  },

  created() {
    let params = this.operationCard.action_params;
    (params.buttons || []).forEach(btn => {
			if (NAME_MAP[btn.label] && btn.action === 'dialog') {
				let name = firstUpperCase(NAME_MAP[btn.label]) + 'Dialog'
				this.comps.push({
					name,
					ref: NAME_MAP[btn.label]
        })
				Vue.component(name, require(`../../pages/contentAudit/task/${params.business}/dialog/${name}.vue`).default)
			}
    })
  },

  computed: {
    ...mapGetters({
      getSelectedTagsStr: 'tags/getSelectedTagsStr'
    }),
    ...mapState({
      dynamicCardData: state => state.dynamicPic.dynamicCardData,
      checkedPics: state => state.pictures.checkedPics
    }),
    noHotkey() {
      let query = this.$route.query
      return DYNAMIC_BIZ.includes(+query.business_id) && (+query.flow_id === 51 || +query.flow_id === 50) && !query.isResource
    },
    showForbid() {
      // 新动态暂时隐藏禁止项
      return this.useForbidBtn && !DYNAMIC_BIZ.includes(+this.$route.query.business_id)
    },
    businessId() {
      return +this.$route.query.business_id
    }
  },

  mounted() {
    this.$nextTick(_ => {
      document.addEventListener('keyup', this.keyup)
		})
    this.query = this.$route.query
  },

  beforeDestroy() {
    document.removeEventListener('keyup', this.keyup)
  },

  methods: {
    getTask() {
      this.$emit('getTask')
    },
    openPassDialog(op) {
      this.$refs.pass[0].openPassDialog(op)
    },
    openRejectDialog(op) {
      this.$refs.reject[0].openRejectDialog(op)
    },
    openBannedDialog(op) {
      this.$refs.banned[0].openBannedDialog(op)
    },
    openLockDialog(op) {
      this.$refs.lock[0].openLockDialog(op)
    },
    confirmCommit(data, op) {
      const commentText = this.noComment(data) ? '<em style="color: var(--red)">禁止评论</em>' : ''
      const forwardText = this.noForward(data) ? '<em style="color: var(--red)">禁止转发</em>' : ''
      this.$confirm(`该动态已被 ${commentText}  ${forwardText}，选择确认则取消该禁止并提交`, '禁止项提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        dangerouslyUseHTMLString: true,
        type: 'warning'
      }).then(() => {
        this.submitTask(op)
      }).catch(() => {})
    },
		submitTask(op) {
			if (this.staffTableData && this.staffTableData.findIndex(d => d.apply_state === -1) > -1) {
				notify.warning('存在未完成审核的合作申请')
				return
			}

      // 获取可选标签
      let selectedTags = this.getSelectedTagsStr() || undefined
			let detail = this.detail
      let resource = detail.resource || {}
      let canCommit = false
      let ids = {}
      canCommit = detail.resource && detail.resource.business_id && resource.oid && detail.flow && detail.flow.rid && detail.flow.flow_id
      ids = {
        business_id: detail.resource.business_id,
        flow_id: detail.flow.flow_id,
        oid: resource.oid,
        rid: detail.flow.rid,
        task_id: (detail.task && detail.task.id) || 0
      }
      if (canCommit) {
        const params = {
          ...ids,
          resource_result: {
            note: selectedTags || '',
            attribute_list: Object.keys(this.forbidList).length && this.businessId !== 18 ? {
              no_comment: +this.attributeList.no_comment,
              no_forward: +this.attributeList.no_forward
            } : undefined
          },
          forbid_params: {},
          binds: op.bind_id_list,
          extra_data: {
            notify: null,
            audit_tags: selectedTags
          }
        }
        switch (this.businessId) {
          // 新动态提交ai和人工审核结果
          case 18:
            params.dyn_ai_info = this.getDynamicAiInfo(resource)
            break
          case 59:
            params.dyn_ai_info = this.getDynamicAiInfo(resource)
            break
          // 话题活动增加一个extra3参数, 这个字段用来告知业务方当前提交审核数据的送审时间
          case 28:
            params.extra_data.extra1s = resource.extra1s
            break
          case 45:
            params.extra_data.extra1s = resource.extra1s
            break
          default:
            break
        }
        const apiFn = +this.query.isResource ? resourceApi.submitResource : taskApi.submitTask
        apiFn(params).then((res) => {
					if (res.code === 0 || res.code === 92015 || res.code === 92029) {
						// 92015 任务已被操作或者删除 92029 审核任务超时 refresh
						res.code === 0
						? (!res.tips && notify.success(`成功提交`)) : res.code === 92015
						? notify.warning(res.message) : notify.error(res.message)
						this.goList()
					} else {
						notify.error(res.message)
					}
        }).catch(_ => {})
      }
    },
    getDynamicAiInfo(resource) {
      const dynamicData = this.dynamicCardData || {}
      const card = (dynamicData.card && JSON.parse(dynamicData.card)) || {}
      if (card.item && card.item.pictures) {
        let pics = []
        const auditResult = {}
        pics = card.item.pictures.map(p => p.img_src) || []
        let dynAiRiskPic = resource.picture_info?.dyn_ai_risk_pic || {}
        if (typeof dynAiRiskPic === 'object' && !Object.keys(dynAiRiskPic)?.length) {
          dynAiRiskPic = resource.metas?.dynAiRiskPic || {}
        }
        this.checkedPics?.forEach(i => {
          auditResult[i] = dynAiRiskPic[i] || []
        })
        return {
          audit_result: auditResult,
          pics
        }
      } else {
        return {}
      }
    },
    openDialog(op) {
      const curBtn = this.operationCard.action_params.buttons.find(btn => btn.label === op.ch_name) || {}
      if (curBtn.action === 'submit') {
        this.submitTask(op)
      } else {
        switch (op.ch_name) {
          case '通过':
            this.openPassDialog(op)
            break
          case '驳回':
            this.openRejectDialog(op)
            break
          case '封禁':
            this.openBannedDialog(op)
            break
          case '锁定':
            this.openLockDialog(op)
            break
          default:
            break
        }
      }
    },
    noComment(data) {
      return data.comment === 0
    },
    noForward(data) {
      return data.forward === 0
    },
    keyup(e) {
      if (this.noHotkey && e.keyCode !== 83) return
      const operations = this.ops.operations || []
      let op
      // S | D | G
      if (document.activeElement.tagName === 'INPUT' || document.activeElement.tagName === 'TEXTAREA' || !operations || !operations.length) return
      if (CODE_KEY_MAP.findIndex(code => code === e.keyCode) > -1) {
        if (e.keyCode === 70) {
          const lock = operations.find(op => op.ch_name === '折叠')
          op = lock || operations.find(op => op.ch_name === '锁定')
        } else if (e.keyCode === 83) {
          const pass = operations.find(op => op.ch_name === '通过')
          op = pass || operations.find(op => op.ch_name === '提交')
        } else if (e.keyCode === 68) {
          op = operations.find(op => op.ch_name === '驳回')
        } else {
          op = operations.find(op => op.ch_name === '封禁')
        }
        if (op && op.ch_name) this.openDialog(op)
      }
    },
    handleCheckedChange(val) {
      if (!val.length) {
        this.attributeList = {
          no_comment: 0,
          no_forward: 0
        }
        return
      }
      this.attributeList.no_comment = val.findIndex(i => i === '2') > -1 ? 1 : 0
      this.attributeList.no_forward = val.findIndex(i => i === '1') > -1 ? 1 : 0
    },
    resetChecked() {
      this.forbidList = []
      this.handleCheckedChange([])
    },
    goBack() {
      this.$emit('quitOrDelay')
		},
		goList(code) {
      if (+this.query.isTask) {
        // 任务清单-开始任务、任务清单-延迟任务-审核操作
        if (!+this.query.is_delay) {
          clearInterval(this.codeTimer)
          this.getTask()
        } else {
          this.goTaskList()
        }
      } else if (+this.query.isResource) {
        // 审核列表
        this.goBackToResource()
      }
    },
    goBackToResource() {
      const back = this.$route.query.back
      const url = getBackUrl(back)
      if (url) {
        window.location.href = url
      } else {
        this.goResourceList()
      }
    },
    goResourceList() {
      this.$router.push({
        path: `/audit/list/${this.query.businessType}`,
        query: {
          oid: '',
          businessId: this.businessId,
          isResource: 1
        }
      })
    },
    goTaskList () {
      this.$router.push({
        path: `/audit/tasks/${TASK_TYPE_MAP['' + this.query.businessType]}`,
        query: {
          business_id: this.businessId,
          flow_id: +this.query.flow_id,
          activeName: +this.query.is_delay ? '3' : '1'
        }
      })
    }
  }
}
</script>
<style lang="stylus" scoped>
.operations-card {

}
</style>
