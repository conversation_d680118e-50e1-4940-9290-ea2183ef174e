# Draggable组件

可拖拽组件，令组件内的节点都能拖拽

## 调用方法：

```html
<Draggable :zIndex="3" noFixed width="100px" height="100px">
  <div :style="{background: '#f00'}">可移动部分</div>
</Draggable>
<Draggable :zIndex="3" width="100px" height="100px">
  可移动部分
  <span slot="dragTrigger">只能选我拖动</span>
</Draggable>
<Draggable :zIndex="3" width="100px" height="100px">
  可移动部分
  <span slot="noDragTrigger">只能选我以外的位置拖动</span>
</Draggable>
```

## props

| 属性名    | 说明 | 类型   | 默认值 |
| --------- | ----- | -------- | ------ |
| disabled | 是否禁用，即不可拖拽  | Boolean | false  |
| width | drag块的宽度 | String | 200px  |
| height | drag块的高度 | String | 200px  |
| fillParentWidth | drag块的宽度是否填满父元素的宽度，如果是，则忽略传入的width，以父元素的宽度为准 | Boolean | false  |
| fillParentHeight | drag块的高度是否填满父元素的高度| Boolean | false  |
| noFixed | position不是fixed ,不设置的都默认是fixed类型定位 | Boolean | false |
| fixPosition | 初始固定的值，在`noFixed`为`true`时无效,不为0时需要带`px`单位 | Object | { left: 0, top: 0, bottom: 0, right: 0 |
| rememberPosition | 如果需要刷新后依然记住最后一次拖动的位置，传入记录和读取缓存时的key，因为是全局的所以要尽量传入与别的drag组件不重复的key值| String | - |
| zIndex | z-index值 | Number | 2  |
| limitQuery | 拖动限制范围节点，传入节点选择器，仅在`noFixed`为`true`时有效 | String | - |
| limitClient: | 是否需要限制在视窗内拖动，`limitClient`为`true`时，会忽略`limitQuery`| false | - |

## slots

| 属性名    | 说明 |
| --------- | ----- |
| noDragTigger | 只能选择不是该slot内的元素才能拖动  |
| dragTigger | 只能选择该slot内的元素才能拖动|