<template>
  <div class="audit-draggable"
    :class="{
      ['audit-draggable-origin']: isOrigin,
      ['audit-draggable-ing']: isDragging,
      ['audit-draggable-disabled']: disabled,
      ['audit-draggable-canMove']: canMove
    }"
    ref="dragSlot"
    @mousedown="dragStart"
    @mousemove="mouseMove"
    :style="styles">
      <slot><span></span></slot>
      <slot name="dragTrigger"></slot>
      <slot name="noDragTrigger"></slot>
  </div>
</template>
<script>
import { get } from 'lodash-es'
import { safeJsonParse } from '@/utils'

export default {
  name: 'Draggable',
  data() {
    return {
      styles: null,
      dragEl: null,
      limitEl: null,
      limitRect: null,
      rect: {},
      dragTrigger: null,
      noDragTrigger: null,
      lastPosition: {},
      originPosition: {},
      isOrigin: true, // 是否在最初始的位置
      isDragging: false, // 是否正在拖拽
      canMove: false,
      disX: 0,
      disY: 0,
      winScrollY: 0,
      winScrollX: 0
    }
  },
  props: {
    positionCacheKey: {
      type: String,
      default: 'dragPositionCache'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    width: {
      type: String,
      default: '200px'
    },
    height: {
      type: String
    },
    fillParentWidth: {
      type: Boolean,
      default: false
    },
    fillParentHeight: {
      type: Boolean,
      default: false
    },
    fixPosition: {
      type: Object,
      default() {
        return { left: 0, top: 0, bottom: null, right: null }
      }
    },
    rememberPosition: {
      type: String
    },
    noFixed: {
      type: Boolean,
      default: false
    },
    zIndex: {
      type: Number,
      default: 2
    },
    limitQuery: {
      type: String
    },
    limitClient: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    hasIFrame() {
      return !!(this.$refs.dragSlot && this.$refs.dragSlot.querySelector('iframe'))
    }
  },
  mounted() {
    this.initLimitEl()
    this.initStyle()
  },
  watch: {
    width(newVal) {
      this.styles = {
        ...this.styles,
        width: this.fillParentWidth ? `${this.parent.offsetWidth}px` : newVal
      }
    }
  },
  methods: {
    initLimitEl() {
      const { limitQuery, limitClient } = this
      let query = (limitClient) ? 'body' : limitQuery
      const limitEl = document.querySelector(query)
      if (!limitEl) return

      this.limitEl = limitEl
    },
    initStyle() {
      const { noFixed, fillParentWidth, fillParentHeight, width, height, zIndex } = this
      const parent = this.$el.parentNode

      this.dragEl = this.$refs.dragSlot

      const initPosition = this.getInitPosition()
      this.lastPosition = initPosition
      this.styles = {
        position: noFixed ? null : 'fixed',
        zIndex,
        ...initPosition,
        width: fillParentWidth ? `${parent.offsetWidth}px` : width,
        height: fillParentHeight ? `${parent.offsetHeight}px` : height
      }
    },
    getInitPosition() {
      const { noFixed, fixPosition } = this
      const cache = this.getCachePosition()
      return noFixed
        ? {}
        : (cache || fixPosition) // 优先用缓存
    },
    toggleBodyUserSelect(isDraging) {
      window.document.body.style.userSelect = isDraging ? 'none' : null
    },
    canNotMove(e) {
      const { disabled } = this
      const dragTrigger = get(this.$slots, 'dragTrigger.0.elm', null)
      const noDragTrigger = get(this.$slots, 'noDragTrigger.0.elm', null)
      return disabled ||
        (dragTrigger && !dragTrigger.contains(e.target)) ||
        (noDragTrigger && noDragTrigger.contains(e.target))
    },
    mouseMove(e) {
      this.canMove = !this.canNotMove(e)
    },
    dragStart(e) {
      if (this.canNotMove(e)) return
      this.recordStyle(e)
      // 开始拖动
      this.isDragging = true
      // body不可选择文字
      this.toggleBodyUserSelect(true)
      // 按下没拖动的时候也计算一下，更平滑
      this.dragMove(e)
      this.isOrigin = false
      document.removeEventListener('mousemove', this.dragMove)
      document.removeEventListener('mouseup', this.dragEnd)
      document.addEventListener('mousemove', this.dragMove)
      document.addEventListener('mouseup', this.dragEnd)
    },
    // 记录开始拖动时的数据
    recordStyle(e) {
      const { scrollX, scrollY } = window
      const { noFixed, isOrigin, dragEl, limitEl, limitClient } = this

      if (limitEl) { // 更新限制范围位置
        const rect = limitEl.getBoundingClientRect()
        this.limitRect = limitClient ? this.limitRect = {
          left: 0,
          top: 0,
          bottom: rect.height,
          right: rect.width
        } : rect
      }
      // 记录拖动元素的宽高
      const rect = this.rect = dragEl.getBoundingClientRect()
      if (isOrigin && noFixed) {
        this.originPosition = {
          x: rect.x + scrollX,
          y: rect.y + scrollY
        }
      }
      // 缓存滚动距离
      this.winScrollX = scrollX
      this.winScrollY = scrollY
      // 记录鼠标在拖动元素中的坐标
      const disX = this.disX = e.clientX - rect.x
      const disY = this.disY = e.clientY - rect.y
      return { disX, disY, rect }
    },
    setDragStyles(styles) {
      this.styles = Object.assign({}, this.styles, styles)
    },
    dragMove(e) {
      if (window.event.buttons === 0) { // 鼠标没有按下，处理拖动时鼠标移出document或者移入iframe，导致无法触发mouseup的情况
        this.dragEnd()
        return
      }
      if (!this.isDragging) return

      if (!this.hasIFrame) { // 在有iframe的情况下不禁止，拖动更顺滑
        e.preventDefault() // 主要为了防止拖动时选中文字
      }
      const { left, top } = this.getComputePosition(e)
      const position = {
        left: `${left}px`,
        top: `${top}px`
      }
      this.setDragPosition(position)
    },
    setDragPosition({ left, top }) {
      // 统一为left, top 形式
      const newPosition = {
        left,
        top,
        right: null,
        bottom: null
      }
      this.lastPosition = newPosition
      this.setDragStyles(newPosition)
    },
    getLimitedPosition(e) {
      const { clientX, clientY } = e
      const { disX, disY, limitRect, rect } = this
      let x = clientX - disX
      let y = clientY - disY

      if (!limitRect) return { x, y }

      let right = rect.width + x
      let bottom = rect.height + y

      if (x < limitRect.left) {
        x = limitRect.left
      } else if (right > limitRect.right) {
        x = limitRect.right - rect.width
      }
      if (y < limitRect.top) {
        y = limitRect.top
      } else if (bottom > limitRect.bottom) {
        y = limitRect.bottom - rect.height
      }
      return { x, y }
    },
    getComputePosition(e) {
      const { originPosition, winScrollX, winScrollY, noFixed } = this
      const newPosition = this.getLimitedPosition(e)
      return {
        left: noFixed ? (winScrollX + newPosition.x - originPosition.x) : newPosition.x,
        top: noFixed ? (winScrollY + newPosition.y - originPosition.y) : newPosition.y
      }
    },
    getCachePosition() {
      const { positionCacheKey, rememberPosition } = this
      const dragPositionCache = safeJsonParse(window.localStorage.getItem(positionCacheKey), false)

      if (!rememberPosition) {
        return null
      }

      return dragPositionCache ? dragPositionCache[rememberPosition] : null
    },
    savePosition() {
      const { positionCacheKey, rememberPosition, lastPosition } = this
      const dragPositionCache = safeJsonParse(window.localStorage.getItem(positionCacheKey), {})

      if (rememberPosition) {
        dragPositionCache[rememberPosition] = lastPosition
      }

      window.localStorage.setItem(positionCacheKey, JSON.stringify(dragPositionCache))
    },
    dragEnd() {
      document.removeEventListener('mouseup', this.dragEnd)
      document.removeEventListener('mousemove', this.dragMove)
      this.isDragging = false
      this.toggleBodyUserSelect(false)
      this.savePosition()
    }
  },
  beforeDestroy() {
    this.dragEl = null
    this.limitEl = null
    document.removeEventListener('mouseup', this.dragEnd)
    document.removeEventListener('mousemove', this.dragMove)
  }
}
</script>
<style lang="stylus" scoped>
.audit-draggable {
  position: relative;
}
.audit-draggable-ing {
  cursor: move;
}
.audit-draggable-canMove {
  cursor: move;
}
</style>
