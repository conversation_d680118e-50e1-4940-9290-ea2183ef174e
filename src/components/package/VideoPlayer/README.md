# VideoPlayer 播放器

> 视频播放器

示例：

```html
<template>
  <div>
    <VideoPlayer :src="playurl" :styleConfig="styleConfig" @on-style-change="onStyleChange"/>
  </div>
</template>

<script>
export default {
  data() {
    return {
      playurl: '',
      styleConfig: {
        minWidth: 320,
        maxWidth: 600,
        maxHeight: 350
      },
    }
  },
  methods: {
    onStyleChange(containerStyle, videoStyle, originVideoStyle) {
      console.log('new style: ', containerStyle, videoStyle, originVideoStyle);
    }
  }
}
</script>
```

## API

### props

| 属性名      | 说明                                                          | 类型  | 默认值 |
| ----------- | ------------------------------------------------------------- | ------ | ------ |
| src         | 播放地址                                                      | String | 必传   |
| styleConfig | `maxWidth`、`minWidth`、`maxHeight`、`minHeight` 限制展示尺寸 | Object | 可选   |
| config      | 播放器配置具体参见https://github.com/sampotts/plyr#options    | Object | 可选   |

### events

| 事件名          | 说明                                 | 返回值                                                                             |
| --------------- | ------------------------------------ | ---------------------------------------------------------------------------------- |
| on-style-change | 最新的样式宽高 `width`, `height`数值 | 容器大小`containerStyle`, 视频显示大小`videoStyle`, 视频实际大小`originVideoStyle` |
