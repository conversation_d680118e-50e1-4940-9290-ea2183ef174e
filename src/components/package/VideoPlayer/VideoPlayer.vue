<template>
  <div
    id="audit-video-container"
    v-loading="loading"
    :class="{ 'window-view': !isFullscreen }"
    :style="containerStyle"
  >
    <video
      id="player"
      class="audit-video"
      :src="src"
      :style="videoStyle">
      您的浏览器版本过低
    </video>
    <div class="extended__controls">
      <div @mouseenter="() => isHovered = true" @mouseleave="() => isHovered = false">
        <SvgIcon v-show="!isHovered" class="rotate-btn" name="rotate" @click.native="handleRotate" />
        <SvgIcon v-show="isHovered" class="rotate-btn" name="rotate-white" @click.native="handleRotate" />
      </div>
    </div>
    <div v-if="needSlowPlayback" class="mt-8" style="color: var(--red);">
      该视频帧率为 120 fps，慢速观看可避免丢帧，播放器已切换 0.5 倍速
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import Plyr from '@bilibili/plyr'
import '@bilibili/plyr/dist/plyr.css'
import './plyr-hack.css'
import trackerFunc from '@/utils/report'
import { getZoomByWidth } from '@/utils'
import notify from '@/lib/notify'
import SvgIcon from '@/v2/pure-components/Icon/SvgIcon.vue'
import { VideoTechReporter } from '@bilibili/video-tech-reporter'
import getMp4FrameRate from '@/v2/biz-utils/parseMp4FrameRate'

const publicPath = process.env.NODE_ENV === 'production' ? 'https://s1.hdslb.com/bfs/static/app/aegis/' : window.location.pathname

export default {
  name: 'VideoPlayer',
  components: {
    SvgIcon
  },
  data() {
    const { minWidth, maxWidth, minHeight, maxHeight } = this.styleConfig
    return {
      player: null,
      canPlay: false,
      playerEl: null,
      media: null,
      isFullscreen: false,
      loadingTimeout: null,
      playerInit: false,
      videoWidth: minWidth || maxWidth || 640, // `${minWidth || maxWidth || 640}px`,
      videoHeight: minHeight || maxHeight || 360, // `${minHeight || maxHeight || 360}px`,
      defaultConfig: {
        controls: [
          'play-large',
          'play',
          'progress',
          'current-time',
          'duration',
          'mute',
          'volume',
          'captions',
          'settings',
          'fullscreen'
        ],
        iconUrl: `${publicPath}libs/plyr/plyr.svg`,
        keyboard: { focused: true, global: true },
        speed: {
          selected: 1,
          options: [0.5, 1, 1.5, 2, 2.5, 3, 3.5, 4]
        },
        invertTime: false, // true: 显示剩余时间 false: 显示播放了多久
        hideControls: false,
        seekTime: 5 // 每次快进和快退间隔5s,
      },
      loading: false, // 正在加载中
      containerWidth: this.videoWidth,
      containerHeight: this.videoHeight + 34,
      renderWidth: this.videoWidth,
      renderHeight: this.videoHeight,
      rotationIdx: 0,
      isFirstCanPlay: true,
      isHovered: false,
      videoElReporter: null,
      needSlowPlayback: false
    }
  },
  props: {
    src: {
      type: [String, Number]
    },
    /** 目前只接收 maxWidth、minWidth、maxHeight、minHeight四个属性 */
    styleConfig: {
      type: Object,
      validator(value) {
        let flag = true
        if (value.maxWidth && value.minWidth) {
          flag = value.maxWidth >= value.minWidth
        }
        if (value.maxHeight && value.minHeight) {
          flag = value.maxHeight >= value.minHeight
        }
        return flag
      },
      default() {
        return {
          minWidth: 360
        }
      }
    },
    /* 播放器配置
     * 具体参见https://github.com/sampotts/plyr#options
     * 会把传入的配置与默认配置浅拷贝的方式合并
     */
    config: {
      type: Object,
      default() {
        return {}
      }
    },
    seek: Number, // 定位时间点
    seekPause: { // 定位是否暂停播放
      type: Boolean,
      default: false
    },
    cid: {
      type: [Number, String],
      default: ''
    },
    getFrameRateEnabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapState({
      uid: (state) => state.user.uid,
      username: (state) => state.user.username
    }),
    containerStyle() {
      return {
        width: `${this.containerWidth}px`,
        height: `${this.containerHeight}px`
      }
    },
    videoStyle() {
      return this.isFullscreen
    ? {}
    : {
        width: `${this.renderWidth}px`,
        height: `${this.renderHeight}px`,
        ...this.getRotationStyle()
      }
    }
  },
  mounted() {
    this.initPlayer()
    this.$EventBus.$off('seek-time', this.goSeekTime)
    this.$EventBus.$on('seek-time', this.goSeekTime)
    window.addEventListener('beforeunload', this.handleBeforeUnload)
  },
  watch: {
    src(newSrc) {
      if (newSrc) {
        this.media.source = newSrc
      }
      this.loading = false
    },
    styleConfig(newValue) {
      this.reLayout(newValue)
    },
    videoWidth: {
      handler(val) {
        this.renderWidth = val
        this.containerWidth = val
      },
      immediate: true
    },
    videoHeight: {
      handler(val) {
        this.renderHeight = val
        this.containerHeight = val + 34
      },
      immediate: true
    }
  },
  methods: {
    initPlayer() {
      const playerEl = this.playerEl = this.$el.querySelector('#player')
      this.videoElReporter = new VideoTechReporter(playerEl, '333.836', {
        business: 2, // 1-直播 2-点播
        businessId: this.$route?.query?.business_id,
        uid: this.uid,
        username: this.username,
        oid: this.cid
      })
      const config = Object.assign({}, this.defaultConfig, this.config)
      this.player = new Plyr(playerEl, config)
      this.media = this.player.media
      this.$emit('init')
      this.eventHandler()
    },
    reLayout(layout) {
      const { minWidth } = layout
      const { width, height, zoom } = getZoomByWidth(this.media)
      const scaleWidth = (width / zoom) || 640
      this.videoWidth = scaleWidth < minWidth ? minWidth : scaleWidth
      this.videoHeight = height / zoom || 360
    },
    initStyle() {
      if (this.playerInit) {
        return
      }
      const { minWidth } = this.styleConfig
      const { width, height, zoom } = getZoomByWidth(this.media)
      const scaleWidth = (width / zoom) || 640
      this.videoWidth = scaleWidth < minWidth ? minWidth : scaleWidth
      this.videoHeight = height / zoom || 360
      this.playerInit = true
    },
    eventHandler() {
      const listeners = this.$listeners || {}
      const player = this.player
      Object.keys(this.$listeners).forEach((name) => {
        if (name.indexOf('on-player-') === 0) {
          const methodName = name.replace('on-player-', '')
          player.on(methodName, listeners[name])
        }
      })
      this.internalEvnetBind()
    },
    removeEventHandler() {
      clearTimeout(this.loadingTimeout)
      this.loading = false
      window.removeEventListener('beforeunload', this.handleBeforeUnload)
    },
    removeFocusOfFullScreen() {
      document.activeElement && document.activeElement.blur()
    },
    internalEvnetBind() {
      const player = this.player
      player.on('canplay', () => {
        this.canPlay = true
        this.initStyle()
        this.$emit('canplay')
        if (!this.isFirstCanPlay) return
        const localSpeedStr = localStorage.getItem('audit-player-speed') || ''
        const localSpeed = parseFloat(localSpeedStr, 10)
        this.player.speed = !!localSpeed ? localSpeed : 1
        this.isFirstCanPlay = false
      })
      player.on('error', this.loadError)
      player.on('loadstart', this.loadStart)
      player.on('loadedmetadata', this.metaLoaded)
      player.on('enterfullscreen', () => {
        this.isFullscreen = true
        this.removeFocusOfFullScreen()
      })
      player.on('exitfullscreen', () => {
        this.isFullscreen = false
        this.removeFocusOfFullScreen()
      })
      player.on('loadeddata', this.loadedData)
    },
    loadError(e) {
      const url = this.src.toString()
      if (url) { // url 默认为空, 空 src 报错由 getPlayUrl 处理
        const error = e.detail.plyr.media.error
        const msg = error && error.message
        if (msg || !this.canPlay) {
          clearTimeout(this.loadingTimeout)
          this.loading = false
          notify.warning(`视频加载错误 ${url}`, 800, {
            offset: 100
          })
          trackerFunc('player-loading-error', { url, msg })
        }
      }
    },
    loadStart() {
      if (this.loadingTimeout !== null) {
        clearTimeout(this.loadingTimeout)
        this.loadingTimeout = null
        this.loading = false
      }
      // src为空 不计超时
      if (!this.src) return

      let url = this.src
      if (this.getFrameRateEnabled) {
        getMp4FrameRate(url).then((frameRate) => {
          if (frameRate > 119) {
            this.needSlowPlayback = true
            this.player.speed = 0.5
          }
        })
      }
      this.loadingTimeout = setTimeout(() => {
        notify.warning(`视频加载超过3s ${url}`, 1500)
        if (window.URL) {
          url = new URL(url).host
        }
        trackerFunc('player-loading-timeout', { url })
      }, 3000)
      this.loading = true
    },
    metaLoaded() {
      clearTimeout(this.loadingTimeout)
      this.playerInit = false
      this.loadingTimeout = null
      this.loading = false
      this.player && this.player.restart()
    },
    loadedData() {
      this.$emit('loadeddata')
      if (this.seek) {
        this.player.currentTime = this.seek
      }
    },
    goSeekTime(time) {
      this.player.currentTime = time
      // 补充定位暂停播放
      !this.seekPause ? this.player.play() : this.player.pause()
    },
    handleRotate() {
      this.rotationIdx += 1
      const prevWidth = this.containerWidth
      const prevHeight = this.containerHeight
      this.containerWidth = prevHeight - 34
      this.containerHeight = prevWidth + 34
    },
    getRotationStyle() {
      const offset = (this.videoHeight - this.videoWidth) / 2
      switch (this.rotationIdx % 4) {
        case 0:
          return {}
        case 1:
          return {
            transform: `rotate(270deg) translate(${offset}px, ${offset}px)`
          }
        case 2:
          return {
            transform: 'rotate(180deg)'
          }
        case 3:
          return {
            transform: `rotate(90deg) translate(${-offset}px, ${-offset}px)`
          }
      }
    },
    handleBeforeUnload() {
      localStorage.setItem('audit-player-speed', this.player.speed || 1)
    }
  },
  beforeDestroy() {
    this.removeEventHandler()
    this.$EventBus.$off('seek-time', this.goSeekTime)
    localStorage.setItem('audit-player-speed', this.player.speed || 1)
    this.$nextTick(() => {
      this.player.destroy()
      this.videoElReporter?.destroy()
    })
  }
}
</script>
<style lang="stylus">
#audit-video-container{
  position relative
  .plyr__video-wrapper{
    text-align center
  }
  .plyr {
    padding-bottom 34px
    box-shadow 0 0 8px #e4e4e4
  }
  &.window-view{
    .plyr__menu{
      margin-right 28px
    }
  }
  .plyr__controls{
    background var(--content-bg-color)
    color var(--text-color-dark-2)
    padding 5px 10px
    .plyr__control{
      padding 3px
    }
    .plyr__control svg{
      width 14px
    }
    .plyr__time{
      font-size 12px
    }
  }
  .extended__controls{
    padding-right 5px
    position absolute
    bottom 0
    right 30px
    background var(--white)
    width 20px
    height 30px
    display flex
    align-items center

    .rotate-btn{
      width 20px
      height 24px

      &:hover {
        cursor pointer
        background: #00b3ff
        border-radius 3px
      }
    }
  }
  .plyr__control--overlaid{
    display none
  }
  .plyr__menu__container{
    .plyr__control--forward{
      padding-right 28px
    }
    .plyr__control--back{
      padding-left 28px
    }
  }
  .plyr__menu__container .plyr__control[role=menuitemradio][aria-checked=true]::after {
    left 8px !important
  }
  .plyr--video {
    overflow visible !important
    height: 100%
  }
}
</style>
