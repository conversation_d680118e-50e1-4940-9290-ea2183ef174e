<template>
  <div class="audit-player-nano">
    <div
      id="aegis-nano-container"
      v-loading="loading"
      :style="containerStyle"
    >
      <div v-if="needSlowPlayback" class="mt-8" style="color: var(--red);">
        该视频帧率为 120 fps，慢速观看可避免丢帧，播放器已切换 0.5 倍速
      </div>
    </div>
    <div
      v-if="enableContextMenuShield"
      class="context-menu-shield"
      @click="handleClick"
      @contextmenu="contextMenuHandler"
    ></div>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import trackerFunc from '@/utils/report'
import notify from '@/lib/notify'
import getMp4FrameRate from '@/v2/biz-utils/parseMp4FrameRate'
import { initTranslationSubtitle, reloadTranslationSubtitle, repositionSubtitle } from '@/v2/biz-utils/subtitle'

export default {
  name: 'NanoModulePlayer',
  components: {
  },
  data() {
    return {
      player: null,
      canPlay: false,
      loadingTimeout: null,
      playerInit: false,
      loading: false, // 正在加载中
      containerWidth: 561,
      containerHeight: 371,
      rotationIdx: 0,
      isFirstCanPlay: true,
      isHovered: false,
      needSlowPlayback: false,
      isPausedBeforeSeek: true,
      playSourceFile: true,
      togglePlayTimer: null, // 判断连续双击用
      enableContextMenuShield: true,
      subtitleX: null
    }
  },
  props: {
    src: {
      type: String
    },
    seek: Number, // 定位时间点
    seekPause: { // 定位是否暂停播放
      type: Boolean,
      default: false
    },
    aid: {
      type: [Number, String],
      default: ''
    },
    cid: {
      type: [Number, String],
      default: ''
    },
    getFrameRateEnabled: {
      type: Boolean,
      default: false
    },
    styleConfig: Object,
    videoPlaylist: {
      type: Array,
      default: () => []
    },
    fetchSubtitle: {
      type: Boolean,
      default: false
    },
    // 是否在顶部留出 100px 的高度
    // 以支持网页全屏下依然可以查看社区机审提示
    hasTopLeftover: {
      type: Boolean,
      default: false
    },
    filename: {
      type: String,
      default: ''
    }
  },
  computed: {
    ...mapState({
      uid: (state) => state.user.uid,
      username: (state) => state.user.username
    }),
    containerStyle() {
      const NANO_CTRL_BAR_HEIGHT = 55
      const { minHeight, minWidth, height: presetHeight } = this.styleConfig || {}
      let width = this.containerWidth
      let height = this.containerHeight
      if (presetHeight) return { height: `${presetHeight}px` }
      if (Number.isInteger(minWidth) && Number.isInteger(minHeight)) { // 宽高同时设定
        width = Math.max(minWidth, this.containerWidth)
        height = Math.max(minHeight, this.containerHeight)
      } else if (Number.isInteger(minWidth)) { // 设定了宽度，按照比例计算高度
        width = Math.max(minWidth, this.containerWidth)
        height = Math.round(width / 16 * 9) + NANO_CTRL_BAR_HEIGHT
      } else if (Number.isInteger(minHeight)) { // 设定了高度，按照比例计算宽度
        height = Math.max(minHeight, this.containerHeight)
        width = Math.round((height - NANO_CTRL_BAR_HEIGHT) / 9 * 16)
      } else if (this.playSourceFile) {
        return {
          width: `${width}px`,
          height: `${height}px`
        }
      } else {
        return {}
      }
      return {
        width: `${width}px`,
        height: `${height}px`
      }
    }
  },
  watch: {
    src: {
      handler(newVal) {
        if (!newVal) return
        this.enableContextMenuShield = true
        if (this.player) {
          this.switchVideo({ src: newVal })
        } else {
          this.$nextTick(() => { // 等待视频容器的 dom 节点挂载
            this.initPlayer()
          })
        }
        this.playSourceFile = true
      },
      immediate: true
    },
    cid: {
      handler(newVal) {
        if (!newVal || !this.aid) return
        this.enableContextMenuShield = false
        if (this.player) {
          this.switchVideo({ aid: this.aid, cid: newVal })
        } else {
          this.$nextTick(() => { // 等待视频容器的 dom 节点挂载
            this.initPlayer()
          })
        }
        this.playSourceFile = false
      },
      immediate: true
    },
    seek(newVal) {
      const roundedVal = Math.round(newVal)
      if (Number.isInteger(roundedVal)) {
        this.goSeekTime(roundedVal)
      }
    }
  },
  mounted() {
    this.$EventBus.$off('seek-time', this.goSeekTime)
    this.$EventBus.$on('seek-time', this.goSeekTime)
    window.addEventListener('beforeunload', this.handleBeforeUnload)
  },
  methods: {
    async initPlayer() {
      const nano = window.nano
      const initParams = {}
      let autoplay = false
      if (this.src) {
        initParams.localUrl = this.src
        this.isPausedBeforeSeek = true
      }
      if (this.aid && this.cid) {
        initParams.aid = this.aid
        initParams.cid = this.cid
        autoplay = true
        this.isPausedBeforeSeek = false
      }
      console.log('NANO INIT with local_url: ', JSON.stringify(initParams))
      const nanoInstance = nano.createPlayer({
        element: document.getElementById('aegis-nano-container'),
        kind: nano.GroupKind.Manager,
        channelKind: nano.ChannelKind.Manager,
        seekUpdateMode: 'realtime',
        featureList: ['noSendingBar'],
        autoplay,
        ...initParams
      })
      this.player = nanoInstance
      window.nanoPlayer = nanoInstance

      this.internalEvnetBind()
      this.player.connect().then(async () => {
        if (this.fetchSubtitle) {
          this.subtitleX = await initTranslationSubtitle(this.filename, this.player)
        } else {
          this.subtitleX = null
        }
      }).catch((e) => {
        console.error('fatal error in nano connect:', e)
      })
      this.$emit('init')
    },
    contextMenuHandler(event) { event.preventDefault() },
    togglePlay() {
      this.isPausedBeforeSeek ? this.player.play() : this.player.pause()
      this.isPausedBeforeSeek = !this.isPausedBeforeSeek
      this.togglePlayTimer = null
    },
    handleClick() {
      if (this.togglePlayTimer) {
        clearTimeout(this.togglePlayTimer)
        this.togglePlayTimer = null
        const nano = window.nano
        this.player.requestStatue(nano.ScreenKind.Full)
      } else {
        this.togglePlayTimer = setTimeout(this.togglePlay, 200)
      }
    },
    removeEventHandler() {
      clearTimeout(this.loadingTimeout)
      this.loading = false
      const nano = window.nano
      window.removeEventListener('beforeunload', this.handleBeforeUnload)
      const player = this.player
      if (!player) return
      player.off(nano.EventType.Player_Canplay, this.handleCanPlay)
      player.off(nano.EventType.Player_LoadStart, this.loadStart)
      player.off(nano.EventType.Player_LoadedMetadata, this.metaLoaded)
      player.off(nano.EventType.Player_LoadedData, this.loadedData)
      player.off(nano.EventType.Player_Play, this.onPlayed)
      player.off(nano.EventType.Player_Statue_Changed, this.switchToWebPageFullscreen)
      player.off(nano.EventType.Player_Seeked, this.handleSeeked)
      player.off(nano.EventType.Player_Pause, this.handlePaused)
    },
    removeFocusOfFullScreen() {
      document.activeElement && document.activeElement.blur()
    },
    internalEvnetBind() {
      const player = this.player
      const nano = window.nano
      player.on(nano.EventType.Player_Canplay, this.handleCanPlay)
      player.on(nano.EventType.Player_LoadStart, this.loadStart)
      player.on(nano.EventType.Player_LoadedMetadata, this.metaLoaded)
      player.on(nano.EventType.Player_LoadedData, this.loadedData)
      player.on(nano.EventType.Player_Play, this.onPlayed)
      player.on(nano.EventType.Player_Statue_Changed, this.switchToWebPageFullscreen)
      player.on(nano.EventType.Player_Seeked, this.handleSeeked)
      player.on(nano.EventType.Player_Pause, this.handlePaused)
    },
    handleCanPlay() {
      this.canPlay = true
      this.$emit('nano-canplay')
      if (!this.isFirstCanPlay) return
      const localSpeedStr = localStorage.getItem('audit-player-speed') || ''
      const localSpeed = parseFloat(localSpeedStr, 10)
      this.player.setPlaybackRate(!!localSpeed ? localSpeed : 1)
      this.isFirstCanPlay = false
      if (this.videoPlaylist) {
        this.player.setState({
          7: { list: this.videoPlaylist, type: 1 }
        })
      }
    },
    switchToWebPageFullscreen(data) {
      let body = document.querySelector('body')
      let videoContainer = document.querySelector('#aegis-nano-container')
      const bodyWebFullClass = 'player-fullscreen-fix'
      const topOverrideClass = 'player-fullscreen-fix-top-leftover'
      const videoFullScreenClass = 'player-fullscreen-margin-fix'
      const isFullScreen = data.detail.sideScreen === window.nano.ScreenKind.Full
      const isWebFull = data.detail.mainScreen === window.nano.ScreenKind.Web
      this.$emit('toggle-fullscreen', {
        isFullScreen,
        isWebFull
      })
      if (isWebFull) {
        body.classList.add(bodyWebFullClass)
        if (this.hasTopLeftover) body.classList.add(topOverrideClass)
        videoContainer.classList.add(videoFullScreenClass)
        if (this.subtitleX) repositionSubtitle(this.subtitleX, { bottom: 80 })
      } else if (isFullScreen) {
        videoContainer.classList.add(videoFullScreenClass)
        if (this.subtitleX) this.subtitleX.updateOptions({ bottom: 80 })
        if (this.subtitleX) repositionSubtitle(this.subtitleX, { bottom: 80 })
      } else {
        body.classList.remove(bodyWebFullClass)
        body.classList.remove(topOverrideClass)
        videoContainer.classList.remove(videoFullScreenClass)
        if (this.subtitleX) repositionSubtitle(this.subtitleX, { bottom: 55 })
      }
      body = null
      videoContainer = null
    },
    loadStart() {
      if (this.loadingTimeout !== null) {
        clearTimeout(this.loadingTimeout)
        this.loadingTimeout = null
        this.loading = false
      }
      // src为空 不计超时
      if (!this.src) return

      let url = this.src
      if (this.getFrameRateEnabled) {
        getMp4FrameRate(url).then((frameRate) => {
          if (frameRate > 119) {
            this.needSlowPlayback = true
            this.player.setPlaybackRate = 0.5
          }
        })
      }
      this.loadingTimeout = setTimeout(() => {
        notify.warning(`视频加载超过3s ${url}`, 1500)
        if (window.URL) {
          url = new URL(url).host
        }
        trackerFunc('player-loading-timeout', { url })
      }, 3000)
      this.loading = true
    },
    metaLoaded() {
      clearTimeout(this.loadingTimeout)
      this.playerInit = false
      this.loadingTimeout = null
      this.loading = false
    },
    loadedData() {
      this.$emit('nano-loaded-data')
      if (this.seek) {
        this.goSeekTime(this.seek)
      }
    },
    onPlayed(event) {
      this.$emit('nano-played')
      const isManualPlayed = event?.detail?.initiator === 'ManualAction'
      if (isManualPlayed) this.isPausedBeforeSeek = false
    },
    playingHandler() {
      this.player.off(window.nano.EventType.Player_Playing, this.playingHandler)
      if (this.isPausedBeforeSeek) this.player.pause()
    },
    handleSeeked() {
      this.player.off(window.nano.EventType.Player_Playing, this.playingHandler)
      this.player.on(window.nano.EventType.Player_Playing, this.playingHandler)
    },
    handlePaused(event) {
      const isManualPaused = event?.detail?.initiator === 'ManualAction'
      if (isManualPaused) this.isPausedBeforeSeek = true
      this.$emit('nano-paused')
    },
    goSeekTime(time) {
      this.player?.seek({ value: time, autoplay: !this.seekPause })
    },
    pauseVideo() {
      this.player?.pause()
    },
    disableShortcut() {
      this.player?.toggleFeature({ shortcut: true })
    },
    enableShortcut() {
      this.player?.toggleFeature({ shortcut: false })
    },
    switchVideo({ src, aid, cid }) {
      const reloadParams = {}
      let autoplay = false
      if (src) {
        reloadParams.localUrl = src
        this.isPausedBeforeSeek = true
      }
      if (aid && cid) {
        reloadParams.aid = aid
        reloadParams.cid = cid
        autoplay = true
        this.isPausedBeforeSeek = false
      }
      this.player.reload({
        autoplay,
        seekUpdateMode: 'realtime',
        featureList: ['noSendingBar'],
        ...reloadParams
      })
      this.enableShortcut()
      if (this.fetchSubtitle) {
        reloadTranslationSubtitle(this.subtitleX, this.filename)
      } else {
        // 隐藏字幕
        this.subtitleX && this.subtitleX.hide && this.subtitleX.hide()
      }
    },
    handleBeforeUnload() {
      localStorage.setItem('audit-player-speed', this?.player?.getPlaybackRate() || 1)
    }
  },
  beforeDestroy() {
    this.removeEventHandler()
    this.$EventBus.$off('seek-time', this.goSeekTime)
    localStorage.setItem('audit-player-speed', this?.player?.getPlaybackRate() || 1)
    if (this.subtitleX) this.subtitleX.destroy()
    if (window.nanoPlayer) {
      window.nanoPlayer.disconnect()
        .then()
        .catch((e) => console.error('failed to destroy nano instance', e))
      window.nanoPlayer = undefined
    }
  }
}
</script>
<style lang="stylus">
.audit-player-nano
  position relative
  .context-menu-shield
    position absolute
    width 100%
    height calc(100% - 55px)
    opacity 0
    top 0
    left 0

.player-fullscreen-fix
  position fixed
  top 0
  left 0
  margin 0
  padding 0
  width 100%
  height 100%

.player-fullscreen-fix #aegis-nano-container
  position fixed !important
  border-radius 0
  z-index 100000 !important
  left 0
  top 0
  width 100% !important
  height 100% !important
  .bpx-player-sending-bar
    display none
.player-fullscreen-margin-fix
  .bpx-player-video-wrap
    margin-bottom 80px !important
    height calc(100% - 80px) !important

.player-fullscreen-fix-top-leftover #aegis-nano-container
  top 80px
  height calc(100% - 80px) !important

#aegis-nano-container
  height 100%
  .bpx-state-paused
    .bpx-player-video-area
      .bpx-player-state-play
        display none
  .bpx-player-dm-root
    display none
  .bpx-player-video-wrap
    margin-bottom 55px
    height calc(100% - 55px)
  .bpx-player-ending-wrap
    display none
  .bpx-player-control-bottom // 播控常驻
    opacity 1
  .bpx-player-control-top // 进度条展示常驻
    opacity 1
    visibility visible
  .bpx-player-shadow-progress-area // 底部进度条不展示
    opacity 0
    visibility hidden

.video-player
  .bpx-player-ctrl-quality
    display none
</style>
