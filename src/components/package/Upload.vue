<template>
  <div class="upload">
    <div class="containerUp">
      <div class="upload-form">
        <label class="upload-btn-container">
          <span
            :class="[
              {
                'v-btn': true,
                'primary': true,
                'upload-btn': true,
                'disabled': disabled
              }
            ]"
          >
            {{ options.buttonName || '上传图片' }}
          </span>
          <input
            type="file"
            style="display: none"
            :name="options.inputName || 'cover'"
            :disabled="disabled"
            ref="fileInput"
            @change="uploadChange"
          />
        </label>
      </div>
    </div>
  </div>
</template>

<script>
import { validUpload } from '@/utils/index'
import { uploadImageByFile } from '@/plugins/bvcflow-upload'
import notify from '@/lib/notify'

export default {
  props: {
    options: {
      type: Object,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    useMockSubmit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  created() {},
  methods: {
    async uploadChange(el) {
      if (this.useMockSubmit) {
        return notify.success('上传封面成功')
      }
      let imgBfsUrl = ''
      // 1.检测文件类型
      const files = el.target.files
      const fileInput = this.$refs.fileInput
      const options = this.options
      const { message, valid } = validUpload(
        fileInput,
        options.filetype,
        options.filesize
      )
      // 2.文件不合格
      if (!fileInput.value || !valid) {
        notify.error(message)
        return
      }
      if (!files) return
      await uploadImageByFile(files[0])
        .then((res) => {
          imgBfsUrl = res
        })
        .catch((e) => {
          console.error(e)
        })
      if (imgBfsUrl) {
        this.$emit('uploaded', imgBfsUrl)
      } else {
        notify.error('上传封面失败')
      }
    }
  }
}
</script>

<style lang="stylus" scoped>
.upload .input
  display inline-block
.base-display
  display inline-block
.containerUp
  display inline-block
.upload-btn-container
  display inline-block
  vertical-align middle
.upload-btn
  padding 9px 15px
  font-size 12px
  border-radius 3px
  line-height 1
  display inline-block
  vertical-align middle
  text-align center
  cursor pointer
  -webkit-appearance none
  white-space nowrap
  margin 0 0 0 5px
  -webkit-user-drag none
  -webkit-user-select none
  -moz-user-select none
  -ms-user-select none
  user-select none
  background-color var(--link-color)
  border-color var(--link-color)
  color var(--text-color-reverse)
.upload-form
  display inline-block
  vertical-align middle
</style>
