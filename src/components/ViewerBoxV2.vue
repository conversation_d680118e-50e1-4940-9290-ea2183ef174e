<template>
  <div class="viewer-box-container">
    <ul :id="viewerID">
      <li v-for="(item, index) in imgArray" :key="item.src + index">
        <img :src="item.src" :alt="item.name">
      </li>
    </ul>
  </div>
</template>

<script>
import Viewer from 'viewerjs'
import 'viewerjs/dist/viewer.min.css'
import assign from 'lodash-es/assign'

/**
   * Viewer.js
   * JavaScript image viewer.
   * https://github.com/fengyuanchen/viewerjs
*/

export default {
  name: 'ViewerBoxV2',
  props: {
    imgArray: {
      type: [Array, Object],
      default() {
        return []
      }
    },
    options: {
      type: Object,
      default() {
        return {}
      }
    },
    viewerID: {
      type: String,
      default() {
        return 'ViewBoxImages'
      }
    }
  },
  data() {
    return {
      viewer: null
    }
  },
  watch: {
    imgArray(arr) {
      if (arr.length) {
        this.$nextTick(_ => {
          this.updateViewer()
        })
      }
    }
  },
  mounted() {
    this.initViewer()
    this.$nextTick(_ => {
      document.addEventListener('keydown', this.keydown)
    })
  },
  beforeD<PERSON>roy() {
    this.destroyViewer()
    document.removeEventListener('keydown', this.keydown)
  },
  methods: {
    updateViewer() {
      this.viewer.update()
    },
    openViewer(index = 0) {
      this.viewer.show(true)
      this.viewer.view(index)
    },
    closeViewer() {
      this.viewer.hide()
      this.$emit('close')
    },
    initViewer() {
      const el = document.getElementById(this.viewerID)
      const options = assign({
        hidden: () => {
          this.closeViewer()
        }
      }, this.options)
      const viewer = new Viewer(el, options)
      this.viewer = viewer
    },
    destroyViewer() {
      if (this.viewer && this.viewer.destroy) {
        this.viewer.destroy()
        this.viewer = null
      }
    },
    keydown(e) {
      if (this.viewer && this.viewer.isShown && !this.options.keyboard) {
        switch (e.keyCode) {
          case 38:
            this.viewer.move(0, 100)
            break
          case 40:
            this.viewer.move(0, -100)
            break
          case 37:
            this.viewer.prev()
            break
          case 39:
            this.viewer.next()
            break
          case 27:
            this.viewer.hide()
            break
          default: break
        }
      }
    }
  }
}
</script>

<style lang="stylus">
.viewer-box-container {
  position: absolute
  visibility: hidden
}
</style>
