<template>
  <div class="dynamic-iframe-container">
    <div v-if="dataReady">
      <DynItem v-if="dynData && dynData.item" :data="dynData.item" end />
      <el-empty v-else description="ERROR" :image-size="100" />
    </div>
  </div>
</template>
<script>
import { DynItem } from '@bplus-common/components-vue2'
import '@bplus-common/components-vue2/style.css'

import notify from '@/lib/notify'
import { getEnvConstant } from '@/utils/constant.js'
import { taskApi } from '@/api/index'

export default {
  components: {
    DynItem
  },
  data() {
    return {
      dynData: null,
      dataReady: false,
      timer: null
    }
  },
  created() {
    this.timer = null
    try {
      const id = window.location.hash.split('?')[1].split('=')[1]
      this.getCardDetail(id)
    } catch (err) {
      console.error(err)
    }
  },
  mounted() {
    this.hideFeedbackIcon()
    this.timer = setInterval(this.hideFeedbackIcon, 100)
  },
  beforeDestroy() {
   this.destroyTimer()
  },
  methods: {
    hideFeedbackIcon() {
      const ele = document.getElementById('tianshu_feedback_container')
      if (ele) {
        ele.style.display = 'none'
        this.destroyTimer()
      }
    },
    destroyTimer() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    },
    async getCardDetail(id) {
      if (id) {
        const host = getEnvConstant('NEW_DYNAMIC_HOST')
        const { code, data } = await taskApi
          .getDynDetail(host, { id })
          .catch((_) => {
            this.dataReady = true
            notify.error(
              `//${host}/x/polymer/web-dynamic/v1/detail/audit?id=${id}`
            )
          })
        this.dataReady = true
        if (code === 0 && data.item) {
          this.dynData = data
        }
      }
    }
  }
}
</script>
<style lang="stylus" scoped>
.el-empty
  padding 28px 0

.dynamic-iframe-container::-webkit-scrollbar {
    display: none; // 隐藏滚动条，滚动功能正常
}
</style>
