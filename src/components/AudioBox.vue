<template>
<div class="music-box">
  <AudioBoxMusic @music-ready="getMusicObj" />
  <!-- <div :class="$style['container']">
  </div> -->
  <div class="audio-rr">
    <div class="audio-player-container">
      <div class="left-part">
        <div class="rate-box v-form">
          <label for="" class="rate-label">播放速度</label>
          <select class="rate-select" v-model="playRate" @change="changePlayRate(0)">
            <option
              :value="value"
              v-for="value in RateSelectList"
              :key="value"
            >{{value}}x</option>
          </select>
        </div>
        <div class="play-box">
          <div class="next-btn" v-html="getSVG('prev')" @click="prevSong" v-if="showNextPrevBtn"></div>
          <div class="rate-down-btn" v-html="getSVG('slow')" @click="changePlayRate(-1)" v-if="showRateBtn"></div>
          <div class="play-btn">
            <div class="loading" v-if="loading">
              <div class="spinner" v-html="getSVG('loading')">
              </div>
            </div>
            <div class="play-pause-btn" @click="togglePlay" v-if="!loading">
              <i>
                <icon :name="!playPause ? 'pause' : 'play'" :scale="1.5"></icon>
              </i>
            </div>
          </div>
          <div class="rate-up-btn" v-html="getSVG('quick')" @click="changePlayRate(1)" v-if="showRateBtn"></div>
          <div class="prev-btn" v-html="getSVG('next')" @click="nextSong" v-if="showNextPrevBtn"></div>
        </div>
      </div>
      <div class="mid-part">
        <div class="mid-top">
          <div class="song-name" :title="songName">
            {{songName}}
          </div>
          <div class="song-time">
            <span class="current-time" >{{ currentTimeStr || '0:00' }}</span>
            /
            <span class="total-time">{{ durationStr || '0:00' }}</span>
          </div>
        </div>
        <div class="mid-down">
          <div class="progress-controls">
            <div class="slider-progress" ref="processSlider" @mouseup.stop="processSliderClick" @mousemove.stop="sliderMove" @mouseleave="sliderLeave">
              <div class="slider-progress-void"></div>
              <div class="slider-progress-bar">
                <div class="slider-progress-buffer" :style="{width: `${bufferWidth}px`, right: `-${bufferWidth}px`}" ></div>
                <div class="slider-progress-green" :style="{width: `${-1 * (pinRight - 8)}px`, right: `${(pinRight - 8)}px`}" ></div>
                <div class="slider-progress-pin" :style="{right: `${pinRight}px`}" @mousedown="pinHold" @mousemove.stop="pinMoveIgnore" @mouseup.stop="pinFree"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="right-part">
        <div class="volume">
          <div class="volume-controls"  >
            <div class="slider-volume" ref="volumeSlider"  @click.stop="volumeSliderClick" >
              <div class="slider-volume-void"></div>
              <div class="slider-volume-buffer" :style="{width: `${volumeWidth}px`}">
                <div class="slider-volume-pin" ></div>
              </div>
            </div>
          </div>
          <div class="volume-btn" @click="toggleVol" v-html="isSilent ?  getSVG('volume-off') : getSVG('volume-btn')">
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</template>

<script>
import { isNumber, isArray, isFunction, assign } from 'lodash-es'
import AudioBoxMusic from './AudioBoxMusic'

export default {
  props: {
    options: {
      type: Object
    }
  },
  components: {
    AudioBoxMusic
  },
  data() {
    return {
      playlist: [],
      playlistMap: {},
      points: [],
      events: {},
      musicObj: {},
      loading: true,
      keydownListening: false,
      oldKeydownFunc: null,
      showRateBtn: 0,
      showNextPrevBtn: 0,
      playPause: 1,
      playRate: 1,
      playingIndex: 0,
      bufferWidth: '',
      pinRight: '',
      volumeWidth: '',
      volumeTemp: 0.25,
      isSilent: false,
      currentTimeStr: '',
      currentTime: 0,
      duration: 0,
      durationStr: '',
      songName: '歌曲名',
      processSliderPressing: false,
      volumeSliderPressing: false,
      RateSelectList: [0.5, 0.75, 1, 1.25, 1.5, 2]
    }
  },
  computed: {
  },
  methods: {
    loadList(playlist) {
      if (!playlist || !isArray(playlist) || !playlist.length) {
        this.updateSongName('歌曲列表为空')
        return
      }
      const map = this.playlistMap
      map.length = playlist.length
      playlist.forEach((item, index) => {
        map[index] = item
      })
      this.changeSong(0)
    },
    pointsChange(newPoints) {
      this.$emit('on-peaks-change', JSON.stringify(newPoints))
    },
    changeSong(index) {
      this.playingIndex = index || 0
      const map = this.playlistMap
      const song = map[this.playingIndex]
      if (!song) return

      this.musicObj.loadAudio(song.src)
      this.updateSongName(song.name || '')
      this.points = song.timeline ? JSON.parse(song.timeline) : []
      this.playPause = 1
    },

    nextSong() {
      if (this.playingIndex >= this.playlistMap.length - 1) return
      this.changeSong(this.playingIndex - 0 + 1)
    },

    prevSong() {
      if (this.playingIndex < 1) return
      this.changeSong(this.playingIndex - 1)
    },

    changePlayRate(plusOrMinus) {
      if (!this.musicObj.$refs.audioDom) return
      const list = this.RateSelectList
      let rate = this.playRate
      const index = list.indexOf(rate)
      if (plusOrMinus === 0) {
        this.musicObj.$refs.audioDom.playbackRate = rate
      } else if (index > -1) {
        rate = list[plusOrMinus + index] ? list[plusOrMinus + index] : rate
        this.playRate = rate
        this.musicObj.$refs.audioDom.playbackRate = rate
      }
    },

    volumeSliderClick(e) {
      if (!this.musicObj.$refs.audioDom) return
      const x = e.offsetX || e.layerX
      const w = this.$refs.volumeSlider.offsetWidth
      const audioDom = this.musicObj.$refs.audioDom
      audioDom.volume = (x / w).toFixed(3)
      this.volumeWidth = x + 5
    },

    processSliderClick(e) {
      if (!this.musicObj.$refs.audioDom) return
      const x = e.offsetX || e.layerX
      const w = this.$refs.processSlider.offsetWidth
      const audioDom = this.musicObj.$refs.audioDom
      audioDom.currentTime = (x / w).toFixed(3) * audioDom.duration
      this.processSliderPressing = false
    },

    pinMoveIgnore() {
      // 为了阻止冒泡
    },

    sliderMove(e) {
      if (!this.processSliderPressing) return
      const x = e.offsetX || e.layerX
      this.pinRight = -1 * (x - 8)
    },

    sliderLeave() {
      this.processSliderPressing = false
    },

    pinHold() {
      this.processSliderPressing = true
    },

    pinFree() {
      if (!this.musicObj.$refs.audioDom) return
      this.processSliderPressing = false
      const x = this.pinRight * -1
      const w = this.$refs.processSlider.offsetWidth
      const audioDom = this.musicObj.$refs.audioDom
      audioDom.currentTime = (x / w).toFixed(3) * audioDom.duration
    },

    togglePlay() {
      this.playPause = !this.playPause ? 1 : 0
      this.musicObj.playAudio()
    },

    secondToTime(second) {
      if (isNaN(second)) {
        return '00:00'
      }
      const add0 = num => (num < 10 ? `0${num}` : `${num}`)
      const min = parseInt(second / 60, 10)
      const sec = parseInt(second - min * 60, 10)
      const hours = parseInt(min / 60, 10)
      const minAdjust = parseInt((second / 60) - (60 * parseInt((second / 60) / 60, 10)), 10)
      return second >= 3600 ? `${add0(hours)}:${add0(minAdjust)}:${add0(sec)}` : `${add0(min)}:${add0(sec)}`
    },

    updateVolume(volume) {
      const audioDom = this.musicObj.$refs.audioDom
      const slider = this.$refs.volumeSlider
      const w = slider.offsetWidth
      const v = isNumber(volume) ? volume : audioDom.volume
      this.isSilent = v < 0.01
      this.volumeWidth = w * v
    },

    updateSongName(name) {
      this.songName = name
    },

    updateTime(currentTime, duration) {
      this.durationStr = this.secondToTime(duration)
      this.currentTimeStr = this.secondToTime(currentTime)
    },

    updateBuffer(percent) {
      const slider = this.$refs.processSlider
      const w = slider.offsetWidth
      this.bufferWidth = w * percent
    },

    updatePin(percent) {
      const slider = this.$refs.processSlider
      const w = slider.offsetWidth
      this.pinRight = -1 * (w * percent - 8)
    },

    toggleVol() {
      const audioDom = this.musicObj.$refs.audioDom
      const v = audioDom.volume
      if (v < 0.01) {
        audioDom.volume = this.volumeTemp
      } else {
        this.volumeTemp = v
        audioDom.volume = 0
      }
    },

    getMusicObj(musicObj) {
      this.musicObj = musicObj
    },

    initKeypressListener() {
      const audioDom = this.musicObj.$refs.audioDom
      const dom = window.document
      const oldKeydownFunc = dom.onkeydown
      this.oldKeydownFunc = oldKeydownFunc
      if (this.keydownListening) return
      const listener = (e, callback) => {
        let ifEnd = false
        if (e.target.nodeName !== 'BODY')ifEnd = true

        if (e.keyCode === 32) {
          // play pause
          this.togglePlay()
          ifEnd = true
        }
        if (!ifEnd && e.ctrlKey && e.keyCode === 37) {
          // arrow left
          this.prevSong()
          ifEnd = true
        }
        if (!ifEnd && e.ctrlKey && e.keyCode === 39) {
          // arrow r
          this.nextSong()
          ifEnd = true
        }
        if (!ifEnd && e.keyCode === 38) {
          // arrow up
          try {
            this.musicObj.$refs.audioDom.volume += 0.05
            ifEnd = true
          } catch (error) {
            console.error('error: maximum volume')
          }
        }
        if (!ifEnd && e.keyCode === 40) {
          // arrow d
          try {
            this.musicObj.$refs.audioDom.volume -= 0.05
            ifEnd = true
          } catch (error) {
            console.error('error: minimum volume')
          }
        }

        if (!ifEnd && e.keyCode === 37) {
          // arrow left
          audioDom.currentTime -= 1
          ifEnd = true
        }

        if (!ifEnd && e.keyCode === 39) {
          // arrow right
          audioDom.currentTime += 1
          ifEnd = true
        }

        if (isFunction(callback))callback.call(window.document, e)
        return true
      }
      if (!oldKeydownFunc) {
        dom.onkeydown = listener
      } else if (isFunction(oldKeydownFunc)) {
        dom.onkeydown = (e) => {
          listener.call(window.document, e, oldKeydownFunc)
        }
      }
      this.keydownListening = true
    },
    removeListeners() {
      const audioDom = this.musicObj.$refs.audioDom
      audioDom.removeEventListener('loadstart', this.loadstartHandler)
      audioDom.removeEventListener('durationchange', this.durationchangeHandler)
      audioDom.removeEventListener('progress', this.progressHandler)
      audioDom.removeEventListener('timeupdate', this.timeupdateHandler)
      audioDom.removeEventListener('error', this.errorHandler)
      audioDom.removeEventListener('canplay', this.canplayHandler)
      audioDom.removeEventListener('ended', this.endedHandler)
      audioDom.removeEventListener('volumechange', this.volumechangeHandler)
    },
    loadstartHandler() { this.loading = true },
    // show audio time: the metadata has loaded or changed
    durationchangeHandler() {
      const audioDom = this.musicObj.$refs.audioDom
      if (audioDom.duration !== 1) this.loading = false // compatibility: Android browsers will output 1 at first
    },
    // show audio loaded bar: to inform interested parties of progress downloading the media
    progressHandler() {
      const audioDom = this.musicObj.$refs.audioDom
      const buffer = audioDom.buffered
      const percentage = buffer.length ? buffer.end(buffer.length - 1) / audioDom.duration : 0
      this.updateBuffer(percentage)
    },
    timeupdateHandler() {
      const audioDom = this.musicObj.$refs.audioDom
      const time = audioDom.currentTime
      this.currentTime = time
      const percentage = time ? time / audioDom.duration : this.currentTime
      this.updatePin(percentage)
      this.updateTime(time, audioDom.duration)
    },
    // audio download error: an error occurs
    errorHandler() {
      this.loading = true
      this.$emit('error', this)
    },
    // audio can play: enough data is available that the media can be played
    canplayHandler() {
      const audioDom = this.musicObj.$refs.audioDom
      // 移动进度条时会触发canplay&progress
      this.loading = false
      this.updateBuffer(audioDom.buffered.end(audioDom.buffered.length - 1) / audioDom.duration)
      this.updateTime(audioDom.currentTime, audioDom.duration)
    },
    endedHandler() {
      const audioDom = this.musicObj.$refs.audioDom
      audioDom.currentTime = this.currentTime = 0
      this.playPause = 1
    },
    volumechangeHandler() {
      this.updateVolume()
    },
    getSVG(type) {
      return `
          <svg xmlns:xlink="http://www.w3.org/1999/xlink" width="${svg[type][0]}" height="${svg[type][1]}" version="1.1" viewBox="${svg[type][2]}" width="100%">
              <path class="aplayer-fill" fill="#566574" stroke="#566574" fill-rule="evenodd" d="${svg[type][3]}"></path>
          </svg>
      `
    }
  },
  mounted() {
    const audioDom = this.musicObj.$refs.audioDom
    const options = assign({}, {
      volume: 0.25,
      showRateBtn: 1,
      showNextPrevBtn: 0
    }, this.options)
    audioDom.volume = options.volume
    this.updateVolume()
    this.showRateBtn = options.showRateBtn
    this.showNextPrevBtn = options.showNextPrevBtn

    audioDom.addEventListener('loadstart', this.loadstartHandler)
    audioDom.addEventListener('durationchange', this.durationchangeHandler)
    audioDom.addEventListener('progress', this.progressHandler)
    audioDom.addEventListener('timeupdate', this.timeupdateHandler)
    audioDom.addEventListener('error', this.errorHandler)
    audioDom.addEventListener('canplay', this.canplayHandler)
    audioDom.addEventListener('ended', this.endedHandler)
    audioDom.addEventListener('volumechange', this.volumechangeHandler)

    this.initKeypressListener()

    this.$emit('ready', this)
  },
  beforeDestroy() {
    this.removeListeners()
    if (this.keydownListening) {
      window.document.onkeydown = this.oldKeydownFunc
      this.keydownListening = false
    }
  }
}

const svg = {
  next: [36, 36, '0 0 200 200', 'm22.27419,12.26878m55.48853,88.55585l-58.0127,60.89639l0.15291,-59.93483l0.15857,-62.15324l57.70122,61.19161l0,0.00005l0,0.00001l0,0.00001zm-55.48853,-88.55585m117.63116,88.71439l-58.01281,60.9382l0.15585,-61.08581l0.15585,-61.08581l57.70111,61.23342zm70.74188,101.26682m-36.81649,-38.11877l-25.43201,-0.06489l0.31624,-123.95015l25.43201,0.06489l-0.31624,123.95015z'],
  prev: [36, 36, '0 0 200 200', 'm188.85712,189.5m-55.71428,-88.41399l57.85715,-61.0442l0,59.93503l0,62.15344l-57.85715,-61.0442l0,-0.00005l0,-0.00001l0,-0.00001zm55.71428,88.41399m-117.85711,-88.41399l57.85715,-61.08601l0,61.08601l0,61.08601l-57.85715,-61.08601zm-71.00001,-101.08601m36.91362,38.02472l25.43209,0l0,123.95055l-25.43209,0l0,-123.95055z'],
  loading: [36, 36, '0 0 500 500', 'm250.21652,103.11755c15.12036,0.01842 29.56513,2.54151 43.245,6.7566l-10.82663,18.04587l76.8515,0l-19.21779,-32.07532l-19.20312,-32.06573l-10.10868,16.89658c-18.94526,-6.67523 -39.36898,-10.41106 -60.72651,-10.41106c-98.30716,0 -177.98821,76.79598 -177.98821,171.53737c0,39.32079 13.87685,75.44799 36.97097,104.39323l27.06403,-20.02607c-18.67788,-23.39173 -29.90806,-52.58136 -29.96899,-84.35769c0.13603,-76.59716 64.4385,-138.57633 143.90841,-138.69378l0.00001,0zm141.03599,34.32726l-27.06397,20.03566c18.67318,23.38283 29.90341,52.55406 29.95479,84.33983c-0.13605,76.59653 -64.4385,138.56684 -143.91303,138.69306c-14.08335,-0.01755 -27.55679,-2.22447 -40.40128,-5.91527l10.18821,-16.97847l-76.85116,0l19.20358,32.05757l19.21743,32.10178l10.714,-17.91871c18.17087,6.0963 37.6182,9.4974 57.92922,9.50624c98.32135,-0.01766 177.98872,-76.81371 178.01181,-171.55517c-0.0231,-39.32071 -13.90981,-75.43908 -36.98961,-104.36652l0,-0.00001z'],
  slow: [36, 36, '42 0 200 200', 'm188.85712,189.5m-55.71428,-88.41399l57.85715,-61.0442l0,59.93503l0,62.15344l-57.85715,-61.0442l0,-0.00005l0,-0.00001l0,-0.00001zm55.71428,88.41399m-117.85711,-88.41399l57.85715,-61.08601l0,61.08601l0,61.08601'],
  quick: [36, 36, '12 28 200 200', 'm73.14288,40m55.71428,88.41399l-57.85715,61.0442l0,-59.93503l0,-62.15344l57.85715,61.0442l0,0.00005l0,0.00001zm-55.71428,-88.41399m117.85711,88.41399l-57.85715,61.08601l0,-61.08601l0,-61.08601'],
  'volume-off': [30, 30, '0 5 30 30', 'M13.728 6.272v19.456q0 0.448-0.352 0.8t-0.8 0.32-0.8-0.32l-5.952-5.952h-4.672q-0.48 0-0.8-0.352t-0.352-0.8v-6.848q0-0.48 0.352-0.8t0.8-0.352h4.672l5.952-5.952q0.32-0.32 0.8-0.32t0.8 0.32 0.352 0.8z'],
  'play-btn': [36, 36, '0 0 18 24', 'M18 12L0 24V0'],
  'pause-btn:': [36, 36, '0 0 18 24', 'M0 0h6v24H0zM12 0h6v24h-6z'],
  'volume-btn': [30, 30, '0 0 30 30', 'M14.667 0v2.747c3.853 1.146 6.666 4.72 6.666 8.946 0 4.227-2.813 7.787-6.666 8.934v2.76C20 22.173 24 17.4 24 11.693 24 5.987 20 1.213 14.667 0zM18 11.693c0-2.36-1.333-4.386-3.333-5.373v10.707c2-.947 3.333-2.987 3.333-5.334zm-18-4v8h5.333L12 22.36V1.027L5.333 7.693H0z']
}
</script>

<style lang="stylus" scope>
.music-box {
  width: 100%;
}
.audio-player-container{
  height: 90px;
  overflow: hidden;
}
.audio-rr {
  position: relative;
  width: 100%;
  max-width: 1050px;
  border: 1px solid var(--border-color-light-2);
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.07);
  display: block;
  justify-content: space-between;
  align-items: center;
  padding-left: 12px;
  padding-right: 12px;
  border-radius: 4px;
  user-select: none;
  -webkit-user-select: none;
  background-color: var(--content-bg-color);
}

.left-part {
  position: relative;
  float: left;
  width: 40%;
  height: 100%;
}

.rate-box {
  position: relative;
  float: left;
  width: 50%;
  line-height: 35px;
  height: 35px;
  top: 25px;
}

.rate-select {
  position: relative;
  float: left;
  height: 35px;
  width: 35%!important;
  margin-right: 15%;
}

.rate-label {
  position: relative;
  float: left;
  width: 40%;
  margin-right: 10%;
  text-align: right;
}

.play-box {
  position: relative;
  float: left;
  width: 50%;
  line-height: 1;
  height: 35px;
  top: 25px;
  text-align: center;
}

.rate-up-btn, .rate-down-btn {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.play-btn {
  position: relative;
  display: inline-block;
  cursor: pointer;
  margin: 0 10px 0 12px;
}

.play-pause-btn {
  position: relative;
  display: inline-block;
  width: 36px;
  height: 36px;
  cursor: pointer;
  top: -5px;
  color: #566574;
}

.loading {
  position: relative;
  cursor: pointer;
}

.next-btn, .prev-btn {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.mid-part {
  position: relative;
  float: left;
  width: 38%;
  height: 100%;
}

.mid-top {
  position: relative;
  float: left;
  width: 100%;
  height: 30%;
  font-size: 16px;
  line-height: 18px;
  padding: 18px 6% 0 6%;
}

.song-name {
  position: relative;
  float: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  max-width: 350px;
}
.song-time {
  position: relative;
}

.mid-down {
  position: relative;
  float: left;
  width: 100%;
  height: 50%;
}

.right-part {
  position: relative;
  float: right;
  width: 22%;
  line-height: 35px;
  height: 35px;
  top: 25px;
}

.spinner {
  width: 36px;
  height: 36px;
  animation: spinRotate 1.4s linear infinite;
}
.slider-progress-buffer {
  background-color: #111113;
  border-radius: inherit;
  position: absolute;
  right: -31px;
  width: 31px;
  height: 100%;
}

.slider-progress-green {
  border-radius: inherit;
  position: absolute;
  right: -31px;
  width: 31px;
  height: 100%;
  background-color: #44BFA3;
}

.progress-controls {
  position: relative;
  display: block;
  float: left;
  width: 90%;
  color: #55606E;
  margin: 0 5%;
  top: 10px;
}

.slider-progress {
  background-color: #D8D8D8;
  cursor: pointer;
  display: block;
  position: relative;
  width: 96%;
  margin: 0 2%;
  border-radius: 2px;
  height: 4px;
}

.slider-progress-void {
  position: absolute;
  width: 100%;
  height: 40px;
  top: -16px;
}

.slider-progress-bar {
  background-color: #44BFA3;
  border-radius: inherit;
  pointer-events: none;
  position: relative;
  width: 0;
  height: 100%;
}

.slider-progress-pin {
  height: 16px;
  width: 16px;
  border-radius: 8px;
  background-color: #44BFA3;
  pointer-events: all;
  box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.32);
  position: relative;
  right: 8px;
  top: -6px;
}

.volume {
  position: relative;
  display: block;
  float: right;
  width: 100%;
}

.volume-btn {
  position: relative;
  display: block;
  float: right;
  width: 30px;
  height: 30px;
  left: 0;
  top: 6px;
  cursor: pointer;
}

.volume-controls {
  position: relative;
  display: block;
  float: right;
  width: 80%;
  height: 30px;
  background: none;
  border-radius: 7px;
  left: 0;
  bottom: -3px;
}

.slider-volume {
  position: relative;
  display: block;
  width: 80%;
  height: 6px;
  margin: 12px 10%;
  border-radius: 20px;
  border: 0;
  background: #D8D8D8;
  cursor: pointer;
}

.slider-volume-void {
  position: absolute;
  width: 100%;
  height: 40px;
  top: -16px;
}

.slider-volume-buffer {
  background-color: #44BFA3;
  border-radius: inherit;
  position: absolute;
  pointer-events: none;
  bottom: 0;
  width: 50%;
  height: 6px;
}

.slider-volume-pin {
  height: 16px;
  width: 16px;
  border-radius: 8px;
  background-color: #44BFA3;
  position: absolute;
  box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.32);
  pointer-events: none;
  right: -10px;
  top: -5px;
}

.svg {
  display: block;
}

@keyframes spinRotate {
  from {
    transform: rotateZ(0);
    transform-origin:50%;
  }
  to {
    transform: rotateZ(1turn);
  }
}
</style>
