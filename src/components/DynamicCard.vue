<template>
  <div id="app" class="mng-card-preview">
    <DynItem v-if="!showOpusView && dynData && dynData.item" :key="cardKey" :data="dynData.item" end />
    <OpusView v-if="showOpusView && dynData && dynData.item" :key="cardKey" :data="dynData.item" />
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import { taskApi } from '@/api/index'
import notify from '@/lib/notify'
import { getEnvConstant } from '@/utils/constant.js'
import { set } from 'lodash-es'

import {
  DynItem,
  OpusView
} from '@bplus-common/components-vue2'
import '@bplus-common/components-vue2/style.css'

export default {
  props: {
    resource: {
      type: Object,
      default: () => {}
    }
  },
  provide: {
    // 动态业务卡片所需
    commonLogData() { return {} },
    enableAVIF: true,
    preview: true // 指定动态卡片使用预览模式 绕开一些需要登陆的地方
  },
  data () {
    return {
      dynData: null,
      cardDetail: null,
      cardEl: null,
      cardKey: 0,
      showOpusView: false
    }
  },
  computed: {
    ...mapState({
      isRedBlue: (state) => state.todoDetail.isRedblue
    }),
    dataReady() {
      return this.cardDetail?.card && this.cardDetail?.card !== '{}'
    },
    todoId() {
      return this.$route.query?.todo_id
    }
  },
  components: {
    DynItem,
    OpusView
  },
  methods: {
    ...mapActions({
      setDynamicCardData: 'dynamicPic/setDynamicCardData',
      resetDynamicCardData: 'dynamicPic/resetDynamicCardData'
    }),
    /**
     * snapshot不为空，则直接赋值，不需要从接口获取
     */
    async getCardDetail (params) {
      const {
        oid, snapshot, showOpusView = false
      } = params
      this.showOpusView = showOpusView
      this.initCardDetail()
      if (snapshot) {
        this.dynData = snapshot
        this.afterGetCardData(snapshot)
      } else if (oid) {
        const host = getEnvConstant('NEW_DYNAMIC_HOST')
        try {
          const apiFn = showOpusView ? taskApi.getOpusDetail : taskApi.getDynDetail
          const { code, data, message } = await apiFn(host, { id: oid })
          if (code === 0 && data.item) {
            if (this.isRedBlue && !showOpusView) this.injectRedBlueData(data)
            this.dynData = data
          } else {
            throw Error(message)
          }

          this.afterGetCardData(this.dynData)
        } catch (e) {
          const apiEndPoint = showOpusView ? 'opus/detail/audit' : 'detail/audit'
          notify.error(`//${host}/x/polymer/web-dynamic/v1/${apiEndPoint}?id=${oid} ${e}`)
        }
      }
    },
    injectRedBlueData(data) {
      const { user_info: mockUserInfo = {} } = this.resource || {}
      set(data, 'item.modules.module_author.mid', mockUserInfo.mid)
      set(data, 'item.modules.module_author.name', mockUserInfo.name)
      set(data, 'item.modules.module_author.face', mockUserInfo.face)
    },
    afterGetCardData(cardData) {
      this.cardKey += 1
      this.setDynamicCardData(cardData)
    },
    initCardDetail() {
      this.cardDetail = null
      this.dynData = null
      this.resetDynamicCardData()
    }
  }
}
</script>

<style lang="stylus" scoped>
.mng-card-preview {
  overflow hidden
  >>> .card {
    width 100%
    min-width auto
    .main-content {
      width auto
    }
  }
	background: var(--content-bg-color)
  .card {
    border-top: none !important
    margin-bottom: 20px
    min-width 100%
  }
  .tags {
    font-size: 14px
    margin-left: 14px
    color var(--text-color)
  }
}
</style>
<style lang="stylus">
.mng-card-preview
  .bili-rich-text__content
    line-height: 25px

.mng-card-preview
  .bili-dyn-item
    .full-screen
      display inline-flex

</style>
