// 根据 aid、cid 获取转码状态并选择播放器来播放视频，分 P 需要外部自行处理
<template>
  <div class="single-video-player-box" :style="{ height: playerHeight + 'px' }">
    <NanoModulePlayer
      :aid="useOnlinePlayer ? aid : undefined"
      :cid="useOnlinePlayer ? cid : undefined"
      :src="useOnlinePlayer ? undefined : videoResourceUrl"
      :class="{ [className]: true }"
      @nano-canplay="useOnlinePlayer ? handleIFramePlayerInit : handleLocalPlayerCanPlay"
    />
  </div>
</template>
<script>
import NanoModulePlayer from '@/components/package/VideoPlayer/NanoModulePlayer'
import { videoApi, workbenchDetailApi } from '@/api/index'
import { changeHttpProtocol } from '@/utils/index'
import notify from '@/lib/notify'

export default {
  name: 'VideoSinglePlayer',
  components: {
    NanoModulePlayer
  },
  props: {
    aid: {
      required: true
    },
    cid: {
      required: true
    },
    // 播放器选择：优先线上（已二转则使用，否则查资源地址用本地）/强制线上/强制本地
    playerMode: {
      type: String,
      validator: function (value) {
        return ['preferOnline', 'forceOnline', 'forceLocal'].includes(value)
      },
      default: 'preferOnline'
    },
    // 播放器组件高度，单位 px
    playerHeight: {
      type: Number,
      default: 500
    },
    // 样式类名
    className: {
      type: String,
      default: ''
    },
    // 额外参数，对于 VideoIFrame/VideoPlayer 不一致，外部需要特殊定制时使用
    extraConfig: {
      type: Object,
      default: () => ({})
    }
    // 还可以加参数如强制指定播放器，默认初始定位点
  },
  data() {
    return {
      // 视频的资源地址，只用于本地播放器
      videoResourceUrl: '',
      videoXcodeState: '',
      localPlayer: null
    }
  },
  computed: {
    useOnlinePlayer() {
      if (this.playerMode === 'forceOnline') return true
      if (this.playerMode === 'forceLocal') return false
      // 二转完成 正在分发 分发完成
      return [4, 5, 6].includes(this.videoXcodeState)
    }
  },
  methods: {
    async seek(second) {
      if (this.useOnlinePlayer) {
        const iframe = document.getElementById('nanoPlayerIframe')
        let nanoPlayer = window.iframePlayerBridge.getPlayerProxy(iframe)
        nanoPlayer?.seek(second)
        nanoPlayer = null
      } else {
        this.$refs.localPlayerRef?.goSeekTime?.(second)
      }
    },
    // 获取视频转码状态，选择播放器
    async choosePlayer() {
      // 若强制指定使用线上播放器，不需要查询视频转码状态，VideoIFrame 组件内直接拼接 iframe url
      if (this.playerMode === 'forceOnline') return
      // 若强制指定使用线上播放器，不需要查询视频转码状态，直接获取视频资源地址
      if (this.playerMode === 'forceLocal') {
        await this.getPlayUrl()
        return
      }
      try {
        // 优先线上播放器，先查转码状态 http://bapi.bilibili.co/project/6892/interface/api/362268
        const res = await videoApi.getVideoXcodeState({ cid: this.cid })
        this.videoXcodeState = res?.data?.state
        if (!this.useOnlinePlayer) {
          // 未完成二转的无法使用嵌入式播放器，需要获取资源地址本地播放
          await this.getPlayUrl()
        }
      } catch (error) {
        // 异常时用线上播放兜底
        this.videoXcodeState = 6
      }
    },
    // 获取视频资源地址，交由本地播放器播放
    async getPlayUrl() {
      this.videoResourceUrl = ''
      const res = await workbenchDetailApi.getPlayUrl({ cid: this.cid })
      this.videoResourceUrl = changeHttpProtocol(res?.data?.playurl)
      if (res?.data?.tips) {
        notify.warning(res.data.tips, 1500)
      }
    },
    handleLocalPlayerInit() {
      this.localPlayer = this.$refs.localPlayerRef.player
      this.$emit('init')
    },
    handleLocalPlayerCanPlay() {
      this.$emit('canplay')
    },
    handleIFramePlayerInit() {
      this.$emit('init')
    }
  },
  watch: {
    cid: {
      async handler(value) {
        if (!value) return
        // TODO 未处理并发调用
        await this.choosePlayer()
      },
      immediate: true
    }
  }
}
</script>
<style lang="stylus">
.single-video-player-box{

}
</style>
