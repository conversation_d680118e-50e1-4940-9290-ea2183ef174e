<template>
  <div class="filter-main">
    <el-form inline @submit.stop.prevent.native ref="filterForm" v-if="showFormItems.length" :model="formData" :rules="formRules">
      <el-form-item v-for="(item, index) in showFormItems" :key="index" :prop="item.name[0] || item.name" :required="item.required" :class="{'group-tag': item.groupTag}">
        <el-input
          v-if="item.type === 'input' || item.type === 'input_zone'"
          type="textarea"
          :autosize="{ minRows: 1, maxRows: 1}"
          :placeholder="item.placeholder"
          v-model="formData[item.name]"
          resize="none"
          class="input"
          style="width: 200px"
          size="small">
        </el-input>

        <el-input-number
          v-else-if="item.type === 'input_number'"
          v-model="formData[item.name]"
          :autosize="{ minRows: 1, maxRows: 1}"
          :placeholder="item.placeholder"
          resize="none"
          class="input"
          style="width: 200px"
          size="small"
        />

        <el-select
          v-else-if="item.type === 'select' || item.type === 'multi_value_select' || item.type === 'multi_value_checkbox' || item.type === 'checkbox'"
          :placeholder="item.placeholder"
          :multiple="item.type.indexOf('checkbox') > -1"
          v-model="formData[item.name]"
          class="select"
          size="small"
          filterable
          @change="handleChange(item.name)"
          :style="{'width': item.type === 'checkbox' ? '250px' : '150px'}"
          :clearable="JSON.stringify(item.clearable) ? item.clearable : true">
          <el-option
            v-for="(obj, index) in filterOptions[item.name]"
            :key="index"
            :value="item.type === 'multi_value_select' || item.type === 'multi_value_checkbox' ? obj.label : obj.val"
            :label="obj.label">
          </el-option>
        </el-select>

        <el-date-picker
          v-else-if="item.type === 'time_zone' || item.type === 'time'"
          :placeholder="item.placeholder"
          v-model="formData[item.name]"
          :type="'datetime'"
          value-format="yyyy-MM-dd HH:mm:ss"
          size="small">
        </el-date-picker>

        <div v-else-if="item.type === 'cascader'">
          <el-select
            :placeholder="item.placeholder[0]"
            v-model="formData[item.name[0]]"
            @change="handleCascaderChange($event, item.name)"
            class="select"
            size="small"
            style="width: 150px; margin-right: 5px"
            clearable>
            <el-option
              v-for="obj in filterOptions[item.name[0]]"
              :key="obj.val"
              :value="obj.val"
              :label="obj.label">
            </el-option>
          </el-select>
          <el-select
            v-if="subOptions.length"
            v-model="formData[item.name[1]]"
            :placeholder="item.placeholder[1]"
            class="select"
            size="small"
            style="width: 150px;"
            clearable>
            <el-option
              v-for="obj in subOptions"
              :key="obj.value"
              :value="obj.value"
              :label="obj.name">
            </el-option>
          </el-select>
        </div>

        <TreeSelect
          v-else-if="item.type === 'arctype'"
          :placeholder="item.placeholder"
          width="150px"
          :options="arcOptions"
          v-bind:selectedOptions="formData[item.name]"
          @change="(val) => formData[item.name] = val">
        </TreeSelect>

        <el-checkbox-group v-model="formData[item.name]" v-else-if="item.type === 'checkbox_group'">
          <el-checkbox :label="item.val" v-for="(item, index) in filterOptions[item.name]" :key="index">{{item.label}}</el-checkbox>
        </el-checkbox-group>

        <el-checkbox
          v-else-if="item.type === 'checkbox_single'"
          :label="item.label"
          :true-label="item.trueValue"
          :false-label="item.falseValue"
          v-model="formData[item.name]">
          {{item.label}}
        </el-checkbox>

        <slot :name="item.slotName" v-else></slot>
      </el-form-item>

      <el-row>
        <el-button type="primary" size="small" @click="getList">搜索</el-button>
        <el-button type="info" size="small" @click="reset">重置</el-button>
      </el-row>
    </el-form>
  </div>
</template>
<script>
import TreeSelect from '@/components/TreeSelect'
import { commonApi } from '@/api/index'

export default {
  components: {
    TreeSelect
  },
  props: {
    formItems: {
      type: Array,
      default: () => {
        return []
      }
    },
    formData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    filterOptions: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    const validateEvent = (rule, value, callback) => {
      if (!value) {
        callback(new Error('这是必填项'))
      } else {
        callback()
      }
    }
    return {
      formRules: {},
      rule: {
        required: true,
        validator: validateEvent,
        trigger: 'blur'
      },
      options: [],
      arcOptions: []
   }
  },

  watch: {
    filterOptions: {
      handler() {
        this.setOptions()
      },
      immediate: true
    },
    formItems: {
      handler() {
        this.setRules()
      },
      immediate: true
    }
  },
  computed: {
    subOptions() {
      return this.options || []
    },
    showFormItems() {
      return this.formItems.filter(item => item.type !== 'hide') || []
    }
  },
  methods: {
    setOptions() {
      let options = this.filterOptions
      for (let key in options) {
        if (options[key].href) {
          // this.$ajax.get(options[key].href, {
          //   hideLoadingBar: true
          // }).then(res => {
          //   let data = []
          //   if (!res.data) {
          //     return []
          //   }
          //   for (let key in res.data) {
          //     data.push({
          //       val: key,
          //       label: res.data[key]
          //     })
          //   }
          //   this.filterOptions[key] = data
          // }).catch(_ => {})
        }
      }
    },
    setRules() {
      this.showFormItems.forEach(item => {
        if (item.required) {
          for (let key in this.formData) {
            if (key === item.name) {
              this.formRules[key] = {...this.rule}
            }
          }
        }
      })
    },
    getList() {
      // 由于接口没做当前查询页是否有数据的边界判断 so每一次在此处点击的查询都强制查询第一页数据
      this.$emit('getList', true)
    },
    reset() {
      this.options = []
      this.$refs['filterForm'].resetFields()
      this.$emit('resetForm')
      this.$emit('refreshTabContent')
    },
    handleCascaderChange(val, name) {
      this.options = []
      this.formData[name[1]] = ''
      const currentOption = this.filterOptions[name[0]] || []
      this.options = ((currentOption.find(o => o.val === val) || {}).extra_kv) || []
    },
    getArchiveType() {
      if (this.formItems.findIndex(item => item.type === 'arctype') < 0) return
      commonApi.getArctype().then(res => {
        this.arcOptions = res.data || []
        this.setArchiveType()
      }).catch(_ => {})
    },
    setArchiveType() {
      this.$refs.treeSelect[0].initDefaultSelected()
    },
    handleChange(name) {
      if (name === 'state') {
        this.$forceUpdate()
      }
    }
  }
}
</script>
<style lang="stylus">
.filter-main
  textarea
    height 32px !important
  .input
    width 300px
    position relative
    top -4px
    height 32px
  .select
    .el-select__tags
      top 47%
    .el-input__inner
      height 32px !important
  .group-tag
    margin-right 5px
</style>
