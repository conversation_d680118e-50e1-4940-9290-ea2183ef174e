<template>
  <div class="viewer-box-container">
    <ul :id="viewerID">
      <li v-for="(item, index) in imgArray" :key="index">
        <img :src="item.src" :alt="item.name">
      </li>
    </ul>
  </div>
</template>

<script>
/**
 * @component
 * @assetTitle 图片预览
 * @assetDescription 基于 Viewer.js 的图片预览组件
 * @assetImportName ViewerBox
 * @assetTag 通用组件
 */
import Viewer from 'viewerjs'
import 'viewerjs/dist/viewer.min.css'
import assign from 'lodash-es/assign'

/**
   * Viewer.js
   * JavaScript image viewer.
   * https://github.com/fengyuanchen/viewerjs
*/

export default {
  props: {
    // 图片源
    imgArray: {
      type: [Array, Object],
      default() {
        return []
      }
    },
    /**
     * 预览器的配置项，可扩展，具体参照 viewer.js 文档 https://github.com/fengyuanchen/viewerjs
     * @param options.keyboard {object} 键盘快捷键配置
     * @param options.start_zoom_ratio {number} 组件载入后的缩放比例
     */
    options: {
      type: Object,
      default() {
        return {}
      }
    },
    // 预览器的id
    viewerID: {
      type: String,
      default() {
        return 'ViewBoxImages'
      }
    }
  },
  data() {
    return {
      viewer: null
    }
  },
  watch: {
    imgArray(arr) {
      if (Array.isArray(arr) && arr.length > 0) {
        this.$nextTick(() => {
          this.openViewer()
        })
      }
    }
  },
  mounted() {
    this.$nextTick(_ => {
      document.addEventListener('keydown', this.keydown)
    })
  },
  beforeDestroy() {
    document.removeEventListener('keydown', this.keydown)
  },
  methods: {
    openViewer() {
      const el = document.getElementById(this.viewerID)
      const options = assign({
        hidden: () => {
          this.closeViewer()
        }
      }, this.options)
      if (this.options.start_zoom_ratio) {
        const startZoomRatio = this.options.start_zoom_ratio
        options.viewed = () => {
        this.viewer.zoomTo(startZoomRatio)
      }
      }
      const viewer = new Viewer(el, options)
      viewer.show()
      this.viewer = viewer
    },
    closeViewer() {
      if (this.viewer && this.viewer.destroy) {
        this.viewer.destroy()
        this.viewer = null
        // 预览器关闭事件
        this.$emit('close')
      }
    },
    keydown(e) {
      if (this.viewer && !this.options.keyboard) {
        switch (e.keyCode) {
          case 38:
            this.viewer.move(0, 100)
            break
          case 40:
            this.viewer.move(0, -100)
            break
          case 37:
            this.viewer.prev()
            break
          case 39:
            this.viewer.next()
            break
          case 27:
            this.viewer.hide()
            break
          default: break
        }
      }
    }
  }
}
</script>

<style lang="stylus">
.viewer-box-container {
  position: absolute
  visibility: hidden
}
</style>
