<template>
  <div class="content">
    <el-breadcrumb separator-class="el-icon-arrow-right" class="breadcrumb">
      <el-breadcrumb-item
      v-if="routes&&routes.length > 1"
      v-for="(route, index) in routes"
      :key="route.path"
      :to="{path: index < routes.length -1 ? route.path : $route.fullPath}">
        {{ route.name }}
      </el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>
<script>
export default {
  data() {
    return {
    }
  },

  computed: {
    routes() {
      let array = []
      const useCustomBc = !!((this.$route.meta || {})).custom
      if (useCustomBc) { // use custom breadcrumb
        array = (this.$route.meta || {}).breadcrumb || []
      } else {
        let names = (this.$route.name || '').split('/')
        let paths = this.$route.path.split('/')
        paths.forEach((p, i) => {
          if (i === 0) {
            array.push({
              path: '/',
              name: ''
            })
          } else if (i > 0 && paths[i]) {
            array.push({
              path: array[i - 1].path + (i > 1 ? '/' : '') + paths[i],
              name: names[i] || this.$route.query.breadcrumb || paths[i]
            })
          }
        })
        array.shift()
      }
      return array
    }
  }
}
</script>

<style lang="stylus" scoped>
.content
  .breadcrumb
    background #F4F4F5
    padding 16px 20px 0px
    color var(--text-color)
    height 45px
    box-sizing border-box
    position relative
    margin-bottom 0
</style>
