<template>
  <div
    class="archive-card font-14 flex-lr flex-ac"
    :class="{
      evenRows: Math.floor(index/2) % 2 === 1
    }"
    @click="goDetail"
  >
    <div class="flex-col">
      <img
        :class="!!archive.cover_v2 ? 'archive-card-img__dual' : 'archive-card-img'"
        loading="lazy"
        :src="archive.cover"
      />
      <img
        v-if="archive.cover_v2"
        class="archive-card-img__dual mt-4"
        loading="lazy"
        :src="archive.cover_v2"
      />
    </div>
    <div class="break-all ml-8 flex-1">
      <div class="archive-card-title flex-ac">
        <div
          class="ml-10 mr-10 archive-state"
          :class="archiveStateClass(archive)"
        >{{ archiveState }}</div>
        <el-tooltip
          :disabled="!showTitlePopover"
          :visible-arrow="false"
          effect="light"
          :content="archive.title"
        >
          <div
            class="font-bold font-grey truncated-2"
            style="word-break: break-all"
            ref="cardTitle"
          >
            <ArchiveTitle :text="archive.title" />
          </div>
        </el-tooltip>
        <i class="el-icon-document-copy ml-8 font-18" style="cursor: pointer;" @click.stop="copyToClipboard"></i>
        <div v-if="archive.mission_id" class="border" style="color: var(--red); flex-shrink: 0;">【活动】</div>
      </div>

      <el-popover
        placement="bottom"
        width="800"
        trigger="hover"
        effect="light"
        :disabled="!showContentPopover"
        :visible-arrow="false"
        :open-delay="500"
      >
        <div
          class="archive-card-content ml-8 mt-8 mb-8 truncated-5"
          ref="cardContent"
          slot="reference"
        >{{ archive.content }}</div>
        <div style="height: 400px; overflow: scroll">
          <ArchiveTitle :text="archive.content" />
        </div>
      </el-popover>

      <el-row class="archive-card-foot ml-8 flex-lr">
        <el-col :span="10" @click.native.stop>
          <UserInfo
            class="leading-4"
            :userGroups="archive.user_group"
            :official="archive.official_verify"
            :fans="archive.fans"
          >
            <a
              :href="`//space.bilibili.com/${archive.mid}/#!/index`"
              target="_blank"
              style="color: var(--link-color)"
            >{{ archive.author }}</a>
          </UserInfo>
        </el-col>
        <el-col :span="6">{{ archive._arcTypeV1 }}</el-col>
        <el-col :span="8">{{ archive.ctime }}</el-col>
      </el-row>
    </div>
    <el-checkbox class="checkbox" v-model="checked" @click.native.stop/>
  </div>
</template>

<script>
import { STATES } from '@/utils/constant'
import { COLORS } from '@/components/ArchiveList.vue'
import UserInfo from '@/components/ArchiveList/UserInfo'
import ArchiveTitle from '@/v2/biz-components/archive/ArchiveTitle'
import notify from '@/lib/notify'
import { openNewPage, copyToClipboard } from '@/utils'

export default {
  components: {
    ArchiveTitle,
    UserInfo
  },
  props: {
    archive: {
      type: Object,
      default: () => {}
    },
    multipleSelection: {
      type: Array,
      default: () => []
    },
    review: {
      type: String,
      default: ''
    },
    index: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      showTitlePopover: false,
      showContentPopover: false,
      STATES,
      COLORS
    }
  },
  computed: {
    archiveState() {
      return this.archive.access && this.archive.access > 0 && this.archive.state >= 0
        ? STATES[this.archive.access] || ''
        : STATES[this.archive.state] || ''
    },
    checked: {
      get() {
        return this.multipleSelection.some(archive => this.archive.id === archive.id)
      },
      set() {
        this.$emit('selection-change', this.archive)
      }
    }
  },
  mounted() {
    this.getShowTooltip()
  },
  methods: {
    archiveStateClass(archive) {
      return {
        pass: archive.state === 0,
        'orange-pass': archive.state === 1,
        pengding: archive.state === -1,
        repaired: archive.state === -6,
        transcoding: archive.state === -9,
        distributing: archive.state === -15,
        default: ![0, 1, -1, -6, -9, -15].includes(archive.state)
      }
    },
    getShowTooltip() {
      this.showTitlePopover = this.$refs.cardTitle?.scrollHeight > this.$refs.cardTitle?.clientHeight
      this.showContentPopover = this.$refs.cardContent?.scrollHeight > this.$refs.cardContent?.clientHeight
    },
    goDetail() {
      openNewPage(this.$router, '/audit/tasks/detail/11', {
        business_id: 11,
        oid: `${this.archive.id}`,
        list_type: `${this.$route.query.list_type}`,
        review: `${this.review}`,
        back: `${this.$route.fullPath}`
      })
    },
    copyToClipboard() {
      try {
        copyToClipboard(this.archive.bvid)
        notify.success('稿件ID复制成功')
      } catch (error) {
        notify.error('稿件ID复制失败')
      }
    }
  }
}
</script>

<style lang="stylus" scoped>
.checkbox
  cursor pointer
  width 30px
  height 30px
  align-self stretch
  text-align:right

.truncated-2
  ellipsisMixin(2, 14, 20)
.truncated-5
  ellipsisMixin(5, 14, 20)
.evenRows
  background-color var(--grey-light-4)
.archive-card:hover
  background-color var(--grey-light-3)
.archive-card
  border 1px var(--grey-light-2) solid
  border-radius 3px
  margin-top -1px
  margin-left -1px
  padding 5px
  overflow hidden
  .archive-card-img
    margin-left 10px
    width 170px
    height 110px
    border 5px var(--grey-light-1) solid
  .archive-card-img__dual
    margin-left 10px
    width 160px
    height 90px
    border 5px var(--grey-light-1) solid
  .archive-card-title
    height 40px
  .archive-card-content
    height 100px
    flex-shrink 0
  .archive-card-foot
    height 48px
    align-items center
  .archive-state
    white-space nowrap
    border-radius 7px
    padding 3px
  .pass,
  .orange-pass
    color var(--success-color)
    border 1px var(--success-color) solid
  .distributing,
  .transcoding,
  .pengding,
  .repaired
    color var(--primary-color)
    border 1px var(--primary-color) solid
  .default
    color var(--red)
    border 1px var(--red) solid
</style>
