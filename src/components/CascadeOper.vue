<template>
  <div v-if="oper && oper.with_options">
    <div class="row" v-if="oper.options && oper.options.length">
      <div class="label">一级标签：</div>
      <el-radio-group :value="currentTag && currentTag.id" @input="onChangeTag">
        <el-radio
          v-for="(t1, index) in oper.options"
          :key="index"
          :value="t1.id"
          :label="t1.id"
          class="tag"
        >
          {{ t1.name }}
        </el-radio>
      </el-radio-group>
    </div>

    <div class="row" v-if="currentTag && currentTag.with_options">
      <div class="label">二级标签：</div>
      <el-radio-group
        :value="secondTag && secondTag.id"
        @input="onChangeSecondTag"
      >
        <el-radio
          v-for="(t2, index) in currentTag.options"
          :key="index"
          :value="t2.id"
          :label="t2.id"
          class="tag"
        >
          {{ t2.name }}
        </el-radio>
      </el-radio-group>
    </div>

    <div class="row" v-if="secondTag && secondTag.with_options">
      <div class="label">三级标签：</div>
      <el-radio-group
        :value="thirdTag && thirdTag.id"
        @input="onChangeThirdTag"
      >
        <el-radio
          v-for="(t3, index) in secondTag.options"
          :key="index"
          :value="t3.id"
          :label="t3.id"
          class="tag"
        >
          {{ t3.name }}
        </el-radio>
      </el-radio-group>
    </div>

    <div class="row" v-if="thirdTag && thirdTag.with_options">
      <div>四级标签：</div>
      <el-radio-group
        :value="fourthTag && fourthTag.id"
        @input="onChangeFourthTag"
      >
        <el-radio
          v-for="(t4, index) in thirdTag.options"
          :key="index"
          :value="t4.id"
          :label="t4.id"
          class="tag"
        >
          {{ t4.name }}
        </el-radio>
      </el-radio-group>
    </div>
  </div>
</template>
<script>
/**
 * 针对/x/admin/aegis-gateway/todo/opers 接口，使用内容标签管理配置的标签
 * {id,group,full_name,name,type,up_notify,with_options,options,extra_data}数据类型
 * 展示为按对应tag展示下级tag
 */
export default {
  props: {
    oper: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      currentTag: null,
      secondTag: null,
      thirdTag: null,
      fourthTag: null
    }
  },
  methods: {
    onChangeTag(id) {
      this.currentTag = (this.oper?.options || []).find((e) => e.id === id)
      this.secondTag = null
    },
    onChangeSecondTag(id) {
      this.secondTag = (this.currentTag?.options || []).find((e) => e.id === id)
      this.thirdTag = null
    },
    onChangeThirdTag(id) {
      this.thirdTag = (this.secondTag?.options || []).find((e) => e.id === id)
      this.fourthTag = null
    },
    onChangeFourthTag(id) {
      this.fourthTag = (this.thirdTag?.options || []).find((e) => e.id === id)
    },
    getResult() {
      if (this.fourthTag) {
        return {
          [this.thirdTag.id]: [this.fourthTag.id]
        }
      }
      if (this.thirdTag) {
        return {
          [this.secondTag.id]: [this.thirdTag.id]
        }
      }
      if (this.secondTag) {
        return {
          [this.currentTag.id]: [this.secondTag.id]
        }
      }
      if (this.currentTag) {
        return {
          [this.currentTag.id]: []
        }
      }
    }
  }
}
</script>
<style lang="stylus" scoped>
.row
    display flex
    flex-direction row
    align-items flex-start
    margin-bottom 40px
.label
    min-width 80px

.tag
    margin-bottom 8px
</style>
