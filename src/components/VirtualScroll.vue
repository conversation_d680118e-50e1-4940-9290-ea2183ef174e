<template>
  <div class="virtual-scroll" :style="containerStyle">
    <div class="table-div" ref="tableSubDiv" @scroll="handleScroll">
      <div class="table-header">
        <slot name="table-header"></slot>
      </div>
      <div class="table-body" id="table-body">
        <div
          class="table-padding"
          :style="{
            height: `${offsetTop}px`
          }"
        ></div>
        <div class="table-row" v-for="(item, index) in renderList" :key="item.id" :class="tableRowClassName(item, item.idx)">
          <slot
            v-if="listData"
            name="table-body"
            v-bind:item="item"
            v-bind:idx="item.idx"
            v-bind:isBottom="index + 1 === renderList.length"
          ></slot>
        </div>
        <div
          class="table-padding"
          :style="{
            height: `${offsetBot}px`
          }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'virtual-scroll',
  data() {
    return {
      // 上块
      offsetTop: 0,
      // 下块
      offsetBot: 0,
      // 视图start点
      viewportBegin: 0,
      // 视图结束点（高度）
      viewportEnd: this.height,
      // 需要实际渲染的数组
      renderList: [],
      //
      transformedData: []
    }
  },
  props: {
    listData: {
      type: Array,
      default() {
        return []
      }
    },
    height: {
      type: Number,
      default: 400
    },
    fixedBlockHeight: {
      type: Number,
      default: 80
    },
    activeClipIndex: {
       type: Number
    },
    highlight: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  watch: {
    listData: {
      handler: function(newVal, oldVal) {
        // code blow used to update view when data changed
        if (oldVal) {
          this.$refs.tableSubDiv.scrollTop = 0
          this.handleScroll()
        }
      },
      immediate: true // when not in page mode, initailize data here
    },
    fixedBlockHeight() {
      // update view when fixedBlockHeight changed
      this.handleScroll()
    }
  },
  computed: {
    containerStyle() {
      return {
        height: `${this.height}px`,
        'overflow-y': 'scroll'
      }
    }
  },
  mounted() {
    this.updatetableSubDiv(0)
  },
  methods: {
    // 滚动事件
    handleScroll() {
      const scrollTop = this.$refs.tableSubDiv.scrollTop
      // use requestAnimationFrame to ensure smooth scrolling visual effects
      // 保证帧数
      window.requestAnimationFrame(() => {
        this.updatetableSubDiv(scrollTop)
      })
    },
    redraw() {
      const scrollTop = this.$refs.tableSubDiv.scrollTop
      this.updatetableSubDiv(scrollTop)
    },
    fixedBlockHeightLowerBound(s, fixedBlockHeight) {
        // used to compute the lower bound in-viewport index for data array
        // when in fixed height mode
        const sAdjusted = s
        const computedStartIndex = ~~(sAdjusted / fixedBlockHeight)
        return computedStartIndex >= 0 ? computedStartIndex : 0
    },
    fixedBlockHeightUpperBound(e, fixedBlockHeight) {
        // used to compute the upper bound in-viewport index for data array
        // when in fixed height mode
        const eAdjusted = e
        const compuedEndIndex = Math.ceil(eAdjusted / fixedBlockHeight)
        return compuedEndIndex <= this.listData.length ? compuedEndIndex : this.listData.length
    },
    findBlocksInViewport(s, e, heightArr, blockArr) {
      if (s < e) {
        const lo = this.fixedBlockHeightLowerBound(s, this.fixedBlockHeight)
        const hi = this.fixedBlockHeightUpperBound(e, this.fixedBlockHeight)

        this.offsetTop = lo >= 0 ? lo * this.fixedBlockHeight : 0
        // set bot spacer
        this.offsetBot = hi >= 0 ? (blockArr.length - hi) * this.fixedBlockHeight : 0
        // return the sliced the data array
        return blockArr.slice(lo, hi)
      } else {
        this.offsetTop = 0
        this.offsetBot = 0
        return []
      }
    },
    updatetableSubDiv(scrollTop) {
      // compute the viewport start position and end position based on the scrollTop value
      const viewportHeight = this.height
      this.viewportBegin = scrollTop
      this.viewportEnd = scrollTop + viewportHeight
      // 进行替换，不进行直接赋值
      this.renderList = this.findBlocksInViewport(
        this.viewportBegin,
        this.viewportEnd,
        this.transformedData,
        this.listData
      )
    },
    tableRowClassName(row, idx) {
      const highLightFlag = !!(this.highlight && this.highlight[row.cid])
      const activeFlag = idx === this.activeClipIndex
      return `${highLightFlag ? 'table-row__highlight' : ''} ${activeFlag ? 'table-row__active' : ''}`
    }
  }
}
</script>
<style lang="stylus">
// 一些滚动样式
.virtual-scroll{
  display flex
  width 100%
  position relative
  .table-div{
    overflow auto
    .table-header{
      display flex
      width 100%
      position sticky
      top 0
      z-index 10
    }
    .table-body{
      display flex
      width 100%
      flex-direction column
      .table-padding{
        width 100%
      }
      .table-cell{
        height 80px
      }
    }
  }
}
</style>
