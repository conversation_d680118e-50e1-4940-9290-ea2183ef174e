<template>
<div class="wrapper">
  <el-select v-model="policy_id" placeholder="请选择" size="small" :disabled="disabled">
    <el-option
      v-for="(policy, index) in policies"
      :key="index"
      :value="policy.id"
      :label="policy.name">
    </el-option>
  </el-select>
  <table class="table" v-if="groupPolicies && groupPolicies.length">
    <thead>
      <tr>
        <th>地区</th>
        <th width="300px">播放权限</th>
        <th width="80px">缓存权限</th>
      </tr>
    </thead>
    <tbody>
      <tr v-for="(item, index) in groupPolicies" :key="index">
        <td>
          <span class="city" :title="item._name">{{item._name}}</span>
        </td>
        <td>
          <el-radio-group v-model="item.play_auth">
            <el-radio v-for="(value, key) in playAuthOptions" :label="parseInt(key, 10)" :key="key" :disabled="true">{{value}}</el-radio>
          </el-radio-group>
        </td>
        <td>
          <el-checkbox v-model="item.down_auth" :true-label="0" :false-label="1" :disabled="true">禁止缓存</el-checkbox>
        </td>
      </tr>
    </tbody>
  </table>
    <p v-else>无策略数据</p>
</div>
</template>

<script>
import { getAreaNameStr } from './areaData'

export default {
  components: { },
  props: {
    value: {
      type: Number,
      required: true,
      default: 0
    },
    policies: {
      type: Array,
      required: true,
      default() {
        return []
      }
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      playAuthOptions: {
        2: '无限制',
        1: '禁止观看',
        3: '正式会员可见',
        4: '付费会员可见'
      }
    }
  },
  computed: {
    policy_id: {
      get() {
        return this.value || ''
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    groupPolicies() {
      const selected = this.policies.find(
        p => parseInt(p.id, 10) === parseInt(this.policy_id, 10)
      )
      if (!selected) return []
      return (selected.items || []).map((item) => {
        item._name = getAreaNameStr(item.area_id.split(','))
        return item
      })
    }
  },
  methods: {
  }
}
</script>

<style lang="stylus" scoped>
.table
  width 710px
  margin-top 5px
  border 1px solid var(--border-color-light-2)
  tr
    border-bottom 1px solid var(--border-color-light-2)
  td,th
    border-left 1px solid var(--border-color-light-2)
    text-align center
    letter-spacing 0
    padding 5px
    font-size 12px
    white-space nowrap
  thead th
    background-color var(--table-th-bg-color)
    line-height 25px
.city
  display block
  width 279px
  padding .5em .75em
  overflow hidden
  color var(--text-color-dark-1)
  border-radius 2px
  line-height 1.4em
.empty
  text-align center
.el-radio__label, .el-checkbox__label
  font-size 12px
</style>
