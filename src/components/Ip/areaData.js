import areaLevelArray from './area-level.json'

export { areaLevelArray }
const areaMap = {}
let areaMapInitialized = false

function genMap(arr) {
  arr.forEach((item) => {
    if (item.id) {
      item.id = parseInt(item.id, 10)
      if (item.zone_id) {
        item.zone_id = parseInt(item.zone_id, 10)
      }
      areaMap[item.id] = item
    }
    if (item.children) {
      genMap(item.children)
    }
  })
}

export function getAreaNameStr(areaIds) {
  const map = areaMap
  if (!areaMapInitialized) {
    genMap(areaLevelArray)
    areaMapInitialized = true
  }

  return areaIds.map((id) => {
    if (!map[id]) {
      return id
    }
    return map[id].name
  }).join(',')
}
