<template>
  <div
    :class="{
      [$style.warningYellowTip]: true,
      [$style.normal]: !isHover,
      [$style.hover]: isHover
    }"
    @mouseover="isHover = true"
    @mouseleave="isHover = false"
  >
    <slot></slot>
  </div>
</template>
<script>
export default {
  name: 'WarningYellowTip',
  props: {
  },
  computed: {},
  components: {
  },
  data() {
    return {
      isHover: false
    }
  },
  methods: {
  }
}
</script>
<style lang="stylus" module>
.warningYellowTip
  margin-bottom 10px
  color var(--text-color)
  box-shadow none
  padding 12px
  position relative
  background-color var(--warning-bg-color)
  border-color var(--warning-border-color)
  font-size 14px !important
  line-height 20px !important
  box-sizing border-box
  &.normal
    height 52px
    word-break break-all
    overflow hidden
    text-overflow ellipsis
    display -webkit-box
    -webkit-line-clamp 2
    -webkit-box-orient vertical
  &.hover
    word-break break-all
    min-height 52px
  div:nth-last-child(1)
    margin-bottom 0
</style>
