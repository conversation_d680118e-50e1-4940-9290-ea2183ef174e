<template>
  <el-row type="flex">
    <el-checkbox
      style="margin-right: 30px"
      :value="j"
      :disabled="noPermission"
      :true-label="1"
      :false-label="0"
      @input="(val) => $emit('update:j', val)"
    >
      跳转
    </el-checkbox>
    <el-input
      style="width: 300px; margin-right: 30px"
      placeholder="请输入跳转地址"
      :value="redirectUrl"
      :disabled="noPermission"
      @input="(val) => $emit('update:redirectUrl', val)"
    ></el-input>
    <el-button type="primary" :disabled="noPermission" @click="submitRedirect">
      保存
    </el-button>
  </el-row>
</template>
<script>
import { archiveApi } from '@/api'
import { mapState } from 'vuex'
import notify from '@/lib/notify'

export default {
  name: 'OgvRedirect',
  props: {
    redirect: {
      type: Object,
      default() {
        return {}
      }
    },
    j: {
      type: Number,
      default: 0
    },
    redirectUrl: {
      type: String,
      default: ''
    },
    aid: {
      type: [Number, String],
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      type: 2 // 1。盗版设置。  新版本, 2.  普通ogv跳转。 老版本
    }
  },
  computed: {
    ...mapState({
      perms: (state) => state.user.perms
    }),
    noPermission() {
      return this.disabled || !this.perms.OGV_JUMP
    }
  },
  methods: {
    submitRedirect() {
      if (this.j === 1 && this.redirectUrl.trim() === '') {
        notify.error('请填写跳转地址')
        return
      } else if (this.j === 1 && !/http(s?):\/\//.test(this.redirectUrl)) {
        notify.error('跳转链接请以http，https开头')
        return
      }
      archiveApi
        .submitRedirectInfo({
          aid: this.aid,
          type: this.type,
          redirect_url: this.redirectUrl,
          is_jump: this.j
        })
        .then((_) => {
          notify.success('保存成功')
          if (this.j === 0) {
            this.$emit('update:redirectUrl', '')
          }
        })
        .catch((_) => {})
    }
  }
}
</script>
