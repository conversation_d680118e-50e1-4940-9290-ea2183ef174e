<template>
  <div class="black-list">
    <div>
      <el-form size="small" label-width="100px" :disabled="disabled">
        <el-form-item label="稿件id">
          <el-row type="flex">
            <el-select
              v-model="listForm.type"
              class="type-select"
              placeholder="请选择"
              style="margin-right: 30px;">
              <el-option
                :value="1"
                label="aid">
              </el-option>
              <el-option
                :value="2"
                label="season_id">
              </el-option>
            </el-select>
            <el-input
              :placeholder="`输入${TYPE_MAP[listForm.type]}，多个用英文逗号隔开`"
              v-model="listForm.oids">
            </el-input>
          </el-row>
        </el-form-item>
        <el-form-item label="备注">
          <el-row type="flex">
            <el-input
              style="margin-right: 50px;"
              placeholder="输入备注"
              v-model="listForm.remark">
            </el-input>
            <el-button
              type="primary"
              style="margin-right: 20px;"
              @click="addItem">
              添加
            </el-button>
            <el-button
              @click="clearForm">
              清空
            </el-button>
            </el-row>
        </el-form-item>
        <el-form-item label="当前黑名单">
          <el-table
            :border="true"
            :data="list"
            style="width: 100%">
            <el-table-column
              v-for="(row, idx) in options" :key="idx"
              empty-text="没有数据"
              :label="row.text"
              :style="{
                width: row.width
              }"
              :class="row.class || ''">
              <template v-slot:default="scope">
                <template v-if="row.href">
                  <a target="_blank" class="a" :href="scope.row[row.href]">
                    {{ row.template ? row.template(scope.row[row.field]) : scope.row[row.field]}}
                  </a>
                </template>
                <span v-else-if="row.getValue">{{ row.getValue(scope.row) }}</span>
                <el-button
                  v-else-if="row.isDeleteBtn"
                  :disabled="disabled"
                  type="danger"
                  size="small"
                  @click="deleteItem(scope.row)"
                >
                  删除
                </el-button>
                <template v-else>
                  {{ row.template ? row.template(scope.row[row.field]) : scope.row[row.field]}}
                </template>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import { TYPE_MAP } from '@/utils/constant'
import { detailApi } from '@/api/index'
import notify from '@/lib/notify'

export default {
  name: 'black-list',
  data() {
    return {
      TYPE_MAP,
      listForm: {
        aid: '',
        type: 1,
        oids: '',
        remark: ''
      },

      list: [],
      options: []
    }
  },
  props: {
    aid: {
      type: [String, Number],
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  components: {},
  computed: {
    ...mapState({
      perms: state => state.user.perms
    })
  },
  watch: {
    aid: {
      handler(newAid) {
        this.listForm.aid = newAid
        this.getList()
      },
      immediate: true
    }
  },
  methods: {
    getList() {
      detailApi.getBlackList({
        aid: this.aid
      }).then((res) => {
        this.list = res.data || []
      }).catch(_ => {})
    },
    addItem() {
      const form = { ...this.listForm }
      if (!form.oids || !form.remark) {
        notify.error('请填写id和备注')
        return
      }
      detailApi.addBlackList(form).then(() => {
        this.getList()
        this.clearForm()
      }).catch(_ => {})
    },
    deleteItem(item) {
      this.$confirm(`确认删除 ${item.arc_title}?`)
        .then(() => {
          detailApi.delBlackList({
            id: item._id
          }).then(() => {
            notify.success('删除成功')
            this.getList()
          })
        })
        .catch(() => {})
    },
    clearForm() {
      this.listForm = {
        ...this.listForm,
        oids: '',
        remark: ''
      }
    }
  },
  created() {
    this.options = [
      {
        text: '类型',
        getValue: (row) => TYPE_MAP[row._type] || '-'
      },
      {
        field: '_oid',
        text: 'oid'
      },
      {
        field: 'arc_title',
        text: '标题'
      }
    ]
    if (this.perms.ARC_UNRECOM_EDIT) {
      this.options.push({
        text: '操作',
        isDeleteBtn: true
      })
    }
  }
}
</script>
<style lang="stylus" scoped>
.black-list
  margin auto
  .type-select
    max-width 100px
</style>
