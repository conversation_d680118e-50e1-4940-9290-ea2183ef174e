<template>
  <div class="charge">
    <el-select 
      v-model="accessForm.access" 
      placeholder="请选择" 
      class="input"
      style="margin-right: 30px;">
      <el-option
        v-for="(access) in ACCESS" 
        :key="access" 
        :value="access" 
        :label="ACCESS_MAP[access]">
      </el-option>
    </el-select>
    <el-button 
      type="primary"
      @click="saveAccess">
      保存
    </el-button>
  </div>
</template>

<script>
import { ACCESS, ACCESS_MAP } from '@/utils/constant.js'
import notify from '@/lib/notify'
import { detailApi } from '@/api/index'

export default {
  props: {
    access: {
      required: true
    },
    aid: {
      type: String / Number,
      required: true
    }
  },
  data() {
    return {
      accessForm: {
        aid: this.aid,
        access: this.access
      },
      ACCESS,
      ACCESS_MAP
    }
  },
  watch: {
    access(newVal) {
      this.accessForm.access = newVal
    }
  },
  methods: {
    saveAccess() {
      const form = { ...this.accessForm }
      detailApi.saveAccess(form).then(_ => {
        notify.success('修改阅读权限成功')
      }).catch(_ => {})
    }
  }
}
</script>

<style lang="stylus">
.charge
  font-size 14px
  .el-checkbox__label 
    font-size 14px
  .el-checkbox__input.is-checked+.el-checkbox__label 
    color unset
</style>
