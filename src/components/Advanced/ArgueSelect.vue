<template>
  <div>
    <el-select
      v-model="argueRole"
      placeholder="角色"
      size="small"
      style="margin-right: 5px"
      @change="argueRoleChange"
      :disabled="disabled"
    >
      <el-option
        v-for="role in argueData.roles"
        :key="role.name"
        :label="role.name"
        :value="role.rid"
      >
      </el-option>
    </el-select>
    <AgTooltip :disabled="toolTipdisabled" placement="top">
      <el-select
        v-model="currentArgueTag"
        placeholder="选择分类"
        no-data-text="分类为空"
        size="small"
        style="margin-right: 10px"
        @visible-change="(val) => (dropdownVisible = val)"
        :disabled="disabled"
      >
        <el-option
          v-for="argue in argueTagList"
          :key="argue.tag_id"
          :label="argue.name"
          :value="argue.tag_id"
        >
        </el-option>
      </el-select>
      <div slot="content">
        {{ tooltipContent }}
      </div>
    </AgTooltip>
  </div>
</template>
<script>
import AgTooltip from '@/components/element-update/Tooltip.vue'
export default {
  name: 'ArgueSelect',
  data() {
    return {
      argueRole: '',
      dropdownVisible: false,
      toolTipdisabled: false
    }
  },
  computed: {
    currentArgueTag: {
      get: function () {
        return this.value
      },
      set: function (newValue) {
        this.$emit('input', newValue)
      }
    },
    argueTagList() {
      if (!this.argueRole) return []
      return this.argueData.tagRoleMap[this.argueRole]
    },
    tooltipContent() {
      this.toolTipdisabled = true
      if (this.currentArgueTag) {
        const idx = this.argueTagList.findIndex((item) => item.tag_id === this.currentArgueTag)
        if (idx > -1) {
          if (this.argueTagList[idx].description && !this.dropdownVisible) this.toolTipdisabled = false
          return this.argueTagList[idx].description || ''
        }
      } else {
        return ''
      }
    }
  },
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    argueData: {
      type: Object,
      default() {
        return {}
      }
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  components: {
    AgTooltip
  },
  methods: {
    argueRoleChange(role) {
      this.$emit('change-role', role)
      if (role) {
        this.currentArgueTag = ''
      }
    }
  }

}
</script>
