<template>
  <div class="argue-tab">
    <el-table
      :data="argueList">
      <el-table-column 
        v-for="(row, idx) in options" :key="idx"
        empty-text="没有数据"
        :label="row.text"
        :style="{
          width: row.width
        }"
        :class="row.class || ''">
        <template v-slot:default="scope">
          <template v-if="row.href">
            <a target="_blank" class="a" :href="scope.row[row.href]">
              {{ row.template ? row.template(scope.row[row.field]) : scope.row[row.field]}}
            </a>
          </template>
          <template v-else-if="row.render">
            <TableCellRender 
              :row="scope.row" 
              :render="row.render" 
              :index="scope.$index">
            </TableCellRender>
          </template>
          <template v-else>
            {{ row.template ? row.template(scope.row[row.field]) : scope.row[row.field]}}
          </template>
        </template>
      </el-table-column>
    </el-table>
    <!-- 下拉项 -->
    <div class="archive-argue-add" v-if="hasPermission">
      操作：
      <ArgueSelect
        v-model="currentArgueTag"
        :argueData="argueData"
        :disabled="disabled"
        @change-role="(val) => this.argueRole = val"
      >
      </ArgueSelect>
      <span>备注：</span>
      <el-input
        v-model="note"
        style="width: 200px;margin-right: 20px;"
        size="small"
        :disabled="disabled"
      >
      </el-input>
      <el-button type="primary" size="small"  @click="addArgue" :disabled="disabled">添加</el-button>
    </div>
    <div class="button-container" v-show="hasPermission">
      <el-button size="small" @click="resetAll" :disabled="disabled">重置</el-button>
      <el-button type="primary" size="small" @click="doSubmit" :disabled="isSubmiting || disabled">保存</el-button>
    </div>
  </div>
</template>
<script>
import { cloneDeep } from 'lodash-es'
import { mapState } from 'vuex'
import { argueApi } from '@/api/index'
import TableCellRender from '@/components/package/TableCellRender'
import AgFormItem from '@/components/element-update/FormItem'
import notify from '@/lib/notify'
import { washArgueData } from '@/utils'
import AgTooltip from '@/components/element-update/Tooltip'
import ArgueSelect from '@/components/Advanced/ArgueSelect.vue'

export default {
  name: 'ArgueTab',
  inheritAttrs: false,
  data() {
    return {
      note: '',
      argueRole: '',
      currentArgueTag: '',
      dropdownVisible: false,
      argueData: {},
      tagId: '',
      isLoading: false,
      isSubmiting: false,
      isShowModal: false,
      argueList: [],
      options: [
        {
          field: 'role_id',
          text: '角色',
          render: (h, params) => {
            const { row } = params
            return h('span', {
              domProps: {
                innerText: `${this.argueData.rolesMap && this.argueData.rolesMap[row.role_id]}`
              }
            })
          }
        },
        {
          field: 'tag_id',
          text: '争议标识',
          render: (h, params) => {
            const { row } = params
            return h('span', {
              domProps: {
                innerText: `${this.argueData.tagMap && this.argueData.tagMap[row.tag_id] && this.argueData.tagMap[row.tag_id].name}`
              }
            })
          }
        },
        {
          field: 'note',
          text: '备注'
        },
        {
          text: '操作',
          render: (h, params) => {
            const { index, row } = params
            return h('el-button', {
              props: {
                type: 'danger',
                size: 'small',
                disabled: this.disabled
              },
              on: {
                click: () => this.deleteArgue(row, index)
              },
              domProps: {
                innerHTML: '删除'
              }
            })
          }
        }
      ],
      resetArgueList: []
    }
  },
  components: { 
    TableCellRender,
    AgFormItem,
    AgTooltip,
    ArgueSelect
  },
  props: {
    aid: {
      type: [String, Number],
      required: true
    },
    stat: {
      type: Object,
      default() {
        return {

        }
      }
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapState({
      perms: state => state.user.perms
    }),
    hasPermission() {
      return this.perms && this.perms.ARC_ARGUMENT_EDIT
    }
  },
  methods: {
    initData() {
      this.getArgumentDetail()
    },
    getArgumentDetail() {
      this.isLoading = true
      argueApi.getArgumentDetail({
        aid: this.aid
      }).then(res => {
        const { argueData } = washArgueData(res.data)
        this.argueData = argueData
        const {
          roleId,
          initTagId: tagId,
          note = ''
        } = argueData
        // 有数据才展示
        if (tagId) {
          this.argueList = [{
            role_id: roleId,
            tag_id: tagId,
            note: note
          }]
        }
        this.resetArgueList = cloneDeep(this.argueList)
      }).catch(_ => {
      }).finally(() => {
        this.isLoading = false
      })
    },
    // 重置一切
    resetAll() {
      this.argueList = cloneDeep(this.resetArgueList)
    },
    addArgue() {
      if (this.argueList.length > 0) return notify.error('争议标识只能有一个，请先删除')
      if (this.note.trim() === '') return notify.error('备注必填')
      if (this.currentArgueTag === '') return notify.error('请选择争议tag')
      this.argueList = [{
        role_id: this.argueRole,
        tag_id: this.currentArgueTag,
        note: this.note
      }]
      this.note = ''
    },
    deleteArgue(item, index) {
      this.$delete(this.argueList, index)
    },
    doSubmit() {
      const { argueList, aid } = this
      const {
        note = '',
        tag_id: argumentTag = 0
      } = argueList[0] || {}
      argueApi.editArgument({
        aid,
        argument_tag: argumentTag,
        note
      }).then(() => {
        notify.success('编辑成功~')
        this.getArgumentDetail()
      }).catch(_ => {}).finally(() => {
        this.isSubmiting = false
      })
    }
  },
  created() {
    this.initData()
  }
}
</script>
<style lang="stylus" scoped>
.argue-tab
  .cursor
    cursor pointer
  .archive-argue
    display inline-block
  .archive-argue-add
    margin-top 20px
    display flex
    align-items center
  .button-container
    text-align center
    margin-top 20px
  .sort-button
    display flex
    flex-direction column
    min-height 30px
    justify-content space-between
</style>
