<template>
  <el-row type="flex">
    <el-checkbox
      v-model="redirect.is_jump"
      style="margin-right: 30px"
      :true-label="1"
      :false-label="0"
      :disabled="noPermission"
    >
      跳转
    </el-checkbox>
    <el-input
      v-model="redirect.redirect_url"
      style="width: 300px; margin-right: 30px"
      placeholder="请输入跳转地址"
      :disabled="noPermission"
    ></el-input>
    <span style="margin-right: 30px">限制跳转地区</span>
    <el-select
      v-model="redirect.policy_id"
      placeholder="请选择"
      size="small"
      style="margin-right: 30px"
      :disabled="noPermission"
    >
      <el-option
        v-for="(policy, index) in redirectOptions"
        :key="index"
        :value="policy.id"
        :label="policy.name"
      ></el-option>
    </el-select>
    <el-button type="primary" :disabled="noPermission" @click="submitRedirect">
      保存
    </el-button>
  </el-row>
</template>
<script>
import { archiveApi } from '@/api'
import { mapState } from 'vuex'
import notify from '@/lib/notify'

export default {
  name: 'PiracyRedirect',
  props: {
    aid: {
      type: [Number, String],
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      type: 1, // 1。盗版设置。  新版本, 2.  普通ogv跳转。 老版本,
      redirect: {
        is_jump: 0,
        policy_id: '',
        redirect_url: ''
      },
      redirectOptions: []
    }
  },
  computed: {
    ...mapState({
      perms: (state) => state.user.perms
    }),
    noPermission() {
      return this.disabled || !this.perms.OGV_JUMP
    }
  },
  watch: {
    aid: {
      handler: function (nextVal, oldVal) {
        if (nextVal !== oldVal && nextVal) this.getRedirectInfo(nextVal)
      },
      immediate: true
    }
  },
  methods: {
    submitRedirect() {
      if (
        this.redirect.is_jump === 1 &&
        this.redirect.redirect_url.trim() === ''
      ) {
        notify.error('请填写跳转地址')
        return
      } else if (
        this.redirect.is_jump === 1 &&
        !/http(s?):\/\//.test(this.redirect.redirect_url)
      ) {
        notify.error('跳转链接请以http，https开头')
        return
      } else if (this.redirect.is_jump === 1 && !this.redirect.policy_id) {
        notify.error('请选择限制跳转地区')
        return
      }
      archiveApi
        .submitRedirectInfo({
          aid: this.aid,
          type: this.type,
          redirect_url:
            this.redirect.is_jump === 0 ? '' : this.redirect.redirect_url,
          is_jump: this.redirect.is_jump,
          policy_id: this.redirect.is_jump === 0 ? 0 : this.redirect.policy_id
        })
        .then((res) => {
          notify.success('保存成功')
          this.getRedirectInfo(this.aid)
        })
        .catch((_) => {})
    },
    getRedirectInfo(aid) {
      archiveApi
        .getRedirectInfo({
          aid
        })
        .then((res) => {
          const { redirect_target: redirect_url = '', policy_id = '' } =
            res.data
          this.redirect = {
            is_jump: redirect_url ? 1 : 0,
            redirect_url,
            policy_id
          }
        })
        .catch((_) => {})
    },
    getRedirectOptions() {
      archiveApi
        .getPolicyGroups({
          type: 5,
          state: 1
        })
        .then((res) => {
          const { items = [] } = res.data
          this.redirectOptions = items.map((item) => {
            return {
              id: item.id,
              name: item.name
            }
          })
        })
        .catch((_) => {})
    }
  },
  mounted() {
    this.getRedirectOptions()
  }
}
</script>
