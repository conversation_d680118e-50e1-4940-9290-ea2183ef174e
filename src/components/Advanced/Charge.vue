<template>
  <div class="charge">
    <el-checkbox 
      v-model="chargeForm.stat" 
      :true-label="2" 
      :false-label="0" 
      style="margin-right: 30px;">
      本投稿屏蔽充电面板
    </el-checkbox>
    <el-select 
      v-model="selectedReason" 
      placeholder="请选择" 
      style="margin-right: 30px;">
      <el-option
        value=""
        label="选择操作理由">
      </el-option>
      <el-option
        v-for="(item, index) in reasons" 
        :key="item.id" 
        :value="item.id" 
        :label="item.reason">
      </el-option>
    </el-select>
    <span>用户操作：<span>{{ chargeState === 1 ? '已屏蔽' : '显示中' }}</span></span>
  </div>
</template>

<script>
import { archiveApi } from '@/api/index'

export default {
  props: {
    aid: {
      type: String / Number,
      required: true
    },
    chargeState: {
      type: Number,
      required: true,
      default: 0
    }
  },
  data() {
    return {
      selectedReason: '',
      chargeForm: {
        av: this.aid,
        stat: this.chargeState === 2 ? 2 : 0,
        reason: '',
        reason_id: ''
      },
      reasons: []
    }
  },
  watch: {
    chargeState(newVal) {
      this.chargeForm = {
        av: this.aid,
        stat: newVal === 2 ? 2 : 0,
        reason: '',
        reason_id: ''
      }
    }
  },
  created() {
    this.getReason()
  },
  methods: {
    getReason() {
      archiveApi.getChargeReason({
        type: '2', 
        parent: '74'
      }).then((res) => {
        this.reasons = res.data
        this.chargeForm.reason = ''
      })
      .catch(_ => {})
    }
  }
}
</script>

<style lang="stylus">
.charge
  font-size 14px
  .el-checkbox__label 
    font-size 14px
  .el-checkbox__input.is-checked+.el-checkbox__label 
    color unset
</style>
