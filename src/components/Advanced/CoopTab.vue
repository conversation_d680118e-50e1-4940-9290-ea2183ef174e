<template>
  <div class="coop-tab">
    <el-table
      :data="staffList">
      <el-table-column
        v-for="(row, idx) in options" :key="idx"
        empty-text="没有数据"
        :label="row.text"
        :style="{
          width: row.width
        }"
        :class="row.class || ''">
        <template v-slot:default="scope">
          <template v-if="row.href">
            <a target="_blank" class="a" :href="scope.row[row.href]">
              {{ row.template ? row.template(scope.row[row.field]) : scope.row[row.field]}}
            </a>
          </template>
          <template v-else-if="row.isSortBtn">
            <span v-if="disabled"></span>
            <div v-else-if="hasPermission" class="sort-button">
              <i
                v-if="idx > 0"
                class="el-icon-arrow-up cursor"
                style="width: 14px;"
                @click="() => changeIndex(scope.$index, true)"
              />
              <i
                v-if="idx < staffList.length - 1"
                class="el-icon-arrow-down cursor"
                style="width: 14px;"
                @click="() => changeIndex(scope.$index)"
              />
            </div>
            <template v-else>
              <span>无权限</span>
            </template>
          </template>
          <template v-else-if="row.isDeleteBtn">
            <el-button
              @click="() => showDeleteStaff(scope.row, scope.$index)"
              type="danger"
              size="small"
              :disabled="disabled"
            >删除</el-button>
          </template>
          <template v-else-if="row.getValue">
            <span>{{ row.getValue(scope.row, scope.$index) }}</span>
          </template>
          <template v-else>
            {{ row.template ? row.template(scope.row[row.field]) : scope.row[row.field]}}
          </template>
        </template>
      </el-table-column>
    </el-table>
    <!-- 下拉项 -->
    <div class="archive-staff-add" v-show="hasPermission">
      <el-select
        v-model="addForm.businessStyle"
        class="staff-add-item staff-add-select"
        size="small"
        placeholder="请选择属性"
        :disabled="disabled"
      >
        <el-option
          v-for="type in STAFF_BUSINESS_STYLE"
          :key="type.id"
          :label="type.name"
          :value="type.id">
        </el-option>
      </el-select>
      <el-select
        v-model="addForm.typeName"
        class="staff-add-item staff-add-select"
        size="small"
        placeholder="请选择职能"
        :disabled="disabled"
      >
        <el-option
          v-for="type in staffTypeList"
          :key="type.id"
          :label="type.name"
          :value="type.name">
        </el-option>
        <el-option label="其他" value="">其他</el-option>
      </el-select>
      <el-input
        v-show="addForm.typeName === ''"
        v-model="addForm.customName"
        size="small"
        placeholder="请输入自定义文案"
        class="staff-add-item staff-add-custom"
        :disabled="disabled"
      >
      </el-input>
      <el-input
        v-model="addForm.mids"
        size="small"
        placeholder="用户mid,多个英文逗号隔开"
        class="staff-add-item staff-add-mids"
        :disabled="disabled"
      >
      </el-input>
      <el-button type="primary" size="small"  @click="addStaff" :disabled="disabled">添加</el-button>
    </div>
    <div class="button-container" v-show="hasPermission">
      <el-button size="small" @click="resetAll" :disabled="disabled">重置</el-button>
      <el-button type="primary" size="small" @click="doSubmit" :disabled="isSubmiting || disabled">保存</el-button>
    </div>
    <!-- 删除理由弹窗 -->
    <el-dialog
      title="删除"
      :append-to-body="true"
      :visible.sync="isShowModal">
      <el-form
        size="small"
        label-width="100px"
        ref="deleteDialog"
        :model="deleteReason"
        :rules="rules">
        <el-form-item label="理由模板">
          <el-select
            v-model="deleteReason.reason_id"
            size="small"
            placeholder="请选择删除理由"
            @change="handleChange">
            <el-option
              v-for="type in REASONS"
              :key="type.id"
              :label="type.name"
              :value="type.name">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="删除理由" required prop="reason">
          <el-input
            v-model="deleteReason.reason">
          </el-input>
        </el-form-item>
        <el-form-item label="消息通知">
          <el-radio-group v-model="deleteReason.notify">
            <el-radio :label="1">通知</el-radio>
            <el-radio :label="0">不通知</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-button size="small" @click="closeModal">取消</el-button>
        <el-button type="primary" size="small" @click="confirmDelete">确认</el-button>
      </el-form>
    </el-dialog >
  </div>
</template>
<script>
import { get, cloneDeep } from 'lodash-es'
import { mapState } from 'vuex'
import { businessApi, detailApi } from '@/api/index'
import notify from '@/lib/notify'
import { STAFF_BUSINESS_STYLE_MAP, STAFF_BUSINESS_STYLE } from '@/utils/constant'

const STAFF_BID = 15
const REASONS = [
  {
    id: 1,
    name: '携带商业信息违反《联合投稿创作公约》'
  },
  {
    id: 2,
    name: '违反《联合投稿创作公约》中描述的发布时间约定'
  },
  {
    id: 3,
    name: '携带与该稿件无关人员'
  },
  {
    id: 4,
    name: '侵犯其他第三方权益'
  },
  {
    id: 5,
    name: 'STAFF参与创作程度不足'
  },
  {
    id: 6,
    name: '素材提供方不适合作为STAFF参与该稿件创作（版权方请作为主投UP主）'
  },
  {
    id: 7,
    name: '该稿件属于低创不适合使用联合投稿'
  },
  {
    id: 8,
    name: 'STAFF职能描述与稿件内容不符'
  }
]
export default {
  name: 'CoopTab',
  inheritAttrs: false,
  data() {
    return {
      isLoading: false,
      isSubmiting: false,
      addForm: {
        customName: '',
        mids: '',
        typeName: '',
        businessStyle: 0
      },
      isShowModal: false,
      staffList: [],
      staffTypeList: [],
      STAFF_BUSINESS_STYLE,
      deleteStaffList: [],
      options: [
        {
          text: '顺序',
          getValue: (_row, index) => index + 1
        },
        {
          field: 'business_style',
          text: '职能属性',
          getValue: (row, _index) => STAFF_BUSINESS_STYLE_MAP[row.businessStyle]
        },
        {
          field: 'title',
          text: '职能类型'
        },
        {
          field: 'mid',
          text: 'mid'
        },
        {
          field: 'name',
          text: '合作人'
        },
        {
          text: '调序',
          isSortBtn: true
        },
        {
          text: '操作',
          isDeleteBtn: true
        }
      ],
      resetStaffTypeList: [],
      REASONS,
      deleteReason: {
        reason_id: '',
        reason: '',
        notify: 1
      },
      deleteItem: {},
      rules: {
        reason: [
          {
            type: 'string',
            required: true,
            message: '理由不能为空',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  components: {},
  props: {
    aid: {
      type: [String, Number],
      required: true
    },
    stat: {
      type: Object,
      default() {
        return {

        }
      }
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapState({
      perms: state => state.user.perms
    }),
    hasPermission() {
      return this.perms && this.perms.ARC_STAFF
    },
    hasStaff() {
      return !!this.stat.archiveData.hasStaff
    }
  },
  methods: {
    initData() {
      this.getStaffTypeList()
      this.getStaffList()
    },
    handleChange(label) {
      this.deleteReason = {
        ...this.deleteReason,
        reason: label
      }
    },
    getStaffList() {
      this.isLoading = true
      detailApi.getStaffs({
        aid: this.aid
      }).then(res => {
        const list = res.data || []
        this.staffList = list.map(item => ({
          title: item.staff_title,
          mid: item.staff_mid,
          name: item.staff_name,
          isOrigin: true,
          business_style: item.business_style
        }))
        this.resetStaffTypeList = cloneDeep(this.staffList)
      }).catch(_ => {}).finally(() => {
        this.isLoading = false
      })
    },
    getStaffTypeList() {
      businessApi.getTagList({
        bid: STAFF_BID,
        state: 1,
        ps: 300,
        sort: 'desc',
        order: 'weight'
      }).then(res => {
        const data = res.data || {}
        this.staffTypeList = data.data || []
        this.addForm.typeName = get(res, '0.name', '')
      }).catch(_ => {})
    },
    showModal(item, index) {
      this.deleteItem = {
        ...item,
        index
      }
      this.isShowModal = true
    },
    closeModal() {
      this.resetDeleteReason()
      this.resetDeleteItem()
      this.isShowModal = false
    },
    resetDeleteItem() {
      this.deleteItem = {}
    },
    confirmDelete() {
      // 1.先验证
      // 2.添加删除理由到这个obj
      this.$refs.deleteDialog.validate((valid) => {
        if (valid) {
          const { index } = this.deleteItem
          const { reason, reason_id: reasonId, notify } = this.deleteReason
          const { mid } = this.staffList[index]
          this.deleteStaffList.push({
            mid,
            reason,
            reason_id: reasonId,
            notify
          })
          this.staffList.splice(index, 1)
          this.closeModal()
        } else {
          notify.error('理由不能为空')
        }
      })
    },
    resetDeleteReason() {
      this.deleteReason = {
        reason_id: '',
        reason: '',
        notify: 1
      }
    },
    resetAddForm() {
      this.addForm = {
        customName: '',
        mids: '',
        typeName: get(this.staffTypeList, '0.name', ''),
        businessStyle: 0
      }
    },
    // 重置一切
    resetAll() {
      this.staffList = cloneDeep(this.resetStaffTypeList)
      this.deleteStaffList = []
    },
    validateForm(formData) {
      const title = formData.typeName || formData.customName
      const businessStyle = formData.businessStyle
      const midReg = /^\d+(,\d+)*$/g
      if (!title) {
        notify.error('请输入职务类型')
        return false
      }
      if (!formData.mids) {
        notify.error('请输入mid')
        return false
      }
      if (!midReg.test(formData.mids)) {
        notify.error('mid格式不正确')
        return false
      }
      return {
        ...formData,
        title,
        business_style: businessStyle
      }
    },
    addStaff() {
      const { addForm, staffList } = this
      const form = this.validateForm(addForm)
      if (!form) return

      const midList = Array.from(new Set(addForm.mids.split(',')))
      // 去掉旧数据里有新加mid的数据
      const staffs = staffList.filter(staff => {
        return !midList.some(mid => +mid === +staff.mid)
      })
      const newStaffs = midList.map(mid => ({
        mid: +mid,
        title: form.title,
        isOrigin: false,
        business_style: form.business_style
      }))
      this.staffList = staffs.concat(newStaffs)
      this.resetAddForm()
    },
    showDeleteStaff(item, index) {
      // 1.如果这个是新增的，则直接删
      // 2.如果这个是已有的，则弹出弹窗删不删
      if (item.isOrigin) {
        this.showModal(item, index)
      } else {
        this.deleteStaff(item, index)
      }
    },
    deleteStaff(item, index) {
      this.$delete(this.staffList, index)
    },
    changeIndex(index, isUp) {
      const { staffList } = this
      const targetIndex = isUp ? index - 1 : index + 1
      const arr = staffList.slice()
      // 交换2项
      arr[index] = arr.splice(targetIndex, 1, arr[index])[0]
      this.staffList = arr
    },
    doSubmit() {
      const { staffList, aid, deleteStaffList } = this
      const staffs = staffList.map((staff) => {
        delete staff.isOrigin
        return staff
      })
      const delStaffs = deleteStaffList.map((staff) => {
        delete staff.isOrigin
        delete staff.reason_id
        return staff
      })
      detailApi.modifyStaffs({
        aid,
        staffs: JSON.stringify(staffs),
        del_all: staffList.length > 0 ? 0 : 1,
        del_staffs: JSON.stringify(delStaffs)
      }).then(() => {
        notify.success('编辑成功~')
        this.getStaffList()
      }).catch(_ => {}).finally(() => {
        this.isSubmiting = false
      })
      this.deleteStaffList = []
    }
  },
  created() {
    this.initData()
  }
}
</script>
<style lang="stylus" scoped>
.coop-tab
  .cursor
    cursor pointer
  .archive-staff
    display inline-block
  .archive-staff-add
    margin-top 20px
    display flex
    .staff-add-item
      margin-right 10px
    .staff-add-custom
      flex 1.5
    .staff-add-select
      flex: 1
    .staff-add-mids
      flex: 2
  .button-container
    text-align center
    margin-top 20px
  .sort-button
    display flex
    flex-direction row
    min-height 30px
</style>
