<template>
  <div class="author">
    <el-input-number 
      v-model="form.new_mid"
      class="input"
      style="margin-right: 30px;"
      :controls="false"
      :disabled="!perms.CHANGE_AUTHOR">
    </el-input-number>
    <el-button 
      type="primary" 
      :disabled="!perms.CHANGE_AUTHOR" 
      @click="submitAuthor">
      保存
    </el-button>
  </div>
</template>

<script>
import { detailApi } from '@/api/index'
import { mapState } from 'vuex'

export default {
  props: {
    aid: {
      required: true
    },
    mid: {
      required: true
    }
  },
  computed: {
    ...mapState({
      perms: state => state.user.perms
    })
  },
  data() {
    return {
      form: {
        aid: this.aid,
        new_mid: this.mid
      }
    }
  },
  watch: {
    mid(newVal) {
      this.form.new_mid = newVal
    }
  },
  methods: {
    submitAuthor() {
      const form = { ...this.form }
      // 后端会返回tips，直接全局弹提示
      detailApi.saveAuthor(form).then(() => {
      }).catch(_ => {})
    }
  }
}
</script>

<style lang="stylus" scoped>
.author
  .input
    display inline-block
    width 300px
    vertical-align middle
</style>
