<template>
  <div class="filter-main">
    <el-form inline @submit.stop.prevent.native ref="filterForm" v-if="showFormItems.length" :model="formData" :rules="formRules">
      <el-form-item v-for="(item, index) in showFormItems" :key="index" :prop="item.name[0] || item.name" :required="item.required" :class="{'group-tag': item.groupTag}">
        <el-input
          v-if="item.type === 'input' || item.type === 'input_zone'"
          type="textarea"
          :autosize="{ minRows: 1, maxRows: 1}"
          :placeholder="item.placeholder"
          v-model="formData[item.name]"
          resize="none"
          class="input"
          style="width: 200px"
          size="small">
        </el-input>

        <el-input-number
          v-else-if="item.type === 'input_number'"
          v-model="formData[item.name]"
          :autosize="{ minRows: 1, maxRows: 1}"
          :placeholder="item.placeholder"
          resize="none"
          class="input"
          style="width: 180px; margin-top: 8px;"
          size="small"
        />

        <el-select
          v-else-if="item.type === 'select' || item.type === 'multi_value_select' || item.type === 'multi_value_checkbox' || item.type === 'checkbox'"
          :placeholder="item.placeholder"
          :multiple="item.type.indexOf('checkbox') > -1"
          v-model="formData[item.name]"
          class="select"
          size="small"
          @change="handleChange(item.name)"
          :style="{'width': item.type.indexOf('checkbox') > -1 ? '250px' : '150px'}"
          :clearable="JSON.stringify(item.clearable) ? item.clearable : true">
          <el-option
            v-for="(obj, index) in filterOptions[item.name]"
            :key="index"
            :value="obj.val"
            :label="obj.label">
          </el-option>
        </el-select>

        <el-date-picker
          v-else-if="item.type === 'time_zone' || item.type === 'time'"
          :placeholder="item.placeholder"
          v-model="formData[item.name]"
          :type="'datetime'"
          value-format="yyyy-MM-dd HH:mm:ss"
          size="small">
        </el-date-picker>

        <div v-else-if="item.type === 'cascader'">
          <el-select
            :placeholder="item.placeholder[0]"
            v-model="formData[item.name[0]]"
            @change="handleCascaderChange($event, item.name)"
            class="select"
            size="small"
            style="width: 150px; margin-right: 5px"
            clearable>
            <el-option
              v-for="obj in filterOptions[item.name[0]]"
              :key="obj.val"
              :value="obj.val"
              :label="obj.label">
            </el-option>
          </el-select>
          <el-select
            v-if="subOptions.length"
            v-model="formData[item.name[1]]"
            :placeholder="item.placeholder[1]"
            class="select"
            size="small"
            style="width: 150px;"
            clearable>
            <el-option
              v-for="obj in subOptions"
              :key="obj.value"
              :value="obj.value"
              :label="obj.name">
            </el-option>
          </el-select>
        </div>

        <FormatInput
          v-model="formData[item.name]"
          v-else-if="item.type === 'format_input'"
          type="textarea"
          :autosize="{ minRows: 1, maxRows: 1}"
          :placeholder="item.placeholder"
          resize="none"
          class="input"
          style="width: 200px"
          size="small">
        </FormatInput>

        <el-checkbox-group v-model="formData[item.name]" v-else-if="item.type === 'checkbox_group'">
          <el-checkbox :label="item.val" v-for="(item, index) in filterOptions[item.name]" :key="index">{{item.label}}</el-checkbox>
        </el-checkbox-group>

        <el-checkbox
          v-else-if="item.type === 'checkbox_single'"
          :label="item.label"
          :true-label="item.trueValue"
          :false-label="item.falseValue"
          v-model="formData[item.name]">
          {{item.label}}
        </el-checkbox>

        <el-select
          v-else-if="item.type === 'up-arctype'"
          :placeholder="item.placeholder"
          :multiple="true"
          v-model="formData[item.name]"
          size="small"
          :clearable="JSON.stringify(item.clearable) ? item.clearable : true"
          style="width: 250px"
        >
          <el-option
            v-for="(item, index) in upArcTypeOptions"
            :key="index"
            :value="item.name"
            :label="item.name">
          </el-option>
        </el-select>

        <slot :name="item.slotName" v-else></slot>
      </el-form-item>

      <!-- <el-row> -->
      <el-form-item>
        <el-button type="primary" size="small" @click="getList">搜索</el-button>
        <el-button type="info" size="small" @click="reset">重置</el-button>
      </el-form-item>
      <!-- </el-row> -->
      <el-form-item v-if="showSwitch" style="float: right; margin-right: 30px;">
        <el-radio-group
          class="ml-8"
          v-model="getDisplayModel"
          size="small"
        >
          <el-radio-button :label="DISPLAY_MODEL.LIST_MODEL">
            <SvgIcon style="width: 1em; height: 1em; vertical-align: top; fill: currentColor;" name="menu" />
          </el-radio-button>
          <el-radio-button :label="DISPLAY_MODEL.CARD_MODEL">
            <i class="el-icon-menu"></i>
          </el-radio-button>
        </el-radio-group>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import FormatInput from './FormatInput.vue'
import { mapState, mapActions } from 'vuex'
import { DISPLAY_MODEL } from '@/utils/constant'
import SvgIcon from '@/v2/pure-components/Icon/SvgIcon.vue'

export default {
  components: {
    FormatInput,
    SvgIcon
  },
  props: {
    formItems: {
      type: Array,
      default: () => []
    },
    formData: {
      type: Object,
      default: () => {}
    },
    filterOptions: {
      type: Object,
      default: () => {}
    },
    displayModel: {
      type: String,
      default: 'list_model'
    },
    showSwitch: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const validateEvent = (rule, value, callback) => {
      if (!value) {
        callback(new Error('这是必填项'))
      } else {
        callback()
      }
    }
    return {
      formRules: {},
      rule: {
        required: true,
        validator: validateEvent,
        trigger: 'blur'
      },
      DISPLAY_MODEL,
      options: [],
      arcOptions: []
   }
  },

  watch: {
    filterOptions: {
      handler() {
        this.setOptions()
      },
      immediate: true
    },
    formItems: {
      handler(items) {
        this.setRules()
        items.forEach(i => {
          if (['arctype', 'up-arctype'].includes(i.type)) {
            this.getArchiveType()
          }
        })
      },
      immediate: true
    }
  },
  computed: {
    ...mapState({
      arctypes: state => state.arctype.arctypes,
      arctypeMap: state => state.arctype.arctypeMap,
      perms: (state) => state.user.perms
    }),
    getDisplayModel: {
      get() {
        return this.displayModel
      },
      set(val) {
        this.$emit('updateDisplayModel', val)
      }
    },
    subOptions() {
      return this.options || []
    },
    showFormItems() {
      return this.formItems.filter(item => {
        return item.type !== 'hide' && this.authValid(item.auth) &&
          !['meal_id', 'user_type'].includes(item.name) // 去掉 禁止项（meal_id）和 用户组 (user_type)
      }) || []
    },
    upArcTypeOptions() {
      return [{ name: '无分区' }, ...this.arctypes]
    }
  },
  methods: {
    ...mapActions({
      getArctype: 'arctype/getArctype'
    }),
    authValid(auth) {
      // 为空 或者有权限
      return !auth || this.perms[auth]
    },
    mappingKV(keys, response) {
      let value = ''
      if (keys && response) {
        const newArray = keys.split('.')
        newArray.forEach((key, index) => {
          if (index === 0) {
            value = response.hasOwnProperty(key) ? response[key] : {}
          } else {
            value = (value && value.hasOwnProperty(key)) ? value[key] : ''
          }
        })
      }
      return value
    },
    setOptions() {
      const options = this.filterOptions
      for (const key in options) {
        if (options[key] && options[key].href) {
          this.$ajax.get(options[key].href).then(res => {
            if (!res) {
              return []
            }
            let data = []
            const newData = []
            if (options[key].dataPath) {
              data = this.mappingKV(options[key].dataPath, res) || []
            } else {
              data = res.data
            }
            for (const key in data) {
              newData.push({
                val: key,
                label: data[key]
              })
            }
            this.filterOptions[key] = newData
          }).catch(_ => {})
        }
      }
    },
    setRules() {
      this.showFormItems.forEach(item => {
        if (item.required) {
          for (const key in this.formData) {
            if (key === item.name) {
              this.formRules[key] = { ...this.rule }
            }
          }
        }
      })
    },
    getList() {
      // 由于接口没做当前查询页是否有数据的边界判断 so每一次在此处点击的查询都强制查询第一页数据
      this.$emit('getList', true)
    },
    reset() {
      this.options = []
      this.$refs.filterForm.resetFields()
      this.$emit('resetForm')
      // 强行清空已选分区数据
      this.$refs.tree && this.$refs.tree[0].deletedAll()
    },
    handleCascaderChange(val, name) {
      this.options = []
      if (val || val === 0) {
        const currentOption = this.filterOptions[name[0]] || []
        this.options = ((currentOption.find(o => o.val === val) || {}).extra_kv) || []
        this.formData[name[1]] = this.options[0].value
      } else {
        // 如果第一级没有选任何数据，那么第二级数据无脑清空
        this.formData[name[1]] = ''
      }
    },
    getArchiveType() {
      this.getArctype().then(res => {
        this.arcOptions = this.arctypes || []
      }).catch(_ => {})
    },
    handleChange(name) {
      if (name === 'state') {
        this.$forceUpdate()
      }
    }
  }
}
</script>
<style lang="stylus">
.filter-main
  textarea
    height 32px !important
  .input
    width 300px
    position relative
    top -4px
    height 32px
  .select
    .el-select__tags
      top 47%
    // .el-input__inner
    //   height 32px !important
  .group-tag
    margin-right 5px
  .el-form-item
    margin-bottom: 4px
</style>
