<template>
  <el-card class="box-card class-contents">
    <div slot="header" class="clearfix">
      <span>{{ title }}</span>
    </div>
    <p>目录列表</p>
    <br />
    <el-table
      border
      stripe
      size="small"
      style="width: 100%"
      ref="table"
      :data="tableData"
    >
      <el-table-column label="序号" width="80" prop="id" />
      <el-table-column label="课时标题" prop="title" />
      <el-table-column label="课程类型">
        <template slot-scope="scope">
          <p>{{ COURSE_TYPE[scope.row.type] }}</p>
        </template>
      </el-table-column>
      <el-table-column label="预计发布">
        <template slot-scope="scope">
          <p>{{ formatTime(scope.row.pubtime) }}</p>
        </template>
      </el-table-column>
      <el-table-column label="是否正课">
        <template slot-scope="scope">
          <p>{{ scope.row.is_course ? '是' : '否' }}</p>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      layout="total, prev, pager, next"
      :total="contents.length"
      :page-size="pager.ps"
      :current-page="pager.pn"
      @current-change="onCurrentChange"
    />
  </el-card>
</template>
<script>
import moment from 'moment'

export default {
  name: 'ClassContents',
  data() {
    return {
      title: 'Package信息',
      pager: {
        pn: 1,
        ps: 30,
        total: 0
      },
      COURSE_TYPE: {
        1: '视频课',
        2: '直播课',
        3: '直播回放'
      }
      // (1:视频课2:直播课3:直播回放1和3可以认为是视频课)
    }
  },
  props: {
    detail: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  computed: {
    contents() {
      return this.detail?.detail?.content?.items || []
    },
    tableData() {
      const { pn, ps } = this.pager
      return this.contents.slice((pn - 1) * ps, pn * ps)
    }
  },
  methods: {
    onCurrentChange(e) {
      this.pager.pn = e
    },
    formatTime(time) {
      return moment(time * 1000).format('YYYY-MM-DD HH:mm:ss')
    }
  }
}
</script>
