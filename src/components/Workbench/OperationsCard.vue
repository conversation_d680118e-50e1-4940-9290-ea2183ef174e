<template>
  <div class="operations-card">
    <el-card class="box-card">
      <div slot="header" class="clearfix" v-if="showHead">
        <span>审核操作</span>
      </div>
      <el-form inline @submit.stop.prevent.native>
        <el-form-item v-if="ops && ops.length && !isAutoQa">
          <el-button
            v-for="op in ops"
            :key="op.bind_id_list"
            :style="{ 
              backgroundColor: op?.actionParams?.customStyle?.bgc,
              borderColor:  op?.actionParams?.customStyle?.bgc
            }"
            :type="Object.keys(OPER_BUTTON_TYPES).includes(op.ch_name) ? OPER_BUTTON_TYPES[op.ch_name] : 'primary'"
            size="small"
            @click="clickedOper(op)"
          >
            {{ `${op.ch_name}${showShortcut ? KEY_MAP[op.ch_name] : ''}` }}
          </el-button>
        </el-form-item>
        <el-form-item v-if="isAutoQa">
          <QaOperButtons
            @clickQaOper="(qaOper) => handleQaOperClick(qaOper)"
            :hideLogBtn="true"
          />
        </el-form-item>
      </el-form>
    </el-card>
    <AutoQaOperationDialog ref="qaOperation" @submit="onQaSubmit" :bizConfig="bizConfig" />
  </div>
</template>
<script>
import { mapState, mapActions } from 'vuex'
import { KEY_MAP } from '@/pages/contentAudit/constants'
import { OPER_BUTTON_TYPES } from '@/pages/workbench/constants.js'
import { QaOperButtons } from '@/v2/biz-components/quality-assurance'
import { clickQaOperHandler, submitQaOperHandler } from '@/v2/biz-utils/submission/qa'
import AutoQaOperationDialog from '@/pages/workbench/dialogConfig/AutoQaOperationDialog'
import { getBackUrl } from '@/utils'

const CODE_KEY_MAP = [83, 68, 71, 70]

export default {
  components: {
    AutoQaOperationDialog,
    QaOperButtons
  },
  props: {
    auditResource: {
      type: Object,
      default: () => {}
    },
    operationCard: {
      type: Object,
      default: () => {
        return {}
      }
    },
    ops: {
      type: Array,
      default: () => {
        return []
      }
    },
    useForbidBtn: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    codeTimer: {
      type: Number,
      default: null
    },
    detail: {
      type: Object,
      default: () => {
        return {}
      }
		},
		staffTableData: {
			type: Array,
			default: () => {
				return []
			}
		},
    showHead: {
      type: Boolean,
      default: true
    },
    // 是否展示快捷键位
    showShortcut: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    ...mapState({
      isAutoQa: (state) => state.qa.isAutoQa,
      bizConfig: (state) => state.todoDetail.bizConfig
    })
  },
  data() {
    return {
      forbidList: [],
      attributeList: {},
      KEY_MAP,
			comps: [],
      query: {},
      noHotkey: false,
      OPER_BUTTON_TYPES
    }
  },

  created() {
  },

  mounted() {
    this.$nextTick(_ => {
      document.addEventListener('keyup', this.keyup)
		})
    this.query = this.$route.query
  },

  beforeDestroy() {
    document.removeEventListener('keyup', this.keyup)
  },

  methods: {
    ...mapActions({
      getTask: 'todoDetail/getTask'
    }),
    handleQaOperClick(qaOper) {
      clickQaOperHandler(
        qaOper,
        this.auditResource,
        this.$route.query.business_id,
        this.$route.query.todo_id,
        this.$route.query.is_task,
        this.$refs.qaOperation.openDialog,
        this.$route.query.is_task ? this.getTask : this.backToList
      )
    },
    async onQaSubmit(operation, qaOper, row = {}) {
      submitQaOperHandler(
        operation,
        qaOper,
        row,
        this.$route.query.business_id,
        this.$route.query.todo_id,
        this.$route.query.is_task,
        this.isTask ? this.getTask : this.backToList
      )
    },
    // 返回列表页
    backToList() {
      const back = this.$route.query.back || ''
      const url = getBackUrl(back)
      if (url) {
        window.location.href = url
      } else {
        this.$router.go(-1)
      }
    },
    clickedOper(op) {
      this.$emit('clickOpers', op)
    },
    keyup(e) {
      if (this.noHotkey && e.keyCode !== 83) return
      const operations = this.ops || []
      let op
      // S | D | G
      if (document.activeElement.tagName === 'INPUT' || document.activeElement.tagName === 'TEXTAREA' || !operations || !operations.length) return
      if (CODE_KEY_MAP.findIndex(code => code === e.keyCode) > -1) {
        if (e.keyCode === 70) {
          const lock = operations.find(op => op.ch_name === '折叠')
          op = lock || operations.find(op => op.ch_name === '锁定')
        } else if (e.keyCode === 83) {
          const pass = operations.find(op => op.ch_name === '通过')
          op = pass || operations.find(op => op.ch_name === '提交')
        } else if (e.keyCode === 68) {
          op = operations.find(op => op.ch_name === '驳回')
        } else {
          op = operations.find(op => op.ch_name === '封禁')
        }
        if (op && op.ch_name) this.clickedOper(op)
      }
    }
  }
}
</script>
<style lang="stylus" scoped>
.operations-card {

}
</style>
