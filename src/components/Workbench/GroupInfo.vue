<template>
<div v-if="group">
    <el-tooltip placement="bottom">
        <p>{{group.name}}</p>
        <p slot="content">{{group.id}}</p>
    </el-tooltip>
    <div>群成员数：{{group.memberNumber}}</div>
</div>
<div v-else></div>
</template>

<script>
export default {
    data() {
        return {
        }
    },
    props: {
        // memberNumber, id, name
        group: {
            type: Object,
            default: null
        }
    }
}
</script>

<style lang="stylus" scoped>
</style>
