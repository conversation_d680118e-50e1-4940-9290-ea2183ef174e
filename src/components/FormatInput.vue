<template>
  <el-input
    v-bind="$attrs"
    :value="value"
    @input="handleInput"
    @change="handleBlur"
  >
  </el-input>
</template>

<script>

import { inputReplace } from '@/utils'
export default {
  name: 'FormatInput',
  model: {
    prop: 'value',
    event: 'input'
  },
  props: {
    value: {
      type: [String, Number],
      default: ''
    }
  },
  methods: {
    handleInput(val) {
      this.$emit('input', val)
    },
    handleBlur(val) {
      this.$emit('input', inputReplace(val))
    }
  }
}
</script>
