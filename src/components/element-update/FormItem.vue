<template>
  <div class="ag-form-item" :class="{
    'highlight-custom': highlight
  }">
    <label class="ag-form-item__label"
      :style="{
        width: label ? labelWidth : '',
        textAlign: alignFormat(align)
      }"
    >
      {{label}}
    </label>
    <div class="ag-form-item__content"
      :style="{
        marginLeft: label ? labelWidth : ''
      }"
      :class="{
        'flex': type === 'flex',
        'flex-wrap': type === 'flex-wrap',
        'items-start': type === 'items-start'
      }"
    >
      <slot v-if="label || showSlot"/>
    </div>
  </div>
</template>
<script>
export default {
  name: 'AgFormItem',
  data() {
    return {}
  },
  props: {
    label: {
      type: String,
      default: ''
    },
    labelWidth: {
      type: String,
      default: '75px'
    },
    type: {
      type: String,
      default: ''
    },
    align: {
      type: String,
      default: 'right'
    },
    highlight: {
      type: Boolean,
      default: false
    },
    showSlot: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    }
  },
  methods: {
    alignFormat(align) {
      switch (align) {
        case 'left':
          return 'left'
        case 'right':
          return 'right'
        case 'center':
          return 'center'
        default:
          return 'right'
      }
    }
  }
}
</script>
<style lang="stylus">
.ag-form-item
  &.highlight-custom
    background-color var(--green-light-1) !important
    border-radius 2px
    input,
    textarea,
      [class^="vue-treeselect"],
      [class^="highlight-wrapper"]
        background-color var(--green-light-1) !important
</style>
<style lang="stylus" scoped>
.ag-form-item
  margin-bottom 10px
  &__label
    font-size 16px
    font-weight bold
    text-align right
    vertical-align middle
    float left
    font-size 14px
    color var(--text-color)
    line-height 32px
    // padding 0 12px 0 0
    -webkit-box-sizing border-box
    box-sizing border-box
  &__content
    font-size 14px
    line-height 32px
    &.flex
      display flex
    &.flex-wrap
      display flex
      flex-wrap wrap
    &.items-start
      display flex
      align-items flex-start
</style>
