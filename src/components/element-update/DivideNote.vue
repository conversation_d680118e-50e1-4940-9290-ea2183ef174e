<template>
  <div class="divide-note" @keydown.enter.stop>
    <AgSelect 
      v-model="roleIdx"
      style="width: 160px; margin-right: 5px;"
      placeholder="选择角色"
      size="small"
      @change="changeRole"
      :disabled="disabled"
    >
      <el-option
        v-for="(role, idx) in options"
        :key="idx"
        :label="role.label"
        :value="idx">
      </el-option>
    </AgSelect>
    <AgSelect 
      v-if="options[roleIdx]"
      v-model="noteIdx"
      style="width: 160px; margin-right: 5px;"
      placeholder="选择快捷文本"
      size="small"
      @change="handleAddNote"
      :disabled="disabled"
    >
      <el-option
        value=""
        label="选择快捷文本">
      </el-option>
      <el-option
        v-for="(reason, subIdx) in options[roleIdx].options"
        :key="subIdx"
        :label="reason.label"
        :value="subIdx">
      </el-option>
    </AgSelect>
    <div class="divide-note-extend" :class="{'over-focus': isFocus}" @click="isFocus = true" v-click-outside="handleClickOutside">
      <!-- 面板 -->
      <div class="divide-note-panel" :class="{'over-focus': isFocus}" @keydown.enter.stop @click="focusInput">
        <AgInputTag 
          style="margin-bottom: 5px;"
          v-for="(tag, idx) in tags" 
          v-model="tag.tag_name"
          :key="idx" 
          :type="getTagType(tag)"
          :hitState="tag.hitState"
          :closable="tag.canDelete && !disabled"
          :canEdit="tag.canDelete && !disabled"
          @delete="handleEmitDelete(tag, idx)"
          :disabled="disabled"
        />
        <input
          type="text"
          class="divide-note-select-input"
          v-if="allowCreate"
          v-model="query"
          @input="debouncedQueryChange"
          maxlength="60"
          @keydown.enter.prevent="selectOption"
          @keydown.esc.stop.prevent="visible = false"
          @keydown.delete="deletePrevTag"
          @keydown.tab="visible = false"
          ref="input"
          :disabled="disabled"
        >
      </div>
    </div>
  </div>
</template>
<script>
import AgSelect from './Select.vue'
import AgInputTag from './InputTag.vue'
import clickOutside from '@/directives/click-outside'
import notify from '@/lib/notify'

export default {
  name: 'DivideNote',
  data() {
    return {
      noteIdx: '',
      roleIdx: 0,
      roleOption: [
        '组长'
      ],
      isFocus: false,
      visible: false,
      query: '',
      selected: []
    }
  },
  components: {
    AgSelect,
    AgInputTag
  },
  props: {
    tags: {
      type: Array,
      default() {
        return []
      }
    },
    options: {
      type: Array,
      default() {
        return []
      }
    },
    allowCreate: {
      type: Boolean,
      default: false
    },
    aid: {
      type: [Number, String],
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  directives: {
    clickOutside
  },
  watch: {
    aid: {
      handler() {
        this.noteIdx = ''
        this.roleIdx = 0
      },
      immediate: true
    }
  },
  methods: {
    getTagType(tag) {
      if (tag.first_init) return 'danger'
      else return 'warning'
    },
    handleEmitDelete(tag, idx) {
      this.$emit('delete-note', tag, idx)
    },
    handleAddNote(noteIdx) {
      if (noteIdx === '') return 
      const note = this.options[this.roleIdx].options[noteIdx]
      this.$emit('hit-last-note', false)
      this.$emit('add-note', note)
    },
    changeRole(idx) {
      this.$emit('hit-last-note', false)
      this.roleIdx = idx
      this.noteIdx = ''
    },
    handleClickOutside(e) {
      this.$emit('hit-last-note', false)
      this.isFocus = false
    },
    debouncedQueryChange(e) {
      this.$emit('hit-last-note', false)
      this.query = e.target.value
    },
    selectOption() {
      this.$emit('hit-last-note', false)
      this.$emit('add-custom-note', this.query)
      this.query = ''
    },
    deletePrevTag(e) {
      if (e.target.value.length <= 0) {
        if (!this.tags[this.tags.length - 1].hitState) {
          this.$emit('hit-last-note', true)
        } else if (this.tags[this.tags.length - 1].canDelete) {
          this.$emit('delete-last-note')
        } else {
          notify.warning('不能删除其他角色添加的备注', 1500)
        }
      }
    },
    focusInput() {
      if (this.allowCreate) {
        this.$refs.input && this.$refs.input.focus()
      }
    }
  }
}
</script>
<style lang="stylus" scoped>
.divide-note
  display flex
  flex 1
  .divide-note-extend
    flex 1
    display flex
    position relative
    width 0
  .divide-note-panel
    width 100%
    display flex
    resize vertical
    padding 5px 15px
    line-height 1.5
    box-sizing border-box
    font-size inherit
    color var(--text-color)
    background-color var(--content-bg-color)
    background-image none
    border 1px solid #DCDFE6
    border-radius 4px
    flex-direction column
    height 33px
    overflow-y auto
    &:hover
      border-color var(--text-color-light-2)
    &.over-focus
      outline 0
      position absolute
      top 0
      left 0
      z-index 2
      min-width 600px
      max-width 800px
      resize none
      height 7em
      border-color var(--blue-light-1) !important
  .divide-note-select-input
    border none
    outline 0
    padding 0
    color var(--text-color)
    font-size 14px
    appearance none
    height 28px
    background-color transparent
</style>
