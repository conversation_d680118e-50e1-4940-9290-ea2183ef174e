<template>
  <AgTooltip
    class="ag-textover"
    :maxWidth="420"
    :noTooltip="disabled"
    :disabled="disabled">
    <div class="reference" ref="textoverRef">
      <slot />
    </div>
    <template slot="content">
      <slot name="content"/>
    </template>
  </AgTooltip>
</template>
<script>
import AgTooltip from './Tooltip'
export default {
  name: 'AgTextover',
  data() {
    return {
      disabled: false
    }
  },
  components: {
    AgTooltip
  },
  methods: {
    reflow() {
      // 等页面重绘/重排完成判断文本是否被截短了
      this.$nextTick(() => {
        const el = this.$refs.textoverRef && this.$refs.textoverRef.firstElementChild
        let disabled = false
        if (el) {
          disabled = el.clientHeight === el.scrollHeight
        } else {
          disabled = false
        }
        this.disabled = disabled
      })
    }
  },
  mounted() {
    this.reflow()
  }
}
</script>
<style lang="stylus" scoped>
.ag-textover
  .reference
    width 100%
    height 100%
    display flex
</style>
