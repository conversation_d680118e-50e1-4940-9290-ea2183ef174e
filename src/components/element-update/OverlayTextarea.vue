<template>
  <div class="ag-textarea-overlay">
    <el-input
      type="textarea"
      ref="textarea"
      v-model="text"
      :placeholder="placeholder"
      :rows="rows"
      :size="size"
      :class="{
        'over-focus' : showOverInput,
        'over-focus__bottom' : placement === 'bottom'
      }"
      resize="both"
      :disabled="disabled"
      @blur="$emit('blur')"
      @keydown.enter.stop>
    </el-input> 
  </div>
</template>
<script>
export default {
  name: 'AgOverlayTextarea',
  data() {
    return {
      showOverInput: true
    }
  },
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    rows: {
      type: Number,
      default: 1
    },
    placeholder: {
      type: String,
      default: ''
    },
    size: {
      type: String,
      default: 'mini'
    },
    width: {
      type: String,
      default: ''
    },
    height: {
      type: String,
      default: ''
    },
    placement: {
      type: String,
      default: ''
    }
  },
  computed: {
    text: {
      get() {
        return this.value
      },
      set(newVal) {
        this.$emit('input', newVal)
      }
    }
  }
}
</script>
<style lang="stylus">
.ag-textarea-overlay{
  .over-focus{
    width 100%
    textarea {
      outline 0
      position absolute
      bottom 0
      left 0
      z-index 2
      resize none
      min-width 500px
      max-width 800px
      height 7em
    }
    &__bottom{
      textarea{
        top 0
        bottom initial
      }
    }
  }
}
</style>
<style lang="stylus" scoped>
.ag-textarea-overlay{
  position relative
  flex 1
  letter-spacing normal
  word-spacing normal
  text-rendering auto
  height 100%
  display flex
  .el-textarea{
    position initial
  }
}
</style>
