<template>
  <div class="ag-input-tag">
    <template v-if="canEdit">
      <el-input
        class="ag-input-tag-input"
        v-if="inputVisible"
        v-model="inputValue"
        ref="saveTagInput"
        size="mini"
        maxlength="60"
        @keyup.native.enter.stop.prevent="handleInputConfirm"
        @blur="handleInputConfirm"
        :disabled="disabled"
      >
      </el-input>
      <el-tag
        v-else
        class="auto-height-tag"
        size="small"
        :key="inputValue"
        :closable="closable"
        :type="type"
        :disable-transitions="true"
        :class="{
          'ag-input-tag-hit' : hitState
        }"
        @close="handleClose(inputValue)"
        @click.stop="showInput">{{inputValue || '请填写备注'}}</el-tag>
    </template>
    <template v-else>
      <el-tag
        size="small"
        class="auto-height-tag"
        :key="inputValue"
        :closable="closable"
        :type="type"
        :disable-transitions="true"
        :class="{
          'ag-input-tag-hit' : hitState
        }"
         @close="handleClose(inputValue)">{{inputValue || '请填写备注'}}</el-tag>
    </template>
  </div>
</template>
<script>
export default {
  name: 'AgInputTag',
  data() {
    return {
      inputVisible: false
    }
  },
  model: {
    prop: 'value',
    event: 'input'
  },
  props: {
    value: {
      type: String,
      required: true
    },
    type: {
      type: String,
      default: 'warning'
    },
    hitState: {
      type: Boolean,
      default: false
    },
    closable: {
      type: Boolean,
      default: true
    },
    canEdit: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    inputValue: {
      get() {
        return this.value
      },
      set(newValue) {
        this.$emit('input', newValue)
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('delete')
    },
    showInput() {
      this.inputVisible = true
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },
    handleInputConfirm(e) {
      this.inputVisible = false
    }
  }
}
</script>
<style lang="stylus" scoped>
.ag-input-tag
  .button-new-tag
    margin-left 10px
    height 32px
    line-height 30px
    padding-top 0
    padding-bottom 0
  .input-new-tag
    width 90px
    margin-left 10px
    vertical-align bottom
  .ag-input-tag-input
    width 100%
  .ag-input-tag-hit
    border-color var(--grey-light-1)
  .auto-height-tag
    min-height 24px
    height auto
    white-space pre-wrap
</style>
