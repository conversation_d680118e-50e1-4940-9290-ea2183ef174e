<template>
  <div class="ag-radio-tag" :class="{
    'ag-radio-tag__hit' : hitState
  }">
    <span>{{value}}</span>
    <div class="tag-close" v-if="closable" @click="handleClose">
      <i class="el-tag__close el-icon-close"></i>
    </div>
  </div>
</template>
<script>
export default {
  name: 'AgRadioTag',
  model: {
    prop: 'value',
    event: 'input'
  },
  props: {
    value: {
      type: String,
      required: true
    },
    hitState: {
      type: Boolean,
      default: false
    },
    closable: {
      type: Boolean,
      default: true
    },
    canEdit: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {

    }
  },
  methods: {
    handleClose() {
      this.$emit('delete')
    }
  }
}
</script>
<style lang="stylus" scoped>
.ag-radio-tag
  border-radius 15px
  display inline-flex
  border 1px solid var(--grey-light-1)
  background var(--content-bg-color)
  color var(--text-color-light-1)
  padding 0 12px 
  line-height 28px
  align-items center
  margin-right 10px
  height 28px
  float left
  &__hit
    border-color var(--grey-dark-1)
    color var(--text-color-reverse)
    background-color var(--link-color)
    border-color var(--link-color)
  .tag-close
    padding-left 5px
    cursor pointer
    display flex
</style>
