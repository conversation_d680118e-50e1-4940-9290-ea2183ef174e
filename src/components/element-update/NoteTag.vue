<template>
  <div class="note-tag" @keydown.enter.stop>
    <el-row>
      <el-col :span="6">
        <AgSelect
          v-model="roleIdx"
          style="width: calc(100% - 5px)"
          placeholder="选择角色"
          size="small"
          @change="changeRole"
          :disabled="disabled"
        >
          <el-option
            v-for="(role, idx) in options"
            :key="idx"
            :label="role.label"
            :value="idx"
          >
          </el-option>
        </AgSelect>
      </el-col>
      <el-col :span="12">
        <!-- 级联 -->
        <AgCascader
          v-model="noteIdx"
          ref="agCascader"
          size="small"
          style="width: calc(100% - 10px)"
          filterable
          clearable
          :options="cascaderOps"
          :key="roleIdx"
          :cascaderProps="{
            value: 'val',
            label: 'label',
            expandTrigger: 'hover',
            multiple: true,
            maxLevel,
          }"
          :filterMethod="filterByPathAndRules"
          :popperClass="popperClass"
          @changeNodeTree="handleChangeNodeTree"
          @keyup.enter.native="handleAddNote"
        >
        </AgCascader>
      </el-col>
      <el-col :span="6">
        <el-input
          ref="noteTagInputRef"
          v-model="desc"
          placeholder="输入描述"
          size="small"
          style="width: 100%; margin-right: 5px"
          :disabled="disabled"
          @keyup.enter.native="handleAddNote"
        ></el-input>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <div
          class="note-tag-extend"
          :class="{ 'over-focus': isFocus }"
          @click="isFocus = true"
          v-click-outside="handleClickOutside"
        >
          <!-- 面板 -->
          <div
            class="note-tag-panel"
            :class="{ 'over-focus': isFocus }"
            @keydown.enter.stop
            @click="focusInput"
          >
            <p style="color: var(--label-color)" v-if="tags.length === 0">选择备注tag（补充描述）后[回车]确认添加生效  </p>
            <AgInputTag
              style="margin-bottom: 5px; margin-right:5px;"
              v-for="(tag, idx) in tags"
              v-model="tag.tag_name"
              :key="idx"
              :type="getTagType(tag)"
              :hitState="tag.hitState"
              :closable="tag.canDelete && !disabled"
              :canEdit="false"
              :disabled="disabled"
              @delete="handleEmitDelete(tag, idx)"
            />
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import AgSelect from './Select.vue'
import AgInputTag from './InputTag.vue'
import clickOutside from '@/directives/click-outside'
import AgCascader from './Cascader'
import { findMaxLevel } from '@/utils/tree.js'
import notify from '@/lib/notify'

export default {
  name: 'NoteTag',
  data() {
    return {
      noteIdx: [],
      roleIdx: 0,
      roleOption: ['组长'],
      selectedNodeTree: [],
      desc: '',
      isFocus: false,
      visible: false,
      query: '',
      selected: []
    }
  },
  components: {
    AgSelect,
    AgInputTag,
    AgCascader
  },
  computed: {
    cascaderOps() {
      const { options, roleIdx } = this
      return (options[roleIdx] && options[roleIdx].options) || []
    },
    maxLevel() {
      return findMaxLevel(this.cascaderOps) + 1
    }
  },
  props: {
    tags: {
      type: Array,
      default() {
        return []
      }
    },
    options: {
      type: Array,
      default() {
        return []
      }
    },
    allowCreate: {
      type: Boolean,
      default: false
    },
    aid: {
      type: [Number, String],
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    popperClass: String
  },
  directives: {
    clickOutside
  },
  watch: {
    aid: {
      handler() {
        this.noteIdx = []
        this.roleIdx = this.options.length ? 0 : ''
      },
      immediate: true
    },
    options() {
      this.roleIdx = this.options.length ? 0 : ''
    }
  },
  methods: {
    filterByPathAndRules(node, keyword) {
      const query = keyword.toLowerCase()
      const rule = node.data?.keyword.toLowerCase() || ''
      const hitRule = rule ? rule?.indexOf(query) > -1 : false
      const hitTagText = node.text?.toLowerCase().indexOf(query) > -1
      return hitRule || hitTagText
    },
    getTagType(tag) {
      if (tag.first_init) return 'danger'
      else return 'warning'
    },
    handleEmitDelete(tag, idx) {
      this.$emit('delete-note', tag, idx)
    },
    clearNoteTag() {
      this.noteIdx = []
      this.desc = ''
      this.selectedNodeTree = []
    },
    handleChangeNodeTree(val) {
      this.selectedNodeTree = val || []
    },
    addNote(val = '') {
      if (this.noteIdx.length === 0) {
        notify.warning('请先选择备注tag')
        return
      }
      const checkedNodes = this.$refs.agCascader.$refs.cascader.getCheckedNodes(true)

      for (let nodeLeaf of this.noteIdx) {
        const value = nodeLeaf[nodeLeaf.length - 1]
        const node = checkedNodes.find(item => item.value === value)
        const pathLabel = node.pathLabels.join('/')

        this.$emit('add-note', {
          value,
          remark: val,
          label: `${pathLabel}${
            val ? `【${val}】` : val
          }`,
          pre_text: pathLabel
        })
      }
      this.clearNoteTag()
    },
    handleAddNote(e) {
      this.$nextTick(() => {
        this.addNote(this.desc)
      })
      try {
        this.$refs.agCascader.$refs.cascader.toggleDropDownVisible(false)
      } catch (e) {
        console.error(e)
      }
    },
    changeRole(idx) {
      this.roleIdx = idx
      this.noteIdx = []
    },
    handleClickOutside(e) {
      this.isFocus = false
    },
    focusInput() {
      if (this.allowCreate) {
        this.$refs.input && this.$refs.input.focus()
      }
    }
  },
  mounted() {
    this.$EventBus.$off('note-cascader-add', this.handleAddNote)
    this.$EventBus.$on('note-cascader-add', this.handleAddNote)
  },
  beforeDestroy() {
    this.$EventBus.$off('note-cascader-add', this.handleAddNote)
  }
}
</script>
<style lang="stylus" scoped>
.note-tag
  display flex
  flex 1
  flex-direction column
  .note-tag-extend
    flex 1
    display flex
    position relative
    width 100%
    min-height 33px
    margin-top 10px
  .note-tag-panel
    width 100%
    display flex
    flex-wrap wrap
    resize vertical
    padding 5px 15px
    line-height 1.5
    box-sizing border-box
    font-size inherit
    color var(--text-color)
    background-color var(--content-bg-color)
    background-image none
    border 1px solid #DCDFE6
    border-radius 4px

    &:hover
      border-color var(--text-color-light-2)
    &.over-focus
      outline 0
      position absolute
      top 0
      left 0
      z-index 2
      min-width 600px
      max-width 800px
      resize none
      height 7em
      border-color skyblue !important
  .note-tag-select-input
    border none
    outline 0
    padding 0
    color var(--text-color)
    font-size 14px
    appearance none
    height 28px
    background-color transparent
</style>
