<template>
  <div class="ag-card" :class="{'ag-card__flex': type === 'flex'}">
    <div class="ag-card__header">
      <slot name="header" />
    </div>
    <div class="ag-card__body">
      <slot />
    </div>
  </div>
</template>
<script>
export default {
  name: 'AgCard',
  data() {
    return {

    }
  },
  props: {
    type: {
      type: String,
      default: 'flex'
    }
  }
}
</script>
<style lang="stylus" scoped>
.ag-card
  &__header
    margin-bottom 5px
  &__body
    background var(--content-bg-color)
  &__flex
    display flex
    flex-direction column
    .ag-card__body
      overflow hidden
      flex 1
</style>
