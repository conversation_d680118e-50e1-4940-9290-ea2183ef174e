<template>
  <el-select
    v-bind="$attrs"
    v-model="text"
    v-on="$listeners"
    ref="selectRef"
    :popper-class="injectClass"
    :disabled="disabled"
    @change="blurChange"
  >
    <slot></slot>
  </el-select>
</template>
<script>
export default {
  name: 'new-el-select',
  data() {
    return {
    }
  },
  props: {
    value: {
      type: [String, Number, Array],
      default: ''
    },
    injectClass: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    text: {
      get() {
        return this.value
      },
      set(newVal) {
        this.$emit('input', newVal)
      }
    }
  },
  methods: {
    blurChange(newVal) {
      this.$nextTick(() => {
        this.$refs.selectRef.blur()
      })
    }
  }
}
</script>
<style lang="stylus">
.custom-select-height{ 
  .el-select-dropdown__wrap{
    max-height 400px
    .el-select-dropdown__item{
      max-width 1200px
      max-width calc(100vw - 10px)
    }
  }
}
</style>
