<template>
  <span class="rich-text" v-if="value && value.length > 0" :key="componentKey">
    <span
      v-for="(item, index) in value"
      :key="index"
      :class="[{'rich-text-at-highlight': item.type === 2 }, 'rich-text-item']"
    >{{ genText(item) }}</span>
  </span>
</template>

<script>
import keywordHighlightMixin from '@/mixins/keyword-highlight'

export default {
  props: {
    value: Array,
    highlightSelector: {
      type: String,
      default: '.rich-text-item'
    }
  },
  watch: {
    value() {
      // 检测到变化，需要强制重新渲染，否则会导致高亮关键词重复生效
      this.componentKey += 1
    },
    keywordList() {
      this.componentKey += 1
    }
  },
  data() {
    return {
      componentKey: 0
    }
  },
  mixins: [keywordHighlightMixin],
  methods: {
    genText(item) {
      if (item.type === 2) {
        return `@${item.rawText} `
      }

      return item.rawText
    }
  }
}
</script>

<style lang="stylus" scoped>
.rich-text {
  .rich-text-at-highlight {
    color: var(--primary-color);
  }
}
</style>
