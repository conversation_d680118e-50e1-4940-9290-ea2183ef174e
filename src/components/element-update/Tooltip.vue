<template>
  <div :class="[prefixCls]" ref="tooltip">
    <div :class="[prefixCls + '-rel']" ref="reference"><slot></slot></div>
    <transition name="fade">
      <div
        :class="[prefixCls + '-popper', prefixCls + '-' + theme, popoverClass]"
        ref="popper"
        v-show="!disabled && (visible || always)"
        :data-transfer="transfer"
        v-transfer-dom
      >
        <div :class="[prefixCls + '-content']">
          <div :class="[prefixCls + '-arrow']"></div>
          <div :class="innerClasses" :style="innerStyles"><slot v-if="visible" name="content">{{ content }}</slot></div>
        </div>
      </div>
    </transition>
  </div>
</template>
<script>
import Popper from '../../mixins/popper'
import TransferDom from '@/directives/transfer-dom'

import { transferIndex, transferIncrease } from '@/utils/transfer-queue'

const prefixCls = 'ivu-tooltip'
export default {
  name: 'Tooltip',
  directives: { TransferDom },
  mixins: [Popper],
  props: {
    placement: {
      validator(value) {
        return [
          'top',
          'top-start',
          'top-end',
          'bottom',
          'bottom-start',
          'bottom-end',
          'left',
          'left-start',
          'left-end',
          'right',
          'right-start',
          'right-end'
        ].includes(value)
      },
      default: 'bottom'
    },
    content: {
      type: [String, Number],
      default: ''
    },
    delay: {
      type: Number,
      default: 100
    },
    disabled: {
      type: Boolean,
      default: false
    },
    controlled: {
      // under this prop,Tooltip will not close when mouseleave
      type: Boolean,
      default: false
    },
    always: {
      type: Boolean,
      default: false
    },
    transfer: {
      type: Boolean,
      default() {
        return !this.$IVIEW || this.$IVIEW.transfer === ''
          ? false
          : this.$IVIEW.transfer
      }
    },
    theme: {
      validator(value) {
        return ['dark', 'light'].includes(value)
      },
      default: 'dark'
    },
    maxWidth: {
      type: [String, Number]
    },
    maxHeight: {
      type: [String, Number]
    },
    popoverClass: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      prefixCls: prefixCls,
      tIndex: this.handleGetIndex()
    }
  },
  computed: {
    innerStyles() {
      const styles = {}
      if (this.maxWidth) styles['max-width'] = `${this.maxWidth}px`
      if (this.maxHeight) styles['max-height'] = `${this.maxHeight}px`
      return styles
    },
    innerClasses() {
      return [
        `${prefixCls}-inner`,
        {
          [`${prefixCls}-inner-with-width`]: !!this.maxWidth
        }
      ]
    },
    fullClass() {
      return this.full ? 'ivu-tooltip-full' : ''
    }
  },
  watch: {
    content() {
      this.updatePopper()
    }
  },
  methods: {
    handleShowPopper() {
      if (this.timeout) clearTimeout(this.timeout)
      this.timeout = setTimeout(() => {
        this.visible = true
      }, this.delay)
      this.tIndex = this.handleGetIndex()
    },
    handleClosePopper() {
      if (this.timeout) {
        clearTimeout(this.timeout)
        if (!this.controlled) {
          this.timeout = setTimeout(() => {
            this.visible = false
          }, 100)
        }
      }
    },
    handleGetIndex() {
      transferIncrease()
      return transferIndex
    },
    destroyPopper () {
      this.off(this.$refs.tooltip, 'mouseenter', this.handleShowPopper)
      this.off(this.$refs.tooltip, 'mouseleave', this.handleClosePopper)
      this.off(this.$refs.popper, 'mouseenter', this.handleShowPopper)
      this.off(this.$refs.popper, 'mouseleave', this.handleClosePopper)
    },
    off (element, event, handler) {
      if (element && event) {
        document.removeEventListener ? element.removeEventListener(event, handler, false) : element.detachEvent('on' + event, handler)
      }
    },
    on (element, event, handler) {
      if (element && event && handler) {
        document.addEventListener ? element.addEventListener(event, handler, false) : element.attachEvent('on' + event, handler)
      }
    }
  },
  mounted() {
    this.on(this.$refs.tooltip, 'mouseenter', this.handleShowPopper)
    this.on(this.$refs.popper, 'mouseenter', this.handleShowPopper)
    this.on(this.$refs.tooltip, 'mouseleave', this.handleClosePopper)
    this.on(this.$refs.popper, 'mouseleave', this.handleClosePopper)
    if (this.always) {
      this.updatePopper()
    }
  },
  beforeDestroy () {
    if (this.timeout) clearTimeout(this.timeout)
    this.destroyPopper()
  }
}
</script>
<style lang="stylus" scoped>
.ivu-tooltip {
  display: inline-block;

  &.ivu-tooltip-plain {
    .ivu-tooltip-inner {
      background-color: var(--content-bg-color);
      color: var(--text-color-dark-2);
    }
  }

  .ivu-tooltip-rel{
    display: inline-block;
    position: relative;
    width: inherit;
  }

  .ivu-tooltip-popper{
      display: block;
      visibility: visible;
      font-size: 12px;
      line-height: 1.5;
      position: absolute;
      z-index: 1060;

      &[x-placement^="top"] {
        padding: 5px 0 8px 0;
      }
      &[x-placement^="right"] {
        padding: 0 5px 0 8px;
      }
      &[x-placement^="bottom"] {
        padding: 8px 0 5px 0;
      }
      &[x-placement^="left"] {
        padding: 0 8px 0 5px;
      }

      &[x-placement^="top"] .ivu-tooltip-arrow {
        bottom: 3px;
        border-width: 5px 5px 0;
        border-top-color:  rgba(70, 76, 91, .9);
      }
      &[x-placement="top"] .ivu-tooltip-arrow {
        left: 50%;
        margin-left: -5px;
      }
      &[x-placement="top-start"] .ivu-tooltip-arrow {
        left: 16px;
      }
      &[x-placement="top-end"] .ivu-tooltip-arrow {
        right: 16px;
      }

      &[x-placement^="right"] .ivu-tooltip-arrow {
        left: 3px;
        border-width: 5px 5px 5px 0;
        border-right-color:  rgba(70, 76, 91, .9);
      }
      &[x-placement="right"] .ivu-tooltip-arrow {
        top: 50%;
        margin-top: -5px;
      }
      &[x-placement="right-start"] .ivu-tooltip-arrow {
        top: 8px;
      }
      &[x-placement="right-end"] .ivu-tooltip-arrow {
        bottom: 8px;
      }

      &[x-placement^="left"] .ivu-tooltip-arrow {
        right: 3px;
        border-width: 5px 0 5px 5px;
        border-left-color:  rgba(70, 76, 91, .9);
      }
      &[x-placement="left"] .ivu-tooltip-arrow {
        top: 50%;
        margin-top: -5px;
      }
      &[x-placement="left-start"] .ivu-tooltip-arrow {
        top: 8px;
      }
      &[x-placement="left-end"] .ivu-tooltip-arrow {
        bottom: 8px;
      }

      &[x-placement^="bottom"] .ivu-tooltip-arrow {
        top: 3px;
        border-width: 0 5px 5px;
        border-bottom-color:  rgba(70, 76, 91, .9);
      }
      &[x-placement="bottom"] .ivu-tooltip-arrow {
        left: 50%;
        margin-left: -5px;
      }
      &[x-placement="bottom-start"] .ivu-tooltip-arrow {
        left: 16px;
      }
      &[x-placement="bottom-end"] .ivu-tooltip-arrow {
        right: 16px;
      }
  }
  .ivu-tooltip-light .ivu-tooltip-popper{
    display: block;
    visibility: visible;
    font-size: 12px;
    line-height: 1.5;
    position: absolute;
    z-index: 1060;

    &[x-placement^="top"] {
      padding: 7px 0 10px 0;
    }
    &[x-placement^="right"] {
      padding: 0 7px 0 10px;
    }
    &[x-placement^="bottom"] {
      padding: 10px 0 7px 0;
    }
    &[x-placement^="left"] {
      padding: 0 10px 0 7px;
    }

    &[x-placement^="top"] .ivu-tooltip-arrow {
      bottom: 10px - 7px;
      border-width: 7px 7px 0;
      border-top-color: hsla(0,0%,85%,.5);
    }
    &[x-placement="top"] .ivu-tooltip-arrow {
      left: 50%;
      margin-left: -7px;
    }
    &[x-placement="top-start"] .ivu-tooltip-arrow {
      left: 16px;
    }
    &[x-placement="top-end"] .ivu-tooltip-arrow {
      right: 16px;
    }

    &[x-placement^="right"] .ivu-tooltip-arrow {
      left: 10px - 7px;
      border-width: 7px 7px 7px 0;
      border-right-color: hsla(0,0%,85%,.5);
    }
    &[x-placement="right"] .ivu-tooltip-arrow {
      top: 50%;
      margin-top: -7px;
    }
    &[x-placement="right-start"] .ivu-tooltip-arrow {
      top: 8px;
    }
    &[x-placement="right-end"] .ivu-tooltip-arrow {
      bottom: 8px;
    }

    &[x-placement^="left"] .ivu-tooltip-arrow {
      right: 10px - 7px;
      border-width: 7px 0 7px 7px;
      border-left-color: hsla(0,0%,85%,.5);
    }
    &[x-placement="left"] .ivu-tooltip-arrow {
      top: 50%;
      margin-top: -7px;
    }
    &[x-placement="left-start"] .ivu-tooltip-arrow {
      top: 8px;
    }
    &[x-placement="left-end"] .ivu-tooltip-arrow {
      bottom: 8px;
    }

    &[x-placement^="bottom"] .ivu-tooltip-arrow {
      top: 10px - 7px;
      border-width: 0 7px 7px;
      border-bottom-color: hsla(0,0%,85%,.5);
    }
    &[x-placement="bottom"] .ivu-tooltip-arrow {
      left: 50%;
      margin-left: -7px;
    }
    &[x-placement="bottom-start"] .ivu-tooltip-arrow {
      left: 16px;
    }
    &[x-placement="bottom-end"] .ivu-tooltip-arrow {
      right: 16px;
    }

    &[x-placement^="top"] .ivu-tooltip-arrow:after {
      content: " ";
      bottom: 1px;
      margin-left: -7px;
      border-bottom-width: 0;
      border-top-width: 7px;
      border-top-color: var(--border-color-reverse);
    }

    &[x-placement^="right"] .ivu-tooltip-arrow:after {
      content: " ";
      left: 1px;
      bottom: -7px;
      border-left-width: 0;
      border-right-width: 7px;
      border-right-color: var(--border-color-reverse);
    }

    &[x-placement^="bottom"] .ivu-tooltip-arrow:after {
      content: " ";
      top: 1px;
      margin-left: -7px;
      border-top-width: 0;
      border-bottom-width: 7px;
      border-bottom-color: var(--border-color-reverse);
    }

    &[x-placement^="left"] .ivu-tooltip-arrow:after {
      content: " ";
      right: 1px;
      border-right-width: 0;
      border-left-width: 7px;
      border-left-color: var(--border-color-reverse);
      bottom: -7px;
    }
  }
  .ivu-tooltip-inner{
    max-width: 250px;
    // min-height: 34px;
    overflow: auto;
    padding: 8px 12px;
    color:var(--text-color-reverse);
    text-align: left;
    text-decoration: none;
    background-color: rgba(70, 76, 91, .9);
    border-radius: 4px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, .2);
    white-space: nowrap;

    &-with-width{
      white-space: pre-line;
      text-align: justify;
      word-break: break-all;
    }
  }

  .ivu-tooltip-light .ivu-tooltip-inner{
    background-color: var(--content-bg-color);
    color: #515a6e;
  }

  .ivu-tooltip-arrow{
    position: absolute;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
  }

  .ivu-tooltip-light {
    .ivu-tooltip-arrow{
      &:after{
        display: block;
        width: 0;
        height: 0;
        position: absolute;
        border-color: transparent;
        border-style: solid;
        content: "";
        border-width: 7px;
      }
      border-width: 8px;
    }
  }
  .ivu-tooltip-light{
    .ivu-tooltip-arrow{
      border-bottom-color #e7e7e7 !important
    }
  }
}
</style>
