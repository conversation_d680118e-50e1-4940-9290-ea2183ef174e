<template>
  <div
    v-if="useFocusPanel"
    class="ag-focus-panel"
    v-click-outside="close"
    :style="{
      width,
      height
    }"
  >
    <div class="panel" :class="{'over-focus': showFocusPanel}">
      <slot></slot>
    </div>
    <span class="focus-icon" @click="toggle">
      <i v-if="showFocusPanel" class="el-icon-arrow-up"></i>
      <i v-if="!showFocusPanel" class="el-icon-arrow-down"></i>
    </span>
  </div>
  <div v-else>
    <slot></slot>
  </div>
</template>
<script>
import clickOutside from '@/directives/click-outside'
export default {
  name: 'AgFocusPanel',
  directives: {
    clickOutside
  },
  data() {
    return {
      showFocusPanel: false
    }
  },
  props: {
    useFocusPanel: {
      type: Boolean,
      default: true
    },
    width: String,
    height: String
  },
  methods: {
    toggle() {
      this.showFocusPanel = !this.showFocusPanel
    },
    close() {
      this.showFocusPanel = false
    }
  }
}
</script>
<style lang="stylus" scoped>
// 固定宽高
.ag-focus-panel
  width 100%
  height 100%
  position relative
  padding 0 0 0 10px
  .panel
    width 100%
    height 100%
    overflow hidden
    box-sizing border-box
    border 1px solid transparent
  .over-focus
    overflow initial
    position absolute
    top 0
    left 0
    display inline-block
    background-color var(--white)
    border 1px solid var(--blue)
    z-index 2
    min-height 7em
    border-radius 4px
    // 跟外容器的左侧padding一致
    padding 0 0 0 10px
  .focus-icon
    color var(--blue)
    position absolute
    top 2px
    right 0
    width 30px
    height 28px
    display flex
    align-items center
    justify-content center
    z-index 3
    cursor pointer
</style>
