<template>
  <NewElCascader
    ref="cascader"
    v-bind="$attrs"
    :popper-class="popperClass"
    :value="cascaderVal"
    :options="options"
    :props="cascaderProps"
    :filterMethod="filterMethod"
    @visible-change="handleVisibleChange"
    @input="handleInput"
  >
    <template slot-scope="{ node, data }">
      <span>{{ data.label }}</span>
      <span v-if="node.data.rule && node.isLeaf" style="float: right">
        <el-popover
          placement="right"
          title="规则描述"
          width="500"
          trigger="hover"
        >
          <div style="max-height: 400px; overflow-y: auto; white-space:pre-wrap;">{{ node.data.rule }}</div>
          <i slot="reference" class="el-icon-info" />
        </el-popover>
      </span>
    </template>
  </NewElCascader>
</template>

<script>
import NewElCascader from './casc/cascader/src/cascader'

export default {
  name: 'Cascader',
  model: {
    prop: 'value',
    event: 'input'
  },
  props: {
    value: {
      type: [Array, String, Number],
      default: ''
    },
    options: {
      type: Array,
      default() {
        return []
      }
    },
    cascaderProps: {
      type: Object,
      default() {
        return {}
      }
    },
    filterMethod: {
      type: Function,
      default: (node, keyword) => node.text.toLowerCase().includes(keyword.toLowerCase())
    },
    popperClass: String
  },
  components: {
    NewElCascader
  },
  computed: {
    cascaderVal: {
      get: function () {
        return this.value
      },
      set: function (newValue) {
        this.$emit('input', newValue)
      }
    }
  },
  methods: {
    handleVisibleChange(val) {
      this.$EventBus.$emit('note-cascader-show', val)
    },
    handleInput(val) {
      this.cascaderVal = val
    }
  }
}
</script>
