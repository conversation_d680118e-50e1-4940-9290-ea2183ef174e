<template>
  <div class="input-tag-array" @keydown.enter.stop>
    <el-select
      style="width: 100%;"
      :value="selectedTags"
      multiple
      filterable
      allow-create
      default-first-option
      placeholder="请选择文章标签"
      @change="handleChange"
    >
      <el-option
        v-for="(item, idx) in options"
        :key="idx"
        :label="item.name"
        :value="item.name"
      ></el-option>
    </el-select>
  </div>
</template>
<script>
import notify from '@/lib/notify'
import { libraryApi } from '@/api'
import { difference } from 'lodash-es'

export default {
  name: 'InputTagArray',
  model: {
    prop: 'tags',
    event: 'input'
  },
  data() {
    return {
      options: []
    }
  },
  components: {
  },
  props: {
    tags: {
      type: Array,
      default() {
        return []
      }
    },
    maxLimit: {
      type: Number,
      default: 10
    }
  },
  computed: {
    placeholder() {
      if (this.tags.length > 0) {
        return ''
      } else return '将录入的内容提炼关键词作为标签'
    },
    selectedTags() {
      return this.tags.map(item => item.tag_name)
    }
  },
  methods: {
    handleChange(nextVal) {
      const removeText = difference(this.selectedTags, nextVal)[0]
      const addText = difference(nextVal, this.selectedTags)[0]
      if (addText) {
        this.handleAddCustomNote(addText)
      }
      if (removeText) {
        this.handleDeleteNote(removeText)
      }
    },
    handleAddCustomNote(text) {
      if (this.tags.length >= this.maxLimit) {
        notify.warning(`最多添加${this.maxLimit}个标签`, 1500)
      } else if (this.tags.some((item) => item.tag_name === text)) {
        notify.warning('不能添加已有关键词', 1500)
      } else {
        this.tags.push({
          tag_name: text,
          hitState: false,
          closable: true
        })
      }
    },
    handleDeleteNote(text) {
      const idx = this.tags.findIndex(item => item.tag_name === text)
      if (this.tags[idx].closable) {
        this.tags.splice(idx, 1)
      } else {
        notify.warning('没有权限删除该关键词', 1500)
      }
    },
    getTags() {
      libraryApi
        .getUserInfo()
        .then((res) => {
          this.options = res?.data?.tags || []
        })
        .catch((_) => {
          this.options = []
        })
    }
  },
  created() {
    this.getTags()
  }
}
</script>
<style lang="stylus" scoped>
.input-tag-array
  display flex
  flex 1
  width 100%
  &__extend
    flex 1
    display flex
    position relative
    width 0
  &__dropdown
    // width 600px
    width 100%
    height 300px
    background #fff
    border-radius 4px
    border 1px solid #DCDFE6
    box-sizing border-box
    color #606266
    display inline-block
    font-size inherit
    z-index 10
    position absolute
    top calc(100% + 5px)
    left 0
    overflow-y auto
  &__panel
    width 100%
    padding 5px 15px
    line-height 1.5
    box-sizing border-box
    font-size inherit
    color var(--text-color)
    background-color var(--content-bg-color)
    background-image none
    border 1px solid #DCDFE6
    border-radius 4px
    height 50px
    overflow-x auto
    overflow-y hidden
    display flex
    align-items center
    flex-wrap nowrap
    white-space nowrap
    &:hover
      border-color var(--text-color-light-2)
  &__content
    display flex
    flex-shrink 0
    flex-wrap nowrap
    white-space nowrap
  &__select-input
    border none
    outline 0
    padding 0
    color var(--text-color)
    font-size 14px
    appearance none
    height 28px
    background-color transparent
    float left
    flex 1
    &::placeholder
      color var(--text-color-light-2)
</style>
