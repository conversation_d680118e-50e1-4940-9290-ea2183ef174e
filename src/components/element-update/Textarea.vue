<template>
  <div class="ag-textarea">
    <el-input
      type="textarea"
      ref="textarea"
      v-model="text"
      :placeholder="placeholder"
      :rows="rows"
      :size="size"
      :class="{'over-focus' :showOverInput}"
      @focus="showOverInput = true"
      @blur="showOverInput = false"
      @keydown.enter.stop
      :disabled="disabled"
      >
    </el-input> 
  </div>
</template>
<script>
export default {
  name: 'AgTextarea',
  data() {
    return {
      showOverInput: false
    }
  },
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    rows: {
      type: Number,
      default: 1
    },
    placeholder: {
      type: String,
      default: ''
    },
    size: {
      type: String,
      default: 'mini'
    }
  },
  computed: {
    text: {
      get() {
        return this.value
      },
      set(newVal) {
        this.$emit('input', newVal)
      }
    }
  }
}
</script>
<style lang="stylus">
.ag-textarea{
  .over-focus{
    width 100%
    textarea {
      outline 0
      position absolute
      top 0
      left 0
      z-index 2
      min-width 500px
      max-width 800px
      resize none
      height 7em
    }
  }
}
</style>
<style lang="stylus" scoped>
.ag-textarea{
  position relative
  flex 1
  letter-spacing normal
  word-spacing normal
  text-rendering auto
  height 100%
  .el-textarea{
    position initial
    width 100%
  }
}
</style>
