<template>
  <div class="rich-input" @click.prevent="onFocus">
    <el-input
      v-show="isFocus"
      ref="elInput"
      :disabled="disabled"
      v-bind="$attrs"
      :value="rawInputText"
      v-on="listeners"
    >
    </el-input>
    <div
      v-show="!isFocus"
      :class="[
        'el-textarea__inner',
        { 'rich-input-disabled': disabled },
        'rich-input-detail'
      ]"
    >
      <RichText
        :value="value"
        :keywordList="keywordList"
      >
      </RichText>
    </div>
    <at-popup
      ref="atPopup"
      :input-el="inputEl"
      :request="request"
      :internal="true"
      @select="onSelect"
    />
  </div>
</template>

<script>
import axios from 'axios'
import RichText from './RichText.vue'

/**
 * value 格式
 * [{
 *  rawText: '',
 *  type: 1,
 *  bizId: ''
 * }]
 */
export default {
  components: {
    RichText
  },
  data() {
    return {
      inputEl: null,
      isFocus: false,
      rawInputText: '',
      usernameIdMap: {}
    }
  },
  inheritAttrs: false,
  props: {
    value: {
      type: Array,
      require: true
    },
    uid: {
      type: String | Number,
      require: true
    },
    disabled: {
      type: Boolean,
      defautl: false
    },
    keywordList: Array
  },
  mounted() {
    this.inputEl = this.$refs.elInput.$refs[this.$attrs.type]
  },
  watch: {
    value: {
      handler(value) {
        this.rawInputText = this.richToRawText(value || [])
      },
      immediate: true
    }
  },
  computed: {
    listeners() {
      return {
        ...this.$listeners,
        input: this.onInput,
        blur: this.onBlur
      }
    }
  },
  methods: {
    richToRawText(richValue) {
      return richValue.map(item => {
        if (item.type === 2) {
          this.usernameIdMap[item.rawText] = item.bizId

          return `@${item.rawText} `
        }

        return item.rawText
      }).join('')
    },
    rawTextToRich(rawText) {
      const raws = rawText.split(/(@[^@\s]+[^\S\r\n\f\t\v])/)

      return raws
        .filter(item => item !== '')
        .map(item => {
          const match = /^@([^@\s]+)[^\S\r\n\f\t\v]$/.exec(item)

          if (match && match[1]) {
            const username = match[1]
            const uid = this.usernameIdMap[username]

            if (uid) {
              return {
                rawText: username,
                type: 2,
                bizId: uid
              }
            }
          }

          return {
            rawText: item,
            type: 1,
            bizId: ''
          }
        })
    },
    request(url, keyword) {
      return new Promise((resolve, reject) => {
        const params = {
          uid: this.uid
        }

        if (keyword) {
          params.keyword = keyword
        }

        axios.get(url, {
          params
        })
        .then((res) => {
          if (res.status === 200 && res.data.code === 0) {
            resolve(res.data.data.groups)
          } else {
            resolve([])
          }
        })
        .catch(reject)
      })
    },
    onSelect({ item, matchString }) {
      this.rawInputText =
        this.rawInputText.slice(0, this.inputEl.selectionEnd - matchString.length) +
        `@${item.uname} ` +
        this.rawInputText.slice(this.inputEl.selectionEnd)

      this.usernameIdMap[item.uname] = String(item.uid)

      this.$emit('input', this.rawTextToRich(this.rawInputText))
    },
    onInput(value) {
      this.$emit('input', this.rawTextToRich(value))
    },
    onFocus() {
      if (this.disabled) {
        return
      }
      this.isFocus = true
      this.$nextTick(() => {
        this.inputEl && this.inputEl.focus()
      })
    },
    onBlur() {
      this.isFocus = false
    }
  }
}
</script>

<style lang="stylus" scoped>
.rich-input {
  position: relative;
  width: 100%;
  .rich-input-detail {
    overflow-y: scroll;
    width: 100%;
    word-break: break-word;
    white-space: pre-line;
    min-height: 33px; // 最小展示一行的高度
    max-height: 270px; // 最大展示12行半，最后多半行是为了提示用户后续还有内容
  }
  .rich-input-disabled {
    background-color: var(--bg-color);
    border-color: var(--border-color-light-1);
    cursor: not-allowed;
  }
}
</style>
