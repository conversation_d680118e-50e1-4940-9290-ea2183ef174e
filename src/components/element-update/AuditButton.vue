<template>
  <div
    class="audit-button"
    :class="[
      type ? 'audit-button--' + type : '',
      buttonSize ? 'audit-button--' + buttonSize : '',
      {
        'is-disabled': disabled && !active,
        'is-active': !disabled && active,
        'is-plain': !disabled && !active,
        'is-disabled-active': disabled && active
      }
    ]"
    @click="$emit('click')"
  >
    <span v-if="$slots.default"><slot></slot></span>
  </div>
</template>
<script>
export default {
  name: 'AuditButton',
  props: {
    type: {
      type: String,
      default: 'default'
    },
    size: String,
    icon: {
      type: String,
      default: ''
    },
    buttonSize: {
      type: String,
      default: 'small'
    },
    active: {
      type: Boolean,
      default: false
    },
    disabled: <PERSON>olean
  }
}
</script>
<style lang="stylus" scoped>
.audit-button
    display inline-block
    line-height 1
    white-space nowrap
    cursor pointer
    background var(--content-bg-color)
    border 1px solid #DCDFE6
    color var(--text-color)
    text-align center
    box-sizing border-box
    outline 0
    margin 0
    transition .1s
    font-weight 500
    padding 12px 20px
    font-size 14px
    border-radius 4px
    user-select none
    &--small
        padding 9px 15px
        font-size 12px
        border-radius 3px
    &--success
        // 禁用&&未选中
        &.is-disabled
          // color #a4da89
          // background-color #f0f9eb
          // border-color #e1f3d8
          color var(--text-color-light-2)
          background-color var(--content-bg-color)
          border-color var(--border-color-light-2)
          cursor not-allowed
          &:hover
            // color #a4da89
            // background-color #f0f9eb
            // border-color #e1f3d8
            color var(--text-color-light-2)
            background-color var(--content-bg-color)
            border-color var(--border-color-light-2)
            cursor not-allowed
        // 禁用&&已选中
        &.is-disabled-active
            cursor not-allowed
            color var(--text-color-reverse)
            background-color #b3e19d
            border-color #b3e19d
            &:hover
                color var(--text-color-reverse)
                background-color #b3e19d
                border-color #b3e19d
        &.is-plain
            color var(--success-color)
            background var(--content-bg-color)
            border-color #c2e7b0
        &.is-active
            color var(--text-color-reverse)
            background-color var(--success-color)
            border-color var(--success-color)
        &:hover
            background #85ce61
            border-color #85ce61
            color var(--text-color-reverse)
    &--warning
        // 禁用&&未选中
        &.is-disabled
            // color #f0c78a
            // background #fdf6ec
            // border-color #faecd8
            color var(--text-color-light-2)
            background-color var(--content-bg-color)
            border-color var(--border-color-light-2)
            cursor not-allowed
            &:hover
              // color #f0c78a
              // background #fdf6ec
              // border-color #faecd8
              color var(--text-color-light-2)
              background-color var(--content-bg-color)
              border-color var(--border-color-light-2)
              cursor not-allowed
        &.is-disabled-active
            cursor not-allowed
            color var(--text-color-reverse)
            background-color #f3d19e
            border-color #f3d19e
            &:hover
                color var(--text-color-reverse)
                background-color #f3d19e
                border-color #f3d19e
        &.is-plain
            color var(--warning-color)
            background var(--content-bg-color)
            border-color #f5dab1
        &.is-active
            color var(--text-color-reverse)
            background-color var(--warning-color)
            border-color var(--warning-color)
        &:hover
            background #ebb563
            border-color #ebb563
            color var(--text-color-reverse)
    &--danger
        // 禁用&&未选中
        &.is-disabled
            // color #f9a7a7
            // background #fef0f0
            // border-color #fde2e2
            color var(--text-color-light-2)
            background-color var(--content-bg-color)
            border-color var(--border-color-light-2)
            cursor not-allowed
            &:hover
              // color #f9a7a7
              // background #fef0f0
              // border-color #fde2e2
              color var(--text-color-light-2)
              background-color var(--content-bg-color)
              border-color var(--border-color-light-2)
              cursor not-allowed
        &.is-disabled-active
            cursor not-allowed
            color var(--text-color-reverse)
            background-color #fab6b6
            border-color #fab6b6
            &:hover
                color var(--text-color-reverse)
                background-color #fab6b6
                border-color #fab6b6
        &.is-plain
            color var(--error-color)
            background var(--content-bg-color)
            border-color #fbc4c4
        &.is-active
            color var(--text-color-reverse)
            background-color var(--error-color)
            border-color var(--error-color)
        &:hover
            background #f78989
            border-color #f78989
            color var(--text-color-reverse)
</style>
