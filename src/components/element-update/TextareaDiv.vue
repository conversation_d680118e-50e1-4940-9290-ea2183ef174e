<template>
  <div
    v-bind="$attrs"
    v-on="$listeners"
    :style="{ 'height': `${rows * 21}px`, 'width': `${width}px` }"
  >
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'TextareaDiv',
  props: {
    rows: {
      type: Number,
      default: 1
    },
    width: {
      type: Number,
      default: 180
    }
  }
}
</script>

<style lang="stylus" scoped>
div {
  border 1px solid var(--border-color-light-1)
  border-radius 4px
  word-break break-all
  padding 5px 15px
  overflow-y auto
  min-width 120px
  line-height 22px
  white-space pre-line
}
</style>
