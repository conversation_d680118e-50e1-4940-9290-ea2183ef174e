<template>
  <div class="block-topbar">
    <div class="topbar-left">
      <div class="breadcrumb">
        <span class="bread">{{ title }}</span>
        <span class="bread" style="padding: 0px 8px">/</span>
        <span class="bread active-bread" style="margin-right: 20px">
          {{ subTitle }}
        </span>
      </div>
      <slot></slot>
    </div>
    <div>
      <el-button @click="showFilter" class="collapse-button" size="medium">
        过滤条件
        <i
          class="el-icon-arrow-up collapse"
          :style="{ transform: !collapse ? 'rotate(-180deg)' : 'unset' }"
        ></i>
      </el-button>
    </div>
  </div>
</template>
<script>
export default {
  name: 'Topbar',
  data() {
    return {
    }
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    subTitle: {
      type: String,
      default: ''
    },
    collapse: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    showFilter() {
      this.$emit('toggle', !this.collapse)
      localStorage.collapse = this.collapse
    }
  }
}
</script>

<style lang="stylus" scoped>
.block-topbar
	box-sizing border-box
	display flex
	justify-content space-between
	padding 18px
	align-items center
	background #fff
	margin-bottom 8px
	.breadcrumb
		.bread
			color #c0c4cc
			font-size 14px
			font-weight 500
			cursor pointer
		.active-bread
			color #303133
	.collapse-button
		align-self center
  .topbar-left
		display flex
		align-items center
</style>
