<template>
  <div class="player-box">
    <el-row type="flex">
      <el-col :span="16" style="height: 100%; width: 100%">
        <NanoModulePlayer
          :class="{ 'video-player': player === 'origin' }"
          :src="player === 'origin' ? url : undefined"
          :aid="player === 'iframe' ? currentAid : undefined"
          :cid="player === 'iframe' ? currentCid : undefined"
          :styleConfig="{
            minWidth: hideList ? undefined : 620
          }"
        />
      </el-col>
      <el-col class="list" :span="8" v-if="!hideList">
        <ul class="play-ul">
          <li
            v-for="(item, index) in list"
            :key="index"
            @click="clickLi(item, index)"
            :class="[
              {
                active: index === activeIdx
              }
            ]"
          >
            {{ item.title }}
          </li>
        </ul>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import NanoModulePlayer from '@/components/package/VideoPlayer/NanoModulePlayer'

export default {
  components: {
    NanoModulePlayer
  },
  props: {
    list: {
      type: Array,
      default: () => {
        return []
      }
    },
    hideList: {
      type: Boolean,
      default: false
    },
    player: {
      type: String,
      default: 'iframe' // 有 'origin' 'iframe' 两种
    },
    url: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      currentAid: undefined,
      currentCid: undefined,
      activeIdx: 0
    }
  },
  mounted() {
    window.Html5IframeInitialized = () => {
      if (window.player && this.list) {
        window.player.setSegmented(this.list)
      }
    }
  },
  methods: {
    clickLi(item, index) {
      this.currentAid = item.aid
      this.currentCid = item.cid
      this.activeIdx = index
    },
    autoPlayFirst() {
      this.$nextTick(() => {
        this.currentAid = this.list?.[0]?.aid
        this.currentCid = this.list?.[0]?.cid
        this.activeIdx = 0
      })
    }
  }
}
</script>
<style lang="stylus">
.player-box {
	height 500px
	.el-row {
		height inherit
	}
	.list {
		height 100%
		overflow auto
		ul {
			height 100%
			padding 0px 8px 8px
			li {
				width 100%
				height 30px
				margin-bottom 8px
				padding: 8px
				box-sizing border-box
				background var(--bg-color)
				border-radius 2px
				font-size 14px
				color var(--text-color)
				text-align center
				line-height 16px
				overflow hidden
				white-space nowrap
				text-overflow ellipsis
				cursor pointer
				&:hover {
					color var(--link-color)
				}
			}
			.active {
				color var(--link-color)
			}
		}
	}
}
</style>
