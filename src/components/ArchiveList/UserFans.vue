<template>
<span
  :style="styles">
  {{fans}}
</span>
</template>

<script>
const COLOR_ARRAY = [
  { min: 0, max: 10000, color: 'inherit' },
  { min: 10000, max: 10 * 10000, color: 'var(--blue)' },
  { min: 10 * 10000, max: 100 * 10000, color: 'var(--orange)' },
  { min: 100 * 10000, max: Infinity, color: 'var(--red)' }
]
export default {
  props: {
    fans: {
      type: [Number, String],
      default: 0
    }
  },
  computed: {
    styles() {
      const { fans } = this
      return {
        color: COLOR_ARRAY.find(item => item.min <= fans && fans < item.max).color,
        fontWeight: fans <= 10000 ? 'inherit' : 'bold'
      }
    }
  }
}
</script>
