<template>
<span :class="$style.container">
  <span v-if="official.role > 0" :class="{
    [$style.verifyIcon]: true,
    [$style.individual]: roleInfo.isIndividual,
    [$style.enterprise]: roleInfo.isEnterprise
  }" :title="roleInfo.title"></span>
  <span
    v-for="group in allGroups"
    :title="group.group_name"
    :key="group.id"
    :class="colorClass[group.group_id]"
  >{{group.group_short_tag || group.group_tag}}</span>
</span>
</template>

<script>
import { INDIVIDUAL_LIST, ENTERPRISE_LIST } from '@/pages/workbench/constants'

// 特殊用户组列表
const LIST_USER_TYPE = [1, 2, 3, 5]

export default {
  props: {
    userGroups: {
      type: Array,
      default() {
        return []
      }
    },
    official: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {

    }
  },
  computed: {
    allGroups() {
      return this.userGroups.filter(group => LIST_USER_TYPE.includes(group.group_id)).sort()
    },
    roleInfo() {
      const { role } = this.official
      const isIndividual = INDIVIDUAL_LIST.includes(role)
      const isEnterprise = ENTERPRISE_LIST.includes(role)
      return {
        isIndividual,
        isEnterprise,
        title: (isIndividual && '个人认证') ||
               (isEnterprise && '企业认证') ||
               ''
      }
    },
    colorClass() {
      const baseClass = [this.$style.groupIcon]
      return {
        1: [...baseClass, this.$style.priority],
        2: [...baseClass, this.$style.danger],
        3: [...baseClass, this.$style.pgc],
        5: [...baseClass, this.$style.politic]
      }
    }
  }
}
</script>

<style lang="stylus" module>
.container
	display inline-flex
	align-items center
	vertical-align top
	word-break break-word
.type
	margin-right 1px
	font-size 12px
.groupIcon
	display inline-block
.priority
	color #67c23a
	background-color #d1edc3
	background-color rgba(163, 219, 135, 0.5)
.danger
	color #ee5037
	background-color #f0b3b3
	background-color rgba(204, 0, 0, 0.3)
.pgc
	color #409eff
	background-color #b3b3f0
	background-color rgba(0, 0, 204, 0.3)
.politic
	color #e6a23c
	background-color #f0dab3
	background-color rgba(204, 132, 0, 0.3)
.verifyIcon
	display inline-block
	width 18px
	height 18px
	background-image url('@/assets/user-auth.png')
.individual
	background-position -39px -82px
.enterprise
	background-position -5px -82px

</style>
