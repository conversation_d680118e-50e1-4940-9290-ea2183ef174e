<template>
<div :class="$style.userInfo">
  <UserIcon :userGroups="userGroups" :official="official"/><slot v-if="userGroups"></slot>
  <div v-if="fans">粉丝数:<UserFans :fans="fans"/></div>
</div>
</template>

<script>
import UserFans from './UserFans'
import UserIcon from './UserIcon'

export default {
  props: {
    userGroups: {
      type: Array,
      default() {
        return []
      }
    },
    official: {
      type: Object,
      default() {
        return {}
      }
    },
    fans: [String, Number]
  },
  components: {
    UserFans,
    UserIcon
  }
}
</script>

<style lang="stylus" module>
.userInfo
  word-break break-word
</style>
