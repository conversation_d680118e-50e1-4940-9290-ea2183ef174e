.vue-treeselect__menu{
  background var(--content-bg-color)
  border 1px solid var(--border-color-light-2)
  padding 12px
}
.vue-treeselect__menu-container{
  border: 1px solid rgb(220, 223, 230);
  padding: 8px;
  margin-bottom: 12px;
  overflow-y: hidden;
  margin: 0
  display: flex;
  align-items: center;
  .vue-treeselect__multi-value-label{
    color: rgb(144, 147, 153);
    line-height: 30px;
    font-size: 14px;
  }
  .vue-treeselect__multi-value-item-wrapper{
    padding 0
  }
}
.vue-treeselect__multi-value-label__select{
      background-color: #ecf5ff;
    border-color: #d9ecff;
    display: inline-block;
    height: 32px;
    padding: 0 10px;
    line-height: 30px;
    font-size: 12px;
    color: var(--link-color);
    border-width: 1px;
    border-style: solid;
    border-radius: 4px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    white-space: nowrap;
    height: 24px;
    padding: 0 8px;
    line-height: 22px;
}
.vue-treeselect__multi-value-label {
  display: inline-block;
  // margin: $treeselect-multi-value-margin-y $treeselect-multi-value-margin-x;
  // padding: $treeselect-multi-value-padding-y $treeselect-multi-value-padding-x;
  vertical-align: middle;
}
.vue-treeselect__multi-value-label__close{
  border-radius: 50%;
  text-align: center;
  position: relative;
  cursor: pointer;
  font-size: 12px;
  height: 16px;
  width: 16px;
  line-height: 16px;
  vertical-align: middle;
  top: -1px;
  right: -5px;
}
.vue-treeselect__label{
  font-size 14px !important
  &--disabled {
    background rgb(239, 242, 241) !important
    border 0px !important
  }
}
.vue-treeselect__multi-value-item-wrapper{
  margin-right 10px
}
.vue-treeselect__menu-container{
  display inline-block
  white-space nowrap
  width 100%
}