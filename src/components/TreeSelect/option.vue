<template>
  <div :class="[ 'vue-treeselect__list-item', {
        'vue-treeselect__option--root': node.isRootNode,
        'vue-treeselect__option--child': !node.isRootNode,
        'vue-treeselect__option--parent': node.children
      } ]">
    <div :class="[ 'vue-treeselect__option', {
        'vue-treeselect__option--disabled': node.disabled,
        'vue-treeselect__option--root': node.isRootNode,
        'vue-treeselect__option--child': !node.isRootNode,
        'vue-treeselect__option--selected': instance.isSelected(node),
        'vue-treeselect__option--matched': instance.searching && node.isMatched,
        'vue-treeselect__option--hide': instance.searching && !(node.isMatched || node.hasMatchedChild),
      } ]">
      <div v-if="node.isLeaf" class="vue-treeselect__option-arrow-placeholder">&nbsp;</div>
      <div v-else class="vue-treeselect__option-arrow-wrapper" @mousedown="handleMouseDownOnOptionArrow">
        <transition name="vue-treeselect__option-arrow--prepare" appear>
          <span :class="[ 'vue-treeselect__option-arrow', {
            'vue-treeselect__option-arrow--rotated': shouldExpand,
          } ]"></span>
        </transition>
      </div>
      <div class="vue-treeselect__label-wrapper" @mousedown="handleMouseDownOnOption">
        <div v-if="instance.multiple && !instance.disableBranchNodes && !instance.readOnly" class="vue-treeselect__checkbox-wrapper">
          <span v-if="!node.isDisabled" :class="[ 'vue-treeselect__checkbox', {
            'vue-treeselect__checkbox--checked': checkedState === CHECKED,
            'vue-treeselect__checkbox--indeterminate': checkedState === INDETERMINATE,
            'vue-treeselect__checkbox--unchecked': checkedState === UNCHECKED
          }]">
            <span class="vue-treeselect__checkbox-mark"></span>
          </span>
        </div>
        <label class="vue-treeselect__label" :class="[{
          'vue-treeselect__label--disabled': node.isDisabled
        }]">
          <slot name="option" :option="node" :instance="instance">
            {{ node.name }}
            <span v-if="node.raw.subName" class="vue-treeselect__subname">{{node.raw.subName}}</span>
            <span v-if="node.isBranch" class="vue-treeselect__count">
              <template v-if="!instance.searching && instance.showCount">({{ node.count[instance.showCountOf] }})</template>
              <template v-else-if="instance.searching && instance.showCountOnSearchComputed">({{ instance.searchingCount[node.id][instance.showCountOf] }})</template>
            </span>
          </slot>
        </label>
      </div>
    </div>
    <div
      v-if="shouldExpand"
      class="vue-treeselect__list">
      <template v-if="node.isLoaded">
        <template v-if="node.children.length">
          <vue-treeselect--option
            v-for="childNode in node.children"
            :node="childNode"
            :key="childNode.id"
            />
        </template>
        <div v-else class="vue-treeselect__no-children-tip">
          <div class="vue-treeselect__icon-wrapper"><span class="vue-treeselect__icon-warning"></span></div>
          <span class="vue-treeselect__no-children-tip-text">{{ instance.noChildrenText }}</span>
        </div>
      </template>
      <div v-else-if="node.isPending" class="vue-treeselect__loading-tip">
        <div class="vue-treeselect__icon-wrapper"><span class="vue-treeselect__icon-loader"></span></div>
        <span class="vue-treeselect__loading-tip-text">{{ instance.loadingText }}</span>
      </div>
      <div v-else-if="node.loadingChildrenError" class="vue-treeselect__error-tip">
        <div class="vue-treeselect__icon-wrapper"><span class="vue-treeselect__icon-error"></span></div>
        <span class="vue-treeselect__error-tip-text">
          {{ node.loadingChildrenError }}
          <a class="vue-treeselect__retry" @click="instance.loadOptions(false, node)" :title="instance.retryTitle">
            {{ instance.retryText }}
          </a>
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import { onlyOnLeftClick } from './utils'

export default {
  name: 'vue-treeselect--option',
  inject: ['instance', 'UNCHECKED', 'INDETERMINATE', 'CHECKED'],
  props: {
    node: {
      type: Object,
      required: true
    }
  },

  computed: {
    checkedState() {
      return this.instance.nodeCheckedStateMap[this.node.id]
    },

    shouldExpand() {
      if (!this.node.isBranch) return false
      return this.instance.searching
        ? this.node.expandsOnSearch
        : this.node.isExpanded
    }
  },

  watch: {
    'node.isExpanded': function nodeIsExpanded(newValue) {
      if (newValue === true && !this.node.isLoaded) {
        // load children when expanded
        this.instance.loadOptions(false, this.node)
      }
    }
  },

  methods: {
    handleMouseDownOnOption: onlyOnLeftClick(function handleMouseDownOnOptionArrow() {
      if (this.node.isBranch && this.instance.disableBranchNodes) {
        this.instance.toggleExpanded(this.node)
      } else {
        if (!this.node.isDisabled) {
          this.instance.select(this.node)
        }
      }
    }),

    handleMouseDownOnOptionArrow: onlyOnLeftClick(function handleMouseDownOnOptionArrow() {
      this.instance.toggleExpanded(this.node)
    })
  }
}
</script>
