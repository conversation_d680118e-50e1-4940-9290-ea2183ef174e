<template>
<!-- 分区选择组件 -->
  <VueTreeSelect
    ref="treeSelect"
    innerClass="treeselect-category"
    :placeholder="placeholder"
    :load-root-options="loadRootOptions"
    :options="options"
    v-model="modelVal"
    v-bind="$attrs"
    />
</template>

<script>
import VueTreeSelect from './TreeSelect'

export default {
  name: 'CategorySelect',
  components: { VueTreeSelect },
  props: {
    value: null,
    placeholder: {
      type: String,
      default: '选择分区'
    },
    loadRootOptions: {
      type: Function,
      default: () => {}
    },
    options: {
      type: Array
    }
  },
  computed: {
    modelVal: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  methods: {
    clear() {
      const treeSelectEl = this.$refs.treeSelect
      treeSelectEl && treeSelectEl.clear()
    }
  }
}
</script>
