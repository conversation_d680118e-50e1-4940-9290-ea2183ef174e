.vue-treeselect-helper-zoom-effect-off
	transform none !important
@keyframes vue-treeselect-animation-fade-in {
  0% {
    opacity 0
  }
}
@keyframes vue-treeselect-animation-bounce {
  0%,
  100% {
    transform scale(0)
  }
}
@keyframes vue-treeselect-animation-rotate {
  100% {
    transform rotate(360deg)
  }
}
.vue-treeselect-transition-fade-enter-active
	transition 200ms opacity ease-out-circ 50%
	transform scale(1)
.vue-treeselect-transition-fade-leave-active
	transition 200ms opacity ease-in-circ
.vue-treeselect-transition-fade-enter, .vue-treeselect-transition-fade-leave-to
	opacity 0
.vue-treeselect__multi-value-item--zoom-enter-active, .vue-treeselect__multi-value-item--zoom-leave-active
	transition-duration 200ms
	transition-property transform, opacity
.vue-treeselect__multi-value-item--zoom-enter-active
	transition-timing-function ease-out-circ
.vue-treeselect__multi-value-item--zoom-leave-active
	transition-timing-function ease-out-cubic
	position absolute
.vue-treeselect__multi-value-item--zoom-enter, .vue-treeselect__multi-value-item--zoom-leave-to
	transform scale(0.7)
	opacity 0
.vue-treeselect__multi-value-item--zoom-move
	transition 200ms transform ease-out-quart
.vue-treeselect
	position relative
	text-align left
	line-height 20px
	margin-top 4px
	letter-spacing 0
	&:not(.vue-treeselect--disabled):not(.vue-treeselect--focused)
		.vue-treeselect__control
			&:hover
				border-color #cfcfcf
	&:not(.vue-treeselect--disabled)
		.vue-treeselect__arrow-wrapper
			&:hover
				.vue-treeselect__arrow
					border-top-color black
	.vue-treeselect__option
		.vue-treeselect__label
			margin-bottom 0
			letter-spacing 0
	&.vue-treeselect--single
		font-weight normal
		letter-spacing normal
		.vue-treeselect__option
			&.vue-treeselect__option--root
				cursor default
				.vue-treeselect__label
					cursor default
					margin-bottom 0
					letter-spacing 0
			&.vue-treeselect__option--child
				&.vue-treeselect__option--selected
					label
						color #fff
						background #288afa
						border 1px solid #288afa
		.vue-treeselect__list-item
			&.vue-treeselect__option--child
				label
					font-weight normal
					line-height 28px
					padding 0 15px
					border 1px solid #aaa
					border-radius 2px
				&:hover
					color #288afa
	&.music-category
		letter-spacing 0
		.vue-treeselect__option
			.vue-treeselect__label
				margin-bottom 0
				letter-spacing 0
			&.vue-treeselect__option--root
				cursor default
				color #000aaa
				.vue-treeselect__label
					cursor default
					margin-bottom 0
				.vue-treeselect__label-wrapper
					pointer-events none
					.vue-treeselect__checkbox-wrapper
						display none
		&.vue-treeselect--single
			font-weight normal
			letter-spacing normal
			.vue-treeselect__option
				&.vue-treeselect__option--root
					cursor default
					.vue-treeselect__label
						cursor default
						margin-bottom 0
						letter-spacing 0
				&.vue-treeselect__option--child
					&.vue-treeselect__option--selected
						label
							color #fff
							background #288afa
							border 1px solid #288afa
			.vue-treeselect__list-item
				&.vue-treeselect__option--child
					label
						font-weight normal
						line-height 28px
						padding 0 15px
						border 1px solid #aaa
						border-radius 2px
					&:hover
						color #288afa
.vue-treeselect div, .vue-treeselect span
	box-sizing border-box
.vue-treeselect__control
	display table
	table-layout fixed
	width 100%
	height 30px
	border 1px solid #ddd
	border-radius 0.2em
	background #fff
	transition-duration 200ms
	transition-property border-color, box-shadow, width, height, background-color, opacity
	transition-timing-function ease-out-cubic
.vue-treeselect--focused
	&:not(.vue-treeselect--open)
		.vue-treeselect__control
			border-color #3213e2
			box-shadow 0 0 0 3px fade(#3213e2, 10%)
	&.vue-treeselect--searchable
		.vue-treeselect__single-value
			color #c0c4cc
.vue-treeselect--disabled
	.vue-treeselect__control
		background-color #f9f9f9
	.vue-treeselect__multi-value-item
		background #fff
		border-color #e5e5e5
		color #555
	.vue-treeselect__value-remove
		display none
	.vue-treeselect__input-wrapper
		display none
	.vue-treeselect__arrow-wrapper
		cursor default
	.vue-treeselect__arrow
		opacity 0.35
.vue-treeselect--open .vue-treeselect__control, .vue-treeselect__control:hover
	border-color #cfcfcf
.vue-treeselect--open
	&.vue-treeselect--open-below
		.vue-treeselect__control
			border-bottom-left-radius 0
			border-bottom-right-radius 0
	&.vue-treeselect--open-above
		.vue-treeselect__control
			border-top-left-radius 0
			border-top-right-radius 0
.vue-treeselect__value-wrapper, .vue-treeselect__multi-value
	width 100%
	vertical-align middle
.vue-treeselect__value-wrapper
	display table-cell
	position relative
.vue-treeselect--searchable
	&:not(.vue-treeselect--disabled)
		.vue-treeselect__value-wrapper
			cursor text
	.vue-treeselect__input-wrapper
		padding-left 5px
		padding-right 5px
	&.vue-treeselect--multi
		&.vue-treeselect--has-value
			.vue-treeselect__input-wrapper
				padding-top 5px
				padding-left 0
.vue-treeselect__multi-value
	display inline-block
	height 30px
	overflow hidden
	.vue-treeselect__anatagaidakara
		position relative
		width 100%
		display inline-block
		line-height 32px
		text-overflow ellipsis
		white-space nowrap
		overflow hidden
		text-indent 4px
		color var(--text-color)
		font-size 13px
.vue-treeselect__placeholder, .vue-treeselect__single-value
	overflow hidden
	text-overflow ellipsis
	white-space nowrap
	padding-left 5px
	padding-right 5px
	position absolute
	top 0
	right 0
	bottom 0
	left 0
	line-height 32px
	user-select none
	pointer-events none
.vue-treeselect__placeholder
	font-size 13px
	color #c0c4cc
.vue-treeselect__single-value
	color #333
.vue-treeselect__multi-value-item-wrapper
	display inline-block
	padding-top 5px
	padding-right 5px
	max-width 100%
	vertical-align top
.vue-treeselect__multi-value-item
	display inline-block
	display flex
	max-width 100%
	overflow hidden
	background #288afa
	border 1px solid transparent
	border-radius 2px
	color #fff
	font-size 12px
	white-space nowrap
	vertical-align top
.vue-treeselect__value-remove, .vue-treeselect__multi-value-label
	display inline-block
	margin 2px 0
	padding 0 5px
	vertical-align middle
.vue-treeselect__value-remove
	cursor pointer
	border-left 1px solid #fff
	color #fff
	&:hover
		color #f01919
.vue-treeselect__multi-value-label
	overflow hidden
	text-overflow ellipsis
	white-space nowrap
	max-width 100%
	cursor default
	user-select none
.vue-treeselect__limit-tip
	display inline-block
	padding-top 5px
	padding-right 5px
	vertical-align top
.vue-treeselect__limit-tip-text
	display block
	margin 2px 0
	padding 1px 5px
	color black
	font-size 12px
	font-weight 600
	cursor default
.vue-treeselect__input-wrapper
	display block
	max-width 100%
	outline none
.vue-treeselect--single
	.vue-treeselect__input-wrapper
		font-size inherit
		height 100%
	.vue-treeselect__input
		width 100%
		height 100%
	.vue-treeselect__option--selected
		font-weight 600
	.vue-treeselect__icon-wrapper
		padding-left 5px
.vue-treeselect--multi
	.vue-treeselect__input-wrapper
		display inline-block
		font-size 12px
		vertical-align top
	.vue-treeselect__input
		padding-top 3px
		padding-bottom 3px
.vue-treeselect__input, .vue-treeselect__sizer
	margin 0
	line-height inherit
	font-family inherit
	font-size inherit
.vue-treeselect__input
	max-width 100%
	margin 0
	padding 0
	border 0
	outline none
	box-sizing content-box
	box-shadow none
	background none transparent
	line-height 1
	vertical-align middle
	&::-ms-clear
		display none
.vue-treeselect--has-value
	.vue-treeselect__input
		line-height inherit
		vertical-align top
.vue-treeselect__sizer
	position absolute
	top 0
	left 0
	visibility hidden
	height 0
	overflow scroll
	white-space pre
.vue-treeselect__clear
	display table-cell
	vertical-align middle
	width 20px
	text-align center
	font-size 0
	cursor pointer
	color #ccc
	font-size 18px
	font-family Arial, sans-serif
	line-height 0
	animation 200ms vue-treeselect-animation-fade-in ease-out-circ
	&:hover
		color #e75858
.vue-treeselect__arrow-wrapper
	display table-cell
	vertical-align middle
	width 20px
	text-align center
	font-size 0
	cursor pointer
.vue-treeselect__arrow
	display inline-block
	width 0
	height 0
	border-color transparent
	border-top-color #ccc
	border-style solid
	border-width 5px
	border-bottom-width 0
	transition 200ms transform ease-out-expo
	vertical-align middle
.vue-treeselect__arrow--rotated
	transform rotateZ(180deg)
.vue-treeselect__menu
	padding-top 3px
	padding-bottom 3px
	display block
	position absolute
	right 0
	left 0
	z-index 1000
	overflow-x hidden
	overflow-y auto
	border 1px solid #cfcfcf
	background #fff
	line-height 180%
	cursor default
	-webkit-overflow-scrolling touch
	max-height 600px
	min-width 700px
.vue-treeselect--open-below
	.vue-treeselect__menu
		border-bottom-left-radius 0.2em
		border-bottom-right-radius 0.2em
		top 100%
		margin-top -1px
		border-top-color #f2f2f2
		box-shadow 0 1px 0 rgba(0, 0, 0, 0.06)
.vue-treeselect--open-above
	.vue-treeselect__menu
		border-top-left-radius 0.2em
		border-top-right-radius 0.2em
		bottom 100%
		margin-bottom -1px
		border-bottom-color #f2f2f2
.vue-treeselect__list-item
	& > .vue-treeselect__list
		padding-left 20px
	&.vue-treeselect__option--child
		display inline-block
		width 25%
	&.vue-treeselect__option--parent
		width 100%
.vue-treeselect__menu-container
	position relative
	display block
	height auto
	max-height 53px
	overflow-y hidden
	margin 10px
	padding 5px 10px 10px 10px
	border 1px solid #ddd
	transition all 0.3s
	&:hover
		max-height 9999px
.vue-treeselect__option
	display table
	table-layout fixed
	width 100%
	.vue-treeselect__label-wrapper
		display table-cell
		vertical-align middle
		display table
		width auto
		table-layout fixed
		color inherit
		label
			&.vue-treeselect__label
				cursor pointer
				overflow hidden
				text-overflow ellipsis
				white-space nowrap
				display inline-block
				padding-left 5px
				width auto
				overflow visible
				vertical-align middle
				letter-spacing normal
				line-height 25px
				font-weight normal
				margin 0
				font-size 14px
.vue-treeselect__option--disabled
	cursor default
.vue-treeselect__option--hide
	display none
.vue-treeselect__option-arrow-wrapper, .vue-treeselect__option-arrow-placeholder
	display table-cell
	vertical-align middle
	width 20px
	text-align center
	font-size 0
.vue-treeselect__option-arrow-wrapper
	cursor pointer
.vue-treeselect__option-arrow
	display inline-block
	border 5px solid transparent
	border-right 0
	border-left-color #ccc
	vertical-align middle
	transition 200ms transform ease-out-expo
.vue-treeselect__option-arrow-wrapper:hover .vue-treeselect__option-arrow, .vue-treeselect--branch-nodes-disabled .vue-treeselect__option:hover .vue-treeselect__option-arrow
	border-left-color black
.vue-treeselect__option-arrow--rotated
	transform rotateZ(90deg)
	&.vue-treeselect__option-arrow--prepare-enter
		transform rotateZ(0) !important
.vue-treeselect__checkbox-wrapper
	cursor pointer
	display table-cell
	width 20px
	min-width 20px
	height 100%
	text-align center
	vertical-align middle
.vue-treeselect__checkbox
	display block
	margin auto
	width 12px
	height 12px
	border-width 1px
	border-style solid
	border-radius 2px
	position relative
	transition 200ms all ease-out-circ
.vue-treeselect__checkbox-mark
	display block
	position absolute
	top 0
	right 0
	bottom 0
	left 0
	&::before
		transform scale(0)
	&::after
		transform rotateZ(45deg) scale(0)
.vue-treeselect__checkbox-mark::before, .vue-treeselect__checkbox-mark::after
	position absolute
	content ""
	opacity 0
	display table
	transition 200ms all ease 66.6666666667ms
	box-sizing content-box
.vue-treeselect__checkbox--unchecked
	border-color #7f8d59
	background #fff
.vue-treeselect__label-wrapper
	&:hover
		.vue-treeselect__checkbox--unchecked
			border-color #4894f8
			background #fff
		.vue-treeselect__checkbox--indeterminate
			border-color #4894f8
			background #4894f8
			.vue-treeselect__checkbox-mark
				&::before
					border-color #fff
		.vue-treeselect__checkbox--checked
			border-color #4894f8
			background #4894f8
			.vue-treeselect__checkbox-mark
				&::after
					border-color #fff
.vue-treeselect__checkbox--indeterminate
	border-color #4894f8
	background #4894f8
	.vue-treeselect__checkbox-mark
		&::before
			opacity 1
			left 1px
			top 4px
			width 6px
			height 0
			border 2px solid #fff
			border-top 0
			border-left 0
			transform scale(1)
.vue-treeselect__checkbox--checked
	border-color #4894f8
	background #4894f8
	.vue-treeselect__checkbox-mark
		&::after
			opacity 1
			left 3px
			top 0
			width 2.5px
			height 6px
			border 2px solid #fff
			border-top 0
			border-left 0
			transform rotateZ(45deg) scale(1)
.vue-treeselect__count
	font-weight 400
	opacity 0.6
.vue-treeselect__loading-tip, .vue-treeselect__no-children-tip, .vue-treeselect__error-tip, .vue-treeselect__no-results-tip, .vue-treeselect__no-options-tip
	display table
	table-layout fixed
	width 100%
	color #999
.vue-treeselect__loading-tip-text, .vue-treeselect__no-children-tip-text, .vue-treeselect__error-tip-text, .vue-treeselect__no-results-tip-text, .vue-treeselect__no-options-tip-text
	display table-cell
	vertical-align middle
	overflow hidden
	text-overflow ellipsis
	white-space nowrap
	width 100%
	padding-left 5px
	font-size 12px
.vue-treeselect__error-tip
	.vue-treeselect__retry
		cursor pointer
		font-style normal
		font-weight 600
		text-decoration none
		color #3c3
.vue-treeselect__icon-wrapper
	display table-cell
	vertical-align middle
	width 20px
	text-align center
	font-size 0
.vue-treeselect__icon-warning
	display block
	margin auto
	border-radius 50%
	position relative
	width 12px
	height 12px
	background red
	&::after
		display block
		position absolute
		content ""
		left 5px
		top 2.5px
		width 2px
		height 1px
		border 0 solid #fff
		border-top-width 5px
		border-bottom-width 1px
.vue-treeselect__icon-error
	display block
	margin auto
	border-radius 50%
	position relative
	width 12px
	height 12px
	background red
	&::before
		width 6px
		height 2px
		left 3px
		top 5px
	&::after
		width 2px
		height 6px
		left 5px
		top 3px
.vue-treeselect__icon-error::before, .vue-treeselect__icon-error::after
	display block
	position absolute
	content ""
	background #fff
	transform rotate(45deg)
.vue-treeselect__icon-loader
	display block
	margin auto
	position relative
	width 12px
	height 12px
	text-align center
	animation 1.6s vue-treeselect-animation-rotate linear infinite
	&::before
		background black
	&::after
		background #91ce89
		animation-delay -0.8s
.vue-treeselect__icon-loader::before, .vue-treeselect__icon-loader::after
	border-radius 50%
	position absolute
	content ""
	left 0
	top 0
	display block
	width 100%
	height 100%
	opacity 0.6
	animation 1.6s vue-treeselect-animation-bounce ease-in-out infinite
.vue-treeselect__subname
	color #1e88e5
