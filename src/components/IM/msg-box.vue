<template>
  <!-- 具体的对话的某一条 -->
  <div class="im-msg-box not-me" >
    <div class="top">
      <!-- 时间 -->
      <div class="title">
        <div class="name">{{name}}:</div>
        <div class="time">{{datetimeSecondFormat(time)}}</div>
      </div>
    </div>
    <div class="bottom">
      <!-- 头像 -->
      <div class="avatar" :style="avatarStyle"></div>
      <!-- 消息 -->
      <div class="message" :class="{'img-padding' : type === 2}" @click="handleClick">
        <!-- TODO: 要支持图片及文字 -->
        <div class="message-content" :class="{'not-img': type !== 2}"  :data-key="msgKey" v-html="msg2html" @click="openPhotoImager">
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { cutImage } from '@/plugins/bfsImage'
import emotionListData from './lib/emotion-list-data'
import { datetimeSecond } from '@/lib/formattime'
export default {
  name: 'IMMsgBox',
  props: {
    avatar: String,
    mid: null,
    isMe: [Boolean, Number],
    name: String,
    content: null,
    type: Number,
    group: null,
    medal: {
      type: Object,
      default () {
        return {}
      }
    },
    text: String,
    groupType: Number,
    msgKey: [String, Number],
    time: Number,
    isLimit: Boolean,
    limitMsg: String,
    // talkerId: Number,
    msgObj: Object
  },
  data() {
    return {
      photoImager: null,
      image: {
        url: '',
        width: 0,
        height: 0
      },
      targetDom: null,
      triggerUpdate: false,
      showViewer: false
    }
  },
  computed: {
    isShowBv() {
      return window.__BILI_CONFIG__ && window.__BILI_CONFIG__.show_bv
    },
    msg () {
      return this.text || this.content.content || this.content || ''
    },
    avatarStyle () {
      const avatar = this.avatar || '//static.hdslb.com/images/member/noface.gif'
      return {
        backgroundImage: `url(${cutImage(avatar, 30, 30)})`
      }
    },
    msg2html () {
      if (this.triggerUpdate) {
        return
      }
      if (this.type === 2 || this.type === 6) {
        // 图片
        const url = this.content.url && this.content.url.replace(/^http:/, '')
        let width = this.content.width
        let height = this.content.height
        if (width > 490) {
          width = 490
          height = this.content.height / this.content.width * 490
        }
        if (height > 150) {
          height = 150
          width = this.content.width / this.content.height * 150
        }

        const altText = (this.type === 2 ? '[图片]' : '[自定义表情]') + ' 点击查看大图'

        width = parseInt(width)
        height = parseInt(height)
        const urlObj = cutImage(url, width, height)

        const srcUrl = escape(urlObj)
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.image = {
          url,
          width,
          height
        }

        return `
          <img class="im-msg-item-img" title="${altText}" alt="${altText}" src="${srcUrl}" style="width: ${width}px; height: ${height}px">
        `
      } else if (this.type === 1 && typeof this.msg === 'string') {
        const flatten = ary => ary.reduce((a, b) => a.concat(Array.isArray(b) ? flatten(b) : b), [])

        const rems = (re, rep) => {
          return str => {
            const matchs = str.match(re) || []
            const others = str.split(re)
            const replacement = matchs.map(rep)
            return [replacement, others]
          }
        }

        const resolve = (rules, strToResolve) => {
          const rfs = rules.map(rule => {
            if (rule.match instanceof RegExp) {
              return rems(rule.match, rule.replace)
            } else {
              return str => {
                const [matchs, others] = rule.match(str)
                const replacement = matchs.map(rule.replace)
                return [replacement, others]
              }
            }
          })

          const recur = (depth, strArr) => {
            const rf = rfs[depth]
            if (!rf) {
              return strArr
            }

            return strArr.map(str => {
              const [replacement, others] = rf(str)
              const mapedOthers = recur(depth + 1, others)
              const res = []
              for (let i = 0; i < replacement.length; i++) {
                res.push(mapedOthers[i])
                res.push(replacement[i])
              }
              res.push(mapedOthers[replacement.length])

              return res
            })
          }

          return flatten(recur(0, [strToResolve]))
        }

        const escapeHtml = str => str.replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\n/g, '<br/>')

        const rules = [
          {
            match: /(?:(?:(?:http:\/\/|https:\/\/)(?:[a-zA-Z0-9_.-]+\.)*(?:bilibili|biligame)\.com(?:\/[/.$*?~=#!%@&\-\w]*)?)|(?:(?:http:\/\/|https:\/\/)(?:[a-zA-Z0-9_.-]+\.)*(?:acg|b23)\.tv(?:\/[/.$*?~=#!%@&\-\w]*)?)|(?:(?:http:\/\/|https:\/\/)dl\.(?:hdslb)\.com(?:\/[/.$*?~=#!%@&\-\w]*)?))/g,
            replace: matchStr => `
              <a href="${matchStr}" target="_blank" class="dynamic-link">
                <i class="bp-icon-font icon-link"></i> 网页链接
              </a>
            `
          },
          {
            match: /(?:bilibili:\/\/)(?:[/.$*?~=#!%@&\-\w]*)?/g,
            replace: matchStr => `
              <span data-href="${matchStr}">
                【请在移动端上打开链接】
              </span>
            `
          },
          {
            match: /\[(?:[^[\]]+)]/g,
            replace: matchStr => {
              const name = matchStr.slice(1, matchStr.length - 1)
              if (!(name in emotionListData)) {
                return matchStr
              }
              const emotion = emotionListData[name]
              const style = `
                background-image: url(${emotion.img});
                animation-duration: ${emotion.step * 40}ms;
                animation-timing-function: steps(${emotion.step}, end);
                height: ${emotion.step * 32}px
              `
              return `
                <a class="emotion-item active" title="${name}">
                  <div class="img" style="${style}"></div>
                </a>
              `
            }
          },
          ...(this.isShowBv ? [{
            match: /bv1[\d\w]{9}/gi,
            replace: match => `<a href="https://www.bilibili.com/video/${match.slice(0, 2).toLowerCase().concat(match.slice(2))}" target="_blank" class="dynamic-link">${match}</a>`
            }] : []
          ),
          {
            match: /av\d+/gi,
            replace: match => `<a href="//www.bilibili.com/video/${match.toLowerCase()}" target="_blank" class="dynamic-link">${match}</a>`
          },
          {
            match: /vc\d+/gi,
            replace: match => `<a href="//vc.bilibili.com/video/${match.slice(2)}" target="_blank" class="dynamic-link">${match}</a>`
          },
          {
            match: /cv\d+/gi,
            replace: match => `<a href="//www.bilibili.com/read/${match.toLowerCase()}" target="_blank" class="dynamic-link">${match}</a>`
          }
        ]

        let res = escapeHtml(this.msg)
        res = resolve(rules, res).join('')

        return res
      } else {
        return `
          <span class="not-support-icon"></span><span class="not-support-text no-select">不支持的消息类型，请使用最新版客户端查看</span>
        `
      }
    }
  },
  methods: {
    datetimeSecondFormat(...args) {
      return datetimeSecond(...args)
    },
    // 打开图片
    openPhotoImager(e) {
      if (this.image.url) {
        e.preventDefault()
      }
    },
    handleClick (e) {
    }
  }
}
</script>
<style lang="stylus">
  .photo-imager-container
    z-index $z-popup
    .image-count-hinter
      display none
    .icon-close
      float left

  .emotion-item
    display inline-block
    width 32px
    height 32px
    overflow hidden
    vertical-align bottom

    .img
      width 32px
      height 864px
      background top/32px no-repeat
    &:hover .img, &.active .img
      animation im-emotion-step 1080ms steps(27, end) infinite

    &.can-click
      margin 12px 12px 0 0
      cursor pointer

  @keyframes im-emotion-step {
    0% {
      transform translateY(0)
    }
    100% {
      transform translateY(-100%)
    }
  }
</style>
<style lang="stylus" scoped>
@import "~@/styl/im/mixins.styl"
@import "~@/styl/im/variable.styl"
.im-msg-box
  min-height 48px
  padding 0 16px 16px
  overflow hidden
  position relative
  .avatar, .message
    float left
  .avatar
    avatar(30px)
    float left
  .top
    .title
      padding-left 40px
      display flex
      .name 
        color var(--text-color-dark-1)
        font-size 14px
        line-height 22px
        margin-right 10px
      .time
        color var(--text-color-light-1)
        font-size 12px
        line-height 22px
        margin-right 10px
  .bottom
    overflow hidden
    position relative
  .message
    float left
    // background-color $bubble-other-bg
    max-width 480px
    // background $white-bg
    // box-shadow $msg-shadow-other
    // border-radius 0 22px 22px 22px
    margin 0 10px
    position relative
    overflow hidden
    color $bubble-other-text
    .message-content
      line-height 1.5
      font-size 14px
      /*min-height 40px*/
      padding 8px 16px
      word-wrap break-word
      word-break break-word
      /*display flex*/
      /*flex-direction row*/
      /*flex-wrap wrap*/
      /*align-items flex-end*/
      border-radius 0 16px 16px 16px
      overflow hidden
      background #f4f5f7
      &.is-me
        background #80b9f2
        border-radius 16px 0 16px 16px
        &::selection
          color var(--text-color-dark-1)
          background var(--content-bg-color)
        &.share-type
          background var(--content-bg-color)
          padding 0
      &.share-type
        padding 0
      &.not-img
        position relative
        z-index 1
  .not-me .message::before
    left -6px
</style>
