<template>
  <div class="im-component">
    <IMDialog
      :msgs="msgs"
      :noMore="noMore"
      @load-more="$emit('load-more')"
    />
  </div>
</template>
<script>
import IMDialog from './dialog'
export default {
  name: 'IM',
  components: {
    IMDialog
  },
  props: {
    msgs: {
      type: Array,
      default() {
        return []
      }
    },
    noMore: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
    }
  },
  methods: {
  }
}
</script>
<style lang="stylus" scoped>
.im-component
  width 100%
  height 100%
</style>
