<template>
  <div class="im-msg-list" ref="msgContainer" @scroll="handleScroll">
    <div class="msg-list-content" v-viewer>
      <!-- 具体的对话的某一条 -->
      <MsgBox
        v-for="msg in msgs"
        :key="msg.msg_key"
        :avatar="msg.face || ''"
        :content="msg.content"
        :name="msg.uname || '发送者'"
        :isMe="false"
        :mid="msg.sender_uid"
        :type="msg.msg_type"
        :msgKey="msg.msg_key"
        :time="msg.timestamp"
        :msgObj="msg"
      />
    </div>
      <div class="end"
      :style="{visibility: noMore ? 'visible' : 'hidden'}"
      >消息已经到底了~</div>
  </div>
</template>
<script>
import MsgBox from './msg-box'
export default {
  name: 'IMMsgList',
  props: {
    msgs: {
      type: Array,
      default() {
        return []
      }
    },
    noMore: {
      type: Boolean,
      default: false
    }
  },
  components: {
    MsgBox
  },
  data() {
    return {

    }
  },
  methods: {
    // 下拉加载
    handleScroll() {
      const el = this.$refs.msgContainer
      // 滚动到底
      if (el.clientHeight + el.scrollTop + 5 >= el.scrollHeight) {
        this.$emit('load-more')
      }
    }
  }
}
</script>
<style lang="stylus" scoped>
@import "~@/styl/im/variable.styl"
.im-msg-list
  flex 1
  position relative
  overflow-x hidden
  overflow-y scroll
  // background-color $gray-bg
  /* scrollbar */
  &::-webkit-scrollbar
    width 8px
    height 8px
    background-color rgba(0.9, 0.9, 0.9, 0)

  &::-webkit-scrollbar-thumb
    background #aaa
    border-radius 4px
  .msg-list-content
    overflow auto
  .end
    display flex
    justify-content center
    color var(--text-color)
</style>
