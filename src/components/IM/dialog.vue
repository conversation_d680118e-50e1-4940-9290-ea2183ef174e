<template>
  <div class="im-dialog">
    <!-- 对话列表 -->
    <MsgList
      :msgs="msgs"
      :noMore="noMore"
      @load-more="$emit('load-more')"
    />
  </div>
</template>
<script>
import Msg<PERSON>ist from './msg-list'
export default {
  name: 'IMDialog',
  components: {
    MsgList
  },
  props: {
    msgs: {
      type: Array,
      default() {
        return []
      }
    },
    noMore: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
    }
  }
}
</script>
<style lang="stylus" scoped>
.im-dialog
  display flex
  flex-direction column
  align-items stretch
  height 100%
</style>
