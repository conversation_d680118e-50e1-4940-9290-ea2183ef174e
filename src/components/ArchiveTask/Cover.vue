<template>
  <div class="cover">
    <template v-if="hasCover">
      <el-image
        v-if="imagePreview"
        :src="resizeImage(src, size)"
        :preview-src-list="[src]"
        :alt="alt"
        @click="$emit('click-cover')"
        @error="$emit('cover-error')"
      >
      </el-image>
      <template v-else>
        <img
          v-if="noLink"
          :src="resizeImage(src, size)"
          :onerror="$emit('cover-error')"
          @click="$emit('click-cover')"
          :alt="alt"/>
        <a v-else :href="replaceBfsImagesUrl(src)" target="_blank" style="color:var(--link-color)" @click="$emit('click-cover')">
          <img
            :src="resizeImage(src, size)"
            :alt="alt"
            :onerror="$emit('cover-error')"
            :style="imgStyle"/>
        </a>
      </template>
    </template>
    <p v-else class="cover-empty">无封面</p>
  </div>
</template>
<script>
import { cutImage } from '@/plugins/bfsImage'
import { replaceBfsImagesUrl } from '@/utils'

export default {
  name: 'cover',
  props: {
    src: {
      type: String,
      default: ''
    },
    noLink: {
      type: Boolean,
      default: false
    },
    alt: {
      type: String,
      default: '封面'
    },
    size: {
      type: String,
      default: '160x100'
    },
    imagePreview: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    hasCover() {
      const cover = this.src
      if (!cover) {
        this.$emit('no-cover')
        return false
      }
      if (/static.hdslb.com\/images\/transparent.gif/g.test(cover)) {
        this.$emit('no-cover')
        return false
      }
      return true
    },
    imgStyle() {
      const [ maxWidth ] = this.size.split('x')
      if (!maxWidth || parseInt(maxWidth, 10) <= 0) {
        return {}
      }
      return {
        maxWidth: `${maxWidth}px`
      }
    }
  },
  methods: {
    replaceBfsImagesUrl,
    resizeImage(src, sizeString) {
      if (!sizeString.includes('x')) return src
      const [w, h] = sizeString.split('x')
      return replaceBfsImagesUrl(cutImage(src, w, h))
    }
  }
}
</script>
<style lang="stylus" scoped>
.cover{
  img{
    display inline-block
    vertical-align middle
    letter-spacing normal
    width 240px
    height 150px
  }
  &-empty{
    text-align center
    padding 60px 0
    font-size 16px
    border 1px solid #ccc
  }
}
</style>
