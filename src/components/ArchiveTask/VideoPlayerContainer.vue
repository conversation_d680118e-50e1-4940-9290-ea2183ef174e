<template>
  <div class="video-player-container">
    <slot name="aiTag"></slot>
    <slot name="aiLibrary"></slot>
    <NanoModulePlayer
      v-show="videoPlayUrl"
      class="video-player"
      ref="nanoPlayer"
      :cid="cid"
      :filename="filename"
      :fetchSubtitle="fetchSubtitle"
      :src="videoPlayUrl"
      :seek="seek"
      :seekPause="seekPause"
      :getFrameRateEnabled="getFrameRateEnabled"
      v-behavior-track="'video-player-area'"
      @nano-canplay="relayNanoCanplay"
      @nano-played="relayNanoPlayed"
      @nano-loaded-data="relayNanoLoadedData"
      @nano-paused="$emit('nano-paused')"
      @toggle-fullscreen="e => $emit('toggle-fullscreen', e)"
    />
    <div
      v-if="!videoPlayUrl && sourceFileExpired"
      class="dummy-nano"
    >
      <span>该视频上传已超过30天，渣清视频已过期，请点击播放器下方【切换播放源】</span>
    </div>
    <div>
      <el-button
        class="mt-12"
        type="primary"
        size="small"
        @click="() => handleToggle()"
        v-behavior-track="'toggle-play-btn'"
        v-track="{event:'switch-video-src',value:{cid}}"
      >切换播放源
      </el-button>
      <el-button
        type="info"
        size="small"
        @click="openBvcflow"
        v-behavior-track="'bvcflow-btn'"
      >
        bvcflow
      </el-button>
      <el-button
        type="info"
        size="small"
        @click="getRawVideoUrl"
      >
        查看原片
      </el-button>
    </div>
    <div class="video-info">
      <div class="status" v-if="!hideInfo && playerData.watermarkState">
        <label class="title">up主水印：</label>{{ playerData.watermarkState }}
      </div>
      <div class="status" v-if="!hideInfo">
        <label class="title">源码率：</label>{{ playerData.mediaInfo.bitRate }}
      </div>
      <div class="status"  v-if="!hideInfo">
        <label class="title">源分辨率：</label>{{ playerData.mediaInfo.width
        }}<span v-if="playerData.mediaInfo.width > 0">x</span
        >{{ playerData.mediaInfo.height }}
      </div>
      <div class="status"  v-if="!hideInfo">
        <label class="title">帧率：</label>{{ playerData.mediaInfo.frameRate }}
      </div>
      <slot></slot>
    </div>
  </div>
</template>
<script>
import NanoModulePlayer from '@/components/package/VideoPlayer/NanoModulePlayer'
import PlayUrlV2Mixin from '@/mixins/playurl-v2.js'
import { genBvcUrl } from '@/utils'
import { videoApi } from '@/api'

export default {
  name: 'video-player-container',
  data() {
    return {
      videoInfo: {
        width: '',
        height: ''
      },
      toggle: false // true为hd
    }
  },
  mixins: [
    PlayUrlV2Mixin
  ],
  props: {
    sourceFileExpired: {
      tyep: Boolean,
      default: false
    },
    playerData: {
      type: Object,
      required: true
    },
    seek: Number, // 定位时间点
    seekPause: { // 定位是否暂停播放
      type: Boolean,
      default: true
    },
    hideInfo: {
      type: Boolean // 移出视频码率等信息展示
    },
    cid: {
      type: [Number, String],
      default: ''
    },
    filename: {
      type: String,
      default: ''
    },
    fetchSubtitle: {
      type: Boolean,
      default: false
    },
    options: {
      type: Object,
      default: () => {
        return {}
      }
    },
    getFrameRateEnabled: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    cid() {
      this.toggle = false
      this.playurlV2 = ''
    },
    'options.toggle': {
      handler() {
        this.initVideoMode()
      }
    }
  },
  components: {
    NanoModulePlayer
  },
  computed: {
    videoPlayUrl() {
      return this.toggle ? this.playurlV2 : this.playerData.playurl
    }
  },
  methods: {
    pause() {
      this.$refs.nanoPlayer?.pauseVideo()
    },
    disableShortcut() {
      this.$refs.nanoPlayer?.disableShortcut()
    },
    async handleToggle(toggle) {
      const nextToggle = toggle || !this.toggle
      this.toggle = nextToggle
      if (nextToggle) {
        await this.getPlayUrl(this.cid)
      }
    },
    initVideoMode() {
      // 如果默认二审播放器
      const { toggle = false } = this.options
      if (toggle && this.cid) {
        this.handleToggle(true)
      }
    },
    openBvcflow() {
      const url = genBvcUrl('panel_info', {
        flowid: this.filename
      })
      window.open(url, '_blank')
    },
    async getRawVideoUrl() {
      try {
        const res = await videoApi.getRawVideoUrl({ cid: this.cid })
        const { url } = res.data
        window.open(url, '_blank')
      } catch (e) {
        console.error('获取原片链接失败', e)
      }
    },
    relayLoadeddata() { this.$emit('loadeddata') },
    relayNanoCanplay() { this.$emit('nano-canplay') },
    relayNanoPlayed() { this.$emit('nano-played') },
    relayNanoLoadedData() { this.$emit('nano-loaded-data') }
  },
  created() {
    this.initVideoMode()
  }
}
</script>
<style lang="stylus" scoped>
.video-player-container
  .video-info
    display inline-block
    vertical-align top
    margin-left 15px
    font-size 14px
    line-height 21px
  .video-player
    display inline-block
    // margin-left 20px
  .dummy-nano
    width 561px
    height 371px
    background black
    display flex
    align-items center
    justify-content center
    span
      color white
</style>
