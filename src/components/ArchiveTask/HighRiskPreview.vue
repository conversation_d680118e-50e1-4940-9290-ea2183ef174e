<template>
  <div>
    <!-- AI图片提示 -->
    <div
      v-track="{ event: 'click-ai-img-tip', value: trackInfo || {} }"
      v-track.impression="{
        event: 'impression-ai-img-tip',
        value: trackInfo || {}
      }"
      v-if="ai_img_urls && ai_img_urls.length > 0"
    >
      <ClipImgTip
        :ai_img_urls="ai_img_urls"
        @show-focus-img="showFocusImg"
        :isShowDialog="showFocusOptions.isShow"
        @goToPre="goToPre"
        @goToNext="goToNext"
      />
    </div>
    <el-dialog
      v-if="showFocusOptions.isShow"
      class="body-focus-modal"
      :visible.sync="showFocusOptions.isShow"
      :title="showFocusOptions.title"
      @close="showFocusOptions.isShow = false"
      append-to-body
    >
      <p>
        {{ focusImgInfo.tag + ' 时间' }}
        <a @click="() => triggerPlayerSeek(focusImgInfo.time)" class="link">
          {{ focusImgInfo.time }}
        </a>
      </p>
      <img
        :src="focusImgInfo.url"
        class="focus-img"
        @error="onImgError"
        alt="AI图片提示"
      />
    </el-dialog>
  </div>
</template>
<script>
import ClipImgTip from '@/v2/biz-components/workbench/ClipImgTip'
import moment from 'moment'
import notify from '@/lib/notify'

export default {
  components: {
    ClipImgTip
  },
  props: {
    trackInfo: Object,
    ai_img_urls: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      focusImgInfo: {},
      showFocusOptions: {
        isShow: false,
        title: '预览图片'
      }
    }
  },
  methods: {
    showFocusImg(imgInfo) {
      this.focusImgInfo = { ...imgInfo }
      this.showFocusOptions.isShow = true
    },
    goToPre() {
      const currentIdx = this.focusImgInfo.idx
      if (!currentIdx) return
      const currentFocus = this.ai_img_urls[currentIdx - 1]
      this.focusImgInfo = { ...currentFocus, idx: currentIdx - 1 }
    },
    goToNext() {
      const currentIdx = this.focusImgInfo.idx
      if (currentIdx === this.ai_img_urls.length - 1) return
      const currentFocus = this.ai_img_urls[currentIdx + 1]
      this.focusImgInfo = { ...currentFocus, idx: currentIdx + 1 }
    },
    triggerPlayerSeek(mmss) {
      let hhmmss = mmss
      const timeList = mmss.split(':')
      if (timeList.length === 2) {
        hhmmss = `00:${mmss}`
      }
      this.focusModalOptions.isShow = false
      this.showFocusOptions.isShow = false
      const secs = moment.duration(hhmmss).as('seconds')
      this.$EventBus.$emit('seek-time', secs)
    },
    onImgError() {
      notify.error('切片获取失败 @bvcflow', 1500)
    }
  }
}

</script>
<style scoped lang="stylus">
.focus-img {
  min-width: 100px;
  min-height: 80px;
  max-width: 580px;
  max-height: 580px;
  margin: auto;
}
</style>
