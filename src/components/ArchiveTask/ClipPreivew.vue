<template>
  <div class="clip-preview" v-if="clipReady && currentClip.cid">
    <ClipViewer
      ref="smallModalViewer"
      :clip="currentClip"
      :isBigModal="bigModalOptions.isShow"
      :imgWidth="imgWidth"
      :imgHeight="imgHeight"
      :content="content"
      :isTask="true"
      :isFocusViewer="focusModalOptions.isShow"
      @click-clip-preview="clickClipPreview"
      @show-big-modal="showBigModal"
      @close-big-modal="closeBigModal"
      @show-focus="showFocus"
      @page-change="pageChange"
    />

    <el-dialog
      :visible.sync="focusModalOptions.isShow"
      :title="focusModalOptions.title"
      @close="focusModalOptions.isShow = false"
      append-to-body
      class="body-focus-modal"
      v-if="focusModalOptions.isShow"
    >
      <p class="mb-8">
        时间
        <a @click="() => triggerPlayerSeek(focusPicInfo.time)" class="link">
          {{ focusPicInfo.time }}
        </a>
      </p>
      <img
        :src="focusPicInfo.src"
        class="focus-img"
        alt="切片预览"
        @error="onImgError"
      />
    </el-dialog>

    <el-dialog
      :visible.sync="bigModalOptions.isShow"
      @close="bigModalOptions.isShow = false"
      append-to-body
      width="85%"
      height="100%"
      class="big-modal-dialog xx"
      v-if="bigModalOptions.isShow"
    >
      <ClipViewer
        ref="bigModalViewer"
        :clip="currentClip"
        :content="content"
        :isBigModal="bigModalOptions.isShow"
        :imgWidth="imgWidth"
        :imgHeight="imgHeight"
        :isTask="true"
        :isFocusViewer="focusModalOptions.isShow"
        :showPager="true"
        @close-big-modal="closeBigModal"
        @show-focus="showFocus"
        @page-change="pageChange"
        @page-change-by-page-num="pageChangeByPageNum"
        @click-clip-preview="clickClipPreview"
      />
    </el-dialog>

    <!-- AI图片提示 -->
    <div
      v-track="{ event: 'click-ai-img-tip', value: trackInfo || {} }"
      v-track.impression="{
        event: 'impression-ai-img-tip',
        value: trackInfo || {}
      }"
      v-if="ai_img_urls && ai_img_urls.length > 0"
    >
      <ClipImgTip
        :ai_img_urls="ai_img_urls"
        @show-focus-img="showFocusImg"
        :isShowDialog="showFocusOptions.isShow"
        @goToPre="goToPre"
        @goToNext="goToNext"
      />
    </div>
    <el-dialog
      v-if="showFocusOptions.isShow"
      class="body-focus-modal"
      :visible.sync="showFocusOptions.isShow"
      :title="showFocusOptions.title"
      @close="showFocusOptions.isShow = false"
      append-to-body
    >
      <p>
        {{ focusImgInfo.tag + ' 时间' }}
        <a @click="() => triggerPlayerSeek(focusImgInfo.time)" class="link">
          {{ focusImgInfo.time }}
        </a>
      </p>
      <img
        :src="focusImgInfo.url"
        class="focus-img"
        @error="onImgError"
        alt="AI图片提示"
      />
    </el-dialog>
  </div>
</template>
<script>
import ClipViewer from '@/components/TaskDetail/archive/ClipViewer'
import { archiveApi } from '@/api/index'
import { cloneDeep } from 'lodash-es'
import moment from 'moment'
import notify from '@/lib/notify'
import clipKeysMixin from '@/mixins/clip-keys.js'
import ClipImgTip from '@/v2/biz-components/workbench/ClipImgTip'

export default {
  name: 'clip-preview',
  mixins: [clipKeysMixin],
  props: {
    videoshots: {
      type: Array,
      default() {
        return []
      }
    },
    content: {
      type: Array,
      default() {
        return []
      }
    },
    maxWidth: {
      type: Number,
      default: 750
    },
    maxHeight: {
      type: Number,
      default: 521
    },
    ai_img_urls: {
      type: Array,
      default() {
        return []
      }
    },
    trackInfo: Object
  },
  data() {
    return {
      clipReady: false,
      currentClip: {},
      focusPicInfo: {
        src: '',
        time: '',
        idx: 0
      },
      // 切片图默认宽高
      imgWidth: 750,
      imgHeight: 421,

      shotStore: {},
      nextType: 'page', // page, video 切片翻页或切换分p

      focusModalOptions: {
        isShow: false,
        title: '预览图片'
      },
      bigModalOptions: {
        isShow: false,
        width: '85%',
        title: '查看大图'
      },
      focusImgInfo: {},
      showFocusOptions: {
        isShow: false,
        title: '预览图片'
      }
    }
  },
  watch: {
    videoshots(val) {
      if (val.length === 0) return
      this.shotStore = {}
      this.getClip(val[0].dm_inid)
    }
  },
  mounted() {
    if (this.videoshots.length > 0) {
      this.getClip(this.videoshots[0].dm_inid)
    }
  },
  methods: {
    handleClipShift(key) {
      if (!this.focusModalOptions.isShow) return
      const viewerComp = this.bigModalOptions.isShow
        ? this.$refs.bigModalViewer
        : this.$refs.smallModalViewer
      let nextIdx = this.focusPicInfo.idx
      let time = ''
      switch (key) {
        case 'pageUp':
        case 'arrowLeft':
          // 1.判断是否需要翻到上一页
          if (this.focusPicInfo.idx - 1 < 0) {
            this.pageChange('prev')
            this.$nextTick(() => {
              nextIdx = this.currentClip.timeList.length - 1
              time = this.currentClip.timeList[nextIdx]
              viewerComp.focusPic(time, nextIdx)
            })
          } else {
            nextIdx = Math.max(this.focusPicInfo.idx - 1, 0)
            time = this.currentClip.timeList[nextIdx]
            viewerComp.focusPic(time, nextIdx)
          }
          break
        case 'pageDown':
        case 'arrowRight':
          // 2.判断是否需要翻到下一页
          if (
            this.focusPicInfo.idx + 1 >
            this.currentClip.timeList.length - 1
          ) {
            this.pageChange('next')
            this.$nextTick(() => {
              nextIdx = 0
              time = this.currentClip.timeList[nextIdx]
              viewerComp.focusPic(time, nextIdx)
            })
          } else {
            nextIdx = Math.min(
              this.focusPicInfo.idx + 1,
              this.currentClip.timeList.length - 1
            )
            time = this.currentClip.timeList[nextIdx]
            viewerComp.focusPic(time, nextIdx)
          }
          break
      }
    },
    getClip(cid) {
      if (cid == null) {
        return
      }
      const videoshots = this.videoshots
      const shotStore = this.shotStore
      let currentClip = this.currentClip

      if (cid === currentClip.cid) {
        this.clipReady = false
        currentClip.cid = ''
        return
      }
      this.clipReady = true

      const cindex = videoshots.findIndex((oneShot) => oneShot.dm_inid === cid)
      const cachedShot = shotStore[cid]
      if (cachedShot) {
        currentClip = this.currentClip = cloneDeep(cachedShot)
        this.goPage(1)
        return
      }

      // 请求cid切片
      archiveApi
        .getVideoshots({ cid })
        .then((res) => {
          const result = res.data || ''
          if (!result) {
            this.currentClip = {}
            return
          }
          result.cid = cid
          result.page = 1

          result.name = videoshots[cindex].name // 稿件切换切片，展示名字
          result.index.shift()
          result.pageCount = Math.ceil(
            result.index.length / (result.img_x_len * result.img_y_len)
          )
          result.index = result.index.map((time) => secondsToTime(time))

          this.shotStore[cid] = result
          this.prepareImageSize(result.image[0])
          this.currentClip = cloneDeep(result)
          this.goPage(1)
        })
        .catch((_) => {
          this.currentClip = {}
        })
    },

    goPage(pn) {
      const currentClip = this.currentClip
      let page = 1
      if (pn > 0) {
        page = pn
      }
      if (page >= currentClip.pageCount) {
        page = currentClip.pageCount
      }
      const pagesize = currentClip.img_x_len * currentClip.img_y_len
      const indexLen = currentClip.index.length
      let nextSize = indexLen - pagesize * (page - 1)
      nextSize = nextSize > pagesize ? pagesize : nextSize
      const timeList = currentClip.index.slice(
        pagesize * (page - 1),
        pagesize * (page - 1) + nextSize
      )
      if (timeList.length === 0) {
        notify.error('切片数据缺失', 1500)
      }
      currentClip.page = page

      const videoshots = this.videoshots
      const videoIndex = videoshots.findIndex(
        (oneShot) => oneShot.dm_inid === currentClip.cid
      )

      let prev = ''
      let next = ''
      let lastPage = ''
      let nextPage = ''
      const pageCount = currentClip.pageCount

      if (page - 1 > 0) {
        prev = '上一页'
        lastPage = page - 1
        this.nextType = 'page'
      } else if (videoIndex > 0) {
        prev = '前一分P'
        this.nextType = 'video'
      }
      if (page + 1 <= pageCount) {
        next = '下一页'
        nextPage = page + 1
      } else if (videoIndex < videoshots.length - 1) {
        next = '后一分P'
      }

      const pageInfo = {
        prev,
        next,
        lastPage,
        nextPage,
        pageCount
      }

      currentClip.videoIndex = videoIndex
      currentClip.pageInfo = pageInfo
      currentClip.timeList = timeList
    },
    pageChangeByPageNum(pageNum) {
      const nextType = this.nextType
      if (nextType === 'video') {
        this.getClip(this.videoshots[pageNum].dm_inid)
      } else {
        this.goPage(pageNum)
      }
    },
    clickClipPreview({ time, idx }) {
      const { page, index = [], img_x_len = 0, img_y_len = 0 } = this.currentClip || {}
      const currentIndex = (page - 1) * img_x_len * img_y_len + idx + 1
      const total = index?.length || 0
      this.$emit('click-clip-preview', { time, total, currentIndex })
    },
    pageChange(type) {
      const currentClip = this.currentClip
      const nextType = this.nextType
      const pageOffset = type === 'next' ? 1 : -1

      if (nextType === 'video') {
        this.getClip(
          this.videoshots[currentClip.videoIndex + pageOffset].dm_inid
        )
      } else {
        this.goPage(currentClip.page + pageOffset)
      }
    },
    onImgError() {
      notify.error('切片获取失败 @bvcflow', 1500)
    },
    showBigModal() {
      this.$emit('showBigModal')
      this.bigModalOptions.isShow = true
      this.$nextTick(() => {
        const modalMask = document.querySelector('.big-modal-dialog')
        if (modalMask) {
          modalMask.scrollTop = 75
        }
      })
    },
    closeBigModal() {
      this.bigModalOptions.isShow = false
    },

    showFocus(payload) {
      const { src, time, idx = 0 } = payload
      this.focusPicInfo = {
        src,
        time,
        idx
      }
      this.focusModalOptions.isShow = true
    },

    prepareImageSize(src) {
      const image = new Image()
      image.onload = () => {
        let width = image.width
        let height = image.height

        const ratio = width / height

        const MAX_IMG_WIDTH = this.maxWidth
        const MAX_IMG_HEIGHT = this.maxHeight
        if (width > MAX_IMG_WIDTH) {
          width = MAX_IMG_WIDTH
          height = width / ratio
        }
        if (height > MAX_IMG_HEIGHT) {
          height = MAX_IMG_HEIGHT
          width = height * ratio
        }
        this.imgWidth = width
        this.imgHeight = height
      }
      image.src = src
    },
    showFocusImg(imgInfo) {
      this.focusImgInfo = { ...imgInfo }
      this.showFocusOptions.isShow = true
    },
    goToPre() {
      const currentIdx = this.focusImgInfo.idx
      if (!currentIdx) return
      const currentFocus = this.ai_img_urls[currentIdx - 1]
      this.focusImgInfo = { ...currentFocus, idx: currentIdx - 1 }
    },
    goToNext() {
      const currentIdx = this.focusImgInfo.idx
      if (currentIdx === this.ai_img_urls.length - 1) return
      const currentFocus = this.ai_img_urls[currentIdx + 1]
      this.focusImgInfo = { ...currentFocus, idx: currentIdx + 1 }
    },
    triggerPlayerSeek(mmss) {
      let hhmmss = mmss
      const timeList = mmss.split(':')
      if (timeList.length === 2) {
        hhmmss = `00:${mmss}`
      }
      this.focusModalOptions.isShow = false
      this.showFocusOptions.isShow = false
      const secs = moment.duration(hhmmss).as('seconds')
      this.$EventBus.$emit('seek-time', secs)
    }
  },
  components: {
    ClipViewer,
    ClipImgTip
  }
}

/**
 * seconds number to hh:mm:ss string
 */
function secondsToTime(seconds) {
  if (seconds === undefined) {
    return '-'
  }
  if (seconds === 0) {
    return '00:00'
  }
  const dur = moment.duration().add(seconds, 's')
  let hours = dur.hours()
  if (hours === 0) {
    hours = ''
  } else {
    hours = padTime(dur.hours())
    if (hours.length !== 0) {
      hours = `${hours}:`
    }
  }

  let minutes = padTime(dur.minutes())
  if (minutes.length !== 0) {
    minutes = `${minutes}:`
  }
  return hours + minutes + padTime(dur.seconds())
}

/**
 * left pad time number with 0
 */
function padTime(time) {
  return time < 10 ? `0${time}` : `${time}`
}
</script>

<style lang="stylus" scoped>
.clip-preview {
  content-visibility: auto;
  contain-intrinsic-size: auto 640px auto 360px;
}

.focus-img {
  min-width: 100px;
  min-height: 80px;
  max-width: 580px;
  max-height: 580px;
  margin: auto;
}
</style>
<style lang="stylus">
.big-modal-dialog {
  .el-dialog {
    min-height: 100%;
    margin: 20px auto !important;
  }
}

.body-focus-modal {
  .el-dialog {
    width: 600px;
  }

  .el-dialog__body {
    padding: 10px;
  }

  .link {
    color: var(--link-color);
    text-decoration: underline;

    &:hover {
      cursor: pointer;
    }
  }
}
</style>
