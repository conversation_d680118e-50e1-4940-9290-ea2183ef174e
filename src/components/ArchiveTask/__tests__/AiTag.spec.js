import { mount } from '@vue/test-utils'
import AiTag from '../AiTag.vue'
import Vuex from 'vuex'
import {
  has_no_tips,
  has_no_video_tips,
  has_no_cover_tips
} from './mockData/props'

let store

function myMount(props) {
  return mount(AiTag, {
    localVue: global.localVue,
    propsData: props,
    store
  })
}
describe('AiTag.vue', () => {
  beforeEach(() => {
    global.localVue.use(Vuex)
    store = new Vuex.Store({
      state: {
        user: {
          perms: {
            VIDEO_AI_SCORE: true
          },
          uid: 111
        }
      }
    })
  })
  it('无任何审核提示', () => {
    const wrapper = myMount(has_no_tips)
    expect(wrapper.vm.hasAiTag).toBe(false)
    expect(wrapper.vm.hasScore).toBe(false)
    expect(wrapper.vm.hasCoverTag).toBe(false)
    expect(wrapper.vm.aiCoverTags).toEqual([])
    expect(wrapper.vm.aiTagList).toEqual([])
    expect(wrapper.vm.score).toBe('')
  })
  it('无视频审核提示', () => {
    const wrapper = myMount(has_no_video_tips)
    expect(wrapper.vm.hasAiTag).toBe(false)
    expect(wrapper.vm.hasScore).toBe(true)
    expect(wrapper.vm.hasCoverTag).toBe(true)
    expect(wrapper.find('p').exists()).toBe(true)
    expect(wrapper.vm.score).toBe(98)
    expect(wrapper.vm.aiTagList).toEqual([])
    expect(wrapper.vm.aiCoverTags).toEqual([
      { name: '涉0号,国旗(文)', level: 0 },
      { name: '涉0号', level: 0 }
    ])
  })
  it('无封面审核提示', async () => {
    const wrapper = myMount(has_no_cover_tips)
    expect(wrapper.vm.hasAiTag).toBe(true)
    expect(wrapper.vm.hasScore).toBe(true)
    expect(wrapper.vm.hasCoverTag).toBe(false)
    expect(wrapper.find('p').exists()).toBe(true)
    expect(wrapper.find('p').text()).not.toContain('无视频审核提示')
    expect(wrapper.vm.score).toBe(88)
    expect(wrapper.vm.aiCoverTags).toEqual([])
    expect(wrapper.vm.aiTagList).toEqual([
      {
        name: '涉0号',
        data: ['00:03:56~00:04:00', '00:04:01~00:04:04'],
        level: 0
      },
      { name: '涉0号(音)', data: ['00:00:31', '00:03:55'], level: 0 },
      { name: '毛泽东', data: ['00:00:31', '00:03:55'], level: 1 }
    ])
    wrapper.vm.$EventBus.$on('seek-time', (payload) => {
      wrapper.vm.$emit('replay_seek_time', payload)
    })
    await wrapper.vm.goVideoTime('00:00:10')
    expect(wrapper.emitted('replay_seek_time')).toBeTruthy()
    expect(wrapper.emitted('replay_seek_time')[0]).toEqual([10])
  })
})
