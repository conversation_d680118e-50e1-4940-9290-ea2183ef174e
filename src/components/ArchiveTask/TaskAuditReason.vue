<template>
  <div class="task-audit-reason"  v-if="stateStr === '-2' || stateStr === '-4'">
    <div class="cate-row">
      <el-radio-group 
        v-if="dirty" 
        v-model="cateLabel"
        size="small" 
        @change="switchCate"
        v-behavior-track="'audit-reason-category-radio-group'" 
      >
        <el-radio-button :disabled="disabled" v-for="(radio) in reasonCates" :key="radio.label" :label="radio.label"></el-radio-button>
      </el-radio-group>
    </div>
    <div class="select-row" v-if="dirty">
      <NewSelect
        size="small"
        filterable
        default-first-option
        placeholder="审核理由分类"
        injectClass="custom-select-height"
        :value="reasonId"
        :disabled="disabled"
        @change="reasonChange"
        v-behavior-track="'audit-reason-category-selector'"
      >
        <el-option
          v-for="rea in actionReasons"
          :key="rea.id"
          :label="rea.reason"
          :value="rea.id">
        </el-option>
      </NewSelect>
    </div>
    <div class="reason-row">
      <AgTextarea 
        size="small" 
        placeholder="请输入内容"
        :rows="1" 
        :value="reason"
        :disabled="disabled" 
        @input="val => $emit('update:reason', val)"
        v-behavior-track="'audit-reason-textarea'"
      >
      </AgTextarea>
    </div>
  </div>
</template>
<script>
import { archiveApi } from '@/api/index'
import { ROUND_CATEGORY_ID } from '@/utils/constant'
import AgTextarea from '@/components/element-update/Textarea.vue'
import NewSelect from '@/components/element-update/Select'

export default {
  name: 'task-audit-reason',
  props: {
    state: {
      type: [String, Number],
      default: 0
    },
    reasonId: {
      type: [String, Number],
      default: ''
    },
    reason: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    dirty: {
      type: Boolean,
      default: false
    }
  },
  components: {
    AgTextarea,
    NewSelect
  },
  data() {
    return {
      selectedCateIndex: null,
      cateLabel: '',
      reasonCates: [] // 操作分类
    }
  },
  computed: {
    stateStr() {
      return (this.state || '').toString()
    },
    actionReasons() {
      return (this.reasonCates && this.reasonCates[this.selectedCateIndex] && this.reasonCates[this.selectedCateIndex].data) || []
    }
  },
  created() {
    this.actionChange()
  },
  watch: {
    state: {
      handler(val) {
        this.actionChange()
      }
    }
  },
  methods: {
    // radio切换选中，那么审核理由也要变
    switchCate(label) {
      if (!label) return

      const selectedCateIndex = this.reasonCates.findIndex((item) => item.label === label)
      if (selectedCateIndex > -1) {
        this.selectedCateIndex = selectedCateIndex
        if (this.actionReasons.length === 0) {
          this.$emit('update:reason', '')
          this.$emit('update:reasonId', '')
          return
        }
        const newReason = this.actionReasons[0]
        const newReasonStr = (newReason.id).toString()
        this.$emit('update:reason', newReason.reason)
        this.$emit('update:reasonId', newReasonStr)
      }
    },
    // dirty是代表手动改的操作项，否则为数据填充
    actionChange() {
      const { stateStr } = this
      if (stateStr !== '-2' && stateStr !== '-4') return

      // NOTE: 一审默认round为0
      const parent = ROUND_CATEGORY_ID[0][stateStr]
      archiveApi.getChargeReasonOrigin({
        type: 2,
        parent
      }).then((res) => {
        const result = res.data
        // 1.赋值radios
        this.reasonCates = result
        if (this.dirty) {
          // 2.默认选中第一个
          this.selectedCateIndex = 0
          if (result?.length) {
            this.cateLabel = result[0].label
            this.switchCate(this.cateLabel)
          }
        }
      }).catch(_ => {})
    },
    // 原因变化时
    reasonChange(id) {
      let name = ''
      const idx = this.actionReasons.findIndex((item) => item.id === id)
      if (idx > -1) {
        name = this.actionReasons[idx].reason
      }
      this.$emit('update:reason', name)
      this.$emit('update:reasonId', id)
    }
  }
}
</script>
<style lang="stylus" scoped>
.task-audit-reason{
  .cate-row{
    height 35px
    margin-bottom 5px
  }
  .reason-row{
    display inline-flex
    width 500px
    vertical-align top
  }
  .select-row{
    display inline-flex
    margin-right 10px
  }
}
</style>
