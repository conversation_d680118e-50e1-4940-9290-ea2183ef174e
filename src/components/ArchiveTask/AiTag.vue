<template>
  <div :class="$style.aiTag" v-if="hasAiTag || hasScore || hasCoverTag || hasAiString" v-behavior-track="'ai-risk-tag'">
    <p>
      <div v-if="perms.VIDEO_AI_SCORE && hasScore" style="display: inline-block" v-track.impression="{
        event: trackInfo.event,
        value: {
          ...(trackInfo.value || {}),
          subType: '评分'
        }
      }">
        [评分] <span :class="$style.warn">{{score}}分；</span>
    </div>
      <!-- 封面相关的AI提示，取值自ai_image -->
      <template v-if="hasCoverTag">
        <div style="display: inline-block" v-track.impression="{
            event: trackInfo.event,
            value: {
              ...trackInfo.value || {},
              subType: '封面'
            }
          }">
          <span v-for="(item, index) in aiCoverTags" :key="index" :style="item.level === 0 || item.level === 1
                ? 'background:rgba(238,80,55,0.4); font-weight:bold'
                : ''">{{'封面1' + '[' + item.name + ']'}}；</span>
        </div>
        <div style="display: inline-block" v-track.impression="{
            event: trackInfo.event,
            value: {
              ...trackInfo.value || {},
              subType: '封面2'
            }
          }">
          <span v-for="(item, index) in aiCoverTags2" :key="index" :style="item.level === 0 || item.level === 1
                ? 'background:rgba(238,80,55,0.4); font-weight:bold'
                : ''">{{'封面2' + '[' + item.name + ']'}}；</span>
        </div>
      </template>
      <!-- 视频中的AI提示，取值自ai_knowledge -->
      <template v-if="hasAiTag">
        <span v-track.impression="{
            event: trackInfo.event,
            value: {
              ...(trackInfo.value || {}),
              subType: '视频'
            }
          }" v-for="(tag, idx) in aiTagList" :key="idx" :style="
            tag.level === 0 || tag.level === 1
              ? 'background:rgba(238,80,55,0.4);'
              : ''
          ">
          <span :style=" tag.level === 0 || tag.level === 1
              ? 'font-weight:bold'
              : ''">[{{tag.name}}]</span>
          <span v-for="(time, subIdx) in tag.data" :key="subIdx">
            <template v-if="time.indexOf('~') !== -1">
              <span :class="$style.hit" @click="goVideoTime(time.split('~')[0])">
                {{ time.split('~')[0] }}
              </span>
              ~
              <span :class="$style.hit" @click="goVideoTime(time.split('~')[1])">
                {{ time.split('~')[1] }}
              </span>
            </template>
            <span v-else :class="$style.hit" @click="goVideoTime(time)">
              {{time}}
            </span>
            {{subIdx === tag.data.length - 1 ? '；' : ',' }}
          </span>
        </span>
      </template>
      <span v-else>{{ this.list.toString() }}</span>
    </p>
</div>
</template>
<script>
import moment from 'moment'
import { mapState } from 'vuex'

export default {
  name: 'AiTag',
  props: {
    list: {
      type: [Array, String],
      default() {
        return []
      }
    },
    score: {
      type: [Number, String],
      default: ''
    },
    reportBiz: {
      type: String,
      default: ''
    },
    cid: {
      type: [Number, String],
      default: ''
    },
    ai_cover_info: {
      type: Object,
      default() {
        return {}
      }
    },
    trackInfo: {
      type: Object,
      default() {
        return {
          event: 'ai-warning-impression'
        }
      }
    },
    timeClickable: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    ...mapState({
      uid: (state) => state.user.uid,
      perms: (state) => state.user.perms
    }),
    hasAiString() {
      // ai_knowledge.ai_tag_v2为字符串，如 无审核提示 等
      return !!(typeof this.list === 'string' && this.list.length > 0)
    },
    aiTagList() {
      let aiTagList = []
      this.list.map &&
        this.list.map((item) => {
          const keyArr = Object.keys(item)
          for (let key of keyArr) {
            // v1: data为时间数组
            // v2: data为{level,time}
            const data = item[key]
            if (Array.isArray(data)) {
              aiTagList.push({
                name: key,
                data
              })
            } else {
              aiTagList.push({
                name: key,
                data: data?.time || [],
                level: data?.level
              })
            }
          }
        })
      return aiTagList
    },
    aiCoverTags() {
      return this.ai_cover_info?.tags || []
    },
    aiCoverTags2() {
      return this.ai_cover_info?.tags2 || []
    },
    hasAiTag() {
      return this.aiTagList && this.aiTagList.length > 0
    },
    hasScore() {
      return this.score !== ''
    },
    hasCoverTag() {
      return !!(this.aiCoverTags?.length || this.aiCoverTags2?.length)
    }
  },
  data() {
    return {}
  },
  methods: {
    goVideoTime(time) {
      if (!this.timeClickable) return
      this.$tracker('ai-warning-click', {
        cid: this.cid,
        type: this.reportBiz,
        uid: this.uid,
        time: moment.duration(time).as('seconds')
      })
      this.$EventBus.$emit('seek-time', moment.duration(time).as('seconds'))
    }
  }
}
</script>
<style lang="stylus" module>
.aiTag {
  .warn {
    color: var(--red);
    font-weight: bold;
  }

  .hit {
    color: var(--link-color);
    cursor: pointer;
  }
}
</style>
