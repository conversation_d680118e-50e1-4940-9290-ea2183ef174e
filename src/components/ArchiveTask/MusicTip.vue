<template>
  <div :class="$style.musicTip" v-if="hasVideoCopyright" v-track.impression="trackInfo">
    <p v-html="totalAdviceHtml"></p>
    <p v-html="musicHtml"></p>
    <p v-html="videoHtml"></p>
  </div>
</template>
<script>
export default {
  name: 'MusicTip',
  props: {
    trackInfo: {
      type: Object,
      default() {
        return {
          event: 'video-copyright-impression'
        }
      }
    },
    videoCopyright: {
      // 兼容老数据类型array, 只是不报错，但会不展示
      type: [Object, Array],
      default() {
        return {}
      }
    }
  },
  computed: {
    hasVideoCopyright() {
      return this.videoCopyright?.VideoDetailInfo?.length || this.videoCopyright?.MusicDetailInfo?.length || this.videoCopyright?.InterveneStyle?.Value
    },
    musicHtml() {
      const musicTipArr = this.videoCopyright?.MusicDetailInfo || []
      const fullHtml = musicTipArr.map(tip => {
        const line = Object.values(tip).sort((a, b) => (a.Level || 0) - (b.Level || 0)) || []
        const lineHtml = line.map(item => {
          return `<span>${item.Key}：<span style="color: var(--red); font-weight: bold;">${item.Value || ''};&nbsp;</span></span>`
        }).join(' ')
        return lineHtml
      }).join('<br>')
      return fullHtml
    },
    videoHtml() {
      const videoTipArr = this.videoCopyright?.VideoDetailInfo || []
      const fullVideoHtml = videoTipArr.map(tip => {
        const line = Object.values(tip).sort((a, b) => (a.Level || 0) - (b.Level || 0)) || []
        const lineHtml = line.map(item => {
          return `<span>${item.Key}：<span style="color: var(--red); font-weight: bold;">${item.Value || ''};&nbsp;</span></span>`
        }).join(' ')
        return lineHtml
      }).join('<br>')
      return fullVideoHtml
    },
    totalAdviceHtml() {
      return this.videoCopyright?.InterveneStyle?.Value ? `<span>${this.videoCopyright?.InterveneStyle?.Key}：<span style="color: var(--red); font-weight: bold;">${this.videoCopyright?.InterveneStyle?.Value || ''};&nbsp;</span></span>` : ''
    }
  }
}
</script>
<style lang="stylus" module>
.musicTip
  .warn
    color var(--red)
    font-weight bold
</style>
