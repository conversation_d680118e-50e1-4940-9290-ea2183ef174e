<template>
  <div class="crash-info-wrap">
    <div class="crash-info" v-if="(crashList && crashList.length > 0) || watermarkTip">
      <div class="crash-info__module" v-if="crashList && crashList.length > 0">
        <p class="crash-info__title">撞车匹配提示：</p>
        <div class="crash-info__content">
          <span class="crash-info__text" v-for="(crash) in crashList" :key="crash.aid">
            <span>[{{WARNING_TEXT[crash.warning]}}]</span>
            <span v-if="!disableRedirect"><a target="_blank" class="a" :href="crash._filenameLink">{{crash.filename_stored}}</a></span>
            <span v-else>{{crash.filename_stored}}</span>
            |
            <span v-if="!disableRedirect"><a target="_blank" class="a" :href="crash._aidLink">{{crash.aid}}</a>；</span>
            <span v-else>{{crash.aid}}；</span>
          </span>
        </div>
      </div>
      <div class="crash-info__module" v-if="watermarkTip">
        <p class="crash-info__watermark">竞品水印提示：<span class="crash-info__hit">{{watermarkTip}}</span></p>
      </div>
    </div>
    <div class="crash-info-libray" v-if="tipList && tipList.length > 0">
      <AiLibrary style="margin-top: 20px" :tips="tipList" :disableRedirect="disableRedirect"></AiLibrary>
    </div>
  </div>
</template>
<script>
import { archiveApi } from '@/api/index'
import { genHost } from '@/api/utils'
import AiLibrary from '@/components/AiLibrary'

export default {
  name: 'CrashInfo',
  props: {
    cid: {
      type: [String, Number],
      required: true
    },
    disableRedirect: {
      type: Boolean,
      default: false
    }
  },
  components: {
    AiLibrary
  },
  data() {
    return {
      WARNING_TEXT: {
        0: '疑似',
        1: '肯定'
      },
      crashList: [],
      watermarkTip: '',
      tipList: []
    }
  },
  watch: {
    cid(nextVal) {
      this.getCrashData(nextVal)
      this.getCompetitorInfo(nextVal)
    }
  },
  mounted() {
    this.getCrashData()
    this.getCompetitorInfo()
  },
  methods: {
    getCrashData(cid) {
      archiveApi.getCrashInfo({
        cid: cid || this.cid
      }).then((res) => {
        const result = res.data || ''
        if (!result) return Promise.reject(new Error('撞车获取失败'))
        this.crashList = (result.crash || []).map(this.genLinks)
        this.tipList = (result.ai_knowledge || {}).data || []
      }).catch(_ => {
        this.crashList = []
        this.tipList = []
      })
    },
    getCompetitorInfo(cid) {
      archiveApi.getCompetitorInfo({
        cid: cid || this.cid
      }).then((res) => {
        this.watermarkTip = res.data || ''
      }).catch(_ => {})
    },
    genLinks(item) {
      const fnStored = item.filename_stored
      item._filenameLink = `${genHost()}` +
        '/aegis/#/archive/archive-video-task/resource/list?duration_from=&duration_to=' +
        `&filename=${fnStored}` +
        '&order=v_ctime&page=1&searchword=filename&sort_order=0&status=&typeid=&user_type=' +
        `&words=${fnStored}`

      const { fullPath } = this.$route
      // 撞车稿件来源，当作全部稿件列表
      item._aidLink = `${genHost()}/aegis/#/audit/tasks/detail/11?business_id=11&oid=${item.aid}&list_type=00&back=${encodeURIComponent(fullPath)}`
      // item._aidLink =
      return item
    }
  }
}
</script>
<style lang="stylus" scoped>
.crash-info
  background var(--content-bg-color)
  font-size 14px
  padding 10px
  color var(--text-color-dark-1)
  .crash-info__module
    margin-bottom 10px
    .crash-info__title
      font-size 14px
      margin-bottom 10px
    .crash-info__content
      display grid
      grid-template-columns 1fr 1fr
      grid-template-rows auto
      a
        color var(--link-color)
      .crash-info__text
        margin 5px 0
        span
          margin 0 3px
  .crash-info__hit
    color var(--red)
    font-w bold
.crash-info-libray
  background var(--content-bg-color)
  font-size 14px
  padding 10px
</style>
