<template>
  <div class="picture-box">
    <!-- 组件A -->
    <template v-if="showImagesBox">
      <ImagesBox
        ref="imagesBoxRef"
        v-if="imagesBoxVisible"
        :showing.sync="imagesBoxVisible"
        :images="imagesBoxDataSource"
        @overview="openViewer"
      />
      <el-checkbox-group
        v-show="!imagesBoxVisible"
        v-model="checkedPics"
        @change="changeCheck"
      >
        <div v-for="(item, idx) in pictureDataShow" :key="item.index">
          <el-tooltip maxWidth="200" :disabled="!item.tips">
            <p slot="content">
              <em>
                <span v-if="item.tips">{{ item.tips }}</span>
              </em>
              <LibrarySampleTip :tipLibraryIds="item.tipLibraryIds" />
            </p>
            <p class="line">
              <el-checkbox
                v-if="!checkboxOverImage"
                class="show-label"
                :label="item.index"
              >
                {{ `图序 ${item.index}` }}
              </el-checkbox>

              <em v-if="checkboxOverImage">
                图序{{ item.index }}&nbsp;
                <span v-if="item.tips" class="risk-tips">{{ item.tips }}</span>
              </em>
            </p>
          </el-tooltip>
          <div
            class="picture-wrap flex-lr"
            :class="{
              active:
                item.tips || (item.tipPictures && item.tipPictures.length > 0)
            }"
          >
            <div style="margin-right: 0">
              <img
                class="picture"
                @click="showPreivewImage(item.index, idx)"
                :src="item.minSrc"
              />
              <el-checkbox
                v-if="checkboxOverImage"
                class="picture-checkbox"
                :label="item.index"
              />
            </div>
            <div v-for="picObj in item.tipPictures" :key="picObj.url">
              <img
                class="picture ml-8"
                :src="picObj.url"
                @click="openImageViewer(picObj, item.index)"
                @load="(e) => handleImageLoad(e, picObj)"
              />
            </div>
          </div>
        </div>
      </el-checkbox-group>
    </template>
    <!-- 组件B -->
    <template v-else>
      <el-checkbox-group v-model="checkedPics" @change="changeCheck">
        <div v-for="(item, idx) in pictureDataShow" :key="item.index">
          <el-tooltip maxWidth="200" :disabled="!item.tips">
            <p slot="content">
              <em>
                <span v-if="item.tips">{{ item.tips }}</span>
              </em>
              <LibrarySampleTip :tipLibraryIds="item.tipLibraryIds" />
            </p>
            <p class="line">
              <em>
                图序{{ item.index }}&nbsp;
                <span v-if="item.tips" class="risk-tips">{{ item.tips }}</span>
              </em>
            </p>
          </el-tooltip>
          <div
            class="picture-wrap flex-lr"
            :class="{
              active:
                item.tips || (item.tipPictures && item.tipPictures.length > 0)
            }"
          >
            <div style="margin-right: 0">
              <img
                class="picture"
                @click="showPreivewImage(item.index, idx)"
                :src="item.minSrc"
              />
              <el-checkbox
                class="picture-checkbox"
                :label="item.index"
              ></el-checkbox>
            </div>
            <div v-for="picObj in item.tipPictures" :key="picObj.url">
              <img
                class="picture ml-8"
                :src="picObj.url"
                @click="openImageViewer(picObj, item.index)"
                @load="(e) => handleImageLoad(e, picObj)"
              />
            </div>
          </div>
        </div>
      </el-checkbox-group>
    </template>

    <Pagination
      ref="pagination"
      class="pagination"
      justify="start"
      :pager="pager"
      :computedPager="true"
      :tableData="pictures"
      :customPageSize="pageSize"
      showSize="medium"
    ></Pagination>

    <ViewerBox
      v-if="viewerBoxVisible"
      ref="viewer"
      :imgArray="viewerBoxDataSource"
      :options="options"
      viewerID="ViewBoxImages"
    />
  </div>
</template>
<script>
/**
 * @component
 * @assetTitle 图片合集展示组件
 * @assetDescription 展示图片，并提供编号、分页、选中功能，内置ViewerBox和ImagesBox两种单图展示组件
 * @assetImportName PictureBox
 * @assetTag 通用组件
 */
import store from '@/store'
import Pagination from '@/components/Pagination'
import ViewerBox from '@/components/ViewerBoxV2'
import ImagesBox from '@/components/ImagesBox/imagesBox.vue'
import drawRectLayer from '@/v2/utils/drawRectLayer'
import { LibrarySampleTip } from '@/v2/biz-components/workbench/'

export default {
  props: {
    // 图片合集
    pictures: {
      type: Array,
      default: () => []
    },
    // 选中的图片
    checkedData: {
      type: Array,
      default: () => {
        return []
      }
    },
    /**
     * 分页数据对象
     * @param pager.pn {number} - pageNumber 当前的页数
     * @param pager.ps {number} - pageSize 每页的条数
     * @param pager.total {number} - total 总页数
     */
    pager: {
      type: Object,
      default: () => {
        return {
          pn: 1,
          total: 0,
          ps: 18
        }
      }
    },
    // 使用ImagesBox展示单张图片
    showImagesBox: {
      type: Boolean,
      default: false
    },
    // checkbox的位置是否遮住图片
    checkboxOverImage: {
      type: Boolean,
      default: true
    }
  },
  components: {
    Pagination,
    ViewerBox,
    ImagesBox,
    LibrarySampleTip
  },
  data() {
    return {
      viewerBoxVisible: false,
      checkedPics: [],
      pageSize: [12, 24, 36, 60],
      tableData: [],
      options: {
        initialViewIndex: 0,
        keyboard: false,
        loading: false,
        transition: false
      },
      imagesBoxVisible: false
    }
  },
  computed: {
    pictureDataShow() {
      return [...this.pictures].splice(
        (this.pager.pn - 1) * this.pager.ps,
        this.pager.ps
      )
    },
    imagesBoxDataSource() {
      return this.pictureDataShow.map((item) => {
        return {
          src: this.canUseBfsCut(item.maxSrc)
            ? `${item.maxSrc}@1052w.webp`
            : item.maxSrc,
          name: `图序${item.index}`
        }
      })
    },
    viewerBoxDataSource() {
      return this.pictures.map((item) => {
        return {
          src: item.maxSrc, // 只有使用 viewerBox 的时候(全屏弹窗)才展示原图
          name: `图序${item.index}`
        }
      })
    }
  },
  watch: {
    checkedData(val) {
      if (val) {
        this.checkedPics = this.checkedData
      }
    },
    // 下个任务
    pictures() {
      this.imagesBoxVisible = false
      this.viewerBoxVisible = false
    },
    // 分页切换
    pictureDataShow() {
      this.imagesBoxVisible = false
      this.viewerBoxVisible = false
    }
  },
  mounted() {
    this.$nextTick((_) => {
      document.addEventListener('keyup', this.keyup)
      this.checkedPics = this.checkedData
    })
  },
  beforeDestroy() {
    document.removeEventListener('keyup', this.keyup)
  },
  methods: {
    showPreivewImage(index, idxInCurrentPage) {
      this.closePreview = false
      const pics = []
      this.options.initialViewIndex = index
      this.pictures.forEach((item) => {
        pics.push({
          src: item.maxSrc,
          name: `图序${item.index}`
        })
      })
      //
      if (this.showImagesBox) {
        this.imagesBoxVisible = true
        this.$nextTick(() => this.$refs.imagesBoxRef?.show(idxInCurrentPage))
      } else {
        this.openViewer(index)
      }
    },
    openViewer(index) {
      this.viewerBoxVisible = true
      this.$nextTick(() => this.$refs.viewer.openViewer(index))
    },
    canUseBfsCut(url) {
      return !url.includes('soft_delete_backup') // 被软删除的图片不支持 bfs 裁切
    },
    checkedAll(checkedBtn) {
      const pics = []
      this.checkedPics = []
      if (!checkedBtn) {
        const { pn, ps } = this.pager
        const endIndex = ps * pn - 1
        const startIndex = endIndex + 1 - ps
        this.pictures.forEach((item, index) => {
          if (index >= startIndex && index <= endIndex) {
            pics.push(item.index)
          }
        })
        this.checkedPics = [...pics]
      }
      this.changeCheck()
    },
    checkNone() {
      this.checkedPics = []
      this.changeCheck()
    },
    changeCheck() {
      // 返回选中的图片
      this.$emit('getCheckedPicsLength', this.checkedPics)
      store.dispatch('pictures/checkedPictures', this.checkedPics)
    },
    keyup(e) {
      const isLastPage =
        this.pager.pn === Math.ceil(this.pictures.length / this.pager.ps)
      const isFirstPage = this.pager.pn === 1
      // left | right
      if (e.keyCode === 39 && !isLastPage) {
        this.$refs.pagination.pagerPageChange(this.pager.pn + 1)
      } else if (e.keyCode === 37 && !isFirstPage) {
        this.$refs.pagination.pagerPageChange(this.pager.pn - 1)
      }
    },
    handleImageLoad(e, picObj) {
      if (picObj.type !== 'ocr') return
      if (e.target.parentElement) {
        drawRectLayer(e.target.parentElement, picObj.coordinates, {
          strokeStyle: '#FF0000',
          lineWidth: 1
        })
      }
    },
    openImageViewer(picObj, imageIndex) {
      this.$emit('preview-ocr-img', imageIndex)
    }
  }
}
</script>

<style lang="stylus">
.picture-box {
  width: 910px;
  margin: 0 auto;

  .line {
    width: 133px;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }

  .risk-tips {
    color: red;
    background: yellow;
  }

  .el-checkbox__label {
    display: none;
  }

  .picture-wrap {
    padding: 4px;
    margin-top: 8px;
    margin-right: 18px;
    position: relative;
    border: 2px solid transparent;

    &.active {
      border: 2px solid red;
    }
  }

  .picture {
    display: block;
    height: 170px;
    width: 133px;
    box-shadow: 0px 0px 2px 2px rgba(0, 0, 0, 0.1);
    border-radius: 2px;
    cursor: zoom-in;
  }

  .picture-checkbox {
    position: absolute;
    top: 10px;
    right: 10px;
  }

  .pagination {
    margin-top: 8px;
  }

  .el-checkbox-group {
    display: flex;
    flex-wrap: wrap;
    font-size: 14px;
    text-align: center;

    div {
      position: relative;

      em {
        display: block;
      }
    }

    .show-label {
      .el-checkbox__label {
        display: inline;
      }
    }
  }

  .el-checkbox__inner {
    height: 20px;
    width: 20px;

    &::after {
      top: 4px;
      left: 7px;
    }
  }
}
</style>
