<template>
  <div
    class="ailibrary"
    v-if="tips && tips.length > 0"
    v-behavior-track="'ai-library-tip'"
  >
    <p style="margin-bottom: 10px" v-if="!hideLabel">知识库匹配提示：</p>
    <p
      v-track.impression="trackInfo"
      v-for="tip in tips"
      :key="tip.id"
      style="margin-bottom: 10px"
      :style="
        tip.risk && tip.risk.indexOf('高危') > -1
          ? 'background:rgba(238,80,55,0.4);'
          : ''
      "
    >
      <span>【</span>
      <!-- 1. 知识库id -->
      <span
        v-if="!disableRedirect"
        class="tip-text"
        style="cursor: pointer"
        @click="goLibrary(tip)"
      >
        ID{{ tip.id }}
      </span>
      <span v-else class="tip-text">ID{{ tip.id }}</span>

      <!-- 2.来源 -->
      <template v-if="tip.source">
        |
        <span class="tip-text">[来源]</span>
        <span>{{ tip.source }}</span>
      </template>

      <!-- 3.内容描述 -->
      <template v-if="tip.keyword">
        |
        <span class="tip-text">[描述]</span>
        <span>{{ tip.keyword }}</span>
      </template>

      <!-- 4.处理方案 -->
      <template v-if="tip.process">
        |
        <span class="tip-text">[处理方案]</span>
        <span>{{ tip.process }}</span>
      </template>
      <span>】</span>

      <!-- 违规片段 -->
      <template v-if="tip.match_time && tip.match_time.length > 0">
        <span v-for="(time, index) in tip.match_time" :key="index">
          <template v-if="time.indexOf('~') !== -1">
            <span class="hit-text" @click="goVideoTime(time.split('~')[0])">
              {{ time.split('~')[0] }}
            </span>
            ~
            <span class="hit-text" @click="goVideoTime(time.split('~')[1])">
              {{ time.split('~')[1] }}
            </span>
          </template>
          <span v-else class="hit-text" @click="goVideoTime(time)">
            {{ time }}
          </span>
          {{ index === tip.match_time.length - 1 ? '' : ',' }}
        </span>
      </template>
    </p>
  </div>
</template>
<script>
import moment from 'moment'
import { genHost } from '@/api/utils'
export default {
  data() {
    return {}
  },
  props: {
    tips: {
      type: Array,
      default() {
        return []
      }
    },
    hideLabel: Boolean,
    disableRedirect: {
      type: Boolean,
      default: false
    },
    trackInfo: {
      type: Object,
      default() {
        return {
          event: 'ai-library-impression'
        }
      }
    }
  },
  methods: {
    goLibrary(tip) {
      const { category_ids: categoryIds, id } = tip
      const type = (categoryIds[0] || '').toString().substring(0, 2) || '10'
      const url = `${genHost()}/aegis/#/v3/audit-tools/library?breadcrumb=${categoryIds.join(
        '-'
      )}&type=${type}&ids=${id}`
      window.open(url, '_blank')
      this.$emit('click-library')
    },
    goVideoTime(time) {
      const timeArray = time.split(':')
      if (timeArray.length > 3) {
        time = timeArray.slice(0, 3).join(':')
      }
      this.$EventBus.$emit('seek-time', moment.duration(time).as('seconds'))
      this.$emit('click-library')
    }
  }
}
</script>
<style lang="stylus" scoped>
.ailibrary {
  display: block;
  font-size: 14px;

  .tip-text {
    color: var(--link-color);
  }

  .hit-text {
    color: var(--link-color);
    cursor: pointer;
  }
}
</style>
