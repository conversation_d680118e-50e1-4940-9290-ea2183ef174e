import store from '@/store/index'
import fillTemplate from '@/v2/utils/dynamicTemplateLiteral.js'

// 状态列表
export const STATES = {
  0: '开放浏览',
  1: '橙色通过',
  10000: '会员可见',
  15000: '新番搬运',
  20000: '字幕君',
  25000: 'VIP',
  30000: '真·职人',
  '-1': '待审稿件',
  '-2': '回收站',
  '-3': '网警锁定',
  '-4': '锁定稿件',
  '-5': '锁定（可浏览）',
  '-6': '修复待审',
  '-7': '暂缓审核',
  '-8': '补档待审',
  '-9': '等待转码',
  '-10': '延迟发布',
  '-11': '视频源待修',
  '-12': '上传失败',
  '-13': '允许评论待审',
  '-14': '临时回收站',
  '-15': '分发中',
  '-16': '转码失败',
  '-20': '稿件等待发布',
  '-30': '创建已提交',
  '-40': '定时发布',
  '-50': '仅自见',
  '-100': 'UP主删除'
}

export const VIDEO_STATES = {
  '': '全部',
  '-1': '待审核',
  0: '开放浏览',
  '-2': '打回',
  '-4': '锁定',
  10000: '会员可见',
  '-30': '创建已提交',
  '-100': '用户删除'
}

export const FINAL_STATES = {
  0: '开放浏览',
  '-2': '打回',
  '-4': '锁定'
}

export const AUDIT_STAGE = {
  2: '二审',
  3: '三审',
  4: '四审'
}

export const VIDEO_STATES_COLORS = {
  '-1': 'var(--primary-color)', // 待审核
  0: 'var(--success-color)', // 开放浏览
  '-2': 'var(--warning-color)', // 打回
  '-4': 'var(--error-color)' // 锁定
}

export const REASON_CATEGORY_PERM_NAMES = {
  uat: {
    // 打回
    964: 'AEGIS_ARCHIVE_REASON_COMMUNITY', // 社区回查
    996: 'AEGIS_ARCHIVE_REASON_DEFAULT', // 默认
    979: 'AEGIS_ARCHIVE_REASON_BIZ', // 广审
    992: 'AEGIS_ARCHIVE_REASON_AUDIT', // 审核回查
    994: 'AEGIS_ARCHIVE_REASON_OGV', // OGV
    1948: 'AEGIS_ARCHIVE_REASON_LESSON', // 课堂稿件
    2542: 'AEGIS_ARCHIVE_REASON_PLAYLET', // 微短剧
    // 锁定
    978: 'AEGIS_ARCHIVE_REASON_COMMUNITY', // 社区回查
    997: 'AEGIS_ARCHIVE_REASON_DEFAULT', // 默认
    980: 'AEGIS_ARCHIVE_REASON_BIZ', // 广审
    993: 'AEGIS_ARCHIVE_REASON_AUDIT', // 审核回查
    995: 'AEGIS_ARCHIVE_REASON_OGV', // OGV
    1949: 'AEGIS_ARCHIVE_REASON_LESSON', // 课堂稿件
    2543: 'AEGIS_ARCHIVE_REASON_PLAYLET' // 微短剧
  },
  pre: {
    // 打回
    2769: 'AEGIS_ARCHIVE_REASON_COMMUNITY', // 社区回查
    2768: 'AEGIS_ARCHIVE_REASON_DEFAULT', // 默认
    2770: 'AEGIS_ARCHIVE_REASON_BIZ', // 广审
    2771: 'AEGIS_ARCHIVE_REASON_AUDIT', // 审核回查
    2772: 'AEGIS_ARCHIVE_REASON_OGV', // OGV
    4670: 'AEGIS_ARCHIVE_REASON_LESSON', // 课堂稿件
    10143: 'AEGIS_ARCHIVE_REASON_PLAYLET', // 微短剧
    // 锁定
    2787: 'AEGIS_ARCHIVE_REASON_COMMUNITY', // 社区回查
    2773: 'AEGIS_ARCHIVE_REASON_DEFAULT', // 默认
    2788: 'AEGIS_ARCHIVE_REASON_BIZ', // 广审
    2789: 'AEGIS_ARCHIVE_REASON_AUDIT', // 审核回查
    2790: 'AEGIS_ARCHIVE_REASON_OGV', // OGV
    10144: 'AEGIS_ARCHIVE_REASON_PLAYLET' // 微短剧

  },
  prod: {
    // 打回
    2769: 'AEGIS_ARCHIVE_REASON_COMMUNITY', // 社区回查
    2768: 'AEGIS_ARCHIVE_REASON_DEFAULT', // 默认
    2770: 'AEGIS_ARCHIVE_REASON_BIZ', // 广审
    2771: 'AEGIS_ARCHIVE_REASON_AUDIT', // 审核回查
    2772: 'AEGIS_ARCHIVE_REASON_OGV', // OGV
    4670: 'AEGIS_ARCHIVE_REASON_LESSON', // 课堂稿件
    10143: 'AEGIS_ARCHIVE_REASON_PLAYLET', // 微短剧
    // 锁定
    2787: 'AEGIS_ARCHIVE_REASON_COMMUNITY', // 社区回查
    2773: 'AEGIS_ARCHIVE_REASON_DEFAULT', // 默认
    2788: 'AEGIS_ARCHIVE_REASON_BIZ', // 广审
    2789: 'AEGIS_ARCHIVE_REASON_AUDIT', // 审核回查
    2790: 'AEGIS_ARCHIVE_REASON_OGV', // OGV
    10144: 'AEGIS_ARCHIVE_REASON_PLAYLET' // 微短剧
  }
}

export const VIDEO_STATES_OPS = [
  {
    label: '全部',
    val: ''
  },
  {
    label: '待审核',
    val: '-1'
  },
  {
    label: '开放浏览',
    val: '0'
  },
  {
    label: '打回',
    val: '-2'
  },
  {
    label: '锁定',
    val: '-4'
  },
  {
    label: '会员可见',
    val: '10000'
  },
  {
    label: '创建已提交',
    val: '-30'
  },
  {
    label: '用户删除',
    val: '-100'
  }
]

export const RELATED_STATES = [
  '-30',
  '-1',
  '0',
  '10000',
  '-2',
  '-4',
  '-16',
  '-100'
]

export const RELATED_STATE_MAP = {
  '-30': {
    name: '创建已提交',
    className: 'blue'
  },
  '-1': {
    name: '待审',
    className: 'blue'
  },
  0: {
    name: '通过',
    className: 'success'
  },
  10000: {
    name: '会员',
    className: 'success'
  },
  '-2': {
    name: '打回',
    className: 'danger'
  },
  '-4': {
    name: '锁定',
    className: 'danger'
  },
  '-16': {
    name: '转码失败',
    className: 'danger'
  },
  '-100': {
    name: '删除',
    className: 'danger'
  }
}

// UAT: 回查及流程终结
export const UAT_RECHECK_FLOW_ID = [139, 140, 141, 143]

// PROD: 回查及流程终结
export const PROD_RECHECK_FLOW_ID = [31, 32, 33, 34]

// 搜索栏状态列表
export const SEARCH_STATES = [
  { id: '-1', name: '*待审', common: true },
  { id: '-6', name: '*修复待审', common: true },
  { id: '-8', name: '*补档待审', common: false },

  { id: '0', name: '开放浏览', common: true }, // state=0
  { id: '1', name: '橙色通过', common: true }, // state=1
  { id: '-2', name: '*回收站', common: true },
  { id: '-4', name: '*管理锁定', common: false },
  { id: '-7', name: '*暂缓审核', common: false },
  { id: '10000', name: '普通会员', common: true },
  { id: '100001', name: '橙色 普通会员', common: false },

  { id: '-9', name: '*等待转码', common: false },
  { id: '-15', name: '*分发中', common: false },
  { id: '-12', name: '*转储失败', common: false },
  { id: '-16', name: '*转码失败', common: false },

  { id: '-20', name: '*稿件等待发布', common: false },
  { id: '-30', name: '*创建已提交', common: false },
  { id: '-40', name: '*定时发布', common: true },
  { id: '-100', name: '*用户删除', common: true },
  { id: '-11', name: '视频源待修', common: false },
  { id: '-10', name: '*延迟审核', common: false },
  { id: '-13', name: '*允许评论待审', common: false },
  { id: '-3', name: '*网警锁定', common: false },
  { id: '-5', name: '*管理锁定（可浏览）', common: false },
  { id: '-14', name: '*临时回收站', common: false },

  { id: '20000', name: '*字幕君', common: false },
  { id: '200001', name: '*橙色 字幕君', common: false },
  { id: '25000', name: '*VIP', common: false },
  { id: '250001', name: '*橙色 VIP', common: false },
  { id: '30000', name: '*真·职人', common: false },
  { id: '300001', name: '*橙色 真·职人', common: false },
  { id: '15000', name: '*新番搬运', common: false },
  { id: '150001', name: '*橙色 新番搬运', common: false }
]

// 搜索栏属性
export const SEARCH_ATTRS = [
  { id: 1, name: '排行禁止' },
  { id: 2, name: '分区动态禁止' },
  { id: 3, name: '禁止网页输出' },
  { id: 4, name: '禁止移动客户端列表' },
  { id: 5, name: '搜索禁止' },
  { id: 6, name: '海外禁止' },
  { id: 7, name: '禁止推荐' },
  { id: 8, name: '禁止转载' },
  { id: 17, name: '跳转' },
  { id: 18, name: '是否影视' },
  { id: 21, name: '粉丝动态禁止' },
  { id: 22, name: '家长模式' },
  { id: 23, name: '付费' },
  { id: 101, name: '频道禁止' },
  { id: 102, name: '热门禁止' },
  { id: 25, name: '联合投稿' },
  { id: 27, name: '争议内容' }
]

// 审核全部可选操作状态
export const AUDIT_STATE_MAP = {
  0: '开放浏览',
  10000: '会员可见',
  '-2': '打回',
  '-4': '锁定稿件',
  '-7': '暂缓',
  1: '橙色通过',
  10001: '橙色通过，会员可见',
  '-11': '视频源待修',
  '-1': '待审',
  '-13': '允许评论待审',
  '-16': '转码失败',
  '-6': '修复待审'
}

export const VIDEO_AUDIT_REASONS = [
  '报封禁',
  '低危一号：',
  '高危一号：',
  '含竞品信息',
  '私单报备',
  '含争议引战信息',
  '含低俗信息',
  '含血腥暴力信息',
  '含抽奖信息',
  '含低创',
  '双重水印',
  '韩国相关',
  '版权兜底',
  '泛迷信/K2引流'
]

export const VIDEO_AUDIT_POSITION_MAP = {
  1: '视频',
  2: '标题',
  3: '分p标题',
  4: '简介',
  5: '标签',
  6: '稿件动态',
  7: '封面'
}

export const REASONS = [
  '含竞品信息',
  '私单报备',
  '含争议引战信息',
  '含低俗信息',
  '含血腥暴力信息',
  '含抽奖信息',
  '含低创',
  '双重水印',
  '韩国相关',
  '直播切片',
  '美妆护肤',
  '绘画改画',
  '非正当引导数据',
  '低质观感',
  '低成本拼接复述',
  '明星相关内容'
]

// https://www.tapd.bilibili.co/20065591/prong/stories/view/1120065591002492021?url_cache_key=a652eb57914536efadd3f734215a348c#
export const VIDEO_MONITOR_REASONS = [
  '涉0号，',
  '涉低俗，低俗导向',
  '涉其他领导人，',
  '涉先审1，',
  '涉低俗，asmr',
  '涉低俗，未成年',
  '涉重点舆情，',
  '涉低俗，女优',
  '涉突发事件，',
  '涉先发1，',
  '涉紧急事件，'
]

export const FROM_LIST_MAP = {
  0: 'channel_review',
  1: 'hot_review',
  2: 'excitation_list',
  3: 'fly_list'
}

// 审核理由
const STATE_REJECT = '-2'
const STATE_LOCK = '-4'

const CATE_AUDIT_23 = {
  [STATE_REJECT]: 77, // 打回
  [STATE_LOCK]: 78 // 锁定
}

const CATE_AUDIT_SPECIAL = {
  [STATE_REJECT]: 91, // 打回
  [STATE_LOCK]: 92 // 锁定
}

export const IS_COMMON = '1'
export const NOT_COMMON = '0'

export const ROUND_CATEGORY_ID = {
  0: {
    // 一审
    [STATE_REJECT]: 75, // 打回
    [STATE_LOCK]: 76 // 锁定
  },
  10: CATE_AUDIT_23, // 二审
  20: CATE_AUDIT_23, // 三审
  '20-special': CATE_AUDIT_SPECIAL, // 三审特殊
  21: {
    // 21+ 一二三查
    [STATE_REJECT]: 79, // 打回
    [STATE_LOCK]: 80 // 锁定
  }
}

// 视频模块
// export const VIDEO_FORBIDS = [
//   'norank', 'noindex', 'push_blog', 'norecommend', 'nosearch', 'nochannel', 'nohot'
// ]
export const VIDEO_FORBIDS = [
  'norank',
  'noindex',
  'push_blog',
  'norecommend',
  'nosearch'
]

export const VIDEO_FORBID_MAP = {
  norank: '排行禁止',
  noindex: '分区动态禁止',
  push_blog: 'B博禁止',
  norecommend: '推荐禁止',
  // nosearch: '搜索禁止',
  nochannel: '频道禁止'
  // nohot: '热门禁止'
}

export const PERM_MAP = {
  rank_main: 'NO_RANK',
  dynamic_main: 'NO_INDEX',
  recommend_main: 'NO_RECOMMEND',
  nochannel: 'NO_CHANNEL',
  nohot: 'NO_HOT',
  nosearch: 'NO_SEARCH',
  show_mobile: 'NO_MOBILE',
  nomobile: 'NO_MOBILE',
  show_web: 'NO_WEB',
  noweb: 'NO_WEB',
  norank: 'NO_RANK',
  noindex: 'NO_INDEX',
  norecommend: 'NO_RECOMMEND',
  oversea_block: 'OVERSEA_BLOCK',
  push_blog: 'ARC_PUSH_BLOG',
  only_fav_view: 'ONLY_FAV_VIEW',
  hot_down: 'HOT_DOWN',
  rank_down: 'RANK_DOWN'
}

export const ATTR_FIELDS = [
  'norank',
  'noindex',
  'norecommend',
  'nohot',
  'hot_down',
  'nosearch',
  'push_blog',
  'rank_down'
  // 新增禁搜索和禁粉丝动态
]

export const ATTR_FIELD_TEXT = {
  norank: '禁排行',
  noindex: '禁分区动态',
  norecommend: '禁推荐',
  nochannel: '禁频道',
  nohot: '禁热门',
  hot_down: '热门降权',
  nosearch: '禁搜索',
  push_blog: '禁粉丝动态',
  rank_down: '排行降权'
}

export const LIMIT_ATTR = [
  'norank',
  'noindex',
  'norecommend',
  'nohot',
  'hot_down',
  'rank_down',
  'nosearch',
  'push_blog'
]

export const LIMIT_ATTR_TEXT = {
  norank: '排行禁止',
  noindex: '分区动态禁止',
  norecommend: '热门推荐禁止',
  nohot: '热门禁止',
  hot_down: '热门降权',
  nosearch: '搜索禁止',
  push_blog: '粉丝动态禁止',
  rank_down: '排行降权'
}

export const ACCESS = [0, 10000, 15000, 20000, 25000, 30000]

export const ACCESS_MAP = {
  0: '所有用户可见',
  10000: '会员用户可见',
  15000: '新番搬运可见',
  20000: '字幕君可见',
  25000: 'VIP可见',
  30000: '真·职人可见'
}

export const TYPE_MAP = {
  1: 'aid',
  2: 'season_id'
}

// 审核可选操作状态
export const AUDIT_STATES = {
  0: '开放浏览',
  10000: '会员可见',
  '-2': '打回',
  '-4': '锁定稿件',
  '-7': '暂缓',
  1: '橙色通过',
  10001: '橙色通过，会员可见',
  '-11': '视频源待修',
  '-1': '待审',
  '-13': '允许评论待审',
  '-16': '转码失败',
  '-6': '修复待审'
}

export const TASK_ATTR_FORM_FIELDS_TEMP = [
  'norank',
  'noindex',
  'norecommend',
  'nosearch',
  'oversea_block',
  'push_blog'
]

// 禁止项
export const ATTR_FORM_FIELD_TEXT = {
  norank: '排行',
  nodynamic: '动态',
  noindex: '动态',
  norecommend: '推荐',
  nosearch: '搜索',
  oversea_block: '海外',
  push_blog: 'B博',
  only_fav_view: '仅收藏可见'
}

export const COPYRIGHT_TYPE_MAP = {
  1: '自制',
  2: '转载',
  0: '未知'
}

export const USER_TYPE_MAP = {
  '-2': { name: '风控识别疑似虚假账号', className: 'black-market-user' },
  '-3': { className: 'danger-user' }, // 直播间处罚用户
  1: { name: '优质用户', className: 'top-user' },
  2: { name: '高危用户', className: 'danger-user' },
  5: { name: '时政用户', className: 'enterprise-user' },
  6: { name: '三禁用户', className: 'block-user' },
  7: { name: '企业用户', className: 'enterprise-user' },
  10: { name: '二禁用户', className: 'block-user' },
  44: { name: '内部监控', className: 'enterprise-user' },
  46: { name: '高危复审', className: 'enterprise-user' },
  52: { name: '两性用户组', className: 'enterprise-user' },
  84: { name: '明星号', className: 'enterprise-user' },
  90: { name: '医学资质组', className: 'enterprise-user' },
  91: { name: '社科人文组', className: 'enterprise-user' },
  97: { name: '视频高危用户', className: 'danger-user' },
  99: { name: '医疗资质一级', className: 'enterprise-user' },
  100: { name: '医疗资质二级', className: 'enterprise-user' },
  102: { name: '金融资质', className: 'enterprise-user' },
  77: { name: '回查授权组', className: 'enterprise-user' }
}

// json格式的自定义配置
export const CUSTOM_CONFIG_TYPE_JSON = {
  3: '权限',
  5: '属性位',
  11: '前后端中间件',
  15: '敏感词',
  30: '调度配置',
  31: '操作项',
  32: '代办DAG',
  33: '分级',
  34: '业务推模式',
  35: '业务推模式',
  36: '新人模式配置',
  39: '前端配置',
  41: '延迟待办',
  44: '操作理由',
  45: '策略规则配置'
}

export const SETTING_TYPE_JSON = {
  1: 'ES索引(流程)',
  2: '前端回调接口',
  3: '权限',
  4: '保留字段',
  5: '属性位',
  6: '删除状态',
  7: '监控配置',
  9: '适配器',
  10: '前端展示列表',
  11: '前后端中间件',
  12: '临时错误码',
  13: '审核回调',
  14: '前端表单组件',
  15: '敏感词',
  23: '工作台配置',
  40: '变迁操作项过滤'
}

export const LIST_MAP = {
  // 全部
  '00': '全部稿件列表',
  // 稿件
  11: '二审常规列表',
  12: '二审特殊分区列表',
  13: '二审特殊修复待审',
  14: '三审特殊分区列表',
  15: '三审特殊修复待审',
  16: '修复待审稿件列表',
  // 下面这个列表现在不展示
  17: '暂缓稿件列表',
  // 回查
  22: '热门回查列表',
  // 下面这三个列表现在不展示
  23: '一查分区风险列表',
  24: '二查内容规范列表',
  25: '三查累计点击列表',
  '23_new': '一查分区风险列表',
  '24_new': '二查内容规范列表',
  '25_new': '三查累计点击列表',
  26: '激励回查列表',
  27: '起飞回查列表',
  '27_1': '起飞回查列表',
  '27_2': '起飞回查列表',
  28: '审核回查列表',
  '28_1': '审核回查列表',
  '28_2': '审核回查列表',
  '28_3': '审核回查列表',
  '28_4': '审核回查列表',
  29: '排行回查列表',
  // 生产
  31: '常规二审列表',
  32: '常规三审列表',
  33: '机密待查列表',
  34: '机密回查列表',
  35: '全部过审列表',
  36: '全部打回列表',
  37: '合作方嵌套列表',
  38: '全部百视通列表',
  39: '特殊机密二审列表',
  40: '特殊机密三审列表',
  // 活动
  41: '活动稿件列表',
  // 绿洲商单
  50: '全部绿洲商单列表',
  51: '绿洲商单二审列表',
  52: '绿洲商单三审列表',
  53: '绿洲商单四审列表',
  54: '绿洲商单二审修复待审列表',
  55: '绿洲商单三审修复待审列表',
  // 私单
  60: '全部私单',
  61: '私单四审待审',
  62: '私单四审修复待审',
  63: '私单三审待审',
  64: '私单三审修复待审',
  // 付费
  71: '付费待审',
  72: '付费修复待审',
  // 花火商单
  80: '全部花火商单列表',
  81: '花火商单二审列表',
  82: '花火商单三审列表',
  83: '花火商单四审列表',
  84: '花火商单二审修复待审列表',
  85: '花火商单三审修复待审列表',
  86: '花火多绑商单广审',
  87: '花火补绑商单广审',
  88: '花火补绑商单回查',
  // 付费系列
  90: '全部付费系列稿件',
  91: '付费系列待审',
  92: '付费系列修复待审',
  // 付费合集系列
  93: '全部付费合集稿件',
  // 动态稿件
  101: '动态稿件回查',
  102: '动态稿件修复待审',

  // 新频道审核，从新频道->频道精选审核/频道优质审核 进入
  'channel-11': '频道精选审核列表',
  'channel-12': '频道优质审核列表',

  // 版权音乐
  103: '版权音乐二审待审',
  104: '版权音乐修复待审',

  // 海外稿件
  105: '海外机密审核列表',
  // '106': '海外机密回查列表',
  106: '全部海外OGV稿件列表',
  107: '海外UGC二审待审',
  108: '海外UGC修复待审',
  109: '全部海外UGC稿件',
  // '110': '对内投放回查',
  111: '对外投放回查',
  112: 'OGV特殊分区回查',
  114: '全部活动稿件列表',
  115: '活动待审列表',
  116: '活动修复待审列表',
  117: '动态回查稿件分流',
  118: '特殊用户回查',
  119: '基础生态回查',
  120: '限流通知专用',
  121: '政务号专审通道',
  122: '人工拆条列表'
}

export const WorkState = {
  0: { name: '未处理', className: 'unprocessed' },
  1: { name: '已处理', className: 'processed' },
  2: { name: '已关闭', className: 'closed' }
}

export const UAT_REASON_FLOW = {
  default: {
    [STATE_REJECT]: {
      type: 2,
      parent: 77
    },
    [STATE_LOCK]: {
      type: 2,
      parent: 78
    },
    listType: []
  },
  flowerFire: {
    [STATE_REJECT]: {
      type: 2,
      parent: 30062
    },
    [STATE_LOCK]: {
      type: 2,
      parent: 78
    },
    listType: ['80', '85', '86', '87']
  },
  flowerFireForth: {
    [STATE_REJECT]: {
      type: 2,
      parent: 30062
    },
    [STATE_LOCK]: {
      type: 2,
      parent: 80
    },
    listType: ['83']
  },
  privateForth: {
    [STATE_REJECT]: {
      type: 2,
      parent: 60067
    },
    [STATE_LOCK]: {
      type: 2,
      parent: 80
    },
    listType: ['60', '61', '62']
  },
  all: {
    [STATE_REJECT]: {
      type: 2,
      parent: 75
    },
    [STATE_LOCK]: {
      type: 2,
      parent: 76
    },
    listType: ['00', '26', '41', '102', '109', '106']
  },
  more: {
    [STATE_REJECT]: {
      type: 2,
      parent: 77
    },
    [STATE_LOCK]: {
      type: 2,
      parent: 78
    },
    listType: [
      '11',
      '12',
      '13',
      '14',
      '15',
      '16',
      '31',
      '32',
      '33',
      '36',
      '38',
      '51',
      '52',
      '54',
      '55',
      '90',
      '91',
      '92',
      '103',
      '104',
      '107',
      '108',
      '105'
    ]
  },
  hotBack: {
    [STATE_REJECT]: {
      type: 2,
      parent: 79
    },
    [STATE_LOCK]: {
      type: 2,
      parent: 80
    },
    listType: [
      '22',
      '23_new',
      '24_new',
      '25_new',
      '27_2',
      '28',
      '29',
      '34',
      '35',
      '50',
      '53',
      '60',
      '61',
      '62',
      '101',
      '111'
    ]
  }
}

export const PROD_REASON_FLOW = {
  default: {
    [STATE_REJECT]: {
      type: 2,
      parent: 77
    },
    [STATE_LOCK]: {
      type: 2,
      parent: 78
    },
    listType: []
  },
  flowerFire: {
    [STATE_REJECT]: {
      type: 2,
      parent: 120017
    },
    [STATE_LOCK]: {
      type: 2,
      parent: 78
    },
    listType: ['80', '85', '86', '87']
  },
  flowerFireForth: {
    [STATE_REJECT]: {
      type: 2,
      parent: 120017
    },
    [STATE_LOCK]: {
      type: 2,
      parent: 80
    },
    listType: ['83']
  },
  privateForth: {
    [STATE_REJECT]: {
      type: 2,
      parent: 150017
    },
    [STATE_LOCK]: {
      type: 2,
      parent: 80
    },
    listType: ['60', '61', '62']
  },
  all: {
    [STATE_REJECT]: {
      type: 2,
      parent: 75
    },
    [STATE_LOCK]: {
      type: 2,
      parent: 76
    },
    listType: ['00', '26', '41', '102', '109', '106']
  },
  more: {
    [STATE_REJECT]: {
      type: 2,
      parent: 77
    },
    [STATE_LOCK]: {
      type: 2,
      parent: 78
    },
    listType: [
      '11',
      '12',
      '13',
      '14',
      '15',
      '16',
      '31',
      '32',
      '33',
      '36',
      '38',
      '51',
      '52',
      '54',
      '55',
      '90',
      '91',
      '92',
      '103',
      '104',
      '107',
      '108',
      '105'
    ]
  },
  hotBack: {
    [STATE_REJECT]: {
      type: 2,
      parent: 79
    },
    [STATE_LOCK]: {
      type: 2,
      parent: 80
    },
    listType: [
      '22',
      '23_new',
      '24_new',
      '25_new',
      '27_2',
      '28',
      '29',
      '34',
      '35',
      '50',
      '53',
      '60',
      '61',
      '62',
      '101',
      '111'
    ]
  }
}
export const STAFF_BUSINESS_STYLE_MAP = {
  0: '普通',
  1: '商业'
}

export const STAFF_BUSINESS_STYLE = [
  {
    id: 0,
    name: '普通'
  },
  {
    id: 1,
    name: '商业'
  }
]

export const BUSINESS_COMMENT_CONFIG = {
  default: {
    default: {
      onlyEmojiHit: true,
      oid: '',
      rpid: ''
    }
  }
}

// 只有数值变化才会提交
export const UPDATE_ATTR_SUBMIT = [
  'dynamicfold', // 因为大提交不传了，可以删
  'no_relay',
  'no_recommend_pool',
  'nochannel',
  'nohot',
  'hot_push_whitelist', // 因为大提交不传了，可以删
  'recommend_apart', // 因为大提交不传了，可以删
  'hot_down',
  'no_share', // 因为大提交不传了，可以删
  'is_360',
  'only_fav_view',
  'is_dolby'
]

// 是属性位却需要填写备注的
export const NEED_NOTE_ATTR = [
  {
    key: 'only_fav_view',
    name: '仅收藏可见'
  },
  {
    key: 'no_public',
    name: '非公开'
  }
]

// 话题二审理由
export const UAT_TOPIC_REJECT_REASON = [
  {
    id: 26,
    reason: '涉及违禁信息'
  },
  {
    id: 27,
    reason: '涉及低俗信息'
  },
  {
    id: 28,
    reason: '涉及色情信息'
  },
  {
    id: 29,
    reason: '涉及不适宜内容'
  },
  {
    id: 30,
    reason: '涉及人身攻击信息'
  },
  {
    id: 31,
    reason: '涉嫌时政内容'
  },
  {
    id: 32,
    reason: '涉及非法网站信息'
  },
  {
    id: 33,
    reason: '标签或分类与内容不符'
  },
  {
    id: 34,
    reason: '不支持广告水印'
  },
  {
    id: 35,
    reason: '图片画质过低'
  },
  {
    id: 36,
    reason: '不支持第三方链接'
  },
  {
    id: 37,
    reason: '不支持二维码'
  },
  {
    id: 160,
    reason: '随便一个'
  },
  {
    id: 180,
    reason: '违规抽奖-附加自定义的中奖规则'
  },
  {
    reason: '自定义',
    id: '自定义'
  }
]

export const PROD_TOPIC_REJECT_REASON = [
  {
    id: 5,
    reason: '涉及违禁信息'
  },
  {
    id: 6,
    reason: '涉及低俗信息'
  },
  {
    id: 7,
    reason: '涉及色情信息'
  },
  {
    id: 8,
    reason: '涉及不适宜内容'
  },
  {
    id: 9,
    reason: '涉及人身攻击信息'
  },
  {
    id: 10,
    reason: '涉嫌时政内容'
  },
  {
    id: 11,
    reason: '涉及非法网站信息'
  },
  {
    id: 12,
    reason: '标签或分类与内容不符'
  },
  {
    id: 13,
    reason: '不支持广告水印'
  },
  {
    id: 14,
    reason: '图片画质过低'
  },
  {
    id: 15,
    reason: '不支持第三方链接'
  },
  {
    id: 16,
    reason: '不支持二维码'
  },
  {
    id: 155,
    reason: '违规抽奖-附加自定义中奖条件'
  },
  {
    id: 156,
    reason: '违规商业信息'
  },
  {
    id: 267,
    reason: '违规抽奖-引导对稿件点赞、投硬币、收藏、分享等行为'
  },
  {
    id: 268,
    reason: '违规抽奖-附加自定义领奖条件'
  },
  {
    id: 269,
    reason: '违规抽奖-奖品内容、数量不明确'
  },
  {
    id: 270,
    reason: '违规抽奖-动态文案与抽奖设置矛盾'
  },
  {
    id: 271,
    reason: '违规抽奖-单个奖品的价值超过人民币5万元'
  }
]

export const TOPIC_SECOND_REJECT_REASON = [
  {
    id: 1001,
    reason: '活动投稿要求不得含有危险性行为，请修改'
  },
  {
    id: 1002,
    reason: '活动投稿要求不得含有血腥暴力内容，请修改'
  },
  {
    id: 1003,
    reason: '活动投稿要求不得含有封建、迷信内容，请修改'
  },
  {
    id: 1004,
    reason: '活动投稿要求不得含有攻击性行为，请修改。'
  },
  {
    id: 1005,
    reason: '活动投稿要求需符合分区投稿规则，请修改'
  },
  {
    id: 1006,
    reason: '活动投稿要求不得含有非正当引导数据行为，请修改'
  },
  {
    id: 1007,
    reason: '活动投稿要求不得含有违规推广内容，请修改'
  },
  {
    id: 1008,
    reason: '活动规则含有不适宜词条或具有争议性，请修改'
  },
  {
    id: 1009,
    reason: '话题含有不适宜词条或具有争议性，请修改'
  },
  {
    reason: '自定义',
    id: '自定义'
  }
]

// UAT: 话题二审
export const UAT_TOPIC_FLOW_ID = 295

export const PROD_TOPIC_FLOW_ID = 155

// 用户未成年提示映射
export const USER_UNDERAGE_TIP_MAP = {
  0: '未成年',
  1: '成年',
  2: '未知'
}

// 用户历史封禁
export const USER_FORBIDDEN_BEFORE = {
  UNKNOWN: 0, // 未知
  EVER: 1, // 是
  NEVER: 2 // 否
}

// 用户未成年提示来源
export const USER_UNDERAGE_SOURCE_MAP = {
  realname: '实名查询',
  tel: '手机号'
}

// 小提交
export const FORBID_ATTR_SUBMIT = [
  'note',
  'note_tag',
  'noweb',
  'nomobile',
  'oversea_block',
  // 'push_blog',
  'dynamicfold',
  'no_relay',
  'recommend_apart',
  'hot_push_whitelist',
  'no_share',
  'no_share_download',
  'online_smooth_down',
  'no_space',
  'no_recommend_pool',
  'no_recommend_live',
  'no_recommend_activity',
  'no_play_ad',
  'no_play_ad_recommend',
  'no_play_bgm',
  'no_play_center_card',
  'no_play_comment_bar',
  'no_play_negative',
  'no_play_tag',
  'no_play_toast',
  'nochannel'
]

// 大提交删掉的
export const FORBID_ATTR_DELETE = [
  'noweb',
  'nomobile',
  'oversea_block',
  'nochannel',
  // 'push_blog',
  'dynamicfold',
  'no_relay',
  'recommend_apart',
  'hot_push_whitelist',
  'no_share',
  'no_share_download',
  'online_smooth_down',
  'no_space',
  'no_recommend_pool',
  'no_recommend_live',
  'no_recommend_activity',
  'no_play_ad',
  'no_play_ad_recommend',
  'no_play_bgm',
  'no_play_center_card',
  'no_play_comment_bar',
  'no_play_negative',
  'no_play_tag',
  'no_play_toast'
]

// 需要备注
export const BATCH_NOTE_ATTR = [
  // 这两个是稿件属性
  'only_fav_view',
  'hot_push_whitelist',
  // 以下是禁止项的
  'norank',
  'noindex',
  'norecommend',
  'no_recommend_pool',
  'recommend_apart',
  'nosearch',
  'nochannel',
  'nohot',
  'hot_down',
  'rank_down',
  'noweb',
  'nomobile',
  'oversea_block',
  'dynamicfold',
  'push_blog',
  'no_relay',
  'no_share',
  'no_public',
  'no_share_download',
  'no_reprint',
  'requirepay',
  'badgepay',
  'bangumi',
  'hd',
  'allow_bp',
  'parent_mode',
  'allowtag',
  'is_pgc',
  'teenager_mode',
  'is_360',
  'only_fav_view',
  'only_self',
  'is_dolby',
  'hot_push_whitelist',
  'no_public',
  'no_fans_push',
  'no_fans_videocard',
  'share_cover_avatar',
  'online_smooth_down',
  'no_space',
  'no_recommend_live',
  'no_recommend_activity',
  'no_play_ad',
  'no_play_ad_recommend',
  'no_play_bgm',
  'no_play_center_card',
  'no_play_negative',
  'no_play_tag',
  'no_play_toast'
]

// 活动规则自制限制映射
export const MISSION_RULE_SELF_MAP = {
  0: '无限制',
  1: '自制不允许重投',
  2: '自制允许重投'
}

export const FORBID_TEXT_MAP = {
  norank: '排行禁止',
  noindex: '分区动态禁止',
  norecommend: '热门推荐禁止',
  no_recommend_pool: '屏蔽推荐池',
  recommend_apart: '推荐打薄',
  nosearch: '搜索禁止',
  nochannel: '频道禁止',
  nohot: '热门禁止',
  hot_down: '热门降权',
  rank_down: '排行降权',
  noweb: '禁止网页端输出',
  nomobile: '禁止移动端输出',
  oversea_block: '禁止海外版',
  push_blog: '粉丝动态禁止',
  no_relay: '动态转发禁止',
  no_share: '分享禁止',
  no_share_download: '禁止分享下载',
  online_smooth_down: '平滑展示',
  hot_push_whitelist: '设为热门push',
  show_main: '全部其他禁止',
  dynamicfold: '动态折叠',
  no_space: '空间禁止',
  no_recommend_live: '屏蔽直播推荐',
  no_recommend_activity: '屏蔽活动推荐',
  no_play_ad: '屏蔽广告推荐',
  no_play_center_card: '屏蔽播放页C位卡',
  no_play_ad_recommend: '屏蔽商广推荐',
  no_play_bgm: '屏蔽BGM',
  no_play_tag: '屏蔽标签区',
  no_play_negative: '屏蔽点踩',
  no_play_toast: '屏蔽toast提示',
  no_play_comment_bar: '屏蔽评论小黄条'
}

export const SHOW_LIMIT_BUTTON = [
  '00',
  '22',
  '23',
  '24',
  '25',
  '23_new',
  '24_new',
  '25_new',
  '29',
  '120',
  '60',
  '50',
  '80'
]

export const LIMIT_REASON = {
  uat: '60069',
  pre: '1240016',
  prod: '1240016'
}

export const AUTO_LIMIT_REASON = {
  uat: '120063',
  pre: '1240016',
  prod: '1300016'
}

export const UAT_UNLIMIT_REASON_ID = '210215'

export const PROD_UNLIMIT_REASON_ID = '1300012'

export const UNLIMIT_REASON = '受到影响的曝光已恢复正常'

/**
 * 视频审核二次确认Code & 负向操作code
 */
export const VIDEO_AUDIT_RECHECK_CODE_LIST = [
  {
    // 复审
    code: 92144,
    msg: '当前提交操作与审核提交不一致，请确认是否继续提交，点击【确认】按当前提交操作生效，点击【取消】关闭弹窗重新选择操作。'
  },
  {
    // 双盲
    code: 92149,
    msg: '当前操作与首次生效结果不一致，确认是否按当前操作提交'
  }
]

export const DYNAMIC_BIZ = [18, 59]

const ENV_CONSTANTS = {
  MONITOR_TID: {
    uat: 688,
    pre: 2046,
    prod: 2046
  }, // tid 需监控
  FLOW_RECHECK_TAGS: {
    uat: [679, 680, 681, 684, 685, 686],
    pre: [2056, 2055, 2054, 2052, 2051, 2053],
    prod: [2056, 2055, 2054, 2052, 2051, 2053]
  },
  // 稿件回查新人模式资源列表页模板 legoId
  ARCHIVE_REVIEW_NEWER_LIST_LEGOIDS: {
    uat: '828',
    pre: '833',
    prod: '292'
  },
  VIDEO_AUDIT_NEWER_LIST_LEGO_ID: {
    uat: '763',
    pre: '773',
    prod: '232'
  },
  GROUP_ID_CLASS: {
    uat: {
      349: 'enterprise-user', // 对应原用户组 46 高危复审
      348: 'danger-user' // 对应原用户组 97 视频高危
    },
    pre: {
      295: 'enterprise-user', // 对应原用户组 46 高危复审
      296: 'danger-user' // 对应原用户组 97 视频高危
    },
    prod: {
      295: 'enterprise-user', // 对应原用户组 46 高危复审
      296: 'danger-user' // 对应原用户组 97 视频高危
    }
  },
  GROUP_ID_LIST: {
    uat: [349, 348, 636],
    pre: [295, 296, 47],
    prod: [295, 296, 47]
  },
  POLITICAL_ID: {
    uat: 118,
    pre: 74,
    prod: 74
  }, // 时政用户特征组-置顶
  DEVICE_FEATURE_GROUP_CLASS: {
    uat: {
      371: 'warning-device'
    },
    pre: {
      321: 'warning-device'
    },
    prod: {
      321: 'warning-device'
    }
  },
  DEVICE_FEATURE_GROUP_ID: {
    uat: [371],
    pre: [321],
    prod: [321]
  },
  LEADER_TAG_ID: {
    uat: 1744,
    pre: 6586,
    prod: 6586
  },
  FIRST_AUDIT_OPEN_TAG: { // 一审开放标签新老映射 oldTagId: newTagId
    uat: {
      1463: 1744, // 涉0号
      85: 1743, // 进细标
      688: 1745 // 建议监控
    },
    pre: {
      4070: 6586, // 涉0号
      119: 6584, // 进细标
      2046: 6585 // 建议监控
    },
    prod: {
      4070: 6586, // 涉0号
      119: 6584, // 进细标
      2046: 6585 // 建议监控
    }
  },
  DYNAMIC_HOST: {
    uat: 'uat-mng.vc.bilibili.co',
    pre: 'mng.vc.bilibili.co',
    prod: 'mng.vc.bilibili.co'
  },
  NEW_DYNAMIC_HOST: {
    // eslint-disable-next-line no-template-curly-in-string
    uat: fillTemplate('${this.a}${this.b}${this.c}', { a: 'uat-', b: 'polymer-api', c: '.bilibili.co' }),
    // eslint-disable-next-line no-template-curly-in-string
    pre: fillTemplate('${this.a}${this.b}${this.c}', { a: 'pre-', b: 'polymer-api', c: '.bilibili.co' }),
    prod: 'polymer-api.bilibili.co'
  },
  LIVE_AUDIT_EXPERIMENT_ID_MAP: {
    uat: {
      // 直播切片审核
      79: {
        A: 'default',
        B: 'new_616'
      },
      // 直播音视频审核
      97: {
        A: 'default',
        B: 'new_616'
      }
    },
    pre: {
      // 直播切片审核
      79: {
        A: 'default',
        B: 'new_616'
      },
       // 直播音视频审核
       97: {
        A: 'default',
        B: 'new_616'
      }
    },
    prod: {
      // 直播切片审核
      79: {
        A: 'default',
        B: 'new_616'
      },
       // 直播音视频审核
       97: {
        A: 'default',
        B: 'new_616'
      }
    }
  },
  LIVE_AUDIT_BIZ_TAG_ID_MAP: {
    uat: {
      79: 105, // 直播切片审核
      147: 108,
      150: 112, // 直播社区
      97: 105, // 直播音视频审核
      190: 267 // 专栏文集
    },
    pre: {
      79: 81,
      147: 86,
      150: 95, // 直播社区
      97: 105, // 直播音视频审核
      190: 131 // 专栏文集
    },
    prod: {
      79: 81,
      147: 86,
      150: 95, // 直播社区
      97: 105, // 直播音视频审核
      190: 131 // 专栏文集
    }
  },
  // 内容标签id&业务id MAP
  TAG_BIZ_ID_MAP: {
    uat: {
      94: 134, // 直播文案审核打压理由
      93: 133 // 直播封面审核打压理由
    },
    pre: {
      94: 108,
      93: 107 // 直播封面审核打压理由
    },
    prod: {
      94: 108,
      93: 107 // 直播封面审核打压理由
    }
  },
  // 直播活动报备账号特征组
  LIVE_ACTIVITY_REPORTING_ACCOUNT_FEATRUE_GROUP: {
    uat: 688,
    pre: 374,
    prod: 374
  }
}

export function getEnvConstant(key) {
  const env = store.getters['env/getEnv']()
  return ENV_CONSTANTS[key] && ENV_CONSTANTS[key][env]
}

export const HELP_HREF =
  'https://info.bilibili.co/pages/viewpage.action?pageId=*********'

// 自定义理由修改权限业务映射
export const CUSTOM_REASON_BIZ_AUTH_MAP = {
  18: 'AEGIS_REASON_INPUT_IMAGETEXT', // 新动态
  59: 'AEGIS_REASON_INPUT_IMAGETEXT' // 动态回查
}

export const UP_FROM = {
  BIZ_ACCOUNT: 38
}

// 全部稿件列表展示形式
export const DISPLAY_MODEL = {
  LIST_MODEL: 'list_model', // 列表形式
  CARD_MODEL: 'card_model' // 卡片形式
}

export const ADS_REVIEW_LIST_TYPES = ['40', '41', '61', '62', '83', '85', '86', '87', '88']

export const EMERGENCY_TOOL_REASON_CATEGORY = {
	uat: [
    964, // 打回-社区回查
    996, // 打回-默认
    992, // 打回-审核回查
    994, // 打回-OGV
    2542, // 打回-微短剧
    978, // 锁定-社区回查
    997, // 锁定-默认
    993, // 锁定-审核回查
    995, // 锁定-OGV
    2543 // 锁定-微短剧
  ],
	pre: [
    2769, // 打回-社区回查
    2768, // 打回-默认
    2771, // 打回-审核回查
    2772, // 打回-OGV
    10143, // 打回-微短剧
    2787, // 锁定-社区回查
    2773, // 锁定-默认
    2789, // 锁定-审核回查
    2790, // 锁定-OGV
    10144 // 锁定-微短剧
  ],
  prod: [
    2769, // 打回-社区回查
    2768, // 打回-默认
    2771, // 打回-审核回查
    2772, // 打回-OGV
    10143, // 打回-微短剧
    2787, // 锁定-社区回查
    2773, // 锁定-默认
    2789, // 锁定-审核回查
    2790, // 锁定-OGV
    10144 // 锁定-微短剧
  ]
}

// 直播审核业务内容标签树id映射{businessId: 内容标签树id}
export const LIVE_AUDIT_CONTENT_LABEL_MAP = {
  uat: {
    79: 91, // 直播切片审核
    97: 91, // 直播音视频审核
    102: 91, // 直播间举报审核
    108: 91, // 庆会审核
    143: 64, // 开播提示
    146: 64 // 付费留言审核
  },
  pre: {
    79: 70, // 直播切片审核
    97: 70, // 直播音视频审核
    102: 70, // 直播间举报审核
    108: 70, // 庆会审核
    143: 70, // 开播提示
    146: 70 // 付费留言审核
  },
  prod: {
    79: 70, // 直播切片审核
    97: 70, // 直播音视频审核
    102: 70, // 直播间举报审核
    108: 70, // 庆会审核
    143: 70, // 开播提示
    146: 70 // 付费留言审核
  }
}

export const LIVE_NEEDS_QUICK_OPERATE_BIZS = [97, 102, 143, 146]

/**
 * @component
 * @assetTitle 表格-通用日志组件
 * @assetDescription 展示工作台操作日志
 * @assetImportName HistoryLog
 * @assetTag 工作台列表配置组件
 */

//  大括号中的内容显示为蓝色，并将大括号改为中括号

export const COLOR_MAP = {
  未处理: 'var(--primary-color)',
  待处理: 'var(--primary-color)',
  已上报: 'var(--primary-color)',
  已流转: 'var(--primary-color)',
  待审: 'var(--primary-color)',
  已处理: 'var(--success-color)',
  已质检: 'var(--success-color)',
  通过: 'var(--success-color)',
  已删除: 'var(--error-color)',
  驳回: 'var(--error-color)',
  锁定: 'var(--error-color)',
  打回: 'var(--error-color)',
  // 直播切片特有的
  警告: 'var(--orange)',
  遮罩: 'var(--purple)',
  切断并锁定: 'var(--red)',
  切断并二审: 'var(--red)',
  锁定并二审: 'var(--red)',
  切断: 'var(--red)',
  删除: 'var(--red)',
  首页黑名单: 'var(--orange)',
  全公域禁推: 'var(--orange)',
  扣除san值: 'var(--orange)',
  流转二审: 'var(--orange)',
  维持: 'var(--orange)'
}
