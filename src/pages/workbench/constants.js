export const AUDIT_BATCH_MAP = {
  批量通过: {
    value: '0',
    perms: 'VIDEO_BATCH_PASS'
  },
  批量打回: {
    value: '-2',
    perms: 'VIDEO_BATCH_RECYCLE'
  },
  批量锁定: {
    value: '-4',
    perms: 'VIDEO_BATCH_LOCK'
  }
}

export const AUDIT_MAP = {
  通过: {
    value: '0',
    perms: 'VIDEO_BATCH_PASS'
  },
  打回: {
    value: '-2',
    perms: 'VIDEO_BATCH_RECYCLE'
  },
  锁定: {
    value: '-4',
    perms: 'VIDEO_BATCH_LOCK'
  }
}

export const STATUS = {
  0: '生效',
  1: '失效'
}

export const IS_GRAY = {
  false: '开放',
  true: '置灰'
}

export const TODO_TYPES = {
  0: '审核',
  1: '审核质检',
  2: '回查',
  3: '分类(标注)',
  4: '举报',
  5: '回查质检',
  6: '分类质检',
  7: '举报质检',
  8: '新人审核',
  9: '申诉',
  10: '申诉质检'
}

export const QA_TODO_TYPES = {
  1: '审核质检',
  5: '回查质检',
  6: '分类质检',
  7: '举报质检',
  10: '申诉质检'
}

export const TIME_TYPES = {
  ctime: '创建时间',
  mtime: '更新时间'
}

export const KEYWORD_TYPES = {
  name: '待办名',
  remark: '备注'
}

export const ORDER_TYPES = {
  0: '降序',
  1: '升序'
}

export const TODO_STATES = {
  0: '未处理',
  1: '已处理',
  2: '已删除'
}

export const APPEAL_TODO_STATES = {
  0: '未处理',
  1: '有效',
  2: '无效'
}

export const COMMENT_APPEAL_AUDIT_STATES = {
  0: '开放',
  1: '自见',
  2: '删除'
}

export const TODO_STATE_COLORS = {
  0: 'var(--primary-color)',
  1: 'var(--success-color)',
  2: 'var(--error-color)'
}

export const COMMENT_AUDIT_STATES_COLORS = {
  2: 'var(--error-color)'
}

export const DANMU_AUDIT_STATES_COLORS = {
  1: 'var(--error-color)'
}

export const REQUESTS_BUSINESS_NAME = {
  1: '稿件'
}

export const TODO_STATE_VAL_COLORS = {
  未处理: 21,
  已处理: 22,
  已删除: 23
}

export const STATES_COLORS = {
  已通过: 2,
  通过: 2,
  已驳回: 4,
  驳回: 4,
  已折叠: 3,
  已删除: 5,
  // 稿件、视频
  开放浏览: 'green',
  橙色通过: 'green',
  会员可见: 'green',
  待审核: 'blue',
  创建已提交: 'blue',
  待审: 'blue',
  修复待审: 'blue',
  等待转码: 'blue',
  分发中: 'blue',
  未知: 'blue',
  人工审核: 'blue',
  自动过审: 'blue',
  一审过审: 'blue',
  // 视频状态专有
  用户删除: 'red',
  打回: 'red',
  锁定: 'red',
  // 举报/申诉相关
  有效: 'green',
  无效: 'red'
}

export const LIVE_STATES_COLORS = {
  通过: 'green',
  警告: 'yellow',
  切断: 'red',
  切断并锁定: 'red',
  切断并二审: 'red',
  锁定并二审: 'red',
  待审: 'blue',
  删除: 'red',
  忽略: 'green',
  限流: 'red'
}

export const SHARE_STATES_COLORS = {
  已分享: 'red',
  未分享: 'green'
}

// 这些todo_id的待办不展示流转按钮
export const noNeedTransTodo = [
  180023, 180022, 180024, 210027, 240032, 180042, 180044, 180046, 240028,
  240030, 240031, 210020, 450001, 420013, 420010, 450007, 390019, 420011,
  420009, 420008, 390013, 420007, 390010, 450010, 420106
]

// 这些todo_id是召回视频，提交时submit_from参数变化
export const isVideoRecallTodo = [
  210026, 210027, 210035, 240031, 360003, 450009, 450010, 450006, 390010,
  390011, 420007, 4597511, 4680413, 632193, 661473, 420781, 4770247, 4770248,
  2551425, 2221288, 2191690
]

/**
 * 稿件状态颜色
 */
export const STATUS_COLOR = {
  '-30': 'blue',
  '-1': 'blue',
  '-2': 'red',
  '-4': 'red',
  '-16': 'red',
  '-100': 'red',
  0: 'green',
  10000: 'green'
}

/**
 * 内容分级分发列表
 */
const CONTENT_CLASSIFY_DISPATCH_COLUMNS = [
  {
    prop: 'resource.id',
    label: '内容 ID',
    width: '162'
  },
  {
    prop: 'resource.ctime',
    label: '内容创建时间'
  },
  {
    prop: 'resource.title',
    label: '内容名称'
  },
  {
    prop: 'resource.author',
    label: '内容发布人',
    width: '150'
  }
]

const ARCHIVE_DISPATCH_COLUMNS = [
  {
    prop: 'resource.cid',
    label: '内容 ID',
    width: '162'
  },
  {
    prop: 'resource.ctime',
    label: '内容创建时间',
    format: 'time',
    width: '155'
  },
  {
    prop: 'resource.title',
    label: '内容名称'
  },
  {
    prop: 'resource.publisher_name',
    label: '内容发布人',
    width: '150'
  }
]

const DANMU_COLUMNS = [
  {
    label: '内容ID',
    prop: 'oid',
    width: '120',
    type: 'oid'
  },
  {
    label: '资源创建时间',
    prop: 'ctime',
    width: '120',
    type: 'text'
  },
  {
    label: '所属稿件CID',
    prop: 'rsc_info.extra3',
    width: '110',
    type: 'hover_text_link',
    params: {
      content: 'rsc_info.extra2,rsc_info.metas.title',
      link: {
        type: 'dynamic',
        target: '_blank',
        href: 'rsc_info.metas.oidlink'
      }
    }
  },
  {
    label: '内容发布时间',
    prop: 'rsc_info.ctime',
    width: '155',
    type: 'text'
  },
  {
    label: '弹幕位置',
    prop: 'rsc_info.metas.progress',
    width: '80',
    type: 'text'
  },
  {
    label: '发送ip',
    prop: 'rsc_info.extra1s',
    width: '115',
    type: 'text'
  },
  {
    label: '弹幕内容',
    prop: 'rsc_info.text',
    width: '270',
    type: 'richText',
    params: {
      origin_content: 'rsc_info.origin_content'
    }
  },
  {
    label: '发布人',
    prop: 'publisher',
    width: '150',
    type: 'up_info'
  },
  {
    label: '进审理由',
    prop: 'remark_total',
    width: '180',
    type: 'text'
  },
  {
    label: '处理状态 ',
    type: 'processType',
    width: '100',
    align: 'center'
  },
  {
    label: '操作',
    type: 'operation',
    align: 'center'
  }
]

const DANMU_OPERS = [
  {
    name: '日志',
    btn_type: 'info'
  }
]

const DANMU_BATCH_OPERS = [
  {
    name: '批量安全',
    btn_type: 'success',
    tip: '确认批量提交安全弹幕'
  }
]

export const DANMU_DISPATCH_COLUMNS = [
  {
    prop: 'resource.oid',
    label: '内容 ID',
    width: '162'
  },
  {
    prop: 'resource.octime',
    label: '内容创建时间',
    width: '155'
  },
  {
    prop: 'resource.content',
    label: '内容名称'
  },
  {
    prop: 'resource.user_info.name',
    label: '内容发布人',
    width: '150'
  }
]
const INTL_DISPATCH_COLUMNS = [
  {
    prop: 'resource.oid',
    label: '内容 ID',
    width: '162'
  },
  {
    prop: 'resource.octime',
    label: '内容创建时间',
    width: '155'
  },
  {
    prop: 'resource.content',
    label: '内容名称'
  },
  {
    prop: 'resource.mid',
    label: '内容发布人',
    width: '150'
  }
]

const DANMU_DETAIL_COLUMNS = [
  {
    label: '弹幕创建时间',
    prop: 'resource.octime',
    width: '110',
    type: 'text'
  },
  {
    label: '所属稿件ID',
    prop: 'resource.extra2',
    width: '155',
    type: 'link',
    params: {
      type: 'dynamic',
      target: '_blank',
      href: 'resource.metas.pidlink'
    }
  },
  {
    label: '所属稿件标题',
    prop: 'resource.metas.title',
    width: '155',
    type: 'text'
  },
  {
    label: '弹幕位置',
    prop: 'resource.metas.progress',
    width: '80',
    type: 'text'
  },
  {
    label: '发送ip',
    prop: 'resource.extra1s',
    width: '115',
    type: 'text'
  },
  {
    label: '弹幕内容',
    prop: 'resource.text',
    width: '440',
    type: 'richText',
    params: {
      origin_content: 'resource.origin_content'
    }
  },
  {
    label: '发布人',
    prop: 'publisher',
    width: '150',
    type: 'up_info'
  },
  {
    label: '进审理由',
    prop: 'remark_total',
    width: '180',
    type: 'text'
  },
  {
    label: '操作',
    type: 'operation',
    align: 'center'
  }
]

const NEW_DANMU_OPERS = [
  {
    name: '日志',
    btn_type: 'info',
    type: 'danmu',
    requestSingle: true
  }
]

const NEW_DANMU_COLUMNS = [
  {
    label: '内容ID',
    prop: 'oid',
    width: '120',
    type: 'oid'
  },
  {
    label: '资源创建时间',
    prop: 'ctime',
    width: '120',
    type: 'text'
  },
  {
    label: '所属稿件CID',
    prop: 'rsc_info.extra3',
    width: '110',
    type: 'hover_text_link',
    params: {
      content: 'rsc_info.extra2,rsc_info.metas.title',
      link: {
        type: 'dynamic',
        target: '_blank',
        href: 'rsc_info.metas.oidlink'
      }
    }
  },
  {
    label: '内容发布时间',
    prop: 'rsc_info.ctime',
    width: '155',
    type: 'text'
  },
  {
    label: '弹幕位置',
    prop: 'rsc_info.metas.progress',
    width: '80',
    type: 'text'
  },
  {
    label: '发送ip',
    prop: 'rsc_info.extra1s',
    width: '115',
    type: 'text'
  },
  {
    label: '弹幕内容',
    prop: 'rsc_info.text',
    width: '270',
    type: 'richText',
    params: {
      origin_content: 'rsc_info.origin_content'
    }
  },
  {
    label: '发布人',
    prop: 'publisher',
    width: '150',
    type: 'up_info'
  },
  {
    label: '进审理由',
    prop: 'remark_total',
    width: '110',
    type: 'text',
    align: 'center'
  },
  {
    label: '审核状态',
    prop: 'rsc_info.state',
    width: '110',
    type: 'colormap',
    mapping: 'AUDIT_STATE'
  },
  {
    label: '处理状态 ',
    type: 'processType',
    width: '100',
    align: 'center'
  },
  {
    label: '操作',
    type: 'operation',
    align: 'center',
    width: '380'
  }
]

export const NEW_DANMU_DETAIL_COLUMNS = [
  {
    label: '弹幕创建时间',
    prop: 'resource.octime',
    width: '110',
    type: 'text'
  },
  {
    label: '所属稿件ID',
    prop: 'resource.extra2',
    width: '155',
    type: 'link',
    params: {
      type: 'dynamic',
      target: '_blank',
      href: 'resource.metas.pidlink'
    }
  },
  {
    label: '所属稿件标题',
    prop: 'resource.metas.title',
    width: '155',
    type: 'text'
  },
  {
    label: '弹幕位置',
    prop: 'resource.metas.progress',
    width: '80',
    type: 'text'
  },
  {
    label: '发送ip',
    prop: 'resource.extra1s',
    width: '115',
    type: 'text'
  },
  {
    label: '弹幕内容',
    prop: 'resource.text',
    width: '440',
    type: 'richText',
    params: {
      origin_content: 'resource.origin_content'
    }
  },
  {
    label: '发布人',
    prop: 'publisher',
    width: '150',
    type: 'up_info'
  },
  {
    label: '进审理由',
    prop: 'remark_total',
    width: '180',
    type: 'text'
  },
  {
    label: '操作',
    type: 'operation',
    width: '340',
    align: 'center'
  }
]

const REVIEW_VIDEO_DISPATCH_COLUMNS = [
  {
    prop: 'task.wtime_format',
    label: '任务等待时长',
    width: '162'
  },
  {
    prop: 'resource.res.aid',
    label: '稿件id',
    width: '155'
  },
  {
    prop: 'resource.res.cid',
    label: 'cid',
    width: '155'
  },
  {
    prop: 'resource.res.title',
    label: '稿件标题'
  },
  {
    prop: 'resource',
    label: '稿件作者',
    width: '150',
    type: 'up_info'
  }
]

const COOP_DISPATCH_COLUMNS = [
  {
    prop: 'resource.oid',
    label: '内容 ID',
    width: '162'
  },
  {
    prop: 'resource.octime',
    label: '内容创建时间',
    width: '155'
  },
  {
    prop: 'resource.content',
    label: '内容名称'
  },
  {
    prop: 'resource.user_info.name',
    label: '内容发布人',
    width: '200'
  }
]

export const TODO_STATIC_CONFIG = {
  国际版视频: {
    dispatch: INTL_DISPATCH_COLUMNS
  },
  ContentClassify: {
    dispatch: CONTENT_CLASSIFY_DISPATCH_COLUMNS
  },
  视频召回: {
    dispatch: REVIEW_VIDEO_DISPATCH_COLUMNS
  },
  稿件: {
    dispatch: ARCHIVE_DISPATCH_COLUMNS
  },
  新弹幕: {
    columns: NEW_DANMU_COLUMNS,
    washList: true,
    opers: {
      list: NEW_DANMU_OPERS,
      dispatch: [],
      detail: [],
      special: [],
      name: 'danmu'
    },
    batch: [],
    dispatch: DANMU_DISPATCH_COLUMNS,
    detail: NEW_DANMU_DETAIL_COLUMNS,
    auditType: 'list'
  },
  弹幕: {
    columns: DANMU_COLUMNS,
    washList: true,
    opers: {
      list: DANMU_OPERS,
      dispatch: [],
      detail: [],
      special: [],
      name: 'category'
    },
    batch: DANMU_BATCH_OPERS,
    dispatch: DANMU_DISPATCH_COLUMNS,
    detail: DANMU_DETAIL_COLUMNS,
    auditType: 'list'
  },
  评论: {
    dispatch: DANMU_DISPATCH_COLUMNS
  },
  联合投稿: {
    dispatch: COOP_DISPATCH_COLUMNS
  }
}

export const OPER_BUTTON_TYPES = {
  安全: 'success',
  低危: 'info',
  中危: 'danger',
  高危: 'warning',
  通过: 'success',
  驳回: 'danger',
  打回: 'warning',
  驳回并封禁: 'danger',
  折叠: 'warning',
  封禁: 'warning',
  锁定: 'danger',
  降级通过: 'success',
  批量通过: 'success',
  批量驳回: 'danger',
  批量打回: 'warning',
  批量折叠: 'warning',
  批量锁定: 'danger',
  批量审核: 'primary',
  查看详情: 'primary',
  详情审核: 'primary',
  日志: 'info',
  批量上线: 'success',
  批量下线: 'danger',
  批量降级通过: 'success',
  踩: 'warning',
  提交: 'primary',
  自见: 'warning',
  维持: 'success',
  批量维持: 'success',
  批量自见: 'warning',
  批量有效: 'success',
  批量无效: 'danger',
  批量安全: 'success',
  批量恢复: 'success',
  恢复: 'success',
  无问题: 'success',
  漏判: 'warning',
  错判: 'danger',
  误判: 'danger',
  误操作: 'warning',
  误通过: 'danger',
  标签错误: 'warning',
  批量无问题: 'success',
  批量漏判: 'warning',
  批量错判: 'danger',
  批量误判: 'danger',
  批量误操作: 'warning',
  批量误通过: 'danger',
  批量标签错误: 'warning',
  解散: 'warning',
  隐藏: 'info',
  上线: 'success',
  下线: 'danger',
  拉黑: 'info',
  忽略: 'success',
  二审忽略: 'success',
  删除: 'danger',
  批量正确: 'success',
  批量错误: 'danger',
  正确: 'success',
  错误: 'danger',
  批量受理: 'danger',
  批量忽略: 'success',
  批量误驳回: 'danger',
  误驳回: 'danger',
  有效: 'success',
  无效: 'danger',
  '批量延迟/流转': 'primary',
  '延迟/流转': 'primary',
  警告: 'warning',
  遮罩: '',
  切断: 'danger',
  切断并锁定: 'danger',
  限流: 'danger',
  正确并封禁: 'warning',
  批量提交: 'primary',
  提交并退出: 'success',
  处理: 'danger',
  处理并封禁: 'danger',
  负向打标: 'danger',
  打标: 'primary',
  打压: 'warning'
}

export const ACTION_CLASS = {
  // 审核、业务状态
  已通过: 2,
  已驳回: 4,
  已折叠: 3,
  已删除: 5,
  // 稿件、视频
  开放浏览: 'green',
  橙色通过: 'green',
  会员可见: 'green',
  待审核: 'blue',
  创建已提交: 'blue',
  待审: 'blue',
  修复待审: 'blue',
  等待转码: 'blue',
  分发中: 'blue',
  未知: 'blue',
  人工审核: 'blue',
  自动过审: 'blue',
  一审过审: 'blue',
  // 视频状态专有
  用户删除: 'red',
  打回: 'red',
  驳回: 'red',
  锁定: 'red',
  // 私信审核等
  通过: 2,
  已分享: 'red',
  未分享: 'green'
}

/**
 * @doc https://info.bilibili.co/pages/viewpage.action?pageId=8716486
 * role: 0: 无认证 1:up主认证 2:身份认证 3:企业认证 4: 政府认证 5:媒体认证 6:其他认证 7:垂直领域认证
 */
// 个人认证列表
export const INDIVIDUAL_LIST = [1, 2, 7, 9]
// 企业认证列表
export const ENTERPRISE_LIST = [3, 4, 5, 6]
/* eslint-disable */

export const DANMU_FORM_SCHEMA = {
  options: {
    size: 'small'
  },
  controls: [
    {
      name: 'business_id',
      type: 'hide',
      default_value: 0,
      source: {
        type: 'local',
        location: '${$route.query.business_id}'
      }
    },
    {
      name: 'todo_id',
      type: 'hide',
      default_value: 0,
      source: {
        type: 'local',
        location: '${$route.params.todo_id}'
      }
    },
    {
      name: 'oid',
      type: 'input',
      width: '200px',
      default_value: '',
      attributes: {
        placeholder: '内容ID，一行一个',
        type: 'textarea',
        autosize: {
          minRows: 1,
          maxRows: 1
        },
        resize: 'none'
      }
    },
    {
      name: 'todo_state',
      type: 'select',
      default_value: 0,
      width: '100px',
      source: {
        type: 'static',
        data: [
          {
            value: 0,
            label: '未处理'
          },
          {
            value: 1,
            label: '已处理'
          },
          {
            value: 2,
            label: '已删除'
          }
        ]
      }
    },
    {
      name: 'order',
      type: 'select',
      width: '125px',
      default_value: 'todo_ctime',
      source: {
        type: 'static',
        data: [
          {
            value: 'todo_ctime',
            label: '资源创建时间'
          }
        ]
      }
    },
    {
      name: 'sort',
      type: 'select',
      width: '100px',
      default_value: 'desc',
      source: {
        type: 'static',
        data: [
          {
            value: 'desc',
            label: '降序'
          },
          {
            value: 'asc',
            label: '升序'
          }
        ]
      }
    },
    {
      name: 'key_fields',
      type: 'hide',
      default_value: 'content'
    },
    {
      name: 'key_words',
      type: 'input',
      width: '200px',
      default_value: '',
      attributes: {
        placeholder: '内容关键词',
        type: 'input',
        clearable: true
      }
    },
    {
      name: 'mid',
      type: 'input',
      width: '200px',
      default_value: '',
      attributes: {
        placeholder: '内容发送人mid，一行一个',
        type: 'textarea',
        autosize: {
          minRows: 1,
          maxRows: 1
        },
        resize: 'none'
      }
    },
    {
      name: 'extra1s',
      type: 'input',
      width: '120px',
      default_value: '',
      attributes: {
        placeholder: '内容发送IP',
        type: 'input',
        clearable: true
      }
    },
    {
      name: 'extra3',
      type: 'input',
      width: '200px',
      default_value: '',
      attributes: {
        placeholder: '稿件CID，一行一个',
        type: 'textarea',
        autosize: {
          minRows: 1,
          maxRows: 1
        },
        resize: 'none'
      }
    },
    {
      name: 'octime_from',
      type: 'datePicker',
      width: '200px',
      default_value: '',
      style: {
        'margin-right': '4px'
      },
      attributes: {
        placeholder: '内容创建时间_开始',
        type: 'datetime',
        valueFormat: 'yyyy-MM-dd HH:mm:ss'
      }
    },
    {
      name: 'octime_to',
      type: 'datePicker',
      width: '200px',
      default_value: '',
      attributes: {
        placeholder: '内容创建时间_结束',
        type: 'datetime',
        valueFormat: 'yyyy-MM-dd HH:mm:ss'
      }
    },
    {
      name: 'todo_ctime_from',
      type: 'datePicker',
      width: '200px',
      default_value: `moment().subtract(6, 'month').format('YYYY-MM-DD HH:mm:ss')`,
      style: {
        'margin-right': '4px'
      },
      attributes: {
        placeholder: '资源创建时间_开始',
        type: 'datetime',
        valueFormat: 'yyyy-MM-dd HH:mm:ss',
        clearable: false,
        editable: false
      }
    },
    {
      name: 'todo_ctime_to',
      type: 'datePicker',
      width: '200px',
      default_value: `moment(new Date()).format('YYYY-MM-DD HH:mm:ss')`,
      attributes: {
        placeholder: '资源创建时间_结束',
        type: 'datetime',
        valueFormat: 'yyyy-MM-dd HH:mm:ss',
        clearable: false,
        editable: false
      }
    },
    {
      name: 'state',
      type: 'select',
      default_value: '',
      width: '100px',
      source: {
        type: 'requset',
        api: '/x/admin/aegis/net/token/byname',
        params: {
          name: 'state',
          business_id: '${$route.query.business_id}'
        }
      },
      attributes: {
        clearable: true,
        placeholder: '资源状态'
      }
    }
  ]
}
/* eslint-enable */

export const WORK_BUSINESS_COMMENT_CONFIG = {
  default: {
    onlyEmojiHit: true,
    oid: '',
    rpid: ''
  },
  评论: {
    onlyEmojiHit: false,
    oid: 'data.extra2',
    rpid: 'data.oid'
  }
}

/**
 * 举报Tag映射
 */
export const INFORM_TAG_MAP = {
  1: '有其他问题',
  2: '违法违禁',
  3: '色情低俗',
  4: '低俗',
  5: '赌博诈骗',
  6: '血腥暴力',
  7: '人身攻击',
  8: '与站内其他视频撞车',
  9: '引战',
  10: '不能参加充电',
  52: '转载/自制类型错误',
  10000: '青少年不良信息',
  10013: '不良封面/标题',
  // 新增 线上映射（uat可能显示不正确）
  // https://www.tapd.bilibili.co/20065591/prong/stories/view/1120065591002830287
  10014: '涉政谣言',
  10015: '涉社会事件谣言',
  10016: '疫情谣言',
  10017: '虚假不实信息',
  10018: '违规推广',
  10019: '其他不规范行为',
  10020: '危险行为',
  10021: '观感不适',
  10022: '其他',
  10023: '企业商誉侵权',
  10024: '侵权申诉',
  // 新增 线上映射 https://www.tapd.cn/20065591/prong/stories/view/1120065591004298552
  10025: '违法信息外链',
  // 新增 线上映射 https://www.tapd.cn/20065591/prong/stories/view/1120065591004422558
  10026: '盗搬稿件-路人举报'
}

// 展示分发模式和任务清单按钮的业务
export const SHOW_TASK_LIST_BIZ = [
  2, 12, 13, 14, 16, 18, 19, 20, 21, 22, 27, 28, 30, 38, 39, 40, 42, 43, 44, 45, 46, 47, 48, 50, 54, 56, 57, 58, 60, 66, 70, 73, 74, 101, 1000, 1001
]

export const EDIT_OR_ADD = {
  EDIT: 'EDIT',
  ADD: 'ADD'
}

export const DEFAULT_RELEASE_TIME = 540

export const DEFAULT_RENEWAL_TIME = 0

export const DEFAULT_RENEWAL_REMIND_TIME = 0

export const DEFAULT_RELEASE_TIME_INTERVAL = 510

// 稿件审核操作Map
export const ARCHIVE_AUDIT_MAP = {
  通过: '0',
  打回: '-2',
  锁定: '-4'
}

// 工作台稿件批量审核操作Map
export const ARCHIVE_BATCH_AUDIT_MAP = {
  批量通过: '0',
  批量打回: '-2',
  批量锁定: '-4',
  开放浏览: '0',
  打回: '-2',
  锁定: '-4'
}

// --- business_id + todo_type 用于判断新人模式
export const NEWER_TODO_TYPE = 8
export const VIDEO_BUSINESS_ID = 29
export const OFFLINE_NEW_VIDEO_BUSINESS_ID = 55 // 新人视频审核

// --- 图文业务新人模式 ---
export const NEW_REMARK_BUSINESS_ID = 39 // 新评论业务
export const OFFLINE_NEW_REMARK_BUSINESS_ID = 56 // 离线新评论业务
export const NEW_REMARK_NEWER_DETAIL_TPL = 92 // 新人模式-新评论业务-详情-模板id
export const NEW_REMARK_NEWER_LIST_TPL = 93 // 新人模式-新评论业务-列表-模板id
export const NEW_DANMU_BUSINESS_ID = 40 // 新弹幕业务
export const OFFLINE_NEW_DANMU_BUSINESS_ID = 57 // 离线新评论业务
export const NEW_DANMU_NEWER_DETAIL_TPL = 94
export const NEW_DANMU_NEWER_LIST_TPL = 95
export const NEW_DS_BUSINESS_ID = 18 // 新动态业务
export const OFFLINE_NEW_DS_BUSINESS_ID = 58 // 离线新动态业务
export const NEW_DS_NEWER_DETAIL_TPL = 96
export const NEW_DS_NEWER_LIST_TPL = 97
export const REVIEW_NEW_REMARK_BUSINESS_ID = 1000 // 评论社区回查业务
export const REVIEW_NEW_DS_BUSINESS_ID = 1001 // 动态社区回查业务
export const REVIEW_ARCHIVE_BUSINESS_ID = 1002 // 社区稿件业务

// 分类通过操作名, 用于特殊处理操作项
export const ANNOTATION_SINGLE_OPER_PASS_NAME = ['安全', '通过']
export const ANNOTATION_BATCH_OPER_PASS_NAME = ['批量安全', '批量通过']

export const ENABLE_BATCH_SELECT_BIZ = [39, 40, 56, 57, 61, 71, 18, 81, 91, 92] // 有全选反选的business_id
// 37 私信审核 39 新评论 40 新弹幕 18 新动态 61 评论回查 62 弹幕回查 63 漫画社区 64 Vomic 65 Up主小铺 68 bwiki图片 69光呆稿件 71校园稿件 72合集打卡 16播单 66播单回查 91BR审核 92BR举报 97音视频审核 100 esheep
export const BATCH_TRANSFER_BIZ = [
  39, 40, 56, 57, 61, 62, 68, 72, 81, 90, 91, 92, 100, 156
]
export const TRANSFER_BIZ = [
  37,
  18,
  58,
  59,
  8,
  63,
  64,
  65,
  71,
  REVIEW_NEW_REMARK_BUSINESS_ID,
  REVIEW_NEW_DS_BUSINESS_ID,
  16,
  66,
  97,
  102
]

// 质检通过/无二次确认操作名, 用于特殊处理操作项
export const QA_SINGLE_OPER_PASS_NAME = ['无问题', '错判', '漏判', '误驳回']
export const QA_BATCH_OPER_PASS_NAME = [
  '批量无问题',
  '批量错判',
  '批量漏判',
  '批量误驳回'
]

export const QA_SINGLE_OPER_TO_STATE_MAP = {
  无问题: 1,
  漏判: 2,
  错判: 3
}
export const QA_SINGLE_OPER_TO_COLOR_MAP = {
  无问题: 22,
  漏判: 21,
  错判: 23,
  未质检: 21
}
export const QA_SINGLE_NUMBER_STATE_MAP = {
  1: '无问题',
  2: '漏判',
  3: '错判',
  0: '未质检'
}

export const QA_BATCH_OPER_TO_STATE_MAP = {
  批量无问题: 1,
  批量漏判: 2,
  批量错判: 3
}

export const VIDEO_SECOND_TODO_IDS = [4597511]

export const LEGO_ID = {
  uat: 25,
  pre: 24,
  prod: 22
}

// 直播语聊房审核状态map
export const CHANT_ROOM_STATUS_COLOR_MAP = {
  通过: 'green',
  驳回: 'yellow',
  驳回并封禁: 'yellow',
  待审: 'blue',
  删除: 'red'
}
// 直播语聊房签约状态map
export const CHANT_ROOM_SIGN_MAP = {
  1: '是',
  0: '否',
  2: '否'
}
export const CHANT_ROOM_SIGN_COLOR_MAP = {
  是: 21,
  否: 23
}

export const ARCHIVE_APPEAL_TODO_STATES = {
  0: '未处理',
  1: '有效',
  2: '无效',
  '-100': '用户或系统关闭'
}

export const SIGN_STATES = {
  0: '普通',
  1: '签约',
  2: '工会'
}

export const SIGN_STATE_VAL_COLORS = {
  普通: 'black',
  签约: 'red',
  工会: 'black'
}

export const LIVE_STATES = {
  0: '未开播',
  1: '直播中',
  2: '轮播中'
}

export const CELEBRATE_TYPE_MAP = {
  庆会主题: 1,
  庆会心愿挑战: 2,
  庆会海报与寄语: 3,
  普通心愿挑战: 4,
  集赞时刻: 5
}

export const CELEBRATE_TYPE_LABEL_MAP = {
  1: '庆会主题',
  2: '庆会心愿挑战',
  3: '庆会海报与寄语',
  4: '普通心愿挑战',
  5: '集赞时刻'
}

// 97-直播音视频审核 102-直播间举报审核 106-直播影视挂播打压 107-弹幕投票审核 108-庆会审核 143-开播提示审核 146-付费留言审核 150-直播社区 153-互动审核
export const LIVE_AUDIT_BIZ = [97, 102, 106, 107, 108, 143, 146, 150, 153]

// 直播文案模板负向操作名称类型映射
export const LIVE_TEXT_TEMPLATE_NEGATIVE_OPERATION_MAP = {
  驳回: 'reject',
  驳回并封禁: 'rejectBan',
  处理: 'handle',
  处理并封禁: 'handleBan',
  封禁: 'ban',
  打压: 'limit'
}

// 直播文案模板正向操作名称类型映射
export const LIVE_TEXT_TEMPLATE_POSITIVE_OPERATION_MAP = {
  通过: 'pass',
  忽略: 'ignore',
  二审忽略: 'secIgnore'
}

// 直播文案模板负向操作类型名称映射
export const LIVE_TEXT_TEMPLATE_NEGATIVE_OPERATION_NAME_MAP = {
  reject: '驳回',
  rejectBan: '驳回并封禁',
  handle: '处理',
  handleBan: '处理并封禁',
  ban: '封禁',
  limit: '打压'
}

// 直播文案模板正向操作类型名称映射
export const LIVE_TEXT_TEMPLATE_POSITIVE_OPERATION_NAME_MAP = {
  pass: '通过',
  ignore: '忽略',
  secIgnore: '二审忽略'
}

export const CERTIFICATION_TYPE_ENUMS = {
  1: 'up主认证',
  2: '身份认证',
  3: '企业认证 ',
  4: '政府认证',
  5: '媒体认证',
  6: '其他认证',
  7: '垂直领域认证',
  8: '职业认证',
  9: '社会名人'
}
