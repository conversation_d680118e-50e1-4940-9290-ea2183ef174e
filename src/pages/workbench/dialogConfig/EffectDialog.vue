<template>
  <div class="workbench-effect-dialog">
    <DraggableDialog
      title="有效"
      :visible.sync="dialogVisible"
      width="800px"
      :fixPosition="{ left: '100px', top: '150px' }"
      rememberPosition="single-effect-dialog"
      style="max-height:900px;overflow-y:scroll"
    >
      <el-form
        ref="form"
        @submit.stop.prevent.native
        :model="form"
        size="small"
        label-width="100px"
        :disabled="disabled"
      >
        <el-form-item prop="reason_id" label="用户举报理由" type="flex">
          <el-select
            v-model="form.reason_id"
            placeholder="请选择举报理由"
            @change="changedReason"
          >
            <el-option
              v-for="(label, value) in INFORM_TAG_MAP"
              :key="parseInt(value, 10)"
              :label="label"
              :value="parseInt(value, 10)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="custom" label="自定义理由">
          <el-input v-model="form.reject_reason"></el-input>
        </el-form-item>
        <el-form-item prop="status" label="修改稿件状态" type="flex">
          <el-row style="margin-bottom: 10px">
            <el-select
              v-model="form.status"
              placeholder="请选择"
              style="margin-right: 10px"
              @change="changeArchiveStatus"
            >
              <el-option
                v-for="item in states"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <el-checkbox v-model="form.notifyUp">通知被举报人</el-checkbox>
          </el-row>
        </el-form-item>
        <el-form-item
          label="理由类型"
          v-if="form.status === '-2' || form.status === '-4'"
        >
          <AuditReasonStruct
            ref="auditReasonStruct"
            :key="form.status"
            :state="form.status"
            :dirty="true"
            :reason="form.archive_reason"
            :forward="form.forward"
            :reasonTags="reasonTags"
            :reasonMap="reasonMap"
            @update-forward="(val) => (form.forward = val)"
            @update-reason="(val) => (form.archive_reason = val)"
            @update-reason-id="handleUpdateReasonId"
            @clear-reason="(val) => (form.archive_reason = val)"
          />
        </el-form-item>
        <template v-if="isReview">
          <el-form-item label="修改禁止项" type="flex">
            <template v-if="!noAuth">
              <BlockOption
                use_focus_panel
                :norank.sync="form.norank"
                :noindex.sync="form.noindex"
                :norecommend.sync="form.norecommend"
                :nohot.sync="form.nohot"
                :hot_down.sync="form.hot_down"
                :rank_down.sync="form.rank_down"
                :nosearch.sync="form.nosearch"
                :push_blog.sync="form.push_blog"
                :disabled="noReadAuth || disabled"
                :hideOptions="['nosearch', 'push_blog']"
              />
            </template>
            <template v-else>
              <span class="font-14 mr-10 ml-10">
                如需查看或操作敏感信息&nbsp;&nbsp;
                <a :href="HELP_HREF" target="_blank" class="help-text">
                  点击获得帮助
                </a>
              </span>
            </template>
          </el-form-item>
          <el-form-item label="修改类型">
            <el-radio-group v-model="form.copyright" size="small">
              <el-radio :label="1">自制</el-radio>
              <el-radio :label="2">转载</el-radio>
            </el-radio-group>
          </el-form-item>
        </template>
        <!-- 结构化备注 -->
        <el-form-item label="备注" type="flex" v-if="isReview">
          <template>
            <AgNoteTag
              :tags="noteTags"
              :options="noteOptions"
              :aid="aid"
              :disabled="disabled"
              @add-note="handleAddNote"
              @delete-note="handleDeleteNote"
            />
          </template>
          <LimitReasonAndSnapshot
            ref="noteTagReasonRef"
            :aid="aid"
            labelWidth="45px"
            :limitTags="noteTags"
            tagType="noteTag"
            @update:noteTagReason="(newVal) => this.noteTagReason = newVal"
          />
        </el-form-item>
        <!-- 普通备注 -->
        <el-form-item prop="remark" label="备注" type="flex" v-else>
          <div class="flex-ac w-full">
            <AgSelect
              v-model="quickNote"
              class="w-120 mr-4"
              placeholder="选择快捷文本"
              size="mini"
              @change="onChangeQuickNote"
            >
              <el-option
                v-for="reason in VIDEO_AUDIT_REASONS"
                :key="reason"
                :label="reason"
                :value="reason"
              />
            </AgSelect>
            <AgTextarea v-model="form.remark" :maxlength="255" />
          </div>
        </el-form-item>
        <el-form-item label="" type="flex">
          <div style="display: flex; justify-content: space-between">
            <el-checkbox v-model="form.notify">通知举报人</el-checkbox>
          </div>
        </el-form-item>
      </el-form>

      <div class="dialog-footer" style="text-align: center">
        <el-button @click="close" size="medium">取 消</el-button>
        <el-button type="primary" size="medium" @click="submit" :disabled="disabled">
          确 定
        </el-button>
      </div>
    </DraggableDialog>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import { INFORM_TAG_MAP } from '@/pages/workbench/constants'
import { archiveApi } from '@/api'
import DraggableDialog from '@/components/Dialog/DraggableDialog'
import AuditReasonStruct from '@/v2/biz-components/archive/AuditReasonStruct'
import BlockOption from '@/v2/biz-components/archive/BlockOption'
import $notify from '@/lib/notify'
import { AgNoteTag } from '@/v2/pure-components/ElementUpdate'
import washNoteTag from '@/karl/second-audit/note-tag'
import { confirmClassArchive } from '@/v2/biz-utils/classVideoConfirm'
import genOperationTags from '@/v2/biz-utils/archiveOperationTags'
import { FINAL_STATES } from '@/utils/constant'
import { VIDEO_AUDIT_REASONS } from '@/utils/constant.js'
import AgTextarea from '@/components/element-update/Textarea.vue'
import AgSelect from '@/components/element-update/Select.vue'
import LimitReasonAndSnapshot from '@/v2/biz-components/archive/LimitReasonAndSnapshot.vue'

export default {
  components: {
    DraggableDialog,
    AuditReasonStruct,
    BlockOption,
    AgNoteTag,
    AgTextarea,
    AgSelect,
    LimitReasonAndSnapshot
  },
  props: {
    card: {
      type: Object,
      default: () => {
        return {}
      }
    },
    informTagId: Number,
    todoConfig: Object,
    aid: {
      type: Number,
      default: 0
    },
    auditversion: {
      type: Number,
      default: undefined
    },
    isBusinessOrder: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    formOperation: {
      type: Object
    },
    snapshotNoteTags: {
      type: Array
    }
  },
  watch: {
    aid() {
      this.quickNote = ''
    }
  },
  data() {
    return {
      VIDEO_AUDIT_REASONS,
      businessOrderAids: '',
      dialogVisible: false,
      form: {},
      formTemplate: {
        reason_id: '',
        reasonTagText: '',
        fullPathTagIds: [],
        reject_reason: '',
        status: '',
        notify: true,
        archive_reason: '',
        archive_reason_id: '',
        tag_id: '',
        remark: '',
        notifyUp: true,
        forward: '',
        norank: '',
        noindex: '',
        norecommend: '',
        nohot: '',
        hot_down: '',
        rank_down: '',
        nosearch: '',
        push_blog: '',
        copyright: ''
      },
      oper: {},
      states: [
        {
          label: '打回',
          value: '-2'
        },
        {
          label: '锁定',
          value: '-4'
        }
      ],
      INFORM_TAG_MAP,
      reasonTags: [],
      reasonMap: {},
      allReasons: [],
      reasonTypes: [],
      reasonType: '',
      noteTags: [],
      noteOptions: [],
      selectedData: [], // 选中的数据源
      quickNote: '', // 快捷备注
      noteTagReason: [] // 备注标限流理由
    }
  },
  computed: {
    ...mapState({
      perms: (state) => state.user.perms
    }),
    noAuth() {
      return !this.perms.MOD_S_FLOW_READ && !this.perms.MOD_S_FLOW_WRITE
    },
    noReadAuth() {
      return !this.perms.MOD_S_FLOW_WRITE
    },
    isReview() {
      return this.todoConfig?.isReview ?? false
    },
    isUserNote() {
      return this.noteOptions && this.noteOptions.length > 0
    }
  },
  methods: {
    onChangeQuickNote(selected) {
      let newRemark = this.form.remark
      if (newRemark.length > 0) {
        newRemark += '\n'
      }
      newRemark += selected
      this.form.remark = newRemark
    },
    async openDialog(oper, multipleSelections) {
      this.selectedData = multipleSelections
      this.dialogVisible = true
      if (this.isReview) {
        this.getNoteOptions()
        this.states = [
          {
            label: '打回',
            value: '-2'
          },
          {
            label: '锁定',
            value: '-4'
          },
          {
            label: '开放浏览',
            value: '0'
          }
        ]
      }
      this.$nextTick(() => {
        this.$refs.form.resetFields()
        this.noteTags = []
        if (this.formOperation) {
          this.form = this.formOperation
        }
        if (this.snapshotNoteTags) {
          this.noteTags = this.snapshotNoteTags
        }
        if (this.snapshotNoteOptions) {
          this.noteOptions = this.snapshotNoteOptions
        }
      })
      this.form = {
        ...this.formTemplate,
        reason_id: this.informTagId
      }
      this.changedReason()
      this.resetArchiveParams()
      this.oper = oper
      await this.findBizOrders(multipleSelections)
    },
    async findBizOrders(multipleSelections) {
      if (Array.isArray(multipleSelections) && multipleSelections.length) {
        const aids = multipleSelections
          .map((el) => el.resource?.extra2)
          .filter((e) => !!e)
          .join(',')
        const res = await archiveApi.checkSubmit({ aids })
        const { hits_adorder: adorderHits } = res.data
        this.businessOrderAids = adorderHits || ''
      }
    },
    handleUpdateReasonId(
      newReasonId,
      newTagId,
      reasonCategoryId,
      reasonCategoryText,
      reasonTagText,
      reasonTagPathIds
    ) {
      this.form.archive_reason_id = newReasonId
      this.form.tag_id = newTagId
      this.form.reasonTagText = `${reasonCategoryText} / ${reasonTagText}`
      this.form.fullPathTagIds = [reasonCategoryId, ...(reasonTagPathIds || [])]
    },
    close() {
      this.dialogVisible = false
    },
    changedReason() {
      this.form.reject_reason = INFORM_TAG_MAP[this.form.reason_id]
    },
    async changeArchiveStatus() {
      if (this.form.status !== '0') {
        this.form.notifyUp = true
        this.resetArchiveParams()
        this.fetchReason()
      } else {
        this.form.notifyUp = false
      }
    },
    // 获取结构化理由
    async fetchReason() {
      // 审核使用 listType 是 11 与二审常规保持一致
      const listType = this.isReview
        ? this.todoConfig?.list_type || '23_new'
        : '11'
      try {
        const res = await archiveApi.getReasonTags({
          list_type: listType,
          state: this.form.status
        })
        const { reason_map = {}, reason_tags = [] } = res.data || {}
        this.reasonTags = reason_tags || []
        this.reasonMap = reason_map
      } catch (err) {
        console.error(err)
        this.reasonTags = []
        this.reasonMap = {}
      }
    },
    resetArchiveParams() {
      this.form.archive_reason_id = ''
      this.form.tag_id = ''
      this.form.archive_reason = ''
      this.form.forward = ''
      this.reasonTags = []
      this.reasonMap = {}
      this.allReasons = []
      this.reasonTypes = []
      this.reasonType = ''
    },
    getNotifyResult(notify, notifyUp) {
      if (!notifyUp && !notify) {
        return 0
      }
      if (notify && !notifyUp) {
        return 1
      }
      if (!notify && notifyUp) {
        return 2
      }
      if (notify && notifyUp) {
        return 3
      }
    },
    async submit() {
      const {
        reason_id: reasonId,
        reject_reason: rejectReason,
        archive_reason: archiveReason,
        archive_reason_id: archiveReasonId,
        reasonTagText,
        fullPathTagIds,
        tag_id,
        status,
        notify,
        notifyUp,
        forward,
        norank,
        noindex,
        norecommend,
        nohot,
        hot_down,
        rank_down,
        copyright,
        remark
      } = this.form

      if (['-2', '-4'].includes(status)) {
        const reasonError = this.$refs.auditReasonStruct?.validateReason()

        if (reasonError) {
          $notify.warning(reasonError)
          return
        }
        if (this.isBusinessOrder || this.businessOrderAids) {
          let msg =
            '<span>稿件为<b style="color: red">商业稿件</b>，确认是否进行操作</span>'
          if (this.businessOrderAids) {
            msg = `<p style="line-break: anywhere"><span>批量处理稿件中含有<b style="color: red">商业稿件</b>，确认是否进行操作</span><br />【${this.businessOrderAids}】</p>`
          }
          try {
            await this.$confirm(msg, {
              dangerouslyUseHTMLString: true,
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
          } catch (e) {
            return
          }
        }
      }
      if (!this.form.reason_id) {
        $notify.warning('请选择举报理由！')
        return
      }
      if (this.form.status === '') {
        $notify.warning('请选择稿件状态！')
        return
      }

      // 限流理由截图校验
      this.noteTags?.length && this.$refs.noteTagReasonRef?.validateReason()

      const sendnotify = this.getNotifyResult(notify, notifyUp)
      const note_tag = this.noteTags.length
        ? JSON.stringify(
            this.noteTags.map((v) => ({ tag_id: +v.tag_id, remark: v.remark }))
          )
        : ''
      const remark_str = this.noteTags.map((v) => v.tag_name).join(',')
      const data = {
        remark: this.isReview ? remark_str : remark,
        resource_result: {
          full_tag_names: [reasonTagText.split(' / ')],
          full_tag_ids: [fullPathTagIds],
          reject_reason: rejectReason,
          reason_id: reasonId
        },
        binds: this.oper.bind_id_list,
        extra_data: {
          reject_reason: archiveReason,
          reason_id: status === '0' ? 0 : archiveReasonId,
          status,
          notify: sendnotify,
          note_tag,
          flag_copyright: +copyright === 2 ? 1 : 0,
          auditversion: this.auditversion
        }
      }

      this.isReview && (
        data.extra_data.note_tag_reason = JSON.stringify(this.noteTagReason) || '[]'
      )

      // 禁止项逻辑先不上，等服务支持修改开放浏览的稿件状态
      const forbids = {
        norank,
        noindex,
        norecommend,
        nohot,
        hot_down,
        rank_down
      }
      let flag_flow = 0

      Object.keys(forbids).forEach((key) => {
        if (forbids[key]) {
          forbids[key] = String(forbids[key])
          flag_flow = 1 // 存在禁止项改动，需要传
        } else {
          delete forbids[key]
        }
      })

      if (flag_flow === 1) {
        data.extra_data = {
          ...data.extra_data,
          ...forbids,
          flag_flow
        }
        if (data.remark === '') {
          $notify.error('请填写备注！')
          return
        }
      }

      if (copyright > 0) {
        data.extra_data = {
          ...data.extra_data,
          copyright,
          flag_copyright: 1 // 兼容服务历史场景，需要改动场景需要传
        }
      }

      if (status === '-4') {
        data.extra_data.forward = forward
      }

      if (this.form.status === '0') {
        delete data.extra_data.reject_reason
      }
      const aids = this.aid
        ? this.aid + ''
        : this.selectedData
            .map((el) => el.resource?.extra2)
            .filter((e) => !!e)
            .join(',')
      if ([-2, -4].includes(+status)) {
        await confirmClassArchive(aids)
      }
      data.extra_data.operation_tags = genOperationTags({
        archiveState: status,
        reasonTagId: tag_id,
        noteTag: this.noteTags?.map((e) => e.tag_id),
        noteText: this.isReview ? '' : remark
      })
      data.blind_diff_info = [
        { label: '审核结果', value: '有效' },
        { label: '稿件状态', value: FINAL_STATES[status] },
        { label: '操作标签', value: reasonTagText },
        { label: '理由文案', value: archiveReason }
      ]
      if (Array.isArray(this.selectedData) && this.selectedData.length) {
        // 批量不需要上报操作快照
        this.$emit('submit', data, this.oper)
      } else {
        const operationSnapshot = {
          formOperation: this.form,
          snapshotNoteTags: this.noteTags
        }
        this.$emit('submit', data, this.oper, operationSnapshot)
      }
    },
    handleAddNote(note) {
      const { value, label, remark, pre_text: preText } = note
      const existNoteTag = this.noteTags.find(
        (tag) => tag.tag_id === value && tag.pre_text === preText
      )
      if (existNoteTag && existNoteTag.canDelete) {
        $notify.warning('已经存在该备注tag，只修改了备注')
        existNoteTag.remark = remark
        existNoteTag.tag_name = `${preText}【${remark}】`
      } else if (existNoteTag) {
        $notify.warning('已经存在该备注tag，且无权修改')
      } else {
        this.noteTags.unshift({
          tag_id: value,
          tag_name: label,
          remark,
          pre_text: preText,
          hitState: false,
          first_init: false,
          canDelete: true
        })
      }
    },
    handleDeleteNote(note, idx) {
      this.noteTags.splice(idx, 1)
    },
    async getNoteOptions() {
      try {
        const res = await archiveApi.getTagNote()
        const { noteOptions } = washNoteTag({
          note_tag: res.data
        })
        this.noteOptions = noteOptions
      } catch {}
    }
  }
}
</script>
<style lang="stylus" scoped>
.workbench-effect-dialog // 质检回显样式覆盖
  >>>.el-input.is-disabled .el-input__inner
    color var(--black)
  >>>.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after
    border-color var(--black)
  >>>.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner
    border-color var(--black)
  >>>.el-radio__input.is-disabled .el-radio__inner, .el-radio__input.is-disabled.is-checked .el-radio__inner
    border-color var(--black)
  >>>.el-radio__input.is-disabled.is-checked .el-radio__inner::after
    background-color var(--black)
</style>
