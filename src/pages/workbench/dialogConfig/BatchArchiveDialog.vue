<template>
  <div class="batch-archive-dialog">
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      :width="dialogTitle === '回查配置' ? '90%' : '1500px'"
      :destroy-on-close="true"
      :close-on-click-modal="false"
    >
      <el-form
        :model="form"
        v-if="dialogTitle === '批量审核'"
        label-width="100px"
        label-position="right"
      >
        <el-form-item label="稿件ID：">
          <div class="ids-with-check">
            <el-input v-model="form[formKey].ids" size="small" type="textarea" :autosize="{minRows: 2}"></el-input>
            <el-button
              :disabled="checkButtonDisabled"
              :loading="checking"
              @click="validateIds"
            >
              {{ checking ? '检测中' : '检测' }}
            </el-button>
          </div>
          <p>
            （英文逗号分隔的稿件ID，不存在的稿件将被忽略，单次填写不超过 300 个id，超过数量限制请去“应急处理工具-稿件批量查询”处理。）
            <span style="color:var(--red);">{{ checkResultError }}</span>
            <span style="color:var(--green);">{{ checkResultHint }}</span>
            <span style="color:var(--orange);">{{ checkResultWarning }}</span>
          </p>
        </el-form-item>
        <el-form-item label="审核操作：">
          <el-select
            v-model="form[formKey].state"
            placeholder="选择审核操作"
            size="small"
            style="margin-right: 16px"
            @change="changeReasonRound"
          >
            <el-option
              v-for="(item, index) in ops"
              :label="item.label"
              :value="item.value"
              :key="index"
            ></el-option>
          </el-select>
          <el-checkbox
            v-model="form[formKey].sendnotify"
            :true-label="1"
            :false-label="0"
            >系统通知</el-checkbox
          >
        </el-form-item>
        <el-form-item
          label="理由类型："
          v-if="form[formKey].state === -2 || form[formKey].state === -4"
        >
          <AuditReasonStruct
            ref="auditReasonStruct"
            :state="form[formKey].state"
            :dirty="true"
            :reason="form[formKey].reason"
            :reasonTags="reasonTags"
            :reasonMap="reasonMap"
            @update-reason="(val) => form[formKey].reason = val"
            @update-reason-id="handleUpdateReasonId"
            @clear-reason="(val) => form[formKey].reason = val"
          />
        </el-form-item>
        <el-form-item label="投稿类型：">
          <el-checkbox
            v-model="form[formKey].flag_copyright"
            :true-label="1"
            :false-label="0"
            style="margin-right: 16px"
            @change="changeFlagCopyright"
          ></el-checkbox>
          <el-radio-group
            v-model="form[formKey].copyright"
            v-if="form[formKey].flag_copyright"
          >
            <el-radio :label="1">自制</el-radio>
            <el-radio :label="2">转载</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-checkbox
            v-model="form[formKey].xcode2"
            :true-label="1"
            :false-label="0"
            v-if="form[formKey].copyright === 2"
            :disabled="!perms.XCODE2"
            >重新压制视频</el-checkbox
          >
        </el-form-item>
        <el-form-item label="管理备注：" class="remark">
          <el-select
            v-model="noteTemplate"
            size="small"
            style="margin-right: 16px"
            @change="changeNoteTemplate"
          >
            <el-option
              v-for="(item, index) in notes"
              :label="item"
              :value="item"
              :key="index"
            ></el-option>
          </el-select>
          <el-input v-model="form[formKey].note" size="small"></el-input>
        </el-form-item>
      </el-form>

      <el-form
        :model="form"
        v-if="dialogTitle === '批量修改分区'"
        label-width="80px"
        label-position="right"
      >
        <el-form-item label="稿件ID：">
          <el-input v-model="form[formKey].ids" size="small"></el-input>
          <p>（英文逗号分隔的稿件ID，不存在的稿件将被忽略）</p>
        </el-form-item>
        <el-form-item label="分区：">
          <TreeSelect
            width="100%"
            :options="arctypes"
            disableTypeId
            v-model="form[formKey].typeid"
            :multipleSelect="false"
          ></TreeSelect>
        </el-form-item>
        <el-form-item label="备注：">
          <el-input
            type="textarea"
            :rows="2"
            v-model="form[formKey].note"
          ></el-input>
        </el-form-item>
      </el-form>

      <el-form
        :model="form"
        v-if="dialogTitle === '批量IP限制'"
        label-width="80px"
        label-position="right"
      >
        <el-form-item label="稿件ID：">
          <el-input v-model="form[formKey].ids" size="small"></el-input>
        </el-form-item>
        <el-form-item label="IP策略：">
          <IpLimit
            :policies="policies"
            v-model="form[formKey].policy_id"
          ></IpLimit>
        </el-form-item>
        <el-form-item label="备注：">
          <el-input
            type="textarea"
            :rows="2"
            v-model="form[formKey].note"
          ></el-input>
        </el-form-item>
      </el-form>

      <el-form
        :model="form"
        v-if="dialogTitle === '批量添加属性' || dialogTitle === '批量删除属性'"
        label-width="100px"
        label-position="right"
      >
        <el-form-item label="稿件ID：">
          <el-input v-model="form[formKey].ids" size="small" @blur="checkIdNumberLessThousand"></el-input>
          <p>（英文逗号分隔的稿件ID，不存在的稿件将被忽略，<span :class="{'id-input-warn': idInputError}">只可输入1000个稿件ID</span>）</p>
        </el-form-item>
        <el-form-item label="禁止项：">
          <BlockOption
            :norank.sync="form[formKey].attrForm.norank"
            :noindex.sync="form[formKey].attrForm.noindex"
            :norecommend.sync="form[formKey].attrForm.norecommend"
            :nohot.sync="form[formKey].attrForm.nohot"
            :hot_down.sync="form[formKey].attrForm.hot_down"
            :rank_down.sync="form[formKey].attrForm.rank_down"
            :nosearch.sync="form[formKey].attrForm.nosearch"
            :push_blog.sync="form[formKey].attrForm.push_blog"
            :disabled="!perms.MOD_S_FLOW_READ && !perms.MOD_S_FLOW_WRITE"
            :enable_six_limit="enableSixLimit"
          />
        </el-form-item>
        <el-form-item label="高级禁止：">
          <span v-if="!perms.MOD_ARC_FLOW" class="font-14 mr-10 ml-10">暂无权限查看&nbsp;&nbsp;
            <a :href="HELP_HREF" target="_blank" class="help-text">点击获得帮助</a>
          </span>
          <AttrTable v-else v-model="form[formKey].attrForm"></AttrTable>
        </el-form-item>
        <el-form-item label="稿件属性：">
          <AttrList
              v-model="form[formKey].attrForm"
              @enableSixLimit="val => enableSixLimit = val"
            />
        </el-form-item>
        <el-form-item label="备注：">
          <AgNoteTag
            :tags="form[formKey].noteTags"
            :options="noteOptions"
            @add-note="handleAddNote"
            @delete-note="handleDeleteNote"
          />
        </el-form-item>
      </el-form>
      <!-- 批量添加备注 -->
      <el-form
        :model="form"
        v-if="dialogTitle === '批量添加备注'"
        label-width="100px"
        label-position="right"
      >
        <AgNoteTag
          :tags="form[formKey].noteTags"
          :options="noteOptions"
          :disabled="!perms.BATCH_ARC_NOTETAG"
          @add-note="handleAddNote"
          @delete-note="handleDeleteNote"
          popperClass="note-tag-cascader-panel-popper"
        />
        <LimitReasonAndSnapshot
          ref="noteTagReasonRef"
          labelWidth="45px"
          :disabled="!perms.BATCH_ARC_NOTETAG"
          :limitTags="form[formKey].noteTags"
          tagType="noteTag"
          @update:noteTagReason="handleNoteTagReasonChange"
          emptySnapshotListMsg="批量操作下不适用添加单个稿件的截图，如有需要，请前往具体稿件的详情页添加限流截图"
        />
      </el-form>
      <!-- 批量修改争议标识 -->
      <el-form
        :model="form"
        v-if="dialogTitle === '批量修改争议标识'"
        label-width="100px"
        label-position="right"
      >
        <el-form-item label="操作：" required>
          <el-radio-group
            v-model="argueType"
            size="small"
            @change="(val) => (argueType = val)"
          >
            <el-radio-button
              v-for="(item, index) in argueRadioOps"
              :label="item.val"
              :key="index"
              >{{ item.label }}</el-radio-button
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="争议标识：" required>
          <ArgueSelect
            v-model="form[formKey].argument_tag"
            :argueData="argueData"
          >
          </ArgueSelect>
        </el-form-item>
        <el-form-item label="备注：" required>
          <el-input
            type="textarea"
            :rows="2"
            v-model="form[formKey].note"
          ></el-input>
        </el-form-item>
      </el-form>

      <el-form
        :model="form"
        v-if="dialogTitle === '二审配置'"
        label-width="140px"
        label-position="right"
      >
        <el-form-item label="指定待审分区配置：">
          <TreeSelect
            ref="tree"
            width="100%"
            disableTypeId
            :options="arctypes"
            v-bind:selectedOptions="form[formKey].value"
            @change="(val) => (form[formKey].value = val)"
            :multipleSelect="true"
          >
          </TreeSelect>
        </el-form-item>
      </el-form>

      <el-form
        :model="form"
        v-if="dialogTitle === 'ogv特殊分区配置'"
        label-width="140px"
        label-position="right"
      >
        <el-form-item label="ogv特殊分区配置：">
          <TreeSelect
            ref="tree"
            width="100%"
            disableTypeId
            :options="arctypes"
            v-bind:selectedOptions="form[formKey].value"
            @change="(val) => (form[formKey].value = val)"
            :multipleSelect="true"
          >
          </TreeSelect>
        </el-form-item>
      </el-form>

      <el-form
        :model="form"
        v-if="dialogTitle === '回查配置'"
        label-width="180px"
        label-position="right"
      >
        <el-form-item label="一二查UP主粉丝量阈值：">
          <el-row>
            <el-col :span="22">
              <el-input
                v-model.number="form[formKey].round_limit_fans"
                size="small"
                @input="formatNumber('round_limit_fans')"
              ></el-input>
            </el-col>
            <el-col :span="2" align="right">
              <el-button type="primary" size="small" @click="submitConfig('round_limit_fans')">提交</el-button>
            </el-col>
          </el-row>
          <span>（请填写大于等于0的正整数）</span>
        </el-form-item>
        <el-form-item label="一查指定回查分区：">
          <el-row>
            <el-col :span="22">
              <TreeSelect
                ref="tree"
                width="100%"
                disableTypeId
                :options="arctypes"
                v-bind:selectedOptions="form[formKey].round_limit_tids"
                @change="(val) => (form[formKey].round_limit_tids = val)"
                :multipleSelect="true"
              >
              </TreeSelect>
            </el-col>
            <el-col :span="2" align="right">
              <el-button type="primary" size="small" @click="submitConfig('round_limit_tids')">提交</el-button>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="三查超时配置：">
          <el-row>
            <el-col :span="22">
              <el-input
                v-model.number="form[formKey].round_delay_time"
                size="small"
                @input="formatNumber('round_delay_time')"
              ></el-input>
            </el-col>
            <el-col :span="2" align="right">
              <el-button type="primary" size="small" @click="submitConfig('round_delay_time')">提交</el-button>
            </el-col>
          </el-row>
          <span>（单位：天，请填写大于等于0的正整数）</span>
        </el-form-item>
        <el-form-item label="三查点击量阈值：">
          <div style="width:100%;height:100%">
            <table>
              <thead class="threshold-table-thead">
                <tr>
                  <th style="width: 30px">分区</th>
                  <th v-for="(rowItem, index) in gridData" :key="index">
                    {{ rowItem.name }}-{{ rowItem.typeid }}
                  </th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody class="threshold-table-tbody">
                <tr>
                  <td style="width: 50px; text-align: center">当前值</td>
                  <td v-for="(rowItem, index) in gridData" :key="index">
                    <el-input
                      :readonly="!isEdit"
                      v-model.number="rowItem.value"
                      size="mini"
                    ></el-input>
                  </td>
                  <td>
                    <el-button
                      v-if="!isEdit"
                      size="mini"
                      primary
                      @click="editRoundBackThreshold"
                      type="primary"
                      plain
                      >编辑</el-button
                    >
                    <el-button
                      v-if="isEdit"
                      size="mini"
                      primary
                      @click="saveRoundBackThreshold"
                      type="primary"
                      plain
                      >保存</el-button
                    >
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-form-item>
        <el-form-item label="效果说明：">
          <div style="font-size: 10px">
            <p>
              效果1：（除优质/高危用户外）若UP主粉丝量小于阈值，但稿件不属于指定回查分区，则稿件继承一审结果仍开放浏览。
            </p>
            <p>
              效果2：（除优质/高危用户外）若UP主粉丝量小于阈值，且稿件属于指定回查分区，则稿件进入一查列表。
            </p>
            <p>
              效果3：若UP主粉丝量大于等于阈值，则稿件通过一审、转码分发、自动开放浏览后，进入二查列表。
            </p>
            <p>
              效果4：经过效果1或效果2处理后的稿件，在超时配置要求的时间内，达成点击阈值，则稿件进入三查列表。
            </p>
            <span style="margin-left: 42px"
              >未在超时配置要求的时间内达成点击阈值，仍开放浏览且不会再进入任何回查。</span
            >
          </div>
        </el-form-item>
      </el-form>

      <el-form
        :model="form"
        v-if="dialogTitle === '批量审核频道'"
        label-width="80px"
        label-position="right"
      >
        <el-form-item label="稿件ID：">
          <el-input v-model="form[formKey].aids" size="small"></el-input>
          <p>（英文逗号分隔的稿件ID，不存在的稿件将被忽略）</p>
        </el-form-item>
        <el-form-item label="修改tag：">
          <el-checkbox
            v-model="editTag"
            style="margin-right: 18px"
          ></el-checkbox>
          <el-radio-group v-if="editTag" v-model="form[formKey].action">
            <el-radio label="add">新增 tag</el-radio>
            <el-radio label="delete">删除 tag</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="tag名称：" v-if="editTag">
          <el-input
            type="textarea"
            :rows="2"
            v-model="form[formKey].tags"
            placeholder="tag名称，多个用英文逗号隔开"
          ></el-input>
        </el-form-item>
        <el-form-item label="备注：">
          <el-input
            type="textarea"
            :rows="2"
            v-model="form[formKey].note"
          ></el-input>
        </el-form-item>
      </el-form>
      <el-form
        :model="form"
        v-if="dialogTitle === '批量限流通知'"
        label-width="140px"
        label-position="right"
      >
        <LimitForm
          ref="limitForm"
          :form.sync="form.limit_notify_v2"
          :limitReasonOps="limitReasonOps"
          :isBatch="true"
        />
        <!-- 操作项 -->
        <p class="footer-warn"><span>批量操作，谨慎处理。</span><span v-if="form.limit_notify_v2.type === 3" class="footer-warn">一旦发送，不可撤回。</span></p>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="dialogTitle !== '回查配置'" align="right">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapState, mapActions, mapGetters } from 'vuex'
import TreeSelect from '@/components/TreeSelect.vue'
import AttrTable from '@/v2/biz-components/archive/AttrTable.vue'
import IpLimit from '@/components/Ip/IpLimit.vue'
import http from '@/lib/http'
import notify from '@/lib/notify'
import cloneDeep from 'lodash-es/cloneDeep'
import { archiveApi, argueApi, workbenchApi } from '@/api/index'
import { FROM_LIST_MAP, LIMIT_REASON, UAT_UNLIMIT_REASON_ID, PROD_UNLIMIT_REASON_ID, UNLIMIT_REASON, BATCH_NOTE_ATTR } from '@/utils/constant'
import ArgueSelect from '@/components/Advanced/ArgueSelect.vue'
import { washArgueData } from '@/utils'
import AgNoteTag from '@/components/element-update/NoteTag.vue'
import washNoteTag from '@/karl/second-audit/note-tag.js'
import LimitForm from '@/v2/biz-components/archive/LimitForm'
import AuditReasonStruct from '@/v2/biz-components/archive/AuditReasonStruct'
import { HELP_HREF } from '@/v2/data-source/config/local/constant'
import BlockOption from '@/v2/biz-components/archive/BlockOption'
import AttrList from '@/v2/biz-components/archive/AttrList.vue'
import { confirmClassArchive } from '@/v2/biz-utils/classVideoConfirm'
import archiveDetector from '@/v2/biz-utils/archiveDetector'
import genOperationTags from '@/v2/biz-utils/archiveOperationTags'
import LimitReasonAndSnapshot from '@/v2/biz-components/archive/LimitReasonAndSnapshot'

const BTN_MAP = {
  批量审核: 'audit',
  批量修改分区: 'arctype',
  批量IP限制: 'oversea',
  批量添加属性: 'attribute',
  批量删除属性: 'attribute',
  二审配置: 'second',
  ogv特殊分区配置: 'ogvspecial',
  回查配置: 'back',
  批量审核频道: 'channel',
  批量修改争议标识: 'argument',
  批量添加备注: 'note_tag',
  批量限流通知: 'limit_notify_v2'
}

const BATCH_URL = '/x/admin/videoup/archive/batch'
const SECOND_CONFIG_URL = '/x/admin/videoup/arc_conf/kv/submit'
const BATCH_CHANNEL = '/x/admin/videoup/archive/batch/tag'

export default {
  components: {
    AuditReasonStruct,
    TreeSelect,
    AttrTable,
    IpLimit,
    ArgueSelect,
    AgNoteTag,
    LimitForm,
    BlockOption,
    AttrList,
    LimitReasonAndSnapshot
  },
  props: {
    todoId: Number,
    businessId: Number,
    todoConfig: Object,
    mockSubmit: Boolean // 假提交
  },
  data() {
    return {
      dialogTitle: '',
      dialogVisible: false,
      multipleSelection: [],
      formTemplate: {
        audit: {
          ids: '',
          sendnotify: 0,
          state: '',
          reason_id: '',
          tag_id: '',
          reason: '',
          copyright: '',
          flag_copyright: '',
          xcode2: 0,
          note: ''
        },
        oversea: {
          ids: '',
          note: '',
          policy_id: 0
        },
        arctype: {
          ids: '',
          typeid: '',
          note: ''
        },
        attribute: {
          ids: '',
          attrForm: {},
          note: '',
          noteTags: []
        },
        second: {
          name: 'wait_audit_arctype',
          value: ''
        },
        ogvspecial: {
          name: 'ogv_special_typeid',
          value: ''
        },
        back: {
          round_limit_fans: '',
          round_delay_time: '',
          round_limit_tids: ''
        },
        channel: {
          aids: '',
          note: '',
          action: 'add',
          tags: ''
        },
        argument: {
          note: '',
          argument_tag: ''
        },
        note_tag: {
          noteTags: [],
          noteTagReason: []
        },
        limit_notify_v2: {
          type: 3,
          reasons: {},
          unlimitReason: UNLIMIT_REASON
        }
      },
      form: {},
      reasonType: '',
      actionReasons: [],
      allActionReasons: [],
      reasons: [],
      notes: [
        '含竞品信息',
        '私单报备',
        '含争议引战信息',
        '含低俗信息',
        '含血腥暴力信息',
        '含抽奖信息',
        '含低创',
        '双重水印',
        '韩国相关'
      ],
      noteOptions: [],
      argueType: 1,
      argueRadioOps: [
        {
          label: '添加',
          val: 1
        },
        {
          label: '删除',
          val: 0
        }
      ],
      noteTemplate: '',
      // 0: '开放浏览',
      // 10000: '会员可见',
      // '-2': '打回',
      // '-4': '锁定稿件',
      // '-7': '暂缓',
      // 1: '橙色通过',
      // 10001: '橙色通过，会员可见',
      // '-11': '视频源待修',
      // '-1': '待审',
      // '-13': '允许评论待审',
      // '-16': '转码失败',
      // '-6': '修复待审'
      ops: [],
      limitTypeRadioOps: [
        {
          label: '限流',
          value: 3
        },
        {
          label: '解限',
          value: 4
        }
      ],
      limitReasonOps: [],
      policies: [],
      isEdit: false,
      gridData: [],
      editTag: false,
      listType: null,
      fromReview: null,
      argueData: {},
      cascaderDropdownShow: false,
      oper: null,
      checking: false,
      checkResultError: '',
      checkResultHint: '',
      checkResultWarning: '',
      reasonTags: [],
      reasonMap: {},
      HELP_HREF,
      idInputError: false,
      enableSixLimit: false
    }
  },
  provide() {
    return {
      perms: this.perms
    }
  },
  computed: {
    ...mapState({
      perms: (state) => state.user.perms,
      arctypes: (state) => state.arctype.arctypes
    }),
    ...mapGetters({
      getEnv: 'env/getEnv'
    }),
    formKey() {
      return BTN_MAP[this.dialogTitle]
    },
    oids() {
      return (
        // NOTE: 修复待审需要取rsc_info.id
        (this.multipleSelection || []).map((item) => item.rsc_info.oid || item.rsc_info.id).join(',') || ''
      )
    },
    isConfig() {
      return (
        this.dialogTitle === '二审配置' ||
        this.dialogTitle === '回查配置' ||
        this.dialogTitle === 'ogv特殊分区配置'
      )
    },
    checkButtonDisabled() {
      return this.form?.[this.formKey]?.ids?.trim?.()?.length === 0
    }
  },
  methods: {
    ...mapActions({
      getArctype: 'arctype/getArctype'
    }),
    keyDownHandler(event) {
      const keyCode = event.keyCode
      if (event.target.nodeName === 'TEXTAREA' || event.target.nodeName === 'INPUT') {
        return
      }
      // 快捷键回车提交
      if (keyCode === 13 && this.cascaderDropdownShow) {
        this.$EventBus.$emit('note-cascader-add')
      }
    },
    registerKeyHandler() {
      document.addEventListener('keydown', this.keyDownHandler)
      this.$once('hook:beforeDestroy', () => {
        document.removeEventListener('keydown', this.keyDownHandler)
      })
      this.$EventBus.$off('note-cascader-show', this.setCascaderDropdownShow)
      this.$EventBus.$on('note-cascader-show', this.setCascaderDropdownShow)
    },
    setCascaderDropdownShow(val) {
      this.cascaderDropdownShow = val
    },
    handleNoteTagReasonChange(newVal) {
      this.form.note_tag.noteTagReason = newVal
    },
    getLimitReasons() {
      const env = this.getEnv()
      archiveApi.getReason({
        type: 2,
        parent: LIMIT_REASON[env]
      }).then(res => {
        const data = res.data
        this.limitReasonOps = data.map(item => {
          return {
            name: item.short_name,
            label: item.reason,
            value: item.id
          }
        })
      })
    },
    openDialog(oper, multipleSelection) {
      const { name } = oper

      this.oper = oper
      this.multipleSelection = multipleSelection
      this.dialogTitle = name
      this.dialogVisible = true
      this.form = cloneDeep(this.formTemplate)

      this.checking = false
      this.checkResultError = ''
      this.checkResultWarning = ''
      this.enableSixLimit = false

      if (!this.isConfig && this.dialogTitle !== '批量审核频道') {
        this.form[this.formKey].ids = this.oids
        this.form[this.formKey].note = ''
      }
      if (name === '批量修改分区' || this.isConfig) {
        this.getArctype().then(() => {
          if (this.isConfig) {
            this.getConfig()
          }
        })
      } else if (name === '批量审核') {
        this.getEnable(multipleSelection)
        this.actionReasons = []
      } else if (name === '批量IP限制') {
        this.getPolicy()
      } else if (name === '批量审核频道') {
        this.editTag = false
        this.form[this.formKey].aids = this.oids
      } else if (name === '批量修改争议标识') {
        this.getArgueData()
      } else if (name === '批量添加备注' || name === '批量添加属性' || name === '批量删除属性') {
        this.getNoteOptions()
      } else if (name === '批量限流通知') {
        this.limitReasonOps = []
        this.getLimitReasons()
      }

      if (this.todoConfig) {
        const listType = null

        if ([114, 115, 116].find(item => item === +listType)) {
          this.listType = '41'
        } else {
          this.listType = this.todoConfig.list_type
        }

        this.fromReview = this.todoConfig.from_review
      }
    },
    handleAddNote(note) {
      const { value, label, remark, pre_text: preText } = note
      const noteTags = this.form[this.formKey].noteTags
      const existNoteTag = noteTags.find(tag => tag.tag_id === value && tag.pre_text === preText)
      if (existNoteTag && existNoteTag.canDelete) {
        notify.warning('已经存在该备注tag，只修改了备注')
        existNoteTag.remark = remark
        existNoteTag.tag_name = `${preText}【${remark}】`
      } else if (existNoteTag) {
        notify.warning('已经存在该备注tag，且无权修改')
      } else {
        this.form[this.formKey].noteTags.unshift({
          tag_id: value,
          tag_name: label,
          remark,
          pre_text: preText,
          hitState: false,
          first_init: false,
          canDelete: true
        })
      }
    },
    handleDeleteNote(note, idx) {
      this.form[this.formKey].noteTags.splice(idx, 1)
    },
    handleUpdateReasonId(newReasonId, newTagId, categoryId, categoryText, tagPathText, tagPathId) {
      this.form[this.formKey].reason_id = newReasonId
      this.form[this.formKey].tag_id = newTagId
      this.form[this.formKey].full_tag_names = [[categoryText, ...(tagPathText?.split(' / ') || [])]]
      this.form[this.formKey].full_tag_ids = [[categoryId, ...(tagPathId || [])]]
    },
    async getNoteOptions() {
      try {
        const res = await archiveApi.getTagNote()
        const { noteOptions } = washNoteTag({
          note_tag: res.data
        })
        this.noteOptions = noteOptions
      } catch (e) {
        console.error(e)
      }
    },
    async getArgueData() {
      await argueApi
        .getArgumentList()
        .then((res) => {
          const { argueData } = washArgueData(res.data)
          this.argueData = argueData
        })
        .catch((_) => {})
    },
    async getEnable(multipleSelection) {
      try {
        const res = await workbenchApi.getOpers({
          business_id: this.businessId,
          todo_id: this.todoId,
          mode: 2,
          page_detail: 0,
          oids: (multipleSelection || []).map((item) => item.rsc_info.oid || item.rsc_info.id).join(',') || ''
        })
        this.ops = res.data.audit_batch.map(op => ({
          label: op.ch_name,
          value: Number(op.extra_value?.state),
          binds: op.bind_id_list
        }))
      } catch (err) {
        this.ops = []
      }
    },
    getConfig() {
      const needArr = [
        'wait_audit_arctype',
        'ogv_special_typeid',
        'round_limit_tids',
        'threshold_arctype',
        'round_limit_fans',
        'round_delay_time'
      ]
      archiveApi.getKvList().then(res => {
        const data = {}
        for (let i = 0; i < res.data.length; i++) {
          const item = res.data[i]
          if (needArr.find(needItem => needItem === item.name)) {
            data[item.name] = item.name === 'threshold_arctype' ? JSON.parse(item.value) : item.value
          }
        }
        if (this.dialogTitle === '二审配置') {
          this.form[this.formKey].value = data.wait_audit_arctype
        } else if (this.dialogTitle === 'ogv特殊分区配置') {
          this.form[this.formKey].value = data.ogv_special_typeid
        } else {
          const {
            round_limit_tids: roundLimitTids,
            threshold_arctype: thresholdArctype,
            round_limit_fans: roundLimitFans,
            round_delay_time: roundDelayTime
          } = data
          this.form[this.formKey].round_limit_tids = roundLimitTids
          const parentItems = this.arctypes.map((item) => {
            return {
              id: item.id,
              name: item.name
            }
          })
          // key是string类型，判断需要转化
          this.gridData = Object.keys(thresholdArctype).map((key) => {
            return {
              name: parentItems.find((i) => i.id === +key)?.name || key,
              typeid: key,
              value: thresholdArctype[key]
            }
          })

          this.form[this.formKey].round_limit_fans = roundLimitFans || 0
          this.form[this.formKey].round_delay_time = roundDelayTime || 0
        }
        this.$nextTick(() => {
          this.$refs.tree.initDefaultSelected()
        })
      }).catch(_ => {})
    },
    getPolicy() {
      const params = {
        type: 1,
        state: 1,
        order: 'ctime',
        sort: 'asc'
      }
      archiveApi.getPolicyGroups(params).then(res => {
        this.policies = (res.data && res.data.items) || []
      }).catch(_ => {})
    },
    changeNoteTemplate(val) {
      this.form[this.formKey].note = val
    },
    changeReasonRound() {
      const form = this.form[this.formKey]
      // 全部海外ogv、ugc稿件列表勾选逻辑
      // 已开放稿件选择开放默认不勾选通知、未开放稿件选择开放默认勾选通知、打回&锁定默认勾选通知
      if (this.listType === '106' || this.listType === '109') {
        const isOpen = this.multipleSelection
          .map((i) => i.state)
          .filter((state) => state === 0)

        const isWaiting = this.multipleSelection
          .map((i) => i.state)
          .filter((state) => state === -1 || state === -6)

        const OLength = isOpen.length
        const WLength = isWaiting.length
        const selectionLength = this.multipleSelection.length

        if (OLength === selectionLength && form.state === 0) {
          form.sendnotify = 0
        } else if (WLength === selectionLength && form.state === 0) {
          form.sendnotify = 1
        } else if (OLength + WLength === selectionLength && form.state === 0) {
          form.sendnotify = 1
        } else {
          form.sendnotify = form.state === 0 ? 0 : 1
        }
      } else if (this.listType === '107' || this.listType === '108') {
        // 海外UGC二审待审、海外UGC修复待审 开放&打回&锁定默认勾选通知
        form.sendnotify = 1
      } else {
        form.sendnotify = form.state === 0 ? 0 : 1
      }
      if (form.state === -2 || form.state === -4) {
        this.fetchReason(form.state)
      } else {
        this.resetParams()
      }
    },
    fetchReason(newState) {
      archiveApi
        .getReasonTags({
          list_type: this.listType,
          state: newState
        })
        .then((res) => {
          const { reason_map = {}, reason_tags = [] } = res.data || {}
          this.reasonTags = reason_tags || []
          this.reasonMap = reason_map
        })
        .catch((_) => {
          this.reasonTags = []
          this.reasonMap = {}
        })
    },
    changeFlagCopyright(val) {
      this.form[this.formKey].copyright = ''
    },
    resetParams() {
      this.form[this.formKey].reason = ''
      this.form[this.formKey].reason_id = ''
      this.form[this.formKey].tag_id = ''
      this.form[this.formKey].full_tag_names = []
      this.form[this.formKey].full_tag_ids = []
    },
    editRoundBackThreshold() {
      this.isEdit = true
    },
    submitConfig(configName) {
      const data = this.form[this.formKey][configName]
      archiveApi.submitKvList({
        name: configName,
        value: data
      }).then(_ => {
        notify.success('保存成功')
      }).catch(_ => {})
    },
    saveRoundBackThreshold() {
      const data = this.gridData || []
      let thresholdObj = {}
      data.forEach((elem) => {
        thresholdObj[elem.typeid] = elem.value
      })
      thresholdObj = JSON.stringify(thresholdObj)

      archiveApi.submitKvList({
        name: 'threshold_arctype',
        value: thresholdObj
      }).then(_ => {
        this.isEdit = false
        notify.success('保存成功')
      }).catch(_ => {})
    },
    formatNumber(key) {
      this.form[this.formKey][key] = parseInt(
        this.form[this.formKey][key].toString().replace(/^0*(\d+)$/, '$1'),
        10
      )
    },
    async submit(force = 0) {
      if (this.mockSubmit) {
        notify.success('操作成功')
        this.dialogVisible = false
        return
      }
      /* eslint-disable */
      let form = cloneDeep(this.form[this.formKey])
      if (!this.isConfig && !form.aids && !form.ids) {
        notify.warning('稿件ID不能为空')
        return
      }
      ['二审配置', 'ogv特殊分区配置'].includes(this.dialogTitle) || this.checkIdNumberLessThousand()
      if (this.idInputError) return
      let { reason_id, reason, tag_id } = form
      if (this.formKey === 'audit') {
        if (form.state === -2 || form.state === -4) {
          // 批量提交【10万粉丝或50万播放】稿件校验拦截
          form.force = force
          form.reject_reason = reason
          form.reject_reason_id = reason_id
          const reasonError = this.$refs.auditReasonStruct.validateReason()
          if (!reason) return notify.error('请添加驳回理由')
          if (reasonError) {
            notify.warning(reasonError)
            return
          }
        }

        if (this.fromReview >= 0) {
          form.from_review = this.fromReview
          form.from_list = FROM_LIST_MAP[this.fromReview] || ''
        }

        delete form.reason
        delete form.reason_id
        delete form.tag_id

        //  自制不需要传xcode2
        if (form.copyright === 1) {
          delete form.xcode2
        }

        form.list_type = this.listType

        form.operation_tags = genOperationTags({
          archiveState: form.state,
          reasonTagId: tag_id,
          note_text: form?.note
        })
      } else if (this.formKey === 'arctype') {
        form.typeid = parseInt(form.typeid.split(',')[0], 10)
      } else if (this.formKey === 'attribute') {
        for (let key in form.attrForm) {
          if (form.attrForm[key] === 1) {
            form.attrForm[key] = this.dialogTitle === '批量删除属性' ? 0 : 1
          } else {
            delete form.attrForm[key]
          }
        }
        delete form.attrForm.show_main
        form = { ...form, ...form.attrForm }
        form.note_tag = this.tagsArrTransfer(form.noteTags)
        const intersection = Object.keys(form).filter(formKey => BATCH_NOTE_ATTR.includes(formKey)) || []
        if(intersection.length > 0 && !form.note_tag) {
          return notify.error('请填写备注')
        }
        delete form.attrForm
        delete form.note
        delete form.noteTags
      } else if (this.formKey === 'channel') {
        form.form_list = 'channel_review'
        if (!this.editTag) {
          form.action = ''
          form.tags = ''
        }
      } else if (this.formKey === 'argument') {
        // 1.添加 0.删除
        if (form.argument_tag === '' || form.argument_tag_del === '') {
          return notify.error('请选择争议tag')
        }
        if (!form.note) {
          return notify.error('请填写备注')
        }
        if (this.argueType === 0) {
          form.argument_tag_del = form.argument_tag
          delete form.argument_tag
        }
      } else if (this.formKey === 'note_tag') {
        form.note_tag = this.tagsArrTransfer(form.noteTags)
        const errMsg = this.$refs.noteTagReasonRef.validateReason()
        if (errMsg) {
          this.$alert(errMsg, `请检查【备注标】限流理由`)
          return
        }
        form.note_tag_reason = JSON.stringify(form.noteTagReason)
        delete form.note
        delete form.noteTags
        delete form.noteTagReason
      } else if (this.formKey === 'limit_notify_v2') {
        const limitFormValid = await this.$refs['limitForm'].validate()
        if (!limitFormValid) return
        else {
          const limitReasonIds = []
          const limitReasonLabel = []
          for (const limiReasonKey in form.reasons) {
            const limitReason = form.reasons[limiReasonKey]
            if (limitReason.value === 1) {
              limitReasonIds.push(limitReason.id)
              limitReasonLabel.push(limitReason.reason)
            }
          }
          // 限流
          if (form.type === 3) {
            form = {
              reason_ids: limitReasonIds.join(','),
              reject_reason: limitReasonLabel.join(','),
              ids: form.ids,
              limit_notify_type: form.type
            }
          } else {
            // 解限
            const env = this.getEnv()
            form = {
              reason_ids: env === 'uat' ? UAT_UNLIMIT_REASON_ID : PROD_UNLIMIT_REASON_ID,
              reject_reason: form.unlimitReason,
              ids: form.ids,
              limit_notify_type: form.type
            }
          }
        }
      }
      /* eslint-enable */
      // 二审配置和回查配置不传action
      if (!this.isConfig && this.formKey !== 'channel') {
        form.action = this.formKey
      }

      if (this.oper.name === '批量审核') {
        let binds

        if (form.state === '') {
          notify.error('请选择操作项')
          return
        }
        if (this.ops.length > 0) {
          binds = this.ops.find((op) => op.value === form.state).binds
          form.bind_id = this.ops.find((op) => op.value === form.state).binds
        }
        form.aids = form.ids.split(',')
        delete form.ids
        const full_tag_names = form.full_tag_names
        const full_tag_ids = form.full_tag_ids
        delete form.full_tag_names
        delete form.full_tag_ids
        const submitParams = {
          resource_result: {
            note: form.note,
            reject_reason: form.reject_reason,
            reason_id: form.reject_reason_id,
            full_tag_names,
            full_tag_ids
          },
          extra_data: {
            ...form
          },
          binds
        }
        if (this.todoConfig?.flag_disable_base) {
          submitParams.extra_data.flag_disable_base = this.todoConfig?.flag_disable_base
        }
        const { state, aids } = submitParams.extra_data
        if ([-2, -4].includes(+state)) {
          await confirmClassArchive(aids?.join(','))
        }
        this.$emit('submit', submitParams, this.oper)
        this.dialogVisible = false
      } else {
        const url = this.isConfig
          ? SECOND_CONFIG_URL
          : this.formKey === 'channel'
          ? BATCH_CHANNEL
          : BATCH_URL

        http
          .post(url, form, true, false, {
            emulateJSON: true
          })
          .then((res) => {
            // 批量审核频道的接口是code=0是操作成功
            if (res.code === 0) {
              !res.tips && notify.success('操作成功')
              this.dialogVisible = false
            } else {
              if (this.formKey === 'audit' && res.code === 21159) {
                this.showSubmitWarning(res.message)
              } else {
                notify.error(res.message)
              }
            }
          })
      }
    },
    showSubmitWarning(msg) {
      const hitStr = '<em style="color: var(--error-color);font-weight: bold">下线</em>'
      const idStr = `<em style="word-break: break-all">${msg}</em>`
      const text = `含高粉/高播放稿件: ${idStr}，是否确认进行 ${hitStr} 处理（可联系回查团队确认）`
      this.$confirm(text, '批量审核提示', {
        dangerouslyUseHTMLString: true,
        type: 'warning'
      })
        .then(() => {
          this.submit(1)
        })
        .catch((_) => {})
    },
    async validateIds() {
      if (this.checking) return
      this.checking = true
      this.checkResultHint = ''
      this.checkResultWarning = ''
      this.checkResultError = ''
      const res = await archiveDetector(this.form[this.formKey].ids)
      const { hintText, warningText, errorText } = res
      this.checkResultHint = hintText
      this.checkResultWarning = warningText
      this.checkResultError = errorText
      this.checking = false
    },
    tagsArrTransfer(noteTags) {
      const auditTagsArr = noteTags.map(tag => {
        return {
          tag_id: tag.tag_id,
          remark: tag.remark
        }
      })
      return auditTagsArr.length === 0 ? '' : JSON.stringify(auditTagsArr)
    },
    checkIdNumberLessThousand() {
      const idsArr = this.form[this.formKey].ids?.split(',')
        .filter((id) => {
          return id !== ''
        }) || []
      this.idInputError = idsArr.length > 1000
    }
  },
  created() {
    this.registerKeyHandler()
  },
  beforeDestroy() {
    this.$EventBus.$off('note-cascader-show', this.setCascaderDropdownShow)
  }
}
</script>
<style lang="stylus">
.batch-archive-dialog
  .ids-with-check
    display: flex
    .el-button
      margin-left: 12px
  .remark
    .el-form-item__content
      display flex
  .el-dialog__footer
    text-align center
  .threshold-table-thead
    border-top 1px solid var(--border-color-light-2)
    border-bottom 1px solid var(--border-color-light-2)
    border-left 1px solid var(--border-color-light-2)
    background-color var(--table-th-bg-color)
    tr > th
      border-right 1px solid var(--border-color-light-2)
  .threshold-table-tbody
    border-bottom 1px solid var(--border-color-light-2)
    border-left 1px solid var(--border-color-light-2)
    tr > td
      border-right 1px solid var(--border-color-light-2)
  .footer-warn
    text-align right
    color var(--error-color)
.note-tag-cascader-panel-popper
  z-index 10000 !important
.id-input-warn
    color var(--red)
</style>
