<template>
  <div class="workbench-effect-dialog">
    <DraggableDialog
      title="有效"
      :visible.sync="dialogVisible"
      width="800px"
      :fixPosition="{ left: '100px',  top: '150px'}"
      rememberPosition="single-effect-dialog"
      style="max-height: 900px; overflow-y: scroll"
    >
    <el-form
      ref="form"
      submit.stop.prevent.native
      :model="form"
      size="small"
      :rules="rules"
      label-width="100px"
      :disabled="disabled"
    >
      <el-form-item prop="status" label="修改稿件状态">
        <el-row style="margin-bottom: 10px">
          <el-select v-model="form.status" placeholder="请选择" style="margin-right: 10px">
            <el-option
              v-for="item in STATES"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-row>
      </el-form-item>

      <el-form-item v-if="form.status === '-4'" prop="forward" label="撞车跳转">
        <el-input v-model="form.forward"></el-input>
      </el-form-item>
      <el-form-item v-if="['-2', '-4'].includes(form.status)" label="理由类型">
        <AuditReasonStruct
          ref="auditReasonStruct"
          :state="form.status"
          :dirty="true"
          :reason="form.archive_reason"
          :forward="form.forward"
          :reasonTags="reasonTags"
          :reasonMap="reasonMap"
          @update-forward="(val) => form.forward = val"
          @update-reason="(val) => form.archive_reason = val"
          @update-reason-id="handleUpdateReasonId"
          @clear-reason="(val) => form.archive_reason = val"
        />
      </el-form-item>

      <!-- 仅回查展示 -->
      <template v-if="isReview">
        <el-form-item prop="attrForm" label="修改禁止项">
          <BlockOption
            :norank.sync="form.attrForm.norank"
            :noindex.sync="form.attrForm.noindex"
            :norecommend.sync="form.attrForm.norecommend"
            :nohot.sync="form.attrForm.nohot"
            :hot_down.sync="form.attrForm.hot_down"
            :rank_down.sync="form.attrForm.rank_down"
            :nosearch.sync="form.attrForm.nosearch"
            :push_blog.sync="form.attrForm.push_blog"
            :disabled="(!perms.MOD_S_FLOW_READ && !perms.MOD_S_FLOW_WRITE) || disabled"
          />
        </el-form-item>
        <el-form-item prop="copyright" label="修改类型">
          <el-radio v-model="form.copyright" :label="1">自制</el-radio>
          <el-radio v-model="form.copyright" :label="2">转载</el-radio>
          <el-checkbox v-if="perms.XCODE2 && form.copyright === 2 && +archive.copyright === 1" class="ml-20" v-model="form.xcode2">
            重新压制
            <el-tooltip content="该功能用于转自类型修改时去除原用户水印" effect="light" placement="top">
              <i class="el-icon-question font-16"></i>
            </el-tooltip>
          </el-checkbox>
        </el-form-item>
        <el-form-item label="备注">
          <AgNoteTag
            :tags="noteTags"
            :options="noteOptions"
            :aid="aid"
            :disabled="disabled"
            @add-note="handleAddNote"
            @delete-note="handleDeleteNote"
          />
          <LimitReasonAndSnapshot
            :aid="aid"
            ref="noteTagReasonRef"
            labelWidth="45px"
            :limitTags="noteTags"
            tagType="noteTag"
            @update:noteTagReason="(newVal) =>this.noteTagReason = newVal"
          />
        </el-form-item>
        <el-form-item label="灰标" v-if="canReadGrayTags">
          <el-cascader
            ref="grayTagRef"
            style="width: 100%"
            v-model="grayTags"
            :options="grayTagOps"
            :disabled="!canWriteGrayTags"
            clearable
            :props="{
              expandTrigger: 'hover',
              children: 'options',
              label: 'name',
              value: 'id',
              multiple: true
            }"
            @change="handleGrayTagChange"
          />

          <LimitReasonAndSnapshot
            ref="grayTagReasonRef"
            :aid="aid"
            labelWidth="45px"
            :limitTags="selectedGrayTagNodes"
            tagType="grayTag"
            @update:grayTagReason="(newVal) =>this.grayTagReason = newVal"
          />
        </el-form-item>
      </template>

      <!-- 仅审核展示 -->
      <template v-else>
        <el-form-item class="form-reason" prop="reason" label="操作原因">
          <el-input v-model="form.reason" maxlength="100" show-word-limit></el-input>
        </el-form-item>
      </template>
    </el-form>

    <div class="dialog-footer" style="text-align: center">
      <el-button @click="close" size="medium">取 消</el-button>
      <el-button type="primary" size="medium" @click="submit">确 定</el-button>
    </div>
  </DraggableDialog>
  </div>
</template>
<script>
import { mapState, mapActions } from 'vuex'
import DraggableDialog from '@/components/Dialog/DraggableDialog'
import AuditReasonStruct from '@/v2/biz-components/archive/AuditReasonStruct.vue'
import BlockOption from '@/v2/biz-components/archive/BlockOption'
import $notify from '@/lib/notify'
import { AgNoteTag } from '@/v2/pure-components/ElementUpdate'
import { archiveApi } from '@/api'
import washNoteTag from '@/karl/second-audit/note-tag'
import { cloneDeep } from 'lodash-es'
import { getGrayTags } from '@/v2/biz-utils/grayTagsHandler'
import genOperationTags from '@/v2/biz-utils/archiveOperationTags'
import { parseTodoIdFromSearchParams } from '@/v2/biz-utils/parseFromSearchParams'
import LimitReasonAndSnapshot from '@/v2/biz-components/archive/LimitReasonAndSnapshot.vue'
import { getNodesByFullTagIds } from '@/v2/biz-utils/classifyTags'
import { validateAllLimitReasons, overrideAllLimitReasons } from '@/v2/biz-utils/validateLimitReason'

const formTemplate = {
  status: '',
  // notify: true,
  forward: '',
  attrForm: {
    norank: 0,
    noindex: 0,
    norecommend: 0,
    nohot: 0,
    hot_down: 0,
    rank_down: 0,
    nosearch: 0,
    push_blog: 0
  },
  copyright: 1,
  xcode2: false,
  reason: '',
  tag_id: ''
}

const STATES = [
  {
    label: '开放浏览',
    value: '0'
  },
  {
    label: '打回',
    value: '-2'
  },
  {
    label: '锁定',
    value: '-4'
  }
]

export default {
  components: {
    DraggableDialog,
    BlockOption,
    AuditReasonStruct,
    AgNoteTag,
    LimitReasonAndSnapshot
  },
  props: {
    aid: {
      type: Number,
      default: 0
    },
    archive: {
      type: Object,
      default: () => {}
    },
    todoConfig: {
      type: Object,
      default: () => {}
    },
    attrForm: {
      type: Object,
      default: () => {}
    },
    operation: {
      type: Object,
      default: () => {}
    },
    disabled: {
      type: Boolean,
      default: false
    },
    formOperation: Object,
    snapshotGrayTags: Array,
    snapshotNoteTags: Array
  },
  data() {
    return {
      STATES,
      dialogVisible: false,
      oper: {},
      form: {
        status: '',
        forward: '',
        attrForm: {},
        copyright: 1,
        xcode2: false,
        reason: ''
      },
      reasonTags: [],
      reasonMap: {},
      noteTags: [],
      noteOptions: [],
      grayTags: [],
      rules: {
        reason: [
          { required: true, message: '请填写操作原因', trigger: 'change' }
        ]
      },
      selectedGrayTagNodes: [],
      grayTagReason: [],
      selectedNoteTagNodes: [],
      noteTagReason: []
    }
  },
  computed: {
    ...mapState({
      perms: state => state.user.perms,
      grayTagOps: (state) => state.grayTag.grayTagOps,
      leafNodes: (state) => state.grayTag.leafNodes
    }),
    isReview() {
      return this.todoConfig?.isReview ?? false
    },
    canReadGrayTags() {
      return this.perms.ARCH_GRAY_TAGS_READ || this.perms.ARCH_GRAY_TAGS_WRITE
    },
    canWriteGrayTags() {
      return this.perms.ARCH_GRAY_TAGS_WRITE
    }
  },
  methods: {
    ...mapActions({
      fetchGrayTags: 'grayTag/fetchGrayTags'
    }),
    async openDialog(oper) {
      this.dialogVisible = true
      this.oper = oper
      this.$nextTick(() => {
        if (this.disabled) {
          this.form = this.formOperation
          this.noteTags = this.snapshotNoteTags
          this.grayTags = this.snapshotGrayTags
        } else {
          this.resetDialogParams()
        }

        const selectedNodes = getNodesByFullTagIds(this.leafNodes, this.grayTags)
        this.selectedGrayTagNodes = selectedNodes
      })
    },
    resetDialogParams() {
      this.$refs.form.resetFields()
      this.form = cloneDeep(formTemplate)
      // 稿件状态
      this.form.status = this.archive.status + '' || '0'
      // 禁止项
      Object.keys(this.form.attrForm).forEach(attr => {
        this.form.attrForm[attr] = this.attrForm[attr] ? this.attrForm[attr] : 0
      })
      // 转载状态
      this.form.copyright = this.archive.copyright
      // 角色备注
      this.noteTags = [...this.operation.auditTags]
      // 灰标
      this.grayTags = [...this.operation.grayTags]
    },
    close() {
      this.dialogVisible = false
    },
    getSubmitParams() {
      const {
        archive_reason: archiveReason,
        archive_reason_id: archiveReasonId,
        status,
        forward,
        attrForm,
        copyright,
        reason,
        xcode2,
        tag_id
      } = this.form

      const {
        norank,
        noindex,
        norecommend,
        nohot,
        hot_down,
        rank_down,
        nosearch,
        push_blog
      } = attrForm

      if (['-2', '-4'].includes(status)) { // 打回/锁定状态时
        const reasonError = this.$refs.auditReasonStruct?.validateReason()

        if (reasonError) {
          $notify.warning(reasonError)
          return
        }
      }

      if (this.isReview && +status !== 0 && !this.form.archive_reason_id) {
        $notify.warning('请选择申诉理由！')
        return
      }
      if (this.form.status === '') {
        $notify.warning('请选择稿件状态！')
        return
      }

      // 限流理由&截图校验（灰标+备注标）
      this.validateLimitReason()

      const { grayTags, hasGrayTagChange } = getGrayTags(this.grayTags, [...this.operation?.grayTags] || [])
      const note_tag = this.noteTags.length ? JSON.stringify(this.noteTags.map(v => ({ tag_id: +v.tag_id, remark: v.remark }))) : ''
      const remark_str = this.noteTags.length ? this.noteTags.map(v => v.tag_name).join(',') : ''

      const data = {
        remark: this.isReview ? remark_str : '',
        resource_result: {
          reject_reason: reason
        },
        binds: this.oper.bind_id_list,
        extra_data: {
          reject_reason: archiveReason,
          reject_reason_id: archiveReasonId,
          state: status,
          note_tag,
          flag_copyright: +copyright !== +this.archive.copyright, // 稿件类型是否修改
          is_state_change: +status !== +this.archive.state, // 稿件状态是否修改
          has_gray_tag_change: hasGrayTagChange, // 灰标是否被修改
          auditversion: this.archive?.auditversion,
          todo_id: parseTodoIdFromSearchParams()
        }
      }
      const banKeys = [
        'norank',
        'noindex',
        'norecommend',
        'nohot',
        'hot_down',
        'rank_down',
        'nosearch',
        'push_blog'
      ]
      // 禁止项
      const forbids = {
        norank,
        noindex,
        norecommend,
        nohot,
        hot_down,
        rank_down,
        nosearch,
        push_blog
      }
      let flag_flow = 0

      if (status === '-4') { // 锁定
        data.extra_data.forward = forward
      }

      if (this.form.status === '0') { // 开放浏览
        delete data.extra_data.reject_reason
        delete data.extra_data.reject_reason_id
      }

      if (!this.isReview) {
        delete data.remark
        delete data.extra_data.note_tag
        delete data.extra_data.has_gray_tag_change
        delete data.extra_data.flag_copyright
        delete data.extra_data.copyright
        delete data.extra_data.xcode2
      } else {
        data.resource_result = {}

        if (banKeys.some(key => +forbids[key] !== +this.attrForm[key])) {
          flag_flow = 1
        }
        Object.keys(forbids).forEach(key => {
          forbids[key] = String(forbids[key])
        })

        // 禁止项相关
        if (flag_flow === 1) { // 禁止项改动
          data.extra_data = {
            ...data.extra_data,
            ...forbids,
            flag_flow
          }
          if (data.remark === '') {
            $notify.error('请填写备注！')
            return
          }
        }

        // 稿件类型相关
        if (copyright > 0) {
          data.extra_data = {
            ...data.extra_data,
            copyright,
            flag_copyright: copyright !== +this.archive.copyright
          }
          if (data.extra_data.flag_copyright && copyright === 2) {
            data.extra_data.xcode2 = xcode2 ? 1 : 0
          }
        }

        // 灰标相关
        if (data.extra_data.has_gray_tag_change) {
          data.extra_data.gray_tag = grayTags || ''
        }
        // 回查传入灰标理由&备注标理由
        data.extra_data.gray_tag_reason = JSON.stringify(this.grayTagReason)
        data.extra_data.note_tag_reason = JSON.stringify(this.noteTagReason)
      }

      data.extra_data.operation_tags = genOperationTags({
        archiveState: status,
        reasonTagId: tag_id,
        noteTag: this.noteTags?.map(e => e.tag_id),
        grayTag: this.grayTags?.map(e => e[e.length - 1])
      })

      return data
    },
    submit() {
     const params = this.getSubmitParams()
     if (params !== undefined) {
      this.$emit('submit', params, this.oper, {
        formOperation: this.form,
        snapshotNoteTags: this.noteTags,
        snapshotGrayTags: this.grayTags
      })
     }
    },
    handleAddNote(note) {
      const { value, label, remark, pre_text: preText } = note
      const existNoteTag = this.noteTags.find(
        (tag) => tag.tag_id === value && tag.pre_text === preText
      )
      if (existNoteTag && existNoteTag.canDelete) {
        $notify.warning('已经存在该备注tag，只修改了备注')
        existNoteTag.remark = remark
        existNoteTag.tag_name = `${preText}【${remark}】`
      } else if (existNoteTag) {
        $notify.warning('已经存在该备注tag，且无权修改')
      } else {
        this.noteTags.unshift({
          tag_id: value,
          tag_name: label,
          remark,
          pre_text: preText,
          hitState: false,
          first_init: false,
          canDelete: true
        })
      }
    },
    handleDeleteNote(note, idx) {
      this.noteTags.splice(idx, 1)
    },
    handleUpdateReasonId(newReasonId, newTagId) {
      this.form.archive_reason_id = newReasonId
      this.form.tag_id = newTagId
    },
    async getNoteOptions() {
      try {
        const res = await archiveApi.getTagNote()
        const { noteOptions } = washNoteTag({
          note_tag: res.data
        })
        this.noteOptions = noteOptions
      } catch {}
    },
    async fetchReason() {
      try {
        const res = await archiveApi.getReasonTags({
          list_type: '00',
          state: this.form.status
        })
        const { reason_map = {}, reason_tags = [] } = res.data || {}
        this.reasonTags = reason_tags || []
        this.reasonMap = reason_map
      } catch (err) {
        console.error(err)
        this.reasonTags = []
        this.reasonMap = {}
      }
    },
    handleGrayTagChange() {
      this.$nextTick(() => {
        const selectedNodes = this.$refs.grayTagRef?.getCheckedNodes(true)
        this.selectedGrayTagNodes = selectedNodes
      })
    },
    onConsistencyError(msg) {
      this.$alert(msg, '请检查【备注标】和【灰标】限流理由')
    },
    validateLimitReason() {
      let msg = ''
      let msgA = ''
      let msgB = ''
      if (this.form.status === '-2' || this.form.status === '-4') {
        msg = this.$refs.auditReasonStruct.validateReason()
        msgA = this.$refs.noteTagReasonRef?.validateReason() || ''
        msgB = this.$refs.grayTagReasonRef?.validateReason() || ''
      } else {
        msgA = this.$refs.noteTagReasonRef?.validateReason() || ''
        msgB = this.$refs.grayTagReasonRef?.validateReason() || ''
      }
      const fullMsg = `${msg ? '【打锁理由】' : ''}${msg}${
        msgA ? '【备注标限流理由】' : ''
      }${msgA}${msgB ? '【灰标限流理由】' : ''}${msgB}`

      if (!fullMsg) {
        validateAllLimitReasons(this.noteTagReason, this.grayTagReason, this.onConsistencyError)
          // 跨标签业务理由一致性校验 并用新理由覆盖旧理由
        const [noteTagReason, grayTagReason] = overrideAllLimitReasons(
          this.noteTagReason,
          this.grayTagReason
        )
        this.noteTagReason = noteTagReason
        this.grayTagReason = grayTagReason
        }
      return fullMsg
    }
  },
  watch: {
    async 'form.status'(newVal) {
      // "开放浏览"不需要理由
      if (newVal !== '0') {
        await this.fetchReason()
      }
    }
  },
  mounted() {
    this.getNoteOptions()
    this.fetchGrayTags()
  }
}
</script>
<style lang="stylus" scoped>
.workbench-effect-dialog
  .form-reason
    >>>.el-input__inner
      padding-right 60px
</style>
