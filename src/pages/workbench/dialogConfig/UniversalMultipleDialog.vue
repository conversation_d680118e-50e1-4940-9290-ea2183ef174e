<template>
  <div class="workbench-universal-dialog">
    <el-dialog
      :title="oper.name"
      :visible.sync="dialogVisible"
      :close-on-click-modal="true"
      :append-to-body="true"
      width="80%"
      @closed="onClosed"
    >
      <el-form
        ref="universal"
        label-width="100px"
        @submit.stop.prevent.native
        :rules="formRules"
        :model="form"
      >
        <el-form-item v-if="prompt">
          <p>{{ prompt }}</p>
        </el-form-item>
        <component
          v-if="hasConfigFormCmp"
          ref="configFormRef"
          :is="getConfigFormCmp()"
          :reasonTags="allReasonTags"
        />
        <template v-else>
          <el-form-item label="标签" prop="tagSelection">
            <el-cascader
              ref="tagCascader"
              style="width: 100%"
              clearable
              filterable
              v-model="form.tagSelection"
              popper-class="hide-non-leaf-node-checkbox"
              :options="allReasonTags"
              :props="{
                label: 'name',
                value: 'id',
                children: 'options',
                expandTrigger: 'hover',
                multiple: true
              }"
              @change="onTagChange"
            />
          </el-form-item>
          <el-form-item label="理由预览">
            <p v-for="reason in reasonList" :key="reason">
              {{ reason }}
            </p>
          </el-form-item>
        </template>
        <el-form-item label="理由" prop="reason" v-if="showCustomReason">
          <el-input v-model="form.reason" />
        </el-form-item>
        <el-form-item label="备注" prop="note" v-if="showNotifyUp">
          <el-input v-model="form.note" />
        </el-form-item>
        <el-form-item label="是否通知用户" prop="notify" v-if="showNote">
          <el-radio-group v-model="form.notify">
            <el-radio :label="1">通知</el-radio>
            <el-radio :label="0">不通知</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button @click="close" size="medium">取 消</el-button>
        <el-button type="primary" size="medium" @click="beforeCommit">
          确 定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { tagApi, workbenchApi } from '@/api/index'
import { getEnvConstant } from '@/utils/constant.js'
import UserProfileForm from '@/pages/workbench/dialogConfig/dialogForms/UserProfileForm'
import evalExp from '@/v2/utils/evalExp.js'
import { set } from 'lodash-es'
import { REJECT_TYPE_TO_TEXT } from '@/pages/workbench/dialogConfig/dialogForms/constant'

export default {
  components: {
    UserProfileForm
  },
  props: {
    bizConfig: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      dialogVisible: false,
      allReasonTags: [],
      reasonList: [],
      tagNameList: [],
      formTemplate: {
        tagSelection: '',
        reason: '',
        note: '',
        notify: 1
      },
      form: {},
      formRules: {
        tagSelection: [
          { required: true, message: '理由必填', trigger: 'blur' },
          { required: true, message: '理由必填', trigger: 'change' }
        ]
      },
      row: null,
      oper: {},
      tagBusinessId: -1,
      BIZ_ID_FORM_MAP: {
        156: 'UserProfileForm'
      }
    }
  },
  computed: {
    ...mapState('todoDetail', ['detail']),
    ...mapState({
      perms: state => state.user.perms,
      uid: (state) => state.user.uid
    }),
    businessId() {
      return this.$route.query.business_id
    },
    prompt() {
      const operChName = this.oper?.name || '操作'
      const promptTemplate = this.oper.promptTemplate
      if (promptTemplate) {
        return evalExp('string', promptTemplate, this.row)
      }
      return this.oper?.action_params?.prompt ?? `此操作将${operChName}当前选中的审核内容，请确认是否继续`
    },
    showCustomReason() {
      return !!this.oper?.action_params?.need_custom_reason
    },
    showNotifyUp() {
      return !!this.oper?.action_params?.need_notify_up
    },
    showNote() {
      return !!this.oper?.action_params?.need_note
    },
    hasConfigFormCmp() {
      return Object.keys(this.BIZ_ID_FORM_MAP).includes(`${this.businessId}`)
    }
  },
  watch: {
    dialogVisible: {
      handler(val) {
        if (!val) {
          this.$emit('changeVisibleState')
        }
      }
    }
  },
  methods: {
    resetFields() {
      if (this.$refs?.universal?.resetFields && typeof this.$refs?.universal?.resetFields === 'function') {
        this.$refs.universal.resetFields()
      }
      this.reasonList = []
    },
    openDialog(oper, row = null) {
      this.dialogVisible = true
      this.oper = oper
      const schemaTagBizId = this.oper?.action_params?.tag_business_id
      this.tagBusinessId = schemaTagBizId || getEnvConstant('LIVE_AUDIT_BIZ_TAG_ID_MAP')[this.businessId]
      this.form = { ...this.formTemplate }
      this.getReasonTag()
      this.row = row
      this.$nextTick(this.resetFields)
    },
    async getReasonTag() {
      try {
        let allTags = []
        if (this.tagBusinessId) {
          const res = await tagApi.getReasonTagTree({
            tag_business: this.tagBusinessId,
            uid: this.uid
          })
          allTags = res.data?.tags
        } else {
          const res = await workbenchApi.getReasonTags({
            business_id: this.businessId
          })
          allTags = res.data?.options
        }
        const operName = this.oper.name
        const singularOperName = operName.includes('批量') ? operName.split('批量').pop() : operName
        const operTag = allTags.find(e => e.name === singularOperName)
        this.allReasonTags = operTag?.options || []
      } catch (err) {
        console.error(err)
      }
    },
    onTagChange() {
      this.$nextTick(() => {
        const selectedNodes = this.$refs?.tagCascader?.getCheckedNodes() || []
        this.reasonList = selectedNodes.map(e => e?.data?.extra_data?.reason).filter(e => !!e)
        this.tagNameList = selectedNodes.map(e => e.pathLabels.join('/'))
      })
    },
    getConfigFormCmp() {
      return this.BIZ_ID_FORM_MAP[this.businessId]
    },
    async beforeCommit() {
      const {
        reason,
        note,
        notify
      } = this.form

      let cmpSubmitPayload = null

      if (this.hasConfigFormCmp) {
        const validateRes = await this.$refs.configFormRef?.validate()
        if (validateRes !== true) return
        cmpSubmitPayload = this.$refs.configFormRef?.submit()
      }
      this.$refs.universal.validate(async (valid) => {
        if (!valid) {
          return false
        }
        const extra = {}
        if (this.showNotifyUp) extra.notify = notify
        extra.reason = this.reasonList.join('\n')

        const resourceResult = {
          reason_id: 0, // FIXME: 疑似是必传字段
          note: this.reasonList.join(';'), // 记录工作台日志
          full_tag_ids: [],
          full_tag_names: []
        }
        if (this.showCustomReason) resourceResult.reject_reason = reason
        if (this.showNote) resourceResult.note = note
        if (this.tagNameList?.length) resourceResult.tag_names = this.tagNameList

        const payload = {
          resource_result: resourceResult,
          extra_data: extra
        }

        if (cmpSubmitPayload && Array.isArray(cmpSubmitPayload)) {
          try {
            const data = cmpSubmitPayload[0]
            const value = JSON.parse(data.value) || []

            this.allReasonTags.forEach(v => {
              v?.options?.forEach(c => {
                 // 头像/昵称/签名公用一套理由所以添加了条件REJECT_TYPE_TO_TEXT[t.reject_type] === v.name
                const target = value.find(t => REJECT_TYPE_TO_TEXT[t.reject_type] === v.name && t.reject_reason === c.name)

                if (target) {
                  payload.resource_result.full_tag_ids.push([v.id, c.id])
                  payload.resource_result.full_tag_names.push([v.name, c.name])
                }
              })
            })
          } catch (e) {
            console.error(e)
          }

          cmpSubmitPayload.forEach((e) => {
            set(payload, e.fieldPath, e.value)
          })
        }
        this.$emit('submit', payload, this.oper, this.row)
      })
    },
    close() {
      this.dialogVisible = false
    },
    onClosed() {
      if (this.hasConfigFormCmp) this.$refs.configFormRef?.resetFields()
    }
  }
}
</script>

<style lang="stylus">
.hide-non-leaf-node-checkbox .el-cascader-node[aria-haspopup="true"] .el-checkbox
  visibility hidden
</style>
