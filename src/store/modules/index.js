import arctype from './arctype'
import chat from './chat.js'
import common from './common'
import config from './config'
import dataSource from './dataSource'
import dynamicComponents from './dynamicComponents'
import dynamicPic from './dynamicPic'
import emote from './emote'
import env from './env'
import graph from './graph'
import grayTag from './grayTag'
import library from './library.js'
import liveAudit from './liveAudit.js'
import menu from './menu'
import noticeMsg from './notice-msg.js'
import order from './order'
import qa from './qa'
import pictures from './pictures'
import reason from './reason'
import tabulation from './tabulation'
import tags from './tags'
import time from './time'
import todoDetail from './todoDetail'
import todoList from './todoList'
import user from './user'
import proxyQa from './proxyQa'
import report from './report'
import business from './business'

export default {
  arctype,
  chat,
  common,
  config,
  dataSource,
  dynamicComponents,
  dynamicPic,
  emote,
  env,
  graph,
  grayTag,
  library,
  liveAudit,
  menu,
  noticeMsg,
  order,
  qa,
  pictures,
  reason,
  tabulation,
  tags,
  time,
  todoDetail,
  todoList,
  user,
  proxyQa,
  report,
  business
}
