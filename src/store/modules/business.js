import { GET_ALL_OFFICIAL_BRAND } from '../types'
import { businessOrderApi } from '@/v2/api'

const state = {
  allBrands: []
}
const actions = {
  async getAllOfficialBrand({ commit, state }) {
    if (state.allBrands?.length) {
        return
    }
    await businessOrderApi.getBrands().then((res) => {
      const data = (res.data || []).map((item) => {
        return {
          id: item.game_base_id,
          name: item.game_name
        }
      })
        commit(GET_ALL_OFFICIAL_BRAND, data)
    })
  }
}
const mutations = {
  [GET_ALL_OFFICIAL_BRAND](state, payload) {
    state.allBrands = payload
  }
}
export default {
  namespaced: true,
  state,
  actions,
  mutations
}
