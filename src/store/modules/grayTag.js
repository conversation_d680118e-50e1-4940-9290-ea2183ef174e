/**
 * GrayTag Store Module
 *
 * 功能说明：
 * 1. 管理灰标选项和叶子节点
 * 2. 提供 getArchiveLimitReasons API 的缓存机制
 * 3. 提供 getLimitTagsAndReasons API 的缓存机制
 *
 * 缓存机制：
 * 1. getArchiveLimitReasons:
 *    - 缓存键：aid (Archive ID)
 *    - 缓存结构：{ aid: 响应数据 }
 *    - 缓存失效：当请求新的aid时，会清空所有已缓存的数据
 *
 * 2. getLimitTagsAndReasons:
 *    - 缓存键：business_id (1=灰标, 6=备注标)
 *    - 缓存结构：{ business_id: 响应数据 }
 *    - 缓存失效：仅在页面刷新/关闭时失效
 *
 * 使用方法：
 * // 获取Archive限流理由（会自动使用缓存）
 * this.$store.dispatch('grayTag/fetchArchiveLimitReasons', { aid: 123 })
 *
 * // 获取标签和理由映射（会自动使用缓存）
 * this.$store.dispatch('grayTag/fetchLimitTagsAndReasons', { business_id: 1 })
 */

import { SET_GRAY_TAG_OPTIONS, SET_GRAY_TAG_LEAF_NODES, SET_ARCHIVE_LIMIT_REASONS_CACHE, CLEAR_ARCHIVE_LIMIT_REASONS_CACHE, SET_LIMIT_TAGS_AND_REASONS_CACHE } from '../types'
import { flatNodes } from '@/v2/biz-utils/classifyTags'
import { tagApi } from '@/api'

const state = {
  grayTagOps: [],
  leafNodes: [],
  archiveLimitReasonsCache: {}, // 缓存结构: { aid: 响应数据 }
  limitTagsAndReasonsCache: {} // 缓存结构: { business_id: 响应数据 }
}

const actions = {
  fetchGrayTags: async ({ state, commit }) => {
    const curGrayTagOps = state.grayTagOps
    const curLeafNodes = state.leafNodes
    if (curGrayTagOps.length) {
      return {
        grayTagOps: curGrayTagOps,
        leafNodes: curLeafNodes
      }
    }
    try {
      const res = await tagApi.getGrayTagList({ tag_business: 1 })
      const grayTagOps = (res.data?.tags || [])
      const leafNodes = flatNodes(grayTagOps, true)
      commit(SET_GRAY_TAG_OPTIONS, grayTagOps)
      commit(SET_GRAY_TAG_LEAF_NODES, leafNodes)
      return {
        grayTagOps,
        leafNodes
      }
    } catch (e) {
      console.error(e)
    }
  },

  fetchArchiveLimitReasons: async ({ state, commit }, { aid }) => {
    // 检查缓存
    const cached = state.archiveLimitReasonsCache[aid]

    if (cached) return cached

    // 如果请求的是新的aid，清空所有缓存
    const cachedAids = Object.keys(state.archiveLimitReasonsCache)
    if (cachedAids.length > 0 && !cachedAids.includes(String(aid))) {
      commit(CLEAR_ARCHIVE_LIMIT_REASONS_CACHE)
    }

    try {
      const res = await tagApi.getArchiveLimitReasons({ aid })

      commit(SET_ARCHIVE_LIMIT_REASONS_CACHE, { aid, data: res })

      return res
    } catch (e) {
      console.error('Failed to fetch archive limit reasons:', e)
      throw e
    }
  },

  fetchLimitTagsAndReasons: async ({ state, commit }, { business_id }) => {
    // 检查缓存
    const cached = state.limitTagsAndReasonsCache[business_id]

    if (cached) return cached

    try {
      const res = await tagApi.getLimitTagsAndReasons({ business_id })

      commit(SET_LIMIT_TAGS_AND_REASONS_CACHE, { business_id, data: res })

      return res
    } catch (e) {
      console.error('Failed to fetch limit tags and reasons:', e)
      throw e
    }
  }
}

const mutations = {
  [SET_GRAY_TAG_OPTIONS](state, grayTagOps) {
    state.grayTagOps = grayTagOps
  },
  [SET_GRAY_TAG_LEAF_NODES](state, leafNodes) {
    state.leafNodes = leafNodes
  },
  [SET_ARCHIVE_LIMIT_REASONS_CACHE](state, { aid, data }) {
    state.archiveLimitReasonsCache = {
      ...state.archiveLimitReasonsCache,
      [aid]: data
    }
  },
  [CLEAR_ARCHIVE_LIMIT_REASONS_CACHE](state, aid) {
    state.archiveLimitReasonsCache = {}
  },
  [SET_LIMIT_TAGS_AND_REASONS_CACHE](state, { business_id, data }) {
    state.limitTagsAndReasonsCache = {
      ...state.limitTagsAndReasonsCache,
      [business_id]: data
    }
  }
}

export default {
  namespaced: true,
  getters: {},
  state,
  actions,
  mutations
}
