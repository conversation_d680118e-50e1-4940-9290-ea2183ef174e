import {
  GEN_EP_SLICES_OPTIONS,
  RESET_EP_SLICES,
  REMOVE_NTH_SLICE_FROM_EP,
  SET_EP_SLICES,
  SET_BATCH_EP_SLICES
} from '../types'

const state = {
  slices: {}, // { [pIndex]: { text: string, note: string, snapshotUrl: string, sTime: number, eTime: number } }
  sliceOptions: [],
  snapshotOptions: [],
  snapshotSize: 0
}

// 测试数据
// const state = {
//   slices: {'1': {'pIndex': 1, 'slices': [{'uuid': '86544070-752b-428c-88ff-c7ae0ca0f427', 'pIndex': 1, 'sTime': 23, 'text': '00:00:23', 'note': '违规用语', 'snapshotUrl': 'https://uat-i0.hdslb.com/bfs/reason_picture/d460f0dd87b68f111a14d8ddef529b089abc0a34.png'}, {'uuid': '55f4eafc-6336-4887-9c94-5c4ab7d5119e', 'pIndex': 1, 'sTime': 1, 'eTime': 24, 'text': '00:00:01-00:00:24', 'note': '涉嫌违规片段'}, {'uuid': 'ec4a0e74-429b-4b9c-bd0e-4f4c60c37f19', 'pIndex': 1, 'sTime': 12, 'text': '00:00:12', 'note': '私人聊天记录', 'snapshotUrl': 'https://uat-i0.hdslb.com/bfs/reason_picture/5c6fc1ca362650e8d91c26cf44ef9b9562995804.png'}, {'uuid': 'fa90d19d-6c5d-4806-af7a-479d6146826e', 'pIndex': 1, 'sTime': 70, 'text': '00:01:10', 'note': '涉色情', 'snapshotUrl': 'https://uat-i0.hdslb.com/bfs/reason_picture/2fb2373d06355a062fe9b85288918a3a628de8a7.png'}]}, '2': {'pIndex': 2, 'slices': [{'uuid': '12841baf-4662-45b5-a64f-fdf6203c8953', 'pIndex': 2, 'sTime': 0, 'eTime': 9, 'text': '00:00:00-00:00:09', 'note': '端水动作'}, {'uuid': 'ba965d5c-ce0c-4296-aec2-c94dc8884892', 'pIndex': 2, 'sTime': 3, 'text': '00:00:03', 'note': '倒茶动作', 'snapshotUrl': 'https://uat-i0.hdslb.com/bfs/reason_picture/c74107531e8bbfd369768025c085a15e1cad1466.png'}, {'uuid': 'e6e23007-d652-4d45-85cd-d92eb399aa83', 'pIndex': 2, 'sTime': 4, 'text': '00:00:04', 'note': '黑帮动作', 'snapshotUrl': 'https://uat-i0.hdslb.com/bfs/reason_picture/2a4cca682c7b929dd982f755730ea82bbc098a58.png'}, {'uuid': '1977adce-56e0-42ef-871e-41a500c9eeb7', 'pIndex': 2, 'sTime': 16, 'text': '00:00:16', 'note': '高温危险', 'snapshotUrl': 'https://uat-i0.hdslb.com/bfs/reason_picture/2ed8865c9fc8d5e237f9dae35ebae70cab88ddb6.png'}]}},
//   sliceOptions: [{'uuid': '86544070-752b-428c-88ff-c7ae0ca0f427', 'pIndex': 1, 'sTime': 23, 'text': '00:00:23', 'note': '违规用语', 'snapshotUrl': 'https://uat-i0.hdslb.com/bfs/reason_picture/d460f0dd87b68f111a14d8ddef529b089abc0a34.png'}, {'uuid': '55f4eafc-6336-4887-9c94-5c4ab7d5119e', 'pIndex': 1, 'sTime': 1, 'eTime': 24, 'text': '00:00:01-00:00:24', 'note': '涉嫌违规片段'}, {'uuid': 'ec4a0e74-429b-4b9c-bd0e-4f4c60c37f19', 'pIndex': 1, 'sTime': 12, 'text': '00:00:12', 'note': '私人聊天记录', 'snapshotUrl': 'https://uat-i0.hdslb.com/bfs/reason_picture/5c6fc1ca362650e8d91c26cf44ef9b9562995804.png'}, {'uuid': 'fa90d19d-6c5d-4806-af7a-479d6146826e', 'pIndex': 1, 'sTime': 70, 'text': '00:01:10', 'note': '涉色情', 'snapshotUrl': 'https://uat-i0.hdslb.com/bfs/reason_picture/2fb2373d06355a062fe9b85288918a3a628de8a7.png'}, {'uuid': '12841baf-4662-45b5-a64f-fdf6203c8953', 'pIndex': 2, 'sTime': 0, 'eTime': 9, 'text': '00:00:00-00:00:09', 'note': '端水动作'}, {'uuid': 'ba965d5c-ce0c-4296-aec2-c94dc8884892', 'pIndex': 2, 'sTime': 3, 'text': '00:00:03', 'note': '倒茶动作', 'snapshotUrl': 'https://uat-i0.hdslb.com/bfs/reason_picture/c74107531e8bbfd369768025c085a15e1cad1466.png'}, {'uuid': 'e6e23007-d652-4d45-85cd-d92eb399aa83', 'pIndex': 2, 'sTime': 4, 'text': '00:00:04', 'note': '黑帮动作', 'snapshotUrl': 'https://uat-i0.hdslb.com/bfs/reason_picture/2a4cca682c7b929dd982f755730ea82bbc098a58.png'}, {'uuid': '1977adce-56e0-42ef-871e-41a500c9eeb7', 'pIndex': 2, 'sTime': 16, 'text': '00:00:16', 'note': '高温危险', 'snapshotUrl': 'https://uat-i0.hdslb.com/bfs/reason_picture/2ed8865c9fc8d5e237f9dae35ebae70cab88ddb6.png'}],
//   snapshotOptions: [{'uuid': '86544070-752b-428c-88ff-c7ae0ca0f427', 'pIndex': 1, 'sTime': 23, 'text': '00:00:23', 'note': '违规用语', 'snapshotUrl': 'https://uat-i0.hdslb.com/bfs/reason_picture/d460f0dd87b68f111a14d8ddef529b089abc0a34.png'}, {'uuid': 'ec4a0e74-429b-4b9c-bd0e-4f4c60c37f19', 'pIndex': 1, 'sTime': 12, 'text': '00:00:12', 'note': '私人聊天记录', 'snapshotUrl': 'https://uat-i0.hdslb.com/bfs/reason_picture/5c6fc1ca362650e8d91c26cf44ef9b9562995804.png'}, {'uuid': 'fa90d19d-6c5d-4806-af7a-479d6146826e', 'pIndex': 1, 'sTime': 70, 'text': '00:01:10', 'note': '涉色情', 'snapshotUrl': 'https://uat-i0.hdslb.com/bfs/reason_picture/2fb2373d06355a062fe9b85288918a3a628de8a7.png'}, {'uuid': 'ba965d5c-ce0c-4296-aec2-c94dc8884892', 'pIndex': 2, 'sTime': 3, 'text': '00:00:03', 'note': '倒茶动作', 'snapshotUrl': 'https://uat-i0.hdslb.com/bfs/reason_picture/c74107531e8bbfd369768025c085a15e1cad1466.png'}, {'uuid': 'e6e23007-d652-4d45-85cd-d92eb399aa83', 'pIndex': 2, 'sTime': 4, 'text': '00:00:04', 'note': '黑帮动作', 'snapshotUrl': 'https://uat-i0.hdslb.com/bfs/reason_picture/2a4cca682c7b929dd982f755730ea82bbc098a58.png'}, {'uuid': '1977adce-56e0-42ef-871e-41a500c9eeb7', 'pIndex': 2, 'sTime': 16, 'text': '00:00:16', 'note': '高温危险', 'snapshotUrl': 'https://uat-i0.hdslb.com/bfs/reason_picture/2ed8865c9fc8d5e237f9dae35ebae70cab88ddb6.png'}],
//   snapshotSize: 6
// }

const actions = {
  setEpSlices: ({ commit }, payload) => {
    commit(SET_EP_SLICES, payload)
    commit(GEN_EP_SLICES_OPTIONS)
  },
  setBatchEpSlices({ commit }, sliceMap) {
    commit(SET_BATCH_EP_SLICES, sliceMap)
    commit(GEN_EP_SLICES_OPTIONS)
  },
  removeNthSliceFromEp: ({ commit }, payload) => {
    commit(REMOVE_NTH_SLICE_FROM_EP, payload)
    commit(GEN_EP_SLICES_OPTIONS)
  },
  resetEpSlices: ({ commit }) => {
    commit(RESET_EP_SLICES, {})
    commit(GEN_EP_SLICES_OPTIONS)
  }
}

const mutations = {
  [SET_EP_SLICES](state, payload) {
    if (!payload.pIndex) return
    state.slices[payload.pIndex] = payload
  },
  [SET_BATCH_EP_SLICES](state, sliceMap) {
    Object.keys(sliceMap).forEach(pIndex => {
      if (pIndex && sliceMap[pIndex]) {
        state.slices[pIndex] = {
          pIndex,
          slices: sliceMap[pIndex]
        }
      }
    })
  },
  [REMOVE_NTH_SLICE_FROM_EP](state, payload) {
    const { pIndex, sliceIndex } = payload
    const oldVal = state.slices?.[pIndex]?.slices || []
    const newVal = oldVal.filter((_, i) => i !== sliceIndex)
    if (state.slices[pIndex]) state.slices[pIndex].slices = newVal
  },
  [RESET_EP_SLICES](state, payload) {
    state.slices = payload
  },
  [GEN_EP_SLICES_OPTIONS](state) {
    state.sliceOptions = Object.values(state.slices).map(ep => ep.slices).flat()
    state.snapshotOptions = state.sliceOptions.filter(slice => !!slice.snapshotUrl)
    state.snapshotSize = state.snapshotOptions.length
  }
}

const getters = {
  getReasonTimeSlices: () => {
    return Object.values(state.slices).map(ep => ep.slices).flat()
  }
}

export default {
  namespaced: true,
  state,
  actions,
  mutations,
  getters
}
