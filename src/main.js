import 'element-ui/lib/theme-chalk/index.css'
import '@bilibili/b-style/index.css'
import '@bilibili/b-style/dist/theme.css'

import Vue from 'vue'
import VueCompositionAPI from '@vue/composition-api'

import App from './App'
import DynamicCardIframe from '@/components/DynamicCardIframe.vue'

import ElementUI from 'element-ui'
import store from '@/store'
import { formatPerms, getUAPlatform } from '@/utils'
import ajaxPlugin from '@/plugins/ajax'
import authPlugin from '@/directives/auth.js'
import biliMirror, { pbReportPv } from '@bilibili/bili-mirror'
import './styl/index.styl'
import Router from 'vue-router'
import { genRouter } from '@/router/index.js'
import BfsImage from './plugins/bfsImage'
import 'viewerjs/dist/viewer.css'
import Viewer from 'v-viewer'
import { watermark, trackUtil } from '@/utils/index'
import AtPopup from '@bilibili/at'
import '@bilibili/at/dist/at-popup.css'
import tracker from '@/plugins/tracker'
import highlightConfigInjector from '@/plugins/highlight-config-injector'
import { clearRead } from '@/utils/storage'
import { templateApi } from './api'
import { sync } from 'vuex-router-sync'
import Breadcrumb from '@/v2/pure-components/Breadcrumb'
import microApp from '@micro-zoe/micro-app'
import { getUrl } from '@/pages/v3/config'
import { migrateHttps } from '@/config/migrate-https'
import { hideFeedBack } from '@/config/hide-feedback'
import VueSocketIO from 'vue-socket.io'
import { install as NoticeDialog } from '@/global-components/NoticeDialog'
import { REST_AUDIT_STATE } from '@/utils/work-time-constant.js'
import { PageStutterReporter } from '@bilibili/awesome-tech-report'
import { getCookie, setCookie } from './api/utils'
import trackerFunc from '@/utils/report'
import SVGIconNext from '@bilibili/svg-icon-next-vue2'
import { getFfHost } from '@/v2/utils'
import { processHtml } from '@bilibili/csr-gray'
import { register as biliRegister } from '@bilibili/bmg-tracker/dist/register'

Vue.use(SVGIconNext)
/* eslint-enable */
Vue.config.productionTip = false
Vue.use(Router)
Vue.use(ElementUI)
Vue.use(ajaxPlugin)
Vue.use(BfsImage)
Vue.use(Viewer)

Vue.use(authPlugin)
Vue.use(AtPopup)
Vue.use(tracker) // 埋点指令
Vue.use(highlightConfigInjector)
Vue.use(VueCompositionAPI)

Vue.use(
  new VueSocketIO({
    debug: process.env.NODE_ENV !== 'production',
    connection: '//light-socket.bilibili.co/client',
    options: {
      path: '/socket',
      query: { roomId: 10020 },
      autoConnect: false,
      reconnectionAttempts: 3, // 设置重连尝试次数为3次
      reconnectionDelay: 5000,
      reconnectionDelayMax: 10000
    }
  })
)

Vue.component('icon', window.VueAwesome)
Vue.component('Breadcrumb', Breadcrumb)

Vue.prototype.$EventBus = new Vue()

Vue.use(NoticeDialog, Vue)

// 引入svg图片，生成symbol
const requireAll = (requireContext) => requireContext.keys().map(requireContext)
const req = require.context('@/assets/icons', false, /\.svg$/)
requireAll(req)

// 设置env环境
const host = window.location.host
const port = window.location.port || '80'
store.dispatch('env/setEnv', {
  host,
  proxyhost: process.env.VUE_APP_PROXYHOST || host,
  port
})

if (host === 'manager.bilibili.co' || host === 'aegis-backup.bilibili.co') {
  Vue.use(biliMirror, {
    origin: 'main',
    module: 'aegis',
    config: {
      whiteScreen: {
        elemArry: ['html', 'body', '.audit-main'],
        isSkeleton: false,
        checkNum: 3,
        maxLoop: 10
      }
    }
  })
}

store.subscribeAction({
  error: (action, state, error) => {
    trackerFunc('store-error', error)
  }
})

// 设置错误堆栈最大长度
Error.stackTraceLimit = 30

// eslint-disable-next-line no-new
new PageStutterReporter('333.836', { user: getCookie('username') })

const dejavuVersionId = window._GreyResult?.versionId ?? ''

if (window?.location?.hash?.includes('#/dynamic-card')) {
  new Vue({
    render: (h) => h(DynamicCardIframe),
    components: { DynamicCardIframe }
  }).$mount('#app')
} else {
  // 查询用户信息
  Vue.ajax
    .get(`${getFfHost()}/x/admin/aegis/engine/auth`, {
      handle: true
    })
    .then(async (res) => {
      const data = res.data || {}
      if (res.code === -401) {
        window.localStorage.setItem('aegis-auth', 'OUT')
        window.location.href =
          'http://dashboard-mng.bilibili.co/loginPage?caller=mng-aegis'
        return
      }

      if (res.code !== 0) {
        ElementUI.Notification({
          message: res.message,
          type: 'error',
          duration: 1500,
          showClose: true,
          dangerouslyUseHTMLString: true,
          customClass: 'el-message',
          offset: 5
        })
      }

      window.localStorage.setItem('aegis-auth', 'IN')

      const { uid, username, permissions, admin, nickname } = data

      let dynamicRouteRes, abtRes

      const tianshuElement = document.querySelector(
        '#tianshu_feedback_container'
      )
      if (tianshuElement) tianshuElement.style.position = 'relative'

      try {
        ;[dynamicRouteRes, abtRes] = await Promise.all([
          Vue.ajax.get('/template/audit/all/dynamic-route').catch((_) => {}),
          templateApi.getABT({ uid }).catch((_) => {})
        ])
      } catch (error) {
        console.error(error)
      }

      let dynamicRoutes = dynamicRouteRes?.data || []

      dynamicRoutes.forEach((route) => {
        if (route.path === '/audit/tasks/detail/11') {
          route.redirect = '/v2/archive/detail'
          route.component = () =>
            import('@/v2/biz-pages/archive/ArchiveDetail.vue')
          route.name = '稿件详情页'
        } else {
          route.component = () =>
            import('@/components/TaskDetail/TaskDetail.vue')
        }
      })
      dynamicRoutes = [
        {
          path: '*',
          redirect: '/'
        },
        ...dynamicRoutes
      ]

      const perms = formatPerms(permissions)

      store.dispatch('user/saveUserInfo', {
        perms,
        username,
        uid,
        admin,
        nickname,
        workState: REST_AUDIT_STATE
      })
      window.localStorage.setItem('workStatus', REST_AUDIT_STATE) // 本地存储工作状态
      const abts = abtRes?.data?.abts

      if (abts) {
        store.dispatch('user/setUserAbt', abts)
      }

      // 生成权限路由
      const router = new Router({
        routes: genRouter()
      })
      router.addRoutes(dynamicRoutes)

      router.beforeEach(async (to, from, next) => {
        // 无权限则重定向到首页
        if (!admin) {
          // 超级管理员，不走权限
          if (to.meta?.authMap && to.meta?.authKey) {
            const authMap = to.meta.authMap
            const key = to.meta.authKey
            const auth = authMap[to.params[key]]
            if (auth && !perms[auth]) {
              // 页面有权限点限制，且该用户不具备该权限点
              next('/403')
              return
            }
          } else if (to.meta?.auth) {
            // 数组或字符串
            if (
              (typeof to.meta.auth === 'string' && !perms[to.meta.auth]) ||
              (typeof to.meta.auth !== 'string' &&
                to.meta.auth.every((e) => !perms[e]))
            ) {
              // 页面有权限点限制，且该用户不具备该权限点
              next('/403')
              return
            }
          }
        }

        const ua = getUAPlatform()
        // 不是chrome（或者是edge），不是pc，或者屏幕宽度小于600，则重定向到首页
        if (
          (to.path.indexOf('/audit-tools/library') > -1 ||
            to.path === '/v3/order') &&
          (!ua.isPc || !ua.isChrome || ua.isEdge || window.screen.width < 600)
        ) {
          next('/')
          return
        }

        if (to.path !== from.path) {
          trackUtil(from.path, to.path)
        }

        if (!+to.name && to.name !== 'netease' && to.name !== 'operation-log') {
          store.commit('dynamicComponents/resetKeepAlive')
        }
        if (to.meta.keepAlive) {
          store.commit('dynamicComponents/keepAlive', to.name)
        }

        // 指令系统单独权限
        if (to.meta?.orderAuth) {
          await store
            .dispatch('order/getConfig')
            .then(() => {
              // const orderConfig = store.getters['order/orderConfig']
              // TODO 权限待细化
            })
            .catch(() => {})
        }

        if (migrateHttps(to)) {
          next(false)
          window.location.href = migrateHttps(to)
          return
        }

        hideFeedBack(to)

        next()
      })

      let observers = {}
      router.afterEach((to, from) => {
        const reportingApiExtra = JSON.stringify({
          url: `${window.location.origin}${window.location.pathname}#${to.fullPath}`,
          version: dejavuVersionId
        })
        setCookie('reportingApiExtra', reportingApiExtra)
        // pv打点，设置spm_id，some检查是否满足有meta.spm_id 或者 meta.send_pv
        if (to.path !== from.path && window.reportObserver) {
          Promise.resolve().then(() => {
            window.reportObserver.sendPV(from.fullPath, { uid, username })
            pbReportPv()
          }) // 上报refer_url
        }
        // 进入指令后台使用更明显的水印
        if (!from.meta?.orderAuth && to.meta?.orderAuth) {
          observers = watermark(`${username}(${uid})`, document.body, {
            height: 150,
            width: 200,
            fontSize: 16,
            rotate: 25,
            alpha: 0.3,
            color: '#bbb'
          })
        }
        // 退出指令后台去掉更明显的水印
        if (from.meta?.orderAuth && !to.meta?.orderAuth) {
          Object.values(observers).forEach((observer) => observer.disconnect())
          document.body.firstChild.remove()
        }
      })

      sync(store, router)

      new Vue({
        render: (h) => h(App),
        store,
        router,
        components: { App },
        mounted() {
          // 初始化接口拦截
          store.dispatch('proxyQa/onStartQaIntercept')
        },
        beforeDestroy() {
          // 清除接口拦截
          store.dispatch('proxyQa/onEndQaIntercept')
        }
      }).$mount('#app')
      microApp.start({
        lifeCycles: {
          unmount() {
            store.state.v3Route = null
          }
        },
        preFetchApps: [{ name: 'audit-manager-fe-v3', url: getUrl() }],
        plugins: {
          global: [
            {
              processHtml: (code) => {
                const html = processHtml(code)
                return html.template
              }
            }
          ]
        }
      })

      // 创建水印
      if (window.watermark) {
        window.watermark({ watermark_txt: username })
      }
      watermark(`${username}(${uid}) ${dejavuVersionId}`, document.body, {
        height: 150,
        width: 200,
        fontSize: 16,
        rotate: 25,
        alpha: 0.1,
        color: '#bbb'
      })

      clearRead()

      // 是否有访问权限
      if (
        data.ok ||
        window.location.hash?.includes('#/v3/audit-tools/order/')
      ) {
        store.state.menuLoaded = true
      } else {
        store.state.menuLoaded = false
      }
    })
}

try {
  biliRegister('onlyext')
  window.bmgCmptOnload = function (img) {
    if (window.__MICRO_APP_PROXY_WINDOW__?.bmgCmptOnload) {
      window.__MICRO_APP_PROXY_WINDOW__.bmgCmptOnload(img)
      return
    }
    window.bmgOnload?.(img)
  }
} catch (e) {
  console.error(e)
}
