import Vue from 'vue'
import { mapiBaseReport } from './base'

const config = {
  isJson: true
}

export default {
  getLogList: (params) => {
    return Vue.ajax.post(`${mapiBaseReport}/getAudit`, params, config)
  },
  setLog: (params) => {
    return Vue.ajax.post(`${mapiBaseReport}/setAudit`, params, config)
  },
  getLiveSliceLog: (params) => {
    return Vue.ajax.get(
      '/xlive/admin/argus/resourcedisposallog',
      { params },
      config
    )
  }
}
