import Vue from 'vue'
import { templateBase } from './base'

export default {
  getTemplateAll: () => {
    return Vue.ajax.get(`${templateBase}/audit/all/list`)
  },
  getAllDynamicRoutes: () => {
    return Vue.ajax.get(`${templateBase}/audit/all/dynamic-route`)
  },
  getPageDetail: (params) => {
    return Vue.ajax.get(`${templateBase}/audit/detail`, {params})
  },
  updatePageDetail: (params) => {
    return Vue.ajax.post(`${templateBase}/audit/detail/update`, params)
  },
  addPageDetail: (params) => {
    return Vue.ajax.post(`${templateBase}/audit/detail/add`, params)
  },
  editPage: (params) => {
    return Vue.ajax.post(`${templateBase}/audit/list/edit`, params)
  },
  addPage: (params) => {
    return Vue.ajax.post(`${templateBase}/audit/list/add`, params)
  },
  switchPage: (params) => {
    return Vue.ajax.post(`${templateBase}/audit/list/switch`, params, {
      handle: true
    })
  },
  getAllResourceIds: (params) => {
    return Vue.ajax.get(`${templateBase}/audit/list/resource`, {params})
  },
  getList: (params) => {
    return Vue.ajax.get(`${templateBase}/audit/list/all`, {params})
  },
  getABT: (params) => {
    return Vue.ajax.get(`${templateBase}/abt`, { params, handle: true })
  }
}
