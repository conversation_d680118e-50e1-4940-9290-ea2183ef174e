import Vue from 'vue'
import { adminBase } from './base'

export default {
  getGroupList: (params) => {
    return Vue.ajax.get(`${adminBase}/feature/group/list`, {params})
  },
  getGroupTypeList: (params) => {
    return Vue.ajax.get(`${adminBase}/feature/type/list`, {params})
  },
  switchGroup: (params) => {
    return Vue.ajax.post(`${adminBase}/feature/group/state`, params)
  },
  getGroupDetail: (params) => {
    return Vue.ajax.get(`${adminBase}/feature/group/detail`, {params})
  },
  getRoles: (params) => {
    return Vue.ajax.get(`${adminBase}/feature/role/list`, {params})
  },
  addGroup: (params) => {
    return Vue.ajax.post(`${adminBase}/feature/group/add`, params)
  },
  editGroup: (params) => {
    return Vue.ajax.post(`${adminBase}/feature/group/edit`, params)
  },
  getContentValueList: (params) => {
    return Vue.ajax.get(`${adminBase}/feature/content/list`, {params})
  },
  getValueTypeList: (params) => {
    return Vue.ajax.get(`${adminBase}/feature/group/list`, {params})
  },
  getContentLog: (params) => {
    return Vue.ajax.get(`${adminBase}/search/log`, {params})
  },
  switchContentState: (params) => {
    return Vue.ajax.post(`${adminBase}/feature/content/state`, params)
  },
  getFeatureTag: () => {
    return Vue.ajax.get(`${adminBase}/feature/tag/list`)
  },
  addContent: (params) => {
    return Vue.ajax.post(`${adminBase}/feature/content/add`, params)
  },
  editContent: (params) => {
    return Vue.ajax.post(`${adminBase}/feature/content/edit`, params)
  },
  getFeatureDetail: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis-strategy/feature/rule/info`, {params})
  },
  getFeatureGroupList: (params) => {
    return Vue.ajax.get(`${adminBase}/feature/group/type/list`, {params})
  },
  submitFeatureDetail: (params) => {
    return Vue.ajax.post(`${adminBase}/aegis-strategy/feature/rule/add`, params, {
      isJson: true
    })
  },
  getScene: () => {
    return Vue.ajax.get(`${adminBase}/feature/business/list`)
  },
  addScene: (params) => {
    return Vue.ajax.post(`${adminBase}/feature/business/add`, params)
  },
  editScene: (params) => {
    return Vue.ajax.post(`${adminBase}/feature/business/edit`, params)
  },
  getFeatureSearch: (params) => {
    return Vue.ajax.get(`${adminBase}/feature/content/search`, { params })
  },
  // 批量置顶
  goToTop: (params) => {
    return Vue.ajax.post(`${adminBase}/feature/group/top/add`, params)
  },
  // 取消置顶
  cancelTop: (params) => {
    return Vue.ajax.post(`${adminBase}/feature/group/top/cancel`, params)
  },
  getSpecialGroup: (params) => {
    return Vue.ajax.get(`${adminBase}/feature/content/config`, {params})
  },
  /**
   * 内容类型添加
   * http://bapi.bilibili.co/project/6571/interface/api/289988
   */
  typeAdd: (params) => {
    return Vue.ajax.post(`${adminBase}/feature/type/add`, params)
  },
  /**
   * 内容类型列表
   * http://bapi.bilibili.co/project/6571/interface/api/290004
   */
  typeList: (params) => {
    return Vue.ajax.get(`${adminBase}/feature/type/list`, { params })
  }
}
