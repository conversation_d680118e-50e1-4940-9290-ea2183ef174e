// 稿件任务接口
import Vue from 'vue'
import { adminBase } from './base'

export default {
  getTask: (params) => {
    return Vue.ajax.get(`${adminBase}/vt/aegis/dispatch/next`, {params})
  },
  forceOff: (params) => {
    return Vue.ajax.get(`${adminBase}/vt/aegis/dispatch/consumer/forceoff`, {params})
  },
  getTagList: (params) => {
    return Vue.ajax.get(`${adminBase}/manager/tag/list`, {
      params
    }).then((res) => {
      const data = res.data || {}
      // 因为params传了state: 1，所以不用再filter tag.state !== 0了
      const allTags = (data.data || [])
      const tagMap = allTags.reduce((result, tag) => {
        result[tag.tag_id] = tag.name
        return result
      }, {})

      return {
        allTags,
        tagMap
      }
    })
  },
  getContentClassifyTagList: (params) => {
    return Vue.ajax.get(`${adminBase}/content-classify/video-audit/tag/list`, {params})
  },
  getPlayUrl: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/archive/video/playurl`, {
      params
    })
  },
  getCopyright: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/search/copyright/v2`, params)
  },
  getUndoneStats: (params) => {
    return Vue.ajax.get(`${adminBase}/vt/aegis/dispatch/undostat`, {params})
  },
  jumpToTask: (params) => {
    return Vue.ajax.get(`${adminBase}/vt/aegis/dispatch/jump`, {params})
  },
  taskLogin: (params) => {
    return Vue.ajax.get(`${adminBase}/vt/aegis/dispatch/consumer/on`, {params})
  },
  taskLogout: (params) => {
    return Vue.ajax.get(`${adminBase}/vt/aegis/dispatch/consumer/off`, {params})
  },
  delayTask: (params) => {
    return Vue.ajax.get(`${adminBase}/vt/aegis/dispatch/delay`, {params})
  },
  submitAuditTask: (params) => {
    return Vue.ajax.post(`${adminBase}/vt/aegis/dispatch/submit`, params)
  },
  getAiMark: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis/engine/ai/video/res`, {params})
  }
}
