import Vue from 'vue'
import { adminBase } from './base.js'

export default {
  getResourceInfo: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis/engine/resource/info`, { params })
  },
  submitResource: (params) => {
    return Vue.ajax.post(`${adminBase}/aegis/engine/resource/submit`, params, {
      handle: true,
      isJson: true
    })
  },
  // 数据源属于审核平台的用这个接口获取资源列表
  getResourceList: (params, cancelToken = null) => {
    return Vue.ajax.get(`${adminBase}/aegis/engine/resource/lists`, {
      cancelToken,
      params
    })
  },
  batchSubmit: (params) => {
    return Vue.ajax.post(`${adminBase}/aegis/engine/resource/batchsubmit`, params, {
      isJson: true
    })
  },
  // 获取资源操作历史
  getResourceLog(params) {
    return Vue.ajax.get(`${adminBase}/aegis/engine/resource/log`, { params })
  },
  getOper(params) {
    return Vue.ajax.get(`${adminBase}/aegis/engine/business/oper/result`, { params })
  }
}
