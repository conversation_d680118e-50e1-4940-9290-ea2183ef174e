import Vue from 'vue'
import { adminBase } from './base'

export default {
  getUserInfo: () => {
    return Vue.ajax.get(`${adminBase}/knowledge/user/info`)
  },
  getRoles: () => {
    return Vue.ajax.get(`${adminBase}/knowledge/roles`)
  },
  getCategoryList: (params) => {
    return Vue.ajax.get(`${adminBase}/knowledge/category/list`, {params})
  },
  saveCategory: (params) => {
    return Vue.ajax.post(`${adminBase}/knowledge/category/create`, params, {
      isJson: true
    })
  },
  getList: (params) => {
    return Vue.ajax.get(`${adminBase}/knowledge/content/list`, {params})
  },
  getDetail: (params) => {
    return Vue.ajax.get(`${adminBase}/knowledge/content/detail`, {params})
  },
  addDetail: (params) => {
    return Vue.ajax.post(`${adminBase}/knowledge/content/add`, params)
  },
  editDetail: (params) => {
    return Vue.ajax.post(`${adminBase}/knowledge/content/edit`, params)
  },
  switchState: (params) => {
    return Vue.ajax.post(`${adminBase}/knowledge/content/state`, params)
  },
  getLog: (oid) => {
    return Vue.ajax.get(`${adminBase}/search/log`, {
      params: {
        appid: 'log_audit',
        business: 631,
        order: 'milli_ctime',
        type: 1,
        oid
      }
    })
  },
  getNormalUploadUrl: (params) => {
    return Vue.ajax.get(`${adminBase}/knowledge/upload/url`, {params})
  },
  editCategory: (params) => {
    return Vue.ajax.post(`${adminBase}/knowledge/category/edit`, params, {
      isJson: true
    })
  },
  addDownloadLog: (params) => {
    return Vue.ajax.post(`${adminBase}/knowledge/content/log`, params)
  },
  deleteCategory: (params) => {
    return Vue.ajax.post(`${adminBase}/knowledge/category/state`, params, {
      isJson: true
    })
  },
  extractText: (params) => {
    return Vue.ajax.get(`${adminBase}/knowledge/content/convert`, {params})
  },
  textPoll: (params) => {
    return Vue.ajax.get(`${adminBase}/knowledge/content/textpoll`, {params})
  },
  createTransferTask: (params) => {
    return Vue.ajax.post(`${adminBase}/knowledge/upload/video/init`, params)
  },
  checkTransferTask: (params) => {
    return Vue.ajax.get(`${adminBase}/knowledge/upload/video/complete`, {params})
  },
  searchVideoByCid: (params) => {
    return Vue.ajax.get(`${adminBase}/knowledge/video/info`, {params})
  },
  transferImage: (params) => {
    return Vue.ajax.post(`${adminBase}/knowledge/upload/picture`, params)
  },
  createLiveTransferTask: (params) => {
    return Vue.ajax.post(`${adminBase}/knowledge/live/replay`, params, {
      isJson: true
    })
  },
  checkLiveTransferTask: (params) => {
    return Vue.ajax.get(`${adminBase}/knowledge/live/replay`, {params})
  }
}
