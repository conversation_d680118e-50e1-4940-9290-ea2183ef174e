import Vue from 'vue'
import { adminBase } from './base'

export default {
  loginTaskDispatch: (params) => {
    return Vue.ajax.get(`${adminBase}/vt/aegis/archive/dispatch/consumer/on`, {
      params
    })
  },
  getTaskProcessStat: (params) => {
    return Vue.ajax.get(`${adminBase}/vt/aegis/archive/dispatch/processstat`, {
      params
    })
  },
  getList: (params) => {
    return Vue.ajax.get(`${adminBase}/vt/aegis/archive/dispatch/list`, {
      params
    })
  },
  getRole: (params) => {
    return Vue.ajax.get(`${adminBase}/vt/aegis/archive/dispatch/role`, {
      params
    })
  },
  getTask: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/aegis/dispatch/archive/next`, {
      params
    })
  },
  getUndoneStats: (params) => {
    return Vue.ajax.get(`${adminBase}/vt/aegis/archive/dispatch/undostat`, {
      params
    })
  },
  jumpToTask: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/aegis/dispatch/archive/jump`, {
      params
    })
  },
  taskLogout: (params) => {
    return Vue.ajax.get(`${adminBase}/vt/aegis/archive/dispatch/consumer/off`, {
      params
    })
  },
  getTacticConfig: (params) => {
    return Vue.ajax.get(`${adminBase}/vt/aegis/archive/dispatch/config/info`, {
      params
    })
  },
  delayTask: (params) => {
    return Vue.ajax.get(`${adminBase}/vt/aegis/archive/dispatch/delay`, {
      params
    })
  },
  submitAuditTask: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/aegis/dispatch/archive/submit`, params, {
      silent: true
    })
  }
}
