import Vue from 'vue'
import { adminBase } from './base'

export default {
  getMemberStat(params) {
    return Vue.ajax.get(`${adminBase}/vt/aegis/dispatch/memberstats`, {params})
  },
  getArchiveMemberStat(params) {
    return Vue.ajax.get(`${adminBase}/vt/aegis/archive/dispatch/memberstats`, {params})
  },
  getTaskList(query) {
    return Vue.ajax.get(`${adminBase}/vt/aegis/dispatch/list`, { params: query })
  },
  getProcessStat(params) {
    return Vue.ajax.get(`${adminBase}/vt/aegis/dispatch/processstat`, { params }).then(({ data }) => {
      const { first_processstat: first, second_processstat: second, review2_processstat: review2 } = data
      return { first, second, review2 }
    })
  },
  getVideoHistory() {
    return Vue.ajax.get(`${adminBase}/videoup/archive/video/history`)
  },
  getArchiveHistory() {
    return Vue.ajax.get(`${adminBase}/videoup/archive/view/history`)
  },
  getArchiveDispatchRole(params) {
    return Vue.ajax.get(`${adminBase}/vt/aegis/archive/dispatch/role`, {params})
  }
}
