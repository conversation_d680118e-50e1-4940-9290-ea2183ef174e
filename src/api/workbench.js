import Vue from 'vue'
import { adminBase, templateBase } from './base'

const config = {
  isJson: true
}

export default {
  goToTop: (params) => {
    return Vue.ajax.post(`${adminBase}/workbench/todo/totop`, params, config)
  },
  cancelTop: (params) => {
    return Vue.ajax.post(
      `${adminBase}/workbench/todo/canceltop`,
      params,
      config
    )
  },
  getManagerList: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/todo/manager/list`, { params })
  },
  switchState: (params) => {
    return Vue.ajax.post(`${adminBase}/workbench/todo/switch`, params, config)
  },
  getTodoList: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis-gateway/todo/receiver/list`, { params })
  },
  getResourceList: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/todo/resource/list`, { params })
  },
  getResourceList2: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/todo/resource/list2`, {
      params
    })
  },
  getResourceList3: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/todo/resource/list3`, {
      params
    })
  },
  getBusinessAndNum: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/todo/business/list`, { params })
  },
  getRole: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/task/role`, { params })
  },
  getWorkProcess: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/task/processstat`, { params })
  },
  LoginForWork: (params) => {
    return Vue.ajax.post(`${adminBase}/workbench/task/consumer/on`, params)
  },
  getTaskList: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/task/list`, { params })
  },
  getMemberStats: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/task/memberstats`, { params })
  },
  forceoff: (params) => {
    return Vue.ajax.post(
      `${adminBase}/workbench/task/consumer/forceoff`,
      params
    )
  },
  getBusiness: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/business/list`, { params })
  },
  getEvents: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/business/events`, { params })
  },
  getBusinessRoles: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/business/roles`, { params })
  },
  getTodo: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/todo/edit/detail`, { params })
  },
  addTodo(params) {
    return Vue.ajax.post(`${adminBase}/workbench/todo/create`, params, config)
  },
  editTodo(params) {
    return Vue.ajax.post(`${adminBase}/workbench/todo/edit`, params, config)
  },
  cancel(params) {
    return Vue.ajax.post(
      `${adminBase}/workbench/todo/resource/delete`,
      params,
      config
    )
  },
  getLog(params) {
    return Vue.ajax.get(`${adminBase}/search/log`, { params })
  },
  batchSubmitResource(params) {
    return Vue.ajax.post(
      `${adminBase}/workbench/todo/resource/batchsubmit`,
      params,
      config
    )
  },
  getOpers(params) {
    return Vue.ajax.get(`${adminBase}/aegis-gateway/todo/opers`, { params })
  },
  getHistory(params) {
    return Vue.ajax.get(`${adminBase}/workbench/todo/resource/log`, { params })
  },
  getNetStatus(params) {
    return Vue.ajax.get(`${adminBase}/aegis/net/token/byname`, { params })
  },
  getAllTemplate() {
    return Vue.ajax.get(`${templateBase}/audit/workbench/list`)
  },
  getTemplate(params) {
    return new Promise((resolve, reject) => {
      try {
        Vue.ajax
          .get(`${templateBase}/audit/workbench/detail`, { params })
          .then(resolve)
          .catch((error) => {
            if (params.lego_id) {
              const names = `bili_bdjs.aegis_lego_page|${params.lego_id}`
              Vue.ajax
                .get('/x/admin/aegis-gateway/template/config', {
                  params: {
                    names
                  }
                })
                .then((res) => {
                  const data = res?.data || {}
                  const legoConfig = JSON.parse(data[names])
                  resolve({
                    data: {
                      config: legoConfig.json_config,
                      page_desc: legoConfig.page_desc,
                      from_lego: 1
                    }
                  })
                  this.$tracker('template-api-backup', {
                     url: `${templateBase}/audit/workbench/detail`,
                     params,
                     error
                   })
                })
                .catch(reject)
            } else {
              reject(new Error('出错了'))
            }
          })
      } catch (err) {
        reject(err)
      }
    })
  },
  updateTemplate(params) {
    return Vue.ajax.post(`${templateBase}/audit/workbench/update`, params)
  },
  getTodoConfig(params) {
    return new Promise((resolve, reject) => {
      try {
        Vue.ajax
          .get(`${templateBase}/audit/workbench/config`, { params })
          .then(resolve)
          .catch((error) => {
            const names = `bili_bdjs.aegis_todo_template_config|${params.todo_id}`
            Vue.ajax
              .get('/x/admin/aegis-gateway/template/config', {
                params: {
                  names
                }
              })
              .then((res) => {
                const data = res?.data || {}
                const config = JSON.parse(data[names]) || {}
                resolve({
                  data: {
                    ...config,
                    id: config.config_id
                  }
                })
                this.$tracker('template-api-backup', {
                  url: `${templateBase}/audit/workbench/config`,
                  params,
                  error
                })
              })
              .catch(reject)
          })
      } catch (err) {
        reject(err)
      }
    })
  },
  bindTemplate(params) {
    return Vue.ajax.post(`${templateBase}/audit/workbench/bind`, params)
  },
  getMissionCenter(params) {
    return Vue.ajax.get(`${adminBase}/workbench/taskcenter/scheme`, { params })
  },
  getBiReport(params) {
    return Vue.ajax.get(`${adminBase}/aegis/report/bi`, { params })
  },
  getPlanList(params) {
    return Vue.ajax.get(`${adminBase}/workbench/todo/plan/list`, { params })
  },
  createPlan(params) {
    return Vue.ajax.post(`${adminBase}/workbench/plan/create`, params, {
      isJson: true
    })
  },
  getPlan(params) {
    return Vue.ajax.get(`${adminBase}/workbench/plan/info`, { params })
  },
  deletePlan(params) {
    return Vue.ajax.post(`${adminBase}/workbench/plan/delete`, params)
  },
  getTodos(params) {
    return Vue.ajax.get(`${adminBase}/workbench/plan/todo/list`, { params })
  },
  batchTransResource: (params) => {
    return Vue.ajax.post(
      `${adminBase}/workbench/todo/resource/batchtrans`,
      params,
      {
        isJson: true
      }
    )
  },
  addBusiness: (params) => {
    return Vue.ajax.post(`${adminBase}/workbench/aegis/business/add`, params)
  },
  updateBusiness: (params) => {
    return Vue.ajax.post(`${adminBase}/workbench/aegis/business/update`, params)
  },
  getBusinessDetail: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/aegis/business`, { params })
  },
  getConfig: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/aegis/business/config`, {
      params
    })
  },
  addConfig: (params) => {
    return Vue.ajax.post(
      `${adminBase}/workbench/aegis/business/config/add`,
      params
    )
  },
  updateConfig: (params) => {
    return Vue.ajax.post(
      `${adminBase}/workbench/aegis/business/config/update `,
      params
    )
  },
  getConfigTypes: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/aegis/business/config/types`, {
      params
    })
  },
  getEventList: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/business/events`, { params })
  },
  getEventTodoList: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/event/bindtodo/list`, {
      params
    })
  },
  getNewerLog: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/auditlog`, { params })
  },
  // http://bapi.bilibili.co/project/6261/interface/api/276286
  getBusinessConfig: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/aegis/business/config`, {
      params
    })
  },
  getFlowNetList: () => {
    return Vue.ajax.get(`${adminBase}/workbench/flow/net/list`)
  },
  getFlowNetVersionList: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/flow/net/versions`, { params })
  },
  getTodoListByFlowNetVersion: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/flow/net/nodes`, { params })
  },
  /**
   * 互斥组事件列表: http://bapi.bilibili.co/project/6261/interface/api/369957
   */
  todoSplitEvents: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/todo/split/events`, { params })
  },
  /**
   * 待办拆分列表: http://bapi.bilibili.co/project/6261/interface/api/366789
   */
  getTodoSplitList: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/todo/split/list`, { params })
  },
  /**
   * 待办拆分保存: http://bapi.bilibili.co/project/6261/interface/api/366801
   */
  todoSplitSave: (params) => {
    return Vue.ajax.post(`${adminBase}/workbench/todo/split/save`, params, {
      isJson: true
    })
  },
  /**
   * 待办拆分测试: http://bapi.bilibili.co/project/6261/interface/api/366795
   */
  todoSplitTest: (params) => {
    return Vue.ajax.post(`${adminBase}/workbench/todo/split/test`, params)
  },
  /**
   * 待办拆分测试v2: http://bapi.bilibili.co/project/6261/interface/api/385346
   */
  todoSplitTestV2: (params) => {
    return Vue.ajax.post(`${adminBase}/workbench/todo/split/test/v2`, params)
  },
  /**
   * 待办拆分提交: http://bapi.bilibili.co/project/6261/interface/api/366880
   */
  todoSplitSubmit: (params) => {
    return Vue.ajax.post(`${adminBase}/workbench/todo/split/submit`, params)
  },
  /**
   * 规则字符串: http://bapi.bilibili.co/project/6261/interface/api/371181
   */
  todoSplitRule: (params) => {
    return Vue.ajax.post(`${adminBase}/workbench/todo/split/rule`, params, {
      isJson: true,
      silent: true
    })
  },
  /**
   * 根据待办名称搜索待办: http://bapi.bilibili.co/project/6892/interface/api/393465
   */
  searchTodoListByName: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis-gateway/todo/list/by-name`, {
      params
    })
  },
  /**
   * 测试用例列表: http://bapi.bilibili.co/project/6261/interface/api/385325
   */
  todoSplitTestCaseList: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/todo/split/testcase/list`, {
      params
    })
  },
  /**
   * 添加测试用例: http://bapi.bilibili.co/project/6261/interface/api/385334
   */
  todoSplitTestCaseAdd: (params) => {
    return Vue.ajax.post(
      `${adminBase}/workbench/todo/split/testcase/add`,
      params,
      {
        isJson: true
      }
    )
  },
  /**
   * 移除测试用例: http://bapi.bilibili.co/project/6261/interface/api/385343
   */
  todoSplitTestCaseRemove: (params) => {
    return Vue.ajax.post(
      `${adminBase}/workbench/todo/split/testcase/remove`,
      params
    )
  },
  getSensitiveWordWithRemark(params) {
    return Vue.ajax.post(
      `${adminBase}/aegis-gateway/sensitive-word/filter/new`,
      params,
      {
        isJson: true
      }
    )
  },
  addEvent(params) {
    return Vue.ajax.post(`${adminBase}/workbench/todo/event/add`, params, {
      isJson: true
    })
  },
  updateEvent(params) {
    return Vue.ajax.post(`${adminBase}/workbench/todo/event/update`, params, {
      isJson: true
    })
  },
  deleteEvent(params) {
    return Vue.ajax.post(`${adminBase}/workbench/todo/event/delete`, params, {
      isJson: true
    })
  },
  addDispatchBiz(params) {
    return Vue.ajax.post(
      `${adminBase}/workbench/aegis/business/config/regist`,
      params
    )
  },
  // 获取日志
  getDialog(params) {
    return Vue.ajax.get(`${adminBase}/aegis-gateway/action-log`, { params })
  },
  getReasonTags(params) {
    return Vue.ajax.get(`${adminBase}/aegis-gateway/todo/reason/tags`, {
      params
    })
  },
  /**
   * 报备对象用户组查询
   * http://bapi.bilibili.co/project/6892/interface/api/442493
   */
  getUserGroupList: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis-gateway/apply/user/groups`, {
      params
    })
  },
  /**
   * 报备工单（批量创建）
   * http://bapi.bilibili.co/project/6892/interface/api/442498
   */
  createApply: (params) => {
    return Vue.ajax.post(`${adminBase}/aegis-gateway/apply/create`, params)
  },
  /**
   * diff查询接口
   * http://bapi.bilibili.co/project/6261/interface/api/461231
   */
  getTodoSplitDiff(params) {
    return Vue.ajax.get(`${adminBase}/workbench/todo/split/diff`, { params })
  },
  // 批量收藏
  addStarTodos(params) {
    return Vue.ajax.post(`${adminBase}/workbench/todo/collection/do`, params, {
      isJson: true
    })
  },
  /**
   * 通道置灰
   * http://bapi.bilibili.co/project/6892/interface/api/428574
  */
  setGray(params) {
    return Vue.ajax.post(`${adminBase}/aegis-gateway/todo/gray`, params)
  },
  /**
   * 置灰信息查询
   * http://bapi.bilibili.co/project/6892/interface/api/468270
  */
  grayInfoRequest(params) {
    return Vue.ajax.get(`${adminBase}/aegis-gateway/todo/grayinfo`, { params })
  },
  /**
   * 资源送审工作台
   * http://bapi.bilibili.co/project/6892/interface/api/475053
   */
  transferResource(params) {
    return Vue.ajax.post(`${adminBase}/aegis-gateway/trigger/batch`, params, {
      isJson: true
    })
  },
  /**
   * 批量处理工具任务列表查询
   * http://bapi.bilibili.co/project/6261/interface/api/481383
  */
  getbatchToolsList(params) {
    return Vue.ajax.get(`${adminBase}/workbench/process/task/list`, { params })
  },
  /**
   * 批量处理工具控制任务状态
   * http://bapi.bilibili.co/project/6261/interface/api/481371
  */
  setBatchTaskState(params) {
    return Vue.ajax.post(`${adminBase}/workbench/process/task/control`, params, {
      isJson: true
    })
  },
  /**
   * 批量处理工具查询单个任务详情
   * http://bapi.bilibili.co/project/6261/interface/api/481377
  */
  getBatchTaskDetail(params) {
    return Vue.ajax.get(`${adminBase}/workbench/process/task/detail`, { params })
  },
  /**
   * 批量处理工具查询单个任务状态
   * http://bapi.bilibili.co/project/6261/interface/api/481389
  */
  getBatchTaskState(params) {
    return Vue.ajax.get(`${adminBase}/workbench/process/task/status`, { params })
  },
  /**
   * 批量处理工具查询单个任务日志
   * http://bapi.bilibili.co/project/6261/interface/api/481395
  */
  getBatchTaskLog(params) {
    return Vue.ajax.get(`${adminBase}/workbench/process/task/log`, { params })
  },
  /**
   * 资源可流转列表
   * http://bapi.bilibili.co/project/6261/interface/api/321041
  */
  getDelayTodos(params) {
    return Vue.ajax.get(`${adminBase}/aegis-gateway/todo/trans/list`, { params })
  },
  /**
   *  单个待办策略因子
   * http://bapi.bilibili.co/project/6261/interface/api/481396
  */
  getTodoFactor(params) {
    return Vue.ajax.get(`${adminBase}/workbench/todo/factor`, { params })
  },
  // 获取有权限待办
  getTodoManagerList: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/todo/manager/list`, { params })
  },
  /**
   * 新建批量处理工具
   * http://bapi.bilibili.co/project/6261/interface/api/481365
  */
  createProcessTask(params) {
    return Vue.ajax.post(`${adminBase}/workbench/process/task/create`, params, {
      isJson: true
    })
  },
  createExportTask(params) {
    return Vue.ajax.get(`${adminBase}/aegis-gateway/todo/resource/list/export/init`, { params })
  },
  checkExportTask(params) {
    return Vue.ajax.get(`${adminBase}/aegis-gateway/todo/resource/list/export/query`, { params })
  },
  // 心跳
  ping(params) {
    return Vue.ajax.post(`${adminBase}/aegis-gateway/todo/operator/ping`, params)
  },
  getTranslationSubtitle(params) {
    return Vue.ajax.post(`${adminBase}/aegis-gateway/translation/video`, params, {
      isJson: true
    })
  }
}
