// 应急处理工具-稿件批量查询接口
import Vue from 'vue'
import { adminBase } from './base'

export default {
  getArchiveTaskList: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/archive/task/list`, {params})
  },
  // 获取稿件查询任务信息
  getArchiveTaskView: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/archive/task/view`, {params})
  },
  // 获取查询任务操作日志
  getArchiveTaskLog: (params) => {
    return Vue.ajax.get(`${adminBase}/search/log/audit`, {params})
  },
  // 继续/暂停查询任务
  changeTaskState: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/archive/task/state`, params)
  },
  // 下载报表
  downloadTaskReport: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/archive/task/csv/export`, {params})
  },
  // 获取词库分类
  getWordLibClassifies: () => {
    return Vue.ajax.get(`${adminBase}/creative/wordlib/classifies`)
  },
  // 获取词库词组实例
  getWordLibEntities: (params) => {
    return Vue.ajax.get(`${adminBase}/creative/wordlib/entities`, {params})
  },
  // 上传csv文件
  uploadFileV2: (params) => {
    return Vue.ajax.post(`${adminBase}/upload/file/upload/v2`, params)
  },
  // 提交表单
  submitArchiveTask: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/archive/task/query/submit`, params)
  },
  // 提交导入任务
  importArchiveTask: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/archive/task/import`, params)
  },
  // 获取任务报表详情
  getTaskReport: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/archive/task/report`, {params})
  },
  // 预估任务数量
  getTaskDetectResult: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/archive/task/query/detect/result`, params)
  },
  // 执行任务
  submitExcuteTask: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/archive/task/execute/submit`, params)
  },
  getPolicyGroups: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/archive/policy/groups`, {params})
  }
}
