import Vue from 'vue'
import { adminBase, apiBase } from './base'

export default {
  addBlackList: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/unrecommend/add`, params)
  },
  delBlackList: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/unrecommend/del`, params)
  },
  getBlackList: async (params) => {
    let formatRes = {}
    await Vue.ajax.get(`${adminBase}/videoup/unrecommend`, {params}).then((res) => {
      formatRes = res
      formatRes.data = (res.data || []).map((item) => {
        const { relation } = item
        item._id = relation.id
        item._oid = relation.oid
        item._type = relation.type
        return item
      })
    }).catch(_ => Promise.reject(_))
    return Promise.resolve(formatRes)
  },
  getContentRec: (params) => {
    return Vue.ajax.get(`${apiBase}/archive/onlineresource`, {params})
  },
  getStaffs: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/staff`, {params})
  },
  modifyStaffs: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/staff/batch/submit`, params)
  },
  saveAccess: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/archive/access`, params)
  },
  saveAuthor: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/archive/author`, params)
  },
  saveTags: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/archive/uptag`, params)
  },
  setVideoOrder: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/archive/video/move`, params, {
      isJson: true
    })
  },
  submitDetail: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/archive/submit`, params, {
      silent: true
    })
  }
}
