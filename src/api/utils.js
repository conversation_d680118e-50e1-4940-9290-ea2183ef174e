import { isArray, isObject, isFunction } from '@/utils/type'
import forEach from 'lodash-es/forEach'
import { VIDEO_AUDIT_RECHECK_CODE_LIST } from '@/utils/constant'

export const GO_URL_REGEXP = /^(.+)?(\/x\/|\/pgc\/admin\/|\/pugv\/|\/template\/|manga|cms\.manhua|mng|lds|\/karl\/|polymer-api|\/mapi\/|cm|\/xlive\/)/
const NO_SUCCESS_CODE_URL = ['copilot.bilibili.co']

const VIDEO_SUBMIT_API = ['/x/admin/workbench/todo/resource/submit', '/x/admin/workbench/task/submit', '/x/admin/aegis-gateway/todo/resource/submit', '/x/admin/aegis-gateway/task/submit']

export const noNeedErrorTipApiList = (code, url) => {
  // 业务逻辑
  if (VIDEO_SUBMIT_API.includes(url) && VIDEO_AUDIT_RECHECK_CODE_LIST.find(item => item.code === code)) {
    return true
  } else {
    return false
  }
}

export const examineApiCode = (code, url) => {
  let SUCCESS_CODE = 1
  if (GO_URL_REGEXP.test(url)) {
    SUCCESS_CODE = 0
  }
  if (code === SUCCESS_CODE) {
    return true
  } else {
    const testUrlObj = new URL(url, window.location.origin)
    if (NO_SUCCESS_CODE_URL.includes(testUrlObj.hostname)) return true
    return false
  }
}

export const genHost = () => {
  return '//manager.bilibili.co'
}

function isPlainObject(obj) {
  return isObject(obj) && Object.getPrototypeOf(obj) === Object.prototype
}

export const serialize = (params, obj, scope) => {
  // 判断类型是不是数组
  const array = isArray(obj)
  // 判断是不是简单对象？
  const plain = isPlainObject(obj)
  let hash
  // 不是array，是纯object，且带有key名为length的字段
  // https://github.com/lodash/lodash/issues/277
  if (plain && obj.length !== undefined) {
    Object.keys(obj).map((key) => {
      // key.map
      const value = obj[key]
      let pKey = key
      if (scope) {
        const sKey = plain || hash ? key : ''
        pKey = `${scope}[${sKey}]`
      }
      if (value === undefined) {
        return
      }
      if (!scope && array) {
        params.add(value.name, value.value)
      } else if (hash) {
        serialize(params, value, pKey)
      } else {
        params.add(pKey, value)
      }
    })
  } else {
    forEach(obj, (value, key) => {
      // 是object或者array数组，hash为true
      hash = isObject(value) || isArray(value)
      let pKey = key
      if (scope) {
        const sKey = plain || hash ? key : ''
        pKey = `${scope}[${sKey}]`
      }
      if (value === undefined) {
        return
      }
      if (!scope && array) {
        params.add(value.name, value.value)
      } else if (hash) {
        serialize(params, value, pKey)
      } else {
        params.add(pKey, value)
      }
    })
  }
}

export const trans = (obj) => {
  const params = []
  const escape = encodeURIComponent

  params.add = (key, value) => {
    let val = value
    if (isFunction(value)) {
      val = value()
    }
    if (value === null) {
      val = ''
    }

    params.push(`${escape(key)}=${escape(val)}`)
  }

  serialize(params, obj)

  return params.join('&').replace(/%20/g, '+')
}

export function setCookie(name, value, days) {
  let expires = ''
  if (days) {
    const date = new Date()
    date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000))
    expires = '; expires=' + date.toUTCString()
  }
  document.cookie = name + '=' + (value || '') + expires + '; path=/'
}

export function getCookie(key) {
  const cookie = document.cookie
  if (!cookie) return null

  return decodeURIComponent(
    cookie.replace(
      new RegExp('(?:(?:^|.*;)\\s*' +
        // eslint-disable-next-line no-useless-escape
        encodeURIComponent(key).replace(/[\-\.\+\*]/g, '\\$&') +
        '\\s*\\=\\s*([^;]*).*$)|^.*$'),
      '$1'
    )
  ) || null
}

export function translateForm(json) {
  const k = Object.keys(json)
  let params = []

  for (let i = 0; i < k.length; i++) {
    const m = k[i]
    if (!json.hasOwnProperty(m)) continue
    params.push(`${m}=${encodeURIComponent(json[m])}`)
  }

  return params.join('&')
}
