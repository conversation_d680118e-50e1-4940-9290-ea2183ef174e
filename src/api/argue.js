import Vue from 'vue'
import { adminBase } from './base'

export default {
  getArgumentDetail: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/archive/argument/info`, {params})
  },
  editArgument: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/archive/argument/submit`, params)
  },
  getArgumentList: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/archive/argument/tag`, {params})
  }
}
