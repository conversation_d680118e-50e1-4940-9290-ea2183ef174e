/**
 * 报备工单
 */
import Vue from 'vue'
import { adminBase } from './base.js'

export default {
  /**
   * 报备对象用户组查询
   * http://bapi.bilibili.co/project/6892/interface/api/442493
   */
  getUserGroups: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis-gateway/apply/user/groups`, {params})
  },
  /**
   * 报备工单（批量创建）
   * http://bapi.bilibili.co/project/6892/interface/api/442498
   */
  create: (params) => {
    return Vue.ajax.post(`${adminBase}/aegis-gateway/apply/create`, params)
  }
}
