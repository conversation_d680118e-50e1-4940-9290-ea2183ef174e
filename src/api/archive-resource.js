import Vue from 'vue'
import { adminBase } from './base.js'

export default {
  getArchiveResourceList: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/search/video`, {params})
  },
  getTagList: (params) => {
    return Vue.ajax.get(`${adminBase}/manager/tag/list`, {params})
  },
  getContentClassifyTagList: (params) => {
    return Vue.ajax.get(`${adminBase}/content-classify/video-audit/tag/list`, {params})
  },
  batchSubmitVideoAuditResult: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/archive/video/batch`, params, {
      isJson: true
    })
  },
  getInfo: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis-gateway/archive/video/info`, {
      params
    })
  },
  getPlayUrl: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/archive/video/playurl`, {
      params
    })
  },
  getCopyright: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/search/copyright/v2`, params)
  },
  submitDetail: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/archive/video/submit`, params, {
      silent: true
    })
  }
}
