import Vue from 'vue'
import { adminBase } from './base'

export default {
  getTagList: (params) => {
    return Vue.ajax.get(`${adminBase}/manager/tag/list`, {
      params
    })
  },
  enable: () => {
    return Vue.ajax.get(`${adminBase}/aegis/business/enable`)
  },
  getFilterConfig: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis/business/config/frontform`, {
      params
    })
  }
}
