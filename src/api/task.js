import Vue from 'vue'
import { adminBase } from './base'

export default {
  getDynDetail: (path, params) => {
    return Vue.ajax.get(`//${path}/x/polymer/web-dynamic/v1/detail/audit`, {
      params,
      handle: true
    })
  },
  getOpusDetail: (path, params) => {
    return Vue.ajax.get(`//${path}/x/polymer/web-dynamic/v1/opus/detail/audit`, {
      params,
      handle: true
    })
  },
  getTaskNext: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis/engine/task/next`, { params })
  },
  getTaskInfo: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis/engine/task/info`, { params })
  },
  submitTask: (params) => {
    return Vue.ajax.post(`${adminBase}/aegis/engine/task/submit`, params, {
      handle: true,
      isJson: true
    })
  },
  getBiReport: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis/report/bi`, { params })
  },
  batchSubmit: (params) => {
    return Vue.ajax.post(`${adminBase}/aegis/engine/task/batchsubmit`, params, {
      isJson: true,
      handle: true
    })
  },
  getRestList: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis/engine/task/restlist`, { params })
  },
  getUserInfo: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis/task/role`, { params })
  },
  getTaskList: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis/engine/task/list`, { params })
  },
  delayTask: (params) => {
    return Vue.ajax.post(`${adminBase}/aegis/task/delay`, params)
  },
  quitConsumer: (params) => {
    return Vue.ajax.post(`${adminBase}/aegis/task/consumer/off`, params)
  },
  loginTask: (params) => {
    return Vue.ajax.get(`${adminBase}/vt/aegis/dispatch/consumer/on`, { params })
  }
}
