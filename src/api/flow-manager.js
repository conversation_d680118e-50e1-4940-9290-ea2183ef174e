import Vue from 'vue'
import { adminBase } from './base'

export default {
  getBusinessList: (params) => {
    return Vue.ajax.get(`${adminBase}/content-flow-control/business/list`, {params})
  },
  getMealBindList: (params) => {
    return Vue.ajax.get(`${adminBase}/content-flow-control/meal/bind/list`, {params})
  },
  switchBindState: (params) => {
    return Vue.ajax.post(`${adminBase}/content-flow-control/meal/bind/state`, params)
  },
  switchState: (params) => {
    return Vue.ajax.post(`${adminBase}/content-flow-control/meal/state`, params)
  },
  getMealList: (params) => {
    return Vue.ajax.get(`${adminBase}/content-flow-control/meal/list`, {params})
  },
  addMeal: (params) => {
    return Vue.ajax.post(`${adminBase}/content-flow-control/meal/add`, params)
  },
  editMeal: (params) => {
    return Vue.ajax.post(`${adminBase}/content-flow-control/meal/edit`, params)
  },
  bindMeal: (params) => {
    return Vue.ajax.post(`${adminBase}/content-flow-control/meal/bind/add`, params)
  },
  editBindMeal: (params) => {
    return Vue.ajax.post(`${adminBase}/content-flow-control/meal/bind/edit`, params)
  },
  updateMealBindState: (params) => {
    return Vue.ajax.post(`${adminBase}/content-flow-control/meal/bind/state`, params)
  },
  getBindLog: (params) => {
    return Vue.ajax.get(`${adminBase}/content-flow-control/meal/bind/log/list`, {params})
  },
  getLog: (params) => {
    return Vue.ajax.get(`${adminBase}/search/log`, {params})
  },
  getTagNote: (params) => {
    return Vue.ajax.get(`${adminBase}/manager/tag/list`, {
      params
    }).then((res) => {
      const data = res.data || {}
      const allTags = (data.data || [])
      const tagMap = allTags.reduce((acc, cur) => {
        if (acc[cur.rid] === undefined) {
          acc[cur.rid] = {
            name: cur.rname,
            id: cur.rid,
            list: []
          }
        }
        acc[cur.rid].list.push({
          name: cur.name,
          id: cur.id
        })
        return acc
      }, {})

      const tagNoteList = []
      for (const tagKey in tagMap) {
        tagNoteList.push({
          id: parseInt(tagKey, 10),
          name: tagMap[tagKey].name,
          list: tagMap[tagKey].list
        })
      }

      return tagNoteList
    }) 
  },
  exportCsv: (params) => {
    return Vue.ajax.get(`${adminBase}/content-flow-control/meal/bind/list/export`, {params})
  },
  // http://bapi.bilibili.co/project/6739/interface/api/308709
  getSelectConf: (params) => {
    return Vue.ajax.get(`${adminBase}/content-flow-control/flow-history/select/conf`, { params })
  },
  // http://bapi.bilibili.co/project/6739/interface/api/308253
  getFlowHistoryList: (params) => {
    return Vue.ajax.get(`${adminBase}/content-flow-control/flow-history/list`, { params })
  },
  // http://bapi.bilibili.co/project/6739/interface/api/308262
  cancelFlowHistory: (params) => {
    return Vue.ajax.post(`${adminBase}/content-flow-control/flow-history/cancel`, params, {
      isJson: true
    })
  }
}
