import { mapiBase } from './base'
import Vue from 'vue'
import store from '@/store/index'

const mapiProxyUrl = `${mapiBase}/proxy/call`
const mapiHeavyProxyUrl = `${mapiBase}/proxy/heavyCall`
const service = Vue.ajax
const envMap = {
  'uat': 'test',
  'pre': 'production',
  'prod': 'production'
}

export class Http {
  async get(url, data) {
    const res = await service.get(url, { params: data })
    return res.data
  }
  async post(url, data) {
    const res = await Vue.ajax.post(url, data)
    return res.data
  }
}
// 网关代理转发业务接口封装
export class Api extends Http {
  code
  moduleId

  constructor(code, moduleId) {
    super()
    this.code = code
    this.moduleId = moduleId
  }

  async mapiProxy(data) {
    const getEnv = store?.getters['env/getEnv']
    const env = getEnv && getEnv()
    const d = {
      biz_code: this.code,
      module: this.moduleId,
      real_url: data.url,
      method: data.method || 'get',
      env: envMap[env] || 'test',
      need_trace_info: 0,
      params: JSON.stringify(data.params),
      appid: data.appid
    }
    const res = await this.post(mapiProxyUrl, d)
    return res
  }

  async mapiHeavyProxy(data) {
    const getEnv = store?.getters['env/getEnv']
    const env = getEnv && getEnv()
    const d = {
      biz_code: this.code,
      module: this.moduleId,
      real_url: data.url,
      method: data.method || 'get',
      env: envMap[env] || 'test',
      need_trace_info: 0,
      params: JSON.stringify(data.params),
      appid: data.appid
    }
    const res = await this.post(mapiHeavyProxyUrl, d)
    return res
  }
}
