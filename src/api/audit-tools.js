import Vue from 'vue'
import { adminBase } from './base'

export default {
  /**
   * 告警机器人列表
   * http://bapi.bilibili.co/project/6261/interface/api/413299
   */
  alertList: (params) => {
    return Vue.ajax.get(
      `${adminBase}/workbench/todo/alert/list`, { params }
    )
  },
  /**
   * 告警配置映射
   * http://bapi.bilibili.co/project/6261/interface/api/413314
   */
  alertConfig: (params) => {
    return Vue.ajax.get(
      `${adminBase}/workbench/todo/alert/config`, { params }
    )
  },
  /**
   * 告警详情
   * http://bapi.bilibili.co/project/6261/interface/api/413302
   */
  alertDetail: (params) => {
    return Vue.ajax.get(
      `${adminBase}/workbench/todo/alert/detail`, { params }
    )
  },
  /**
   * 告警状态变更
   * http://bapi.bilibili.co/project/6261/interface/api/413305
   */
  alertStateToggle: (params) => {
    return Vue.ajax.post(
      `${adminBase}/workbench/todo/alert/state-toggle`,
      params,
      { isJson: true }
    )
  },
  /**
   * 告警新增
   * http://bapi.bilibili.co/project/6261/interface/api/413287
   */
  alertAdd: (params) => {
    return Vue.ajax.post(
      `${adminBase}/workbench/todo/alert/add`,
      params,
      { isJson: true }
    )
  },
  /**
   * 告警更新
   * http://bapi.bilibili.co/project/6261/interface/api/413290
   */
  alertStateUpdate: (params) => {
    return Vue.ajax.post(
      `${adminBase}/workbench/todo/alert/update`,
      params,
      { isJson: true }
    )
  }
}
