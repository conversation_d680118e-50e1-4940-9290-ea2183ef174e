import Vue from 'vue'
import { adminBase } from './base'

export default {
  getArctype: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/archive/typeid/list`, {params})
  },
  cancelTasks: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis/engine/cancel`, {params})
  },
  getExtra: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis/business/config/reserve`, {params})
  },
  getNetStatus: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis/net/token/byname`, {params})
  },
  getAuditLog: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis/engine/auditlog`, {params})
  },
  getTranOpers: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis/engine/tranopers`, {params})
  },
  getRole() {
    return Vue.ajax.get(`${adminBase}/vt/task/role`).then(({ data }) => {
      const role = (data && data.role_map) ? data.role_map : ''
      return role.split(',').sort()
    })
  },
  getEmote() {
    return Vue.ajax.get(`${adminBase}/emote/mapping`)
  },
  getEmoteFileUrl() {
    return Vue.ajax.get(`${adminBase}/emote/mapping/url`)
  },
  getCommentParse(params) {
    return Vue.ajax.post(`${adminBase}/reply/reply/list`, params, {
      isJson: true
    })
  },
  getRuleConfig() {
    return Vue.ajax.get(`${adminBase}/filter/rule/config`)
  }
}
