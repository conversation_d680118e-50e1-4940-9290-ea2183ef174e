import Vue from 'vue'
import { adminBase } from './base.js'

export default {
  getApplyList: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/staff/apply/list`, {
      params
    })
  },
  editApplyState: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/staff/apply/state`, params)
  },
  getStaffLog: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/staff/apply/oper/history`, {params})
  }
}
