import Vue from 'vue'
import { adminBase } from './base'

export default {
  getApiUpdate: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis/version`, {
      hideLoadingBar: true,
      handle: true,
      params
    })
  },
  getPageUpdate: (params) => {
    return Vue.ajax.get(`${window.location.protocol}//${window.location.host}${window.location.pathname}#/`, {
      hideLoadingBar: true,
      handle: true,
      params
    })
  }
}
