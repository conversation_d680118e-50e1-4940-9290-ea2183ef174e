import Vue from 'vue'
import { adminBase } from './base'

export default {
  getBusiness: () => {
    return Vue.ajax.get(`${adminBase}/aegis-strategy/business/list`)
  },
  getTacticList: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis-strategy/business/scene/list`, { params })
  },
  switchTactic: (params) => {
    return Vue.ajax.post(`${adminBase}/aegis-strategy/business/scene/enable`, params)
  },
  rollback: (params) => {
    return Vue.ajax.post(`${adminBase}/aegis-strategy/business/scene/rollback`, params)
  },
  getOperationLog: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis-strategy/business/scene/log`, { params })
  },
  getBusinessLog: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis-strategy/business/log`, { params })
  },
  addBusiness: (params) => {
    return Vue.ajax.post(`${adminBase}/aegis-strategy/business/add`, params)
  },
  editBusiness: (params) => {
    return Vue.ajax.post(`${adminBase}/aegis-strategy/business/edit`, params)
  },
  getFactor: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis-strategy/business/scene/oper`, { params })
  },
  getScence: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis-strategy/business/scene/view`, { params })
  },
  getSceneAndLastApprovedVersion: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis-strategy/business/scene/approve/view`, { params })
  },
  submitScence: (params) => {
    return Vue.ajax.post(`${adminBase}/aegis-strategy/business/scene/add`, params, {
      isJson: true
    })
  },
  getAuth: () => {
    return Vue.ajax.get(`${adminBase}/aegis-strategy/auth`)
  },
  getUserGroup: (params) => {
    return Vue.ajax.get(`${adminBase}/manager/business/type/auth_list`, { params })
  }
}
