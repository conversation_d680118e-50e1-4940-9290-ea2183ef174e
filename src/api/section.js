import Vue from 'vue'
import { adminBase } from './base'

export default {
  getSections: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/season/sections`, { params })
  },
  getEps: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/season/section/eps`, { params })
  },
  getLog: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/season/section/logs`, { params })
  },
  getEpLog: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/season/section/ep/logs`, { params })
  }
}
