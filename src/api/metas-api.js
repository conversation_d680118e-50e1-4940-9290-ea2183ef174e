import { Api } from '@/api/mapiHttp.js'
import store from '@/store/index'
import { fillTemplate } from '@/v2/utils'

const code = 1
export const moduleId = 200004 // 直播音视频审核
const api = new Api(code, moduleId)
const envMap = {
  'uat': 'uat-',
  'pre': '',
  'prod': ''
}

export default {
  getMetaList: (params) => {
    const getEnv = store?.getters['env/getEnv']
    const env = getEnv && getEnv()
    // eslint-disable-next-line no-template-curly-in-string
    const hostName = fillTemplate('${this.env}${this.a}.${this.b}.${this.c}', {
      env: envMap[env] || '',
      a: 'bvc-nerve',
      b: 'bilibili',
      c: 'co'
    })
    return api.mapiProxy({
      method: 'get',
      url: `http://${hostName}/x/video/live/hls-record-gateway-proxy/query`,
      params
    })
  }
}
