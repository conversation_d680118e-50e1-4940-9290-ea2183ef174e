import Vue from 'vue'
import { adminBase } from './base.js'

export default {
  getVerify: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/season/verify`, { params })
  },
  batchState: (params) => { // 提交时 带上合集版本号
    return Vue.ajax.post(`${adminBase}/videoup/season/state/batch`, params, {
      isJson: true
    })
  },
  editState: (params) => { // 提交时 带上合集版本号
    return Vue.ajax.post(`${adminBase}/videoup/season/state`, params)
  },
  getRealtimeInfo: (params) => { // 获取 合集版本号
    return Vue.ajax.get(`${adminBase}/videoup/season/info/v2`, { params })
  }
}
