import Vue from 'vue'
import { adminBase } from './base'
// import { genBvcUrl } from '@/utils'

export default {
  delVideo: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/archive/video/delete`, params)
  },
  editVideo: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/archive/video/edit`, params)
  },
  getDownload: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/archive/video/download_url`, {params})
  },
  getVideos: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/archive/video/items`, {params})
  },
  saveWeblink: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/archive/video/weblink`, params)
  },
  getVideoXcodeState: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis-gateway/station/view`, {params})
  },
  getPromotePosInfo(params) {
    return Vue.ajax.get(`${adminBase}/videoup/archive/resource/info`, {params})
  },
  getRawVideoUrl(params) {
    return Vue.ajax.get(`${adminBase}/videoup/archive/video/original/film`, {params})
  },
  getRelatedVideos(params) {
    return Vue.ajax.get(`${adminBase}/videoup/archive/video/info/relation/split`, {params})
  },
  getVideoScreenshot(params) {
    return Vue.ajax.get(`${adminBase}/videoup/archive/video/picture`, {
      params
    })
    // const { cid, filename = '', pos = '' } = params || {}
    // const url =
    //   filename.length === 0
    //     ? `http://vs12450.acg.tv/get_video_pic.php?cid=${cid}&type=jsonp&pos=${time}`
    //     : genBvcUrl({
    //         r: 'panel_videoshot',
    //         flowid: filename,
    //         pos
    //       })
    //   return url
  }
}
