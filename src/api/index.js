import archiveApi from './archive.js'
import detailApi from './detail.js'
import businessApi from './business.js'
import businessOrderApi from './business-order.js'
import commercialApi from './commercial.js'
import commonApi from './common.js'
import configApi from './config.js'
import dynamicApi from './dynamic.js'
import listApi from './list.js'
import userGroupApi from './user-group.js'
import videoApi from './video.js'
import versionApi from './version.js'
import taskApi from './task.js'
import templateApi from './template.js'
import batchApi from './batch.js'
import resourceApi from './resource.js'
import staffApi from './staff.js'
import reasonApi from './reason.js'
import cardApi from './card.js'
import blockApi from './block.js'
import sectionApi from './section.js'
import seasonApi from './season.js'
import trackApi from './track.js'
import pageApi from './page.js'
import archiveTaskApi from './archive-task.js'
import graphApi from './graph.js'
import graphAiApi from './graph-ai.js'
import tacticApi from './tactic.js'
import archiveResourceApi from './archive-resource'
import featureApi from './feature.js'
import mangaApi from './manga.js'
import archiveDispatchApi from './archive-dispatch'
import bgmApi from './bgm'
import workbenchApi from './workbench'
import workbenchDetailApi from './workbench-detail'
import libraryApi from './library'
import workbenchReportApi from './workbench-report'
import chatApi from './chat'
import tagApi from './tag'
import argueApi from './argue'
import flowManagerApi from './flow-manager'
import dataSourceApi from './data-source'
import legoApi from './lego'
import auditToolsApi from './audit-tools'
import frontLogApi from './front-log'
import reportApi from './report'
import mediaApi from './media'
import emergencyToolsApi from './emergency-tool'
import workStatusApi from './work-time-manage.js'
import metaApi from './metas-api.js'
import auditLogApi from './auditLog.js'
import liveApi from './live.js'
import mineApi from './mine.js'

export {
  archiveApi,
  detailApi,
  businessApi,
  businessOrderApi,
  commercialApi,
  commonApi,
  configApi,
  dynamicApi,
  listApi,
  userGroupApi,
  videoApi,
  versionApi,
  taskApi,
  templateApi,
  batchApi,
  resourceApi,
  staffApi,
  reasonApi,
  cardApi,
  blockApi,
  sectionApi,
  seasonApi,
  trackApi,
  pageApi,
  archiveTaskApi,
  graphApi,
  tacticApi,
  archiveResourceApi,
  featureApi,
  mangaApi,
  archiveDispatchApi,
  bgmApi,
  graphAiApi,
  workbenchApi,
  workbenchDetailApi,
  libraryApi,
  workbenchReportApi,
  chatApi,
  tagApi,
  argueApi,
  flowManagerApi,
  dataSourceApi,
  legoApi,
  auditToolsApi,
  frontLogApi,
  reportApi,
  mediaApi,
  emergencyToolsApi,
  workStatusApi,
  metaApi,
  auditLogApi,
  liveApi,
  mineApi
}
