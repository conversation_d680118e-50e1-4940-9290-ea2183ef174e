import Vue from 'vue'
import { adminBase } from './base.js'

export default {
  /**
   * 获取数据源业务配置信息
   */
  getBusinessConfig: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis-offline/business/config`)
  },
  /**
   * 数据源详情: http://bapi.bilibili.co/project/7142/interface/api/332913
   */
  getTaskDetail: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis-offline/task/detail`, { params })
  },
  /**
   * 数据源列表: http://bapi.bilibili.co/project/7142/interface/api/333210
   */
  getTaskList: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis-offline/task/list`, { params })
  },
  /**
   * 数据源添加: http://bapi.bilibili.co/project/7142/interface/api/332916
   */
  addTask: (params) => {
    return Vue.ajax.post(`${adminBase}/aegis-offline/task/add`, params, {
      isJson: true
    })
  },
  /**
   * 数据源删除/恢复: http://bapi.bilibili.co/project/7142/interface/api/332927
   */
  changeTaskState: (params) => {
    return Vue.ajax.post(`${adminBase}/aegis-offline/task/state`, params, {
      isJson: true
    })
  },
  /**
   * 数据源执行追踪列表: http://bapi.bilibili.co/project/7142/interface/api/332935
   */
  getProcessList: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis-offline/process/list`, { params })
  },
  /**
   * 数据源执行追踪状态变更: http://bapi.bilibili.co/project/7142/interface/api/333207
   */
  changeProcessState: (params) => {
    return Vue.ajax.post(`${adminBase}/aegis-offline/process/state`, params, {
      isJson: true
    })
  },
  /**
   * 数据源日志
   */
  getTaskLog: (params) => {
    return Vue.ajax.get(`${adminBase}/search/log`, {
      params: {
        appid: 'log_audit',
        business: 1130,
        order: 'ctime',
        type: 1,
        ...params
      }
    })
  },
  /**
   * 数据源执行追踪日志
   */
  getProcessLog: (params) => {
    return Vue.ajax.get(`${adminBase}/search/log`, {
      params: {
        appid: 'log_audit',
        business: 1130,
        order: 'ctime',
        type: 2,
        ...params
      }
    })
  },
  /**
   * 新人待办数量
   * http://bapi.bilibili.co/project/6571/interface/api/419914
   */
  getNewbieTodoCount: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis-offline/newbie/todo-count`, { params })
  }
}
