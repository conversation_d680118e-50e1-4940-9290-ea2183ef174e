import Vue from 'vue'
import { adminBase } from './base'

export default {
  getBusinessInfo: () => {
    return Vue.ajax.get(`${adminBase}/content-classify/tag/pre`)
  },
  getTagList: (params) => {
    return Vue.ajax.get(`${adminBase}/content-classify/tag/list`, { params })
  },
  getGrayTagList: (params) => {
    return Vue.ajax.get(`${adminBase}/content-classify/fc/config/tag/display/list`, { params })
  },
  switchState: (params) => {
    return Vue.ajax.post(`${adminBase}/content-classify/tag/batch/upstate`, params)
  },
  addTag: (params) => {
    return Vue.ajax.post(`${adminBase}/content-classify/tag/add`, params, {
      handle: true
    })
  },
  editTag: (params) => {
    return Vue.ajax.post(`${adminBase}/content-classify/tag/edit`, params)
  },
  getTagInfo: (params) => {
    return Vue.ajax.get(`${adminBase}/content-classify/tag/info`, { params })
  },
  getLog: ({ id, type }) => {
    return Vue.ajax.get(`${adminBase}/search/log`, {
      params: {
        appid: 'log_audit',
        business: 740,
        order: 'ctime',
        type,
        oid: id
      }
    })
  },
  getSubtags: (params) => {
    return Vue.ajax.get(`${adminBase}/content-classify/tag/subtags`, { params })
  },
  switchProcessState: (params) => {
    return Vue.ajax.post(`${adminBase}/content-classify/process/batch/upstate`, params)
  },
  getTagTree: (params) => {
    return Vue.ajax.get(`${adminBase}/content-classify/tag/tree`, { params })
  },
  getAllTag: (params) => {
    return Vue.ajax.get(`${adminBase}/content-classify/tag/alltags`, { params })
  },
  editPidTag: (params) => {
    return Vue.ajax.post(`${adminBase}/content-classify/tag/batch/editpid`, params)
  },
  getArchiveTagList: (params) => {
    return Vue.ajax.get(`${adminBase}/content-classify/archive/tag/list`, { params })
  },
  // http://bapi.bilibili.co/project/6360/interface/api/302902
  getTaskTodoList: (params) => {
    return Vue.ajax.get(`${adminBase}/content-classify/import/task/todo/list`, { params })
  },
  // http://bapi.bilibili.co/project/6360/interface/api/302924
  getTaskList: (params) => {
    return Vue.ajax.get(`${adminBase}/content-classify/import/task/list`, { params })
  },
  // http://bapi.bilibili.co/project/6360/interface/api/302910
  submitTask: (params) => {
    return Vue.ajax.post(`${adminBase}/content-classify/import/task/submit`, params, {
      isJson: true
    })
  },
  // http://bapi.bilibili.co/project/6360/interface/api/302964
  deleteTask: (params) => {
    return Vue.ajax.post(`${adminBase}/content-classify/import/task/delete`, params, {
      isJson: true
    })
  },
  // 标签迁移 http://bapi.bilibili.co/project/6360/interface/api/485138
  batchTransfer: (params) => {
    return Vue.ajax.post(`${adminBase}/content-classify/tag/batch/transfer`, params)
  },
  // 获取业务 http://bapi.bilibili.co/project/6360/interface/api/278588
  getBusiness: (params) => {
    return Vue.ajax.get(`${adminBase}/content-classify/tag/business/list`, { params })
  },
  // 新增业务 http://bapi.bilibili.co/project/6360/interface/api/278563
  addBusiness: (params) => {
    return Vue.ajax.post(`${adminBase}/content-classify/tag/business/add`, params)
  },
  // 编辑业务 http://bapi.bilibili.co/project/6360/interface/api/306137
  editBusiness: (params) => {
    return Vue.ajax.post(`${adminBase}/content-classify/tag/business/edit`, params)
  },
  // 获取监控使用的标签
  getMonitorTags: (params) => {
    return Vue.ajax.get(`${adminBase}/content-classify/classify/tag/display/set`, { params })
  },
  // 加监控
  addToMonitor: (params) => {
    return Vue.ajax.post(`${adminBase}/content-classify/classify/monitor/add`, params)
  },
  // 监控打标
  updateMonitorStrategy: (params) => {
    return Vue.ajax.post(`${adminBase}/content-classify/classify/tag/submit`, params)
  },
  // 监控打标结果查询
  getMonitorStrategy: (params) => {
    return Vue.ajax.get(`${adminBase}/content-classify/classify/tag/display/result`, { params })
  },
  // 一审标签结果、全量标签、全量理由查询
  getAuditTagsAndReasons: (params) => {
    return Vue.ajax.get(`${adminBase}/content-classify/video-audit/display/tags`, { params })
  },
  // 一审标签结果查询
  getVideoAuditTagResult: (params) => {
    return Vue.ajax.get(`${adminBase}/content-classify/video-audit/display/result`, { params })
  },
  // 获取完整标签树
  getReasonTagTree: (params) => {
    return Vue.ajax.get(`${adminBase}/content-classify/tag/display/list`, { params })
  },
  submitVideoTag: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/archive/video/tag/submit`, params)
  },
  getResultPeek: (data) => {
    return Vue.ajax.post(`${adminBase}/content-classify/tag-rule/result/peek`, data, {
      isJson: true
    })
  },
  // 查所有的理由的接口
  getAllReasons: () => {
    return Vue.ajax.get(`${adminBase}/videoup/reason/list/v2`)
  },
  // 用于回显标签
  getTagRuleInfo: (params) => {
    return Vue.ajax.get(`${adminBase}/content-classify/tag-rule/result/info`, { params })
  },
  // 获取限流标（灰标和备注标）和限流理由的映射关系 https://cloud.bilibili.co/akali/appsManage?appId=main.archive.content-classify-admin&level=2&itemId=340684#sh/sh001/prod
  getLimitTagsAndReasons: (params) => {
    return Vue.ajax.get(`${adminBase}/content-classify/fc/config/tag/reason`, { params })
  },
  // 获取稿件限流理由 https://cloud.bilibili.co/akali/appsManage?appId=main.archive.videoup-admin&level=2&itemId=340694#sh/sh001/prod
  getArchiveLimitReasons: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/archive/limit/info`, { params })
  }
}
