import Vue from 'vue'
import { adminBase } from './base'

const config = {
  isJson: true
}

export default {
  getWorkStatus: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis-gateway/hour/management/member/status`, {
      params
    }, config)
  },
  switchWorkStatus: (params) => {
    return Vue.ajax.post(`${adminBase}/aegis-gateway/hour/management/member/switch`, params, config)
  },
  callbackAdvance: (params) => {
    return Vue.ajax.post(`${adminBase}/aegis-gateway/hour/management/member/occupy/confirm`, params, config)
  }
}
