import Vue from 'vue'
import { adminBase } from './base'

export default {
  getBusinessList: () => {
    return Vue.ajax.get(`${adminBase}/workbench/todo/business/list?type=1`)
  },
  getTodoList: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/business/todo/list`, { params })
  },
  getOperationData: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/report/audit`, { params })
  },
  getReportData: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/report/taskLoad`, { params })
  },
  getWaitReportData: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/report/taskwait`, { params })
  },
  getPersonalIO: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/report/throughput`, { params })
  }
}
