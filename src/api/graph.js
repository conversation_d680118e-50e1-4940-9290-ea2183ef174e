import Vue from 'vue'
import { adminBase } from './base'

// 回查是1
const busId = 1
export default {
  getTaskIoList: (params) => {
    return Vue.ajax.get(`${adminBase}/vt/aegis/dispatch/report/auditors`, {
      params: {
        bus_id: busId,
        ...params
      }
    })
  },
  getReportTaskList: (params) => {
    return Vue.ajax.get(`${adminBase}/vt/aegis/dispatch/report/task`, {
      params
    })
  },
  getDelayAssignReportList: (params) => {
    return Vue.ajax.get(`${adminBase}/vt/aegis/dispatch/report/specialTask`, {
      params: {
        bus_id: busId,
        ...params
      }
    })
  },
  getTaskWaitList: (params) => {
    return Vue.ajax.get(`${adminBase}/vt/aegis/dispatch/report/taskWait`, {
      params: {
        bus_id: busId,
        ...params
      }
    })
  },
  getVideoHandlingList: (params) => {
    return Vue.ajax.get(`${adminBase}/vt/aegis/dispatch/report/auditorsAll`, {
      params: {
        bus_id: busId,
        ...params
      }
    })
  }
}
