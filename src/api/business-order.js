import Vue from 'vue'
import { adminBase } from './base'

export default {
  getBrands: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/arc_conf/porder/brand/game`, { params })
  },
  getConfigs: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/arc_conf/porder/config`, { params })
  },
  getFlowTags: (params) => {
    return Vue.ajax.get(`${adminBase}/content-flow-control/group/list`, { params })
  },
  submitBusiness: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/archive/business/submit`, params)
  },
  getBrandList: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/archive/adorder/brand`, { params })
  }
}
