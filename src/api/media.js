import Vue from 'vue'
import { adminBase } from './base'

export default {
  getMediaTitle: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis-gateway/favorites/userfolders`, { params })
  },
  getResourceList: (params) => {
    return Vue.ajax.get(`${adminBase}/medialist/aegis/resource/list`, { params })
  },
  deleteResource: (params) => {
    return Vue.ajax.post(`${adminBase}/medialist/aegis/resource/delete`, params, {
      isJson: true
    })
  }
}
