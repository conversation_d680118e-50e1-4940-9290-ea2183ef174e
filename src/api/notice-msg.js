import Vue from 'vue'
import { adminBase } from './base'

// 获取待推送消息列表
export function getMessageUnreceived(params) {
  return Vue.ajax.get(`${adminBase}/aegis-gateway/msg-push/unreceived`, { params })
}
// 获取单个消息详情
export function getMessageDetail(params) {
  return Vue.ajax.get(`${adminBase}/aegis-gateway/msg-push/msg-detail`, { params })
}
// 消息处理的回调
export function setMessageCallback(params) {
  return Vue.ajax.post(`${adminBase}/aegis-gateway/msg-push/user-callback`, params, { isJson: true })
}

/**
 * @desc 查询消息条数
 * @param {list_type} number -1:查询两个字段, 0:全部, 1:未读
 * @returns {*}
 */
export function getMessageCounter (params) {
  return Vue.ajax.get(`${adminBase}/aegis-gateway/msg-push/unreceived-count`, { params })
}
/**
 * @desc 查询首页小心中心列表
 * @param {list_type} number -1:查询两个字段, 0:全部, 1:未读
 * @returns {*}
 */
export function getHomeMsgList (params) {
  return Vue.ajax.get(`${adminBase}/aegis-gateway/msg-push/user-center/list`, { params })
}
