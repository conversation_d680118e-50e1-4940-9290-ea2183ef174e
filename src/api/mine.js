import Vue from 'vue'
import { adminBase } from './base'

export default {
  // 新增操作项
  addOper: (params) => {
    return Vue.ajax.post(`${adminBase}/aegis/net/operation/add`, params)
  },
  //   编辑操作项
  editOper: (params) => {
    return Vue.ajax.post(`${adminBase}/aegis/net/operation/update`, params)
  },
  // 获取操作项列表
  getOperList: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis/net/operation`, { params })
  },
  // 新增节点
  addNode: (params) => {
    return Vue.ajax.post(`${adminBase}/aegis/net/flow/add`, params)
  },
  // 编辑节点
  editNode: (params) => {
    return Vue.ajax.post(`${adminBase}/aegis/net/flow/update`, params)
  },
  // 获取节点列表
  getNodeList: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis/net/flow/list`, { params })
  },
  // 新增节点绑定
  addBind: (params) => {
    return Vue.ajax.post(`${adminBase}/aegis/net/flow/operationBind`, params, {
      isJson: true
    })
  },
  // 编辑节点绑定
  editBind: (params) => {
    return Vue.ajax.post(
      `${adminBase}/aegis/net/flow/operationBind/update`,
      params,
      {
        isJson: true
      }
    )
  }
}
