import Vue from 'vue'

import { adminBase, templateBase } from './base'
import { cloneDeep } from 'lodash-es'

export default {
  getAuditActions: (params) => {
    return Vue.ajax.get(`${adminBase}/aegis/engine/resource/flow/detail`, { params })
  },
  getReason: async (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/reason/category`, { params })
  },
  getChargeReason: async (params) => {
    let formatRes = {}
    await Vue.ajax.get(`${adminBase}/videoup/reason/category`, { params }).then(res => {
      formatRes = res
      const { data } = res
      if (!data) {
        formatRes.data = []
        return formatRes
      }
      formatRes.data = (data || []).filter(reason => +reason.state !== 1)
    }).catch(_ => {})
    return formatRes
  },
  getChargeReasonOrigin: async (params) => {
    let data = {}
    await Vue.ajax.get(`${adminBase}/videoup/reason/category`, { params }).then(res => {
      data = cloneDeep(res.data)
      if (!data) {
        return res
      }
      data = {
        data: data.map((item) => {
          return {
            ...item,
            data: item.data.map((subItem) => {
              return {
                ...subItem,
                category_id: subItem.category_id.toString(),
                id: subItem.id.toString(),
                tag_id: subItem.tag_id.toString()
              }
            })
          }
        })
      }
    }).catch(_ => {})
    return data
  },

  getConstant: () => {
    return Vue.ajax.get(`${adminBase}/videoup/archive/common/data`)
  },
  getCrashInfo: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/archive/video/crash`, { params })
  },
  getCompetitorInfo: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/archive/video/watermark/competitor`, { params })
  },
  getInfo: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/archive/second/info`, { params })
  },
  getVideoSplit: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/archive/second/info/video/split`, { params })
  },
  getInfoKarlV2: (params) => {
    return Vue.ajax.get(`${templateBase}/karl/archive-info/v2`, {
      params
    })
  },
  getInfoKarlV3: (params) => {
    return Vue.ajax.get(`${templateBase}/karl/archive-info/v3`, {
      params
    })
  },
  getVideoshots: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/archive/video/shots`, { params })
  },
  getNewTagAudit: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/tag/audit`, { params })
  },
  getTagNote: async() => {
    try {
      const localData = window.localStorage.getItem('videoup-tag-note')
      let res
      if (!localData) {
        res = await Vue.ajax.get(`${adminBase}/videoup/tag/note`, {
          params: { need_description: true }
        })
        window.localStorage.setItem('videoup-tag-note', JSON.stringify(res))
      } else {
        res = JSON.parse(localData)
      }
      return new Promise((resolve) => {
        resolve(res)
      })
    } catch (e) {
     throw new Error(e)
    }
  },
  submitForbid: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/archive/flow/batch/submit`, params)
  },
  sendLimitNotify: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/archive/limit/notify/v2`, params)
  },
  getLimitNotify: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/archive/limit/notify/info`, { params })
  },
  getKvList: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/arc_conf/kv/list`, { params })
  },
  submitKvList: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/arc_conf/kv/submit`, params)
  },
  getV2PlayUrl: (params) => {
    return Vue.ajax.get('/v2/playurl', {
      params,
      handle: true
    })
  },
  checkSubmit: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/archive/check/submit`, params)
  },
  // http://bapi.bilibili.co/project/2797/interface/api/329301
  checkResult: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/archive/check/result`, { params })
  },
  getRedirectInfo: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/archive/redirect/info`, { params })
  },
  getPolicyGroups: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/archive/policy/groups`, { params })
  },
  submitRedirectInfo: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/archive/redirect/submit`, params)
  },
  updateCover: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/archive/cover/up`, params)
  },
  getReasonTags: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/reason/audit/tag`, { params })
  },
  getCrashAuditInfo: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/archive/video/crash/audit/info`, { params })
  },
  // 批量检测
  checkVideoIds: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/archive/video/check/submit`, params, {
      isJson: true
    })
  },
  getCommunityAiTag: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/archive/video/community/aiknowledge/v1`, { params })
  },
  getArchiveControl: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/archive-control/info`, { params })
  },
  updateArchiveControl: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/archive-control/submit`, params, {
      isJson: true
    })
  },
  // 获取分区是否可编辑
  checkArctypeV2Editable: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/management/archive/typeid/v2/options`, { params })
  },
  // 修改分区
  editArctypeV2: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/management/archive/typeid/v2/edit`, params)
  }
}
