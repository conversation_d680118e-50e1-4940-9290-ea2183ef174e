import { adminBase, qaBase } from './base'

import Vue from 'vue'

export default {
  taskLogin: (params) => {
    return Vue.ajax.post(`${adminBase}/workbench/task/consumer/on`, params)
  },
  taskLogout: (params) => {
    return Vue.ajax.post(`${adminBase}/workbench/task/consumer/off`, params)
  },
  forceOff: (params) => {
    return Vue.ajax.post(`${adminBase}/workbench/task/consumer/forceoff`, params)
  },
  getTask: (params, handle) => {
    return Vue.ajax.get(`${adminBase}/workbench/task/next`, { params, handle })
  },
  getTaskPush: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/task/next`, {
      params,
      handle: true
    })
  },
  jumpToTask: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/task/jump`, {params})
  },
  getUndoStat: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/task/undostat`, {params})
  },
  getTransTodo: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/todo/trans/list`, {params})
  },
  transferTask: (params) => {
    return Vue.ajax.post(`${adminBase}/workbench/task/trans`, params, {
      isJson: true
    })
  },
  batchTransferTask: (params) => {
    return Vue.ajax.post(`${adminBase}/workbench/task/batchtrans`, params, {
      isJson: true,
      handle: true
    })
  },
  transferResource: (params) => {
    return Vue.ajax.post(`${adminBase}/workbench/todo/resource/trans`, params, {
      isJson: true
    })
  },
  // 提交任务
  submitAuditTask: (params) => {
    return Vue.ajax.post(`${adminBase}/workbench/task/submit`, params, {
      isJson: true
    })
  },
  batchSubmitTask: (params) => {
    return Vue.ajax.post(`${adminBase}/workbench/task/batchsubmit`, params, {
      isJson: true
    })
  },
  // 提交资源
  submitAuditResource: (params) => {
    return Vue.ajax.post(`${adminBase}/workbench/todo/resource/submit`, params, {
      isJson: true
    })
  },
  delayTask: (params) => {
    return Vue.ajax.post(`${adminBase}/workbench/task/delay`, params)
  },
  getCopyright: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/search/copyright/v2`, params)
  },
  getPlayUrl: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/archive/video/playurl`, {
      params
    })
  },
  getFrame: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/archive/video/frame`, {params})
  },
  uploadFrame: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/archive/video/frame/add`, params)
  },
  deleteFrame: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/archive/video/frame/del`, params)
  },
  getV2Frame: (params) => {
    return Vue.ajax.get(`${adminBase}/videoup/archive/video/frame/v2/list`, {params})
  },
  addV2Frame: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/archive/video/frame/v2/add`, params)
  },
  deleteV2Frame: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/archive/video/frame/v2/del`, params)
  },
  updateV2Frame: (params) => {
    return Vue.ajax.post(`${adminBase}/videoup/archive/video/frame/v2/update`, params)
  },
  getList: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/task/list`, {params})
  },
  getResource: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/todo/resource/detail`, {params})
  },
  getTodoDetail: (params) => {
    return Vue.ajax.get(`${adminBase}/workbench/todo/edit/detail`, {params})
  },
  getChallengeList: (params) => {
    return Vue.ajax.get(`${adminBase}/workflow/v3/challenge/list`, {params})
  },
  /**
   * 稿件举报业务方接口
   * http://bapi.bilibili.co/project/3751/interface/api/469538
   */
  getReportStat: (params) => {
    return Vue.ajax.get(`${adminBase}/workflow/v3/group/detail_count`, { params })
  },
  getQaLog: (params) => {
    return Vue.ajax.get(`${qaBase}/qalog`, { params })
  },
  renewalTime: (params) => {
    return Vue.ajax.post(`${adminBase}/workbench/task/renewal`, params)
  },
  batchSubmitTaskV2: (params) => {
    return Vue.ajax.post(`${adminBase}/aegis-gateway/task/batchsubmit/v2`, params, {
      isJson: true
    })
  },
  batchSubmitResourceV2: (params) => {
    return Vue.ajax.post(`${adminBase}/aegis-gateway/todo/resource/batchsubmit/v2`, params, {
      isJson: true
    })
  }
}
