import Vue from 'vue'
import { isTypeObject } from '@/utils/type.js'
import store from '@/store'

export default Vue.directive('auth', {
  inserted: (el, binding) => {
    if (el && binding) {
      // 获取当前用户拥有的权限列表（根据自身业务获取）
      const access = store.state.user.perms || {}
      // some every
      let arg = binding.arg
      let value = binding.value
      // 如果为空直接返回
      if (value === undefined || arg === undefined) return
      // 如果是字符串或者不是数组
      // 2种情况：
      // 1.一种是template里的
      if (isTypeObject(binding.value)) {
        arg = binding.value.arg
        value = binding.value.value
      } else {
        // 2.一种是页面配置化的
        if (typeof value === 'string' || !Array.isArray(value)) {
          value = [value]
        }
      }
      if ((arg === 'some' && !value.some(v => access[v])) ||
        (arg === 'every' && !value.every(v => access[v]))
      ) {
        el.parentNode.removeChild(el)
      }
    }
  }
})
