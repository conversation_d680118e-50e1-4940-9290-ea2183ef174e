export default {
  bind(el, binding, vNode) {
    if (typeof binding.value !== 'function') {
      if (process.env.NODE_ENV === 'production') return
      const compName = vNode.context.name
      let warning = `${binding.name}="${binding.expression}" expects a function value`
      if (compName) {
        warning += `Found in component '${compName}'`
      }
      console.warn(warning)
    }
    el.__clickOutsideSet = true

    el.__clickOutsideHandler = (e) => {
      if (el.contains(e.target)) return null
      if (binding.modifiers.stop) {
        e.stopPropagation()
      }
      return binding.value(e)
    }

    document.addEventListener('click', el.__clickOutsideHandler, true)
  },

  unbind(el) {
    if (!el.__clickOutsideSet) return
    document.removeEventListener('click', el.__clickOutsideHandler, true)
    el.__clickOutsideHandler = null
  }
}
