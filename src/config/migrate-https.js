export function migrateHttps(to) {
  if (
    window.location.protocol === 'http:' &&
    process.env.NODE_ENV !== 'development'
  ) {
    if (isComic(to)) {
      const fullUrl = `${window.location.origin}/aegis/#${to.fullPath}`
      return replaceStr(fullUrl)
    }
  }
}

function isComic(to) {
  // 工作台 漫画举报 详情页
  // 工作台 单话审核 详情页
  return (
    to.path === '/v3/workbench/comic-report' ||
    (to.path === '/workbench/todo-config/detail' &&
      Number(to.query.business_id) === 2) ||
    to.path === '/audit/tasks/detail/2' ||
    to.path === '/audit/list/picture'
  )
}

function replaceStr(fullUrl) {
  return fullUrl.replace('http:', 'https:')
}
