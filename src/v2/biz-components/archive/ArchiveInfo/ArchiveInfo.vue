<template>
  <div class="archive-info">
    <AgFormItem
      label="标题："
      labelWidth="45px"
      :highlight="highlight.title"
      v-behavior-track="'audit-oper-title'"
    >
      <CopyrightHighlight
        :value="title"
        @input="(val) => $emit('update:title', val)"
        :keywordData="highlightKeyword.title"
        style="font-size: 14px"
        :disabled="disabled"
      />
    </AgFormItem>
    <AgFormItem label="UP对接方：" labelWidth="78px" type="flex-wrap">
      <AgFormItem :showSlot="true" style="margin-bottom: 0">
        <span>{{ arctypeV1 }}</span>
      </AgFormItem>
      <div style="display: flex" class="ml-12">
        <div
          class="archive-col"
          style="width: 100%; display: flex; align-items: center"
        >
          <div class="col-title">类型：</div>
          <el-radio-group
            :value="copyright"
            @input="handleInputCopyright"
            size="small"
            style="margin-right: 20px"
            :disabled="disabled"
            v-behavior-track="'audit-oper-copyright'"
          >
            <el-radio :label="1" class="mr-4">自制</el-radio>
            <el-radio :label="2" class="mr-4">转载</el-radio>
            <el-radio :label="0" disabled>未知</el-radio>
          </el-radio-group>
        </div>
      </div>
    </AgFormItem>
    <AgFormItem style="margin-left: 45px" type="flex-wrap" showSlot>
      <UpInfo :author="author" v-behavior-track="'author-link'"></UpInfo>
      <Qualification :profession="profession" />
    </AgFormItem>
    <AgFormItem label="发布：" labelWidth="45px" type="flex-wrap">
      <span v-behavior-track="'audit-oper-publishTime'">
        <el-date-picker
          :value="publishTime"
          @input="(val) => $emit('update:publishTime', val)"
          :disabled="!perms.PUBTIME || disabled"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetime"
          size="small"
          placeholder="选择日期"
          class="mr-4"
        ></el-date-picker>
      </span>
      <el-checkbox
        :value="delay"
        @input="(val) => $emit('update:delay', val)"
        size="small"
        class="mr-4"
        :true-label="1"
        :false-label="0"
        :disabled="!perms.DELAY || disabled"
        v-behavior-track="'audit-oper-delay'"
      >
        启用定时
      </el-checkbox>
      <span v-behavior-track="'audit-oper-delayTime'">
        <el-date-picker
          size="small"
          :value="delayTime"
          @input="(val) => $emit('update:delayTime', val)"
          :disabled="!perms.DELAY || disabled"
          type="datetime"
          value-format="yyyy-MM-dd HH:mm:ss"
          class="mr-4"
          placeholder="选择日期"
        ></el-date-picker>
      </span>

      <div>转载来源：</div>
      <el-input
        style="flex: 1"
        :value="source"
        @input="(val) => $emit('update:source', val)"
        size="small"
        :disabled="disabled"
        v-behavior-track="'audit-oper-source'"
      ></el-input>
    </AgFormItem>
    <AgFormItem label="标签：" labelWidth="45px" type="flex">
      <el-col :span="22">
        <SelectHighlight
          :value="tags"
          @input="(val) => $emit('update:tags', val)"
          multiple
          style="width: 95%"
          filterable
          allow-create
          default-first-option
          placeholder="请选择TAG"
          size="small"
          :disabled="disabled"
          :keywordList="highlightKeyword.tag"
          @keydown.enter.native.stop
          v-behavior-track="'audit-oper-tag'"
        >
          <el-option
            v-for="item in tagOptions"
            :key="item"
            :label="item"
            :value="item"
          ></el-option>
        </SelectHighlight>
      </el-col>
      <el-col :span="2">
        <el-button
          type="primary"
          @click="saveTags"
          style="float: right"
          size="small"
          :disabled="disableSaveBtn"
          v-behavior-track="'audit-tag-save'"
        >
          保存
        </el-button>
      </el-col>
    </AgFormItem>
    <AgFormItem
      label="简介："
      labelWidth="45px"
      type="flex"
      :highlight="highlight.content"
    >
      <RichInput
        type="textarea"
        :rows="5"
        :disabled="disabled"
        :uid="author.mid"
        :keywordList="highlightKeyword.desc"
        :value="content"
        @input="(val) => $emit('update:content', val)"
        v-behavior-track="'audit-oper-content'"
      ></RichInput>
    </AgFormItem>
    <AgFormItem
      label="动态："
      labelWidth="45px"
      type="flex"
      :highlight="highlight.dynamic"
    >
      <RichInput
        type="textarea"
        style="flex: 1"
        :rows="5"
        :disabled="disabled"
        :uid="author.mid"
        :keywordList="highlightKeyword.dynamic"
        :value="dynamic"
        @input="(val) => $emit('update:dynamic', val)"
        v-behavior-track="'audit-oper-dynamic'"
      ></RichInput>
    </AgFormItem>
    <AgFormItem
      v-if="channels.length > 0"
      label="频道："
      labelWidth="45px"
      type="flex"
    >
      <ChannelGroup :channels="channels"></ChannelGroup>
    </AgFormItem>
    <AgFormItem label="声明：" labelWidth="45px" type="flex">
      <el-input :value="neutralMark" disabled size="small">
        <i
          v-if="!disabled"
          slot="suffix"
          class="el-input__icon el-icon-close clear-neutral-mark-btn"
          @click="() => $emit('update:neutralMark', '')"
        />
      </el-input>
    </AgFormItem>
  </div>
</template>
<script>
/**
 * @component
 * @assetTitle 稿件信息模块
 * @assetDescription 展示稿件信息
 * @assetImportName ArchiveInfo
 * @assetTag 稿件业务组件
 */
import { mapState } from 'vuex'
import { detailApi } from '@/v2/api'
import ChannelGroup from '@/v2/pure-components/ChannelGroup/ChanneGroup.vue'
import {
  AgFormItem,
  RichInput,
  SelectHighlight
} from '@/v2/pure-components/ElementUpdate'
import UpInfo from '@/v2/biz-components/archive/UpInfo.vue'
import CopyrightHighlight from '@/v2/biz-components/archive/CopyrightHighlight'
import Qualification from '@/v2/biz-components/archive/Qualification'
import notify from '@/lib/notify'

export default {
  components: {
    ChannelGroup,
    UpInfo,
    AgFormItem,
    CopyrightHighlight,
    RichInput,
    SelectHighlight,
    Qualification
  },
  props: {
    // 展示类属性
    highlight: Object,
    highlightKeyword: Object,
    author: Object,
    channels: Array,
    aid: Number,
    listReview: Number,
    disabled: Boolean,
    tagOptions: Array,
    disableSaveTag: Boolean,
    arctypeV1: String,
    // 可编辑属性
    title: String,
    copyright: Number,
    publishTime: String,
    delay: Number,
    delayTime: String,
    source: String,
    tags: Array,
    content: Array,
    dynamic: Array,
    neutralMark: String,
    useMockSubmit: {
      type: Boolean,
      default: false
    },
    mockSubmitFn: Function
  },
  computed: {
    ...mapState({
      perms: (state) => state.user.perms
    }),
    disableSaveBtn() {
      if (this.useMockSubmit) return false
      if (this.disabled || this.disableSaveTag) return true
      return false
    },
    profession() {
      const profession = this.author?.profession || {}
      return profession.name || ''
    }
  },
  methods: {
    handleInputCopyright(val) {
      this.$emit('update:copyright', val)
      // 自制改成转载，需要去掉勾选“禁止转载”
      if (val === 2) {
        this.$emit('updateNoReprint', 0)
      }
    },
    saveTags() {
      if (this.useMockSubmit) {
        if (this.mockSubmitFn && typeof this.mockSubmitFn === 'function')
          return this.mockSubmitFn()
        return notify.success('tag保存成功')
      }
      // NOTE::频道回查列表 0 做特殊处理
      const params = {
        aid: this.aid,
        tags: this.tags.join(',')
      }

      if (this.listReview === 0) {
        params.channel_review = '1'
      }

      detailApi
        .saveTags(params)
        .then(() => {
          notify.success('tag保存成功')
        })
        .catch((e) => {})
    },
    keyDownHandler(event) {
      const keyCode = event.keyCode

      if (event.target.nodeName === 'TEXTAREA') {
        return
      }
      // q键盘提交tag，防止q快捷键input输入框
      if (event.target.nodeName === 'INPUT') {
        return
      }
      if (keyCode === 81) {
        this.saveTags()
      }
    }
  },
  created() {
    document.addEventListener('keydown', this.keyDownHandler)
  },
  beforeDestroy() {
    document.removeEventListener('keydown', this.keyDownHandler)
  }
}
</script>
<style lang="stylus">
.archive-info
  .el-tag--info
    background var(--blue) !important
    color var(--content-bg-color) !important
  .el-tag__close
    color var(--blue-dark-1) !important
    font-size 14px !important
    margin-right 4px !important
    background: var(--blue) !important
  .el-radio__label
    padding-left 2px !important
  .el-checkbox__label
    padding-left 2px
  .up-icon
    vertical-align text-top
</style>
<style lang="stylus" scoped>
.archive-info
  &__fans
    margin-left auto
    &-label
      font-weight 400
  .el-form-item
    margin-bottom 10px !important
  .clear-neutral-mark-btn
    cursor pointer

>>> .hightlght-input-disabled {
  color: var(--text-color-light)
}

>>> .el-input.is-disabled .el-input__inner {
  color: var(--text-color-light)
}

>>> .el-radio__input.is-disabled+span.el-radio__label {
  color: var(--text-color-light)
}

>>> .el-radio__input.is-disabled.is-checked .el-radio__inner:after {
  background-color: #606266
}
</style>
