<template>
  <div>
    <ArchiveOperAlert
      :initArchiveState="initArchiveState"
      :state="state"
      :listType="listType"
      :flowName="flowName"
    />
    <TagModeration
      ref="tagModerationRef"
      :operTags="operTags"
      :sendnotify="sendnotify"
      :aid="aid"
      :getExtraDataFn="getExtraDataFn"
      :workbenchItem="workbenchItem"
      :listType="listType"
      :listReview="listReview"
      @update:sendnotify="relayUpdateSendnotify"
      @update:state="relayUpdateState"
      @update:operTagInfo="relayUpdateOperTagInfo"
    />
    <!-- 备注标 -->
    <StructuredNote
      ref="noteTagRef"
      :aid="aid"
      :auditTags="auditTags"
      :disabled="disabled"
      @update:auditTags="relayUpdateAuditTags"
      @update:noteTagReason="relayNoteTagReasonChange"
    />
    <!-- 灰标 -->
    <GrayTag
      :aid="aid"
      :grayTags="grayTags"
      :disbaled="disabled"
      @update:grayTags="relayUpdateGrayTags"
      @update:grayTagReason="relayGrayTagReasonChange"
    />
  </div>
</template>
<script>
import { mapState } from 'vuex'
import TagModeration from './TagModeration.vue'
import StructuredNote from './StructuredNote.vue'
import GrayTag from './GrayTag.vue'
import { safeJsonParse } from '@/v2/utils'
import ArchiveOperAlert from './ArchiveOperAlert.vue'
import { validateAllLimitReasons } from '@/v2/biz-utils/validateLimitReason'

export default {
  name: 'AuditOperationV2',
  components: {
    ArchiveOperAlert,
    GrayTag,
    StructuredNote,
    TagModeration
  },
  props: {
    aid: {
      type: Number,
      default: 0
    },
    auditTags: {
      // 备注标
      type: Array,
      default: () => []
    },
    attrForm: {
      type: Object,
      default: () => {}
    },
    disabled: {
      type: Boolean,
      default: false
    },
    getExtraDataFn: Function,
    grayTags: {
      type: Array,
      default: () => []
    },
    flowName: {
      type: String,
      default: ''
    },
    initArchiveState: {
      type: String
    },
    listReview: {
      type: [Number, String],
      default: ''
    },
    listType: {
      type: String,
      default: ''
    },
    operTags: {
      // 操作标签
      type: Object,
      default: () => {}
    },
    sendnotify: {
      type: Number,
      default: 0,
      validator: (value) => [0, 1].includes(value)
    },
    state: {
      type: [Number, String],
      default: ''
    },
    workbenchItem: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    ...mapState({
      perms: (state) => state.user.perms
    })
  },
  data() {
    return {
      grayTagReason: [],
      noteTagReason: [],
      selectedGrayTagNodes: []
    }
  },
  mounted() {},
  methods: {
    relayUpdateAuditTags(newVal) {
      this.$emit('update:auditTags', newVal)
    },
    relayUpdateGrayTags(newVal) {
      this.$emit('update:grayTags', newVal)
    },
    relayUpdateSendnotify(newVal) {
      this.$emit('update:sendnotify', newVal)
    },
    relayUpdateState(newVal) {
      this.$emit('update:state', newVal)
    },
    relayUpdateOperTagInfo(newVal) {
      this.$emit('update:operTagInfo', newVal)
      const newMultipleReason = (newVal?.incremental_tag_rules || []).map(
        (tagRule) => ({
          reason: tagRule.reason || '',
          reason_id: tagRule.reason_id,
          reason_tag: tagRule.reasonLabel, // 全链路标签名称
          reason_tag_id: tagRule.tag_id, // 叶子标签id
          submit_state: `${tagRule.bind_rsc_state}`,
          picture: safeJsonParse(tagRule.picture, [])
        })
      )
      this.$emit('update:multipleReason', newMultipleReason)
    },
    relayGrayTagReasonChange(newVal) {
      this.grayTagReason = newVal
      this.$emit('update:grayTagReason', newVal)
    },
    relayNoteTagReasonChange(newVal) {
      this.noteTagReason = newVal
      this.$emit('update:noteTagReason', newVal)
    },
    validateNote() {
      return this.$refs.noteTagRef.validateNote()
    },
    onConsistencyError(msg) {
      this.$alert(msg, `请检查【备注标】和【灰标】限流理由`)
    },
    validateReason() {
      // 跨标签业务理由一致性校验 并用新理由覆盖旧理由
      validateAllLimitReasons(
        this.noteTagReason,
        this.grayTagReason,
        this.onConsistencyError
      )
      let msg = ''
      let msgA = ''
      let msgB = ''
      if (this.state === '-2' || this.state === '-4') {
        msg = this.$refs.auditReasonStruct.validateReason()
        msgA = this.$refs.noteTagReasonRef?.validateReason() || ''
        msgB = this.$refs.grayTagReasonRef?.validateReason() || ''
      } else {
        msgA = this.$refs.noteTagReasonRef?.validateReason() || ''
        msgB = this.$refs.grayTagReasonRef?.validateReason() || ''
      }
      const fullMsg = `${msg ? '【打锁理由】' : ''}${msg}${
        msgA ? '【备注标】限流理由' : ''
      }${msgA}${msgB ? '【灰标】限流理由' : ''}${msgB}`
      return fullMsg
    }
  }
}
</script>
<style lang="stylus" scoped></style>
