<template>
  <!-- 稿件信息-基础编辑 -->
  <div style="min-height: 800px">
    <div class="flex-row">
      <ArchiveInfo
        v-bind.sync="innerArchive"
        :disableSaveTag="disableSaveTag || coverTagClipDisabled"
        :tagOptions="tagOptions"
        :listReview="listReview"
        :disabled="false"
        :useMockSubmit="enableRedBlue"
        :mockSubmitFn="
          () => {
            $emit('mockSubmitFn')
          }
        "
        @updateNoReprint="(e) => $emit('updateNoReprint', e)"
        class="flex-1"
      />

      <div style="margin-left: 80px" class="flex-1">
        <ArchiveCover
          :bvid="archive.bvid"
          :listType="listType"
          :fullCover.sync="innerArchiveCover.fullCover"
          :fullCoverV2="innerArchiveCover.fullCoverV2"
          :highlightCover="innerArchiveCover.highlightCover"
          :disabled="coverTagClipDisabled"
          :coverAiTags="innerArchiveCover.coverAiTags"
          :coverAiTagsV2="innerArchiveCover.coverAiTagsV2"
        />
        <ArchiveSource
          :arctypeV2="innerArchive.arctypeV2"
          :upFrom="innerArchive.upFrom"
          :material="innerArchive.materialTemplate"
        />
      </div>
    </div>

    <div class="mt-4">
      <ClipList
        :aid="aid"
        :bvid="innerArchive.bvid"
        :showVideoToggle="innerVideo.showVideoToggle"
        :sourceFile="innerVideo.sourceFile"
        :videos.sync="innerVideo.clips"
        :disabled="false"
        :videoTitleHighlightKeywords="videoTitleHighlightKeywords"
        :listType="listType"
        :listReview="listReview"
        :clipNumber="innerVideo.clipNumber"
        :highlightArea="innerArchive.highlightArea"
        @show-video-preview="showPreviewVideo"
        @hide-video-preview="hidePreviewVideo"
      />
    </div>

    <div class="flex-c" style="margin-top: 20px">
      <el-button @click="reset">重置</el-button>
      <el-button type="primary" @click="save">保存</el-button>
    </div>

    <!-- 确认弹窗 -->
    <Suppress
      v-if="suppressDialogShow"
      :visible="suppressDialogShow"
      @close="handleCloseSuppress"
      @confirm="handleConfirmSuppress"
    />
  </div>
</template>

<script>
import ArchiveInfo from '@/v2/biz-components/archive/ArchiveInfo/ArchiveInfo.vue'
import ArchiveCover from '@/v2/biz-components/archive/ArchiveCover'
import ArchiveSource from '@/v2/biz-components/archive/ArchiveSource'
import notify from '@/lib/notify'
import archiveApi from '@/api/archive'
import {
  genArchive,
  genArchiveCover,
  genVideo
} from '@/v2/data-source/archive/info/adapter'
import ClipList from '@/v2/biz-components/archive/ClipList'
import { richToRawText, richToServiceV2 } from '@/utils/rich'
import moment from 'moment'
import Suppress from '@/components/Common/Suppress'

export default {
  components: {
    ArchiveInfo,
    ArchiveCover,
    ArchiveSource,
    ClipList,
    Suppress
  },
  props: {
    aid: Number,
    archive: {
      type: Object,
      default: () => ({})
    },
    video: {
      type: Object,
      default: () => ({})
    },
    archiveCover: {
      type: Object,
      default: () => ({})
    },
    listReview: {
      type: Number,
      default: -1
    },
    disableSaveTag: Boolean,
    coverTagClipDisabled: Boolean,
    tagOptions: {
      type: Array,
      default: () => []
    },
    enableRedBlue: Boolean,
    listType: {
      type: String,
      default: '00'
    }
  },
  data() {
    return {
      innerArchive: this.archive || {},
      innerArchiveCover: this.archiveCover || {},
      innerVideo: this.video || {},
      // 重新压制视频的弹窗
      suppressDialogShow: false,
      xcode2: undefined,
      archive_info: null
    }
  },
  watch: {
    aid() {
      // aid改变则重新获取稿件详情
      this.getArchiveDetail()
    }
  },
  created() {
    this.getArchiveDetail()
  },
  computed: {
    videoTitleHighlightKeywords() {
      return this.innerArchive?.highlightKeyword?.video_title || []
    }
  },
  methods: {
    handleCloseSuppress() {
      this.suppressDialogShow = false
      this.xcode2 = undefined
    },
    handleConfirmSuppress(val) {
      this.suppressDialogShow = false
      if (val === 1) {
        this.xcode2 = 1
      } else {
        this.xcode2 = undefined
      }
      // 跳过校验
      this.save({ skip: true })
    },
    save({ skip }) {
      if (!this.innerArchive?.aid || !this.innerArchiveCover) {
        notify.error('出错了，请重试')
        return
      }

      // 如果需要二转弹窗确认
      // 从自制变成转载
      if (
        this.archive_info?.archive?.copyright === 1 &&
        this.innerArchive.copyright === 2 &&
        !skip
      ) {
        this.suppressDialogShow = true
        return
      }
      archiveApi
        .modifyArchiveInfo({
          mtime: moment(this.innerArchive.mtime, 'YYYY-MM-DD HH:mm:ss')
            .utcOffset('+08:00')
            .unix(),
          aid: this.innerArchive.aid,
          title: this.innerArchive.title,
          cover: this.innerArchiveCover.fullCover,
          ptime: moment(this.innerArchive.publishTime, 'YYYY-MM-DD HH:mm:ss')
            .utcOffset('+08:00')
            .unix(),
          dtime: moment(this.innerArchive.delayTime, 'YYYY-MM-DD HH:mm:ss')
            .utcOffset('+08:00')
            .unix(),
          delay: !!this.innerArchive.delay,
          copyright: this.innerArchive.copyright,
          source: this.innerArchive.source,
          xcode2: !!this.xcode2, // 重新转码
          desc_format_id: this.innerArchive.descFormatId,
          desc: richToRawText(this.innerArchive.content),
          desc_v2: richToServiceV2(this.innerArchive.content),
          dynamic: richToRawText(this.innerArchive.dynamic),
          dynamic_v2: richToServiceV2(this.innerArchive.dynamic)
        })
        .then(() => {
          notify.success('提交成功')
          this.getArchiveDetail()
        })
        .catch(() => {
          notify.error('提交失败，请重试')
        })
    },
    getArchiveDetail() {
      this.xcode2 = undefined
      archiveApi
        .getInfo({
          aid: this.aid,
          from_review:
            isNaN(this.listReview) || this.listReview === -1
              ? ''
              : this.listReview
        })
        .then((res) => {
          const rawArchiveInfo = res?.data
          if (!rawArchiveInfo) {
            return
          }

          this.archive_info = rawArchiveInfo // 备份
          this.reset()
          this.triggerChange() // 将库里的数据同步给父页面
        })
    },
    reset() {
      this.innerArchive = genArchive(this.archive_info)
      this.innerArchiveCover = genArchiveCover(this.archive_info)
      this.innerVideo = genVideo(this.archive_info)
      this.xcode2 = undefined
    },
    // 同步给父页面，纯展示用
    triggerChange() {
      this.$emit('update:archive', this.innerArchive)
      this.$emit('update:archiveCover', this.innerArchiveCover)
      this.$emit('update:video', this.innerVideo)
    },
    // 复用父页面的视频预览弹窗
    showPreviewVideo(payload) {
      this.$emit('showPreviewVideo', payload)
    },
    hidePreviewVideo() {
      this.$emit('hidePreviewVideo')
    }
  }
}
</script>

<style scoped></style>
