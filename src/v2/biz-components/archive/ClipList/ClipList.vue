<template>
  <div class="clip-list" id="clip-list">
    <!-- 超过50p虚拟滚动 -->
    <VirtualScroll
      ref="vsRef"
      :listData="largeVideos"
      v-if="needVS"
      :activeClipIndex="activeClipIndex"
    >
      <template v-slot:table-header>
        <ListHeader
          :showDragTip="!!aid"
          :columnWidth="columnWidth"
          :enableAiInfo="enableAiInfo"
        />
      </template>
      <template v-slot:table-body="slotProps">
        <ClipItem
          ref="clipItemRefs"
          :class="tableRowClassName(slotProps.item, slotProps.item.idx)"
          :aid="aid"
          :columnWidth="columnWidth"
          :copyrightFocusWidth="copyrightFocusWidth"
          :disabled="disabled"
          :disabledMoreOper="disabledMoreOper"
          :item="slotProps.item"
          :videoTitleHighlightKeywords="videoTitleHighlightKeywords"
          :enableTagsAndMonitor="enableTagsAndMonitor"
          :enableAiInfo="enableAiInfo"
          @showClipPreview="showClipPreview"
          @showVideoPreview="showVideoPreview"
          @clickMenu="handleMenu"
        />
      </template>
    </VirtualScroll>
    <!-- 不超过普通展示 -->
    <div class="table-div" ref="tableSubDiv" v-else>
      <ListHeader
        :showDragTip="!!aid && !showPager"
        :columnWidth="columnWidth"
        :enableAiInfo="enableAiInfo"
      />
      <div
        class="table-body table-body-overflow"
        :class="{
          'table-body-with-pager': showPager
        }"
      >
        <ClipItem
          ref="clipItemRefs"
          v-for="(item, idx) in paginatedVideos"
          :key="item.id"
          :class="tableRowClassName(item, idx)"
          :aid="aid"
          :columnWidth="columnWidth"
          :copyrightFocusWidth="copyrightFocusWidth"
          :disabled="disabled"
          :disabledMoreOper="disabledMoreOper"
          :item="item"
          :videoTitleHighlightKeywords="videoTitleHighlightKeywords"
          :enableTagsAndMonitor="enableTagsAndMonitor"
          :enableAiInfo="enableAiInfo"
          @showClipPreview="showClipPreview"
          @showVideoPreview="showVideoPreview"
          @clickMenu="handleMenu"
        />
      </div>
    </div>
    <div v-if="showPager" class="flex-lr mt-4">
      <div class="w-480">
        <el-alert
          title="100P以上的稿件，若需调整分P顺序，请联系@审核平台小姐姐"
          type="warning"
          show-icon
          :closable="false"
        />
      </div>
      <el-pagination
        layout="total, prev, pager, next, jumper"
        :current-page.sync="currentPage"
        :page-size="100"
        :total="clipNumber"
        @current-change="handleCurrentChange"
      />
    </div>
    <!-- 下载弹窗 -->
    <el-dialog
      title="获取视频"
      :visible.sync="showDownloadModal"
      width="20%"
      :destroy-on-close="true"
    >
      <el-button type="primary" size="small" @click="handleDownload">
        下载
      </el-button>
    </el-dialog>
    <!-- 编辑外链地址弹窗  -->
    <el-dialog
      title="编辑外链地址"
      :visible.sync="showWeblinkModal"
      :destroy-on-close="true"
    >
      <el-form inline>
        <el-row>
          <el-form-item label="weblink">
            <el-input placeholder="" v-model="weblinkForm.weblink"></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item>
            <el-button type="primary" size="small" @click="saveWeblink">
              保存
            </el-button>
          </el-form-item>
        </el-row>
      </el-form>
    </el-dialog>
    <!-- 弹窗 -->
    <!-- 切片弹窗 -->
    <ClipPreviewModal
      ref="cpmodalRef"
      v-if="showClipPreviewModal"
      :clip="clipPreviewData"
      :videos="paginatedVideos"
      :cid="clipPreviewData.cid"
      :pIndex="clipPreviewData.pIndex"
      :aid="aid"
      @change-clip-index="(newClipIndex) => (activeClipIndex = newClipIndex)"
      @close="closeClipPreview"
      @modal-opened="clipModalOpened"
      @modal-closed="clipModalClosed"
    >
      <CrashInfo
        :cid="clipPreviewData.cid"
        :disableRedirect="clipPreviewData.disableRedirectInPreview"
      ></CrashInfo>
    </ClipPreviewModal>

    <el-dialog
      v-if="enableTagsAndMonitor"
      title="监控细标"
      :visible.sync="tagsAndMonitorVisible"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      @closed="
        () => {
          tagsAndMonitorItem = null
        }
      "
    >
      <TagsAndMonitor
        v-if="tagsAndMonitorVisible"
        :item="tagsAndMonitorItem"
        :aid="aid"
        :open0Tag="false"
        :disabled="false"
        @cancel="
          () => {
            tagsAndMonitorVisible = false
            tagsAndMonitorItem = null
          }
        "
        @afterSubmit="afterSubmitTagsAndMonitor"
      ></TagsAndMonitor>
    </el-dialog>
  </div>
</template>
<script>
/**
 * @component
 * @assetTitle 稿件视频P模块
 * @assetDescription 展示视频多P列表
 * @assetImportName ClipList
 * @assetTag 稿件业务组件
 */
import moment from 'moment'
import { mapState, mapGetters } from 'vuex'
import Sortable from 'sortablejs'
import { detailApi, videoApi, archiveTaskApi, archiveApi } from '@/v2/api'
import { STATES } from '@/v2/data-source/config/local/constant'
import {
  genVideo,
  genVideoLink,
  getAttrAndNote,
  genVideosCompetitor
} from '@/v2/data-source/archive/info/adapter'
import { genHost, rewriteUrlToHttps } from '@/v2/utils'
import VirtualScroll from '@/v2/pure-components/VirtualScroll'
import CrashInfo from '@/v2/biz-components/archive/CrashInfo'
import ClipPreviewModal from '@/v2/biz-components/archive/ClipPreviewModal'
import ListHeader from './ListHeader'
import ClipItem from './ClipItem'
import notify from '@/lib/notify'
import TagsAndMonitor from './TagsAndMonitor.vue'

export default {
  data() {
    return {
      currentPage: 0,
      downloadUrl: '',
      showDownloadModal: false,
      showWeblinkModal: false,
      tagsAndMonitorVisible: false, // 监控细标弹窗显隐
      showClipPreviewModal: false,
      weblinkForm: { id: '', weblink: '' },
      STATE_COLOR: {
        '-30': 'blue',
        '-1': 'blue',
        '-2': 'red',
        '-4': 'red',
        '-16': 'red',
        '-100': 'red',
        0: 'green',
        10000: 'green'
      },
      STATES,
      clipPreviewData: {
        pIndex: 0,
        cid: 0,
        filename: '',
        disableRedirectInPreview: false
      },
      activeClipIndex: -1,
      disabledEpShow: false,
      hasSorted: false,
      columnWidth: {
        P序: {
          width: 60,
          res: 60
        },
        cid: {
          width: 130,
          res: 130
        },
        // 待分配
        filename: {
          flex: 1,
          res: 0
        },
        AI提示: this.enableAiInfo ? { flex: 1 } : { width: 0 },
        转码状态: {
          width: 130,
          res: 130
        },
        类型: {
          width: 85,
          res: 85
        },
        // 待分配
        分P标题: {
          flex: 1
        },
        // 待分配
        '一审结果&建议': {
          flex: 1,
          res: 0,
          minWidth: 120
        },
        一审时间: {
          width: 95,
          res: 95
        },
        一审操作人: {
          width: 90,
          res: 90
        },
        视频操作: {
          width: 140,
          res: 140
        }
      },
      copyrightFocusWidth: null,
      needVirtualScrollLimit: 5,
      oldVideoItem: {},
      newVideoItem: {},
      largeVideos: [],
      sortableObj: null,
      unwatch: null,
      videoId: '',
      paginatedVideos: [],
      tagsAndMonitorItem: null
    }
  },
  props: {
    aid: Number,
    bvid: String,
    clipNumber: {
      type: Number,
      default: -1
    },
    videos: {
      type: Array,
      default() {
        return []
      }
    },
    sourceFile: String,
    disabled: {
      type: Boolean,
      default: false
    },
    isCategory: Boolean,
    disabledMoreOper: {
      type: Boolean,
      default: false
    },
    videoTitleHighlightKeywords: Array,
    listType: String,
    listReview: Number,
    highlightArea: {
      type: Array,
      default: () => []
    },
    useMockSave: {
      type: Boolean,
      default: false
    },
    mockSaveFn: Function,
    useMockDelete: {
      type: Boolean,
      default: false
    },
    mockDeleteFn: Function,
    useMockWeblink: {
      type: Boolean,
      default: false
    },
    mockWeblinkFn: Function,
    enableTagsAndMonitor: Boolean, // 支持细标监控操作
    enableAiInfo: Boolean // AI提示
  },
  components: {
    ClipPreviewModal,
    ClipItem,
    CrashInfo,
    ListHeader,
    VirtualScroll,
    TagsAndMonitor
  },
  watch: {
    aid: {
      handler(val) {
        this.closeVideoPreview()
        this.closeClipPreview()
      },
      immediate: true
    },
    videos: {
      handler(val) {
        this.paginatedVideos = val
        this.updateVisibleClipList(this.paginatedVideos)
      },
      immediate: true
    }
  },
  computed: {
    ...mapGetters({
      getEnv: 'env/getEnv'
    }),
    ...mapState({
      collapseMenu: (state) => state.menu.collapseMenu
    }),
    videoInfoUrl() {
      return `${genHost()}/aegis/#/archive/archive-video-task/resource/detail?cid=`
    },
    videoPlaylist() {
      return this.videos.map((e) => ({ page: e.index, cid: e.cid }))
    },
    needVS() {
      const { videos, needVirtualScrollLimit } = this
      // videos有变化时，重设largeVideos
      this.setLargeVideos()
      return (
        videos && videos.length >= needVirtualScrollLimit && !this.showPager
      )
    },
    showClipAiRisk() {
      // 可以展示分P ai 风险提示的 listType
      // 31 常规二审 34 机密回查 39 特殊机密二审
      const canShowAiRisk = ['31', '34', '39']
      if (this.listType && canShowAiRisk.includes(this.listType)) return true
      else return false
    },
    showPager() {
      return this.clipNumber > 99
    }
  },
  methods: {
    // 监控细标小提交
    afterSubmitTagsAndMonitor() {
      this.tagsAndMonitorVisible = false
      this.tagsAndMonitorItem = null
      this.handleCurrentChange(this.currentPage || 1)
    },
    async handleCurrentChange(pn) {
      const serverPn = pn - 1
      try {
        const extraParams = {}
        if (this.listReview !== -1) {
          extraParams.from_review = this.listReview
        }
        const res = await archiveApi.getVideoSplit({
          aid: this.aid,
          area: (this.highlightArea || []).join(','),
          ps: 100,
          pn: serverPn,
          ...extraParams
        })
        const video = genVideo(res.data, {}, { env: this.getEnv() })
        const newVideosCompetitor = genVideosCompetitor(res.data)
        this.paginatedVideos = video.clips || []
        this.$emit('updateVideosCompetitor', newVideosCompetitor)
        this.updateVisibleClipList(this.paginatedVideos)
      } catch (err) {
        console.error(err)
      }
    },
    updateVisibleClipList(paginatedVideos) {
      this.$emit(
        'updateVisibleClipList',
        paginatedVideos.map((e) => e.cid)
      )
    },
    resizeLayout() {
      this.$nextTick(() => {
        this.resizeTable()
        this.setLargeVideos()
      })
    },
    setLargeVideos() {
      this.largeVideos =
        this.videos &&
        this.videos.map((item, idx) => {
          item.idx = item.index - 1
          return item
        })
    },
    resizeTable() {
      let tableDivEl = null
      if (this.needVS) {
        tableDivEl = this.$refs.vsRef.$refs.tableSubDiv
      } else {
        tableDivEl = this.$refs.tableSubDiv
      }
      let totalWidth = tableDivEl.clientWidth
      const el = document.getElementById('clip-list')
      if (el) {
        const parentEl = el.parentElement
        totalWidth = parentEl.clientWidth - 16
      }
      let occupyWidth = 0
      const calcCols = []
      let flexCount = 0
      for (const col in this.columnWidth) {
        // 如果没有设置minWidth，即设置的固定宽度
        if (!this.columnWidth[col].flex && !this.columnWidth[col].minWidth) {
          const maxWidth = this.columnWidth[col].width || 0
          this.columnWidth[col].res = this.columnWidth[col].width || 0
          occupyWidth += maxWidth
        } else {
          // 放入计算池
          calcCols.push({
            col,
            flex: this.columnWidth[col].flex || 1,
            minWidth: this.columnWidth[col].minWidth
          })
          flexCount += this.columnWidth[col].flex
        }
      }
      // 分配策略：优先分配minWidth，剩余的宽度再分配给flex
      const leftWidth = totalWidth - occupyWidth
      const onePieceWidth = leftWidth / (flexCount || 1)
      calcCols.forEach((item) => {
        this.columnWidth[item.col].res = onePieceWidth * (item.flex || 1)
      })
      this.$nextTick(() => {
        if (this.$refs.clipItemRefs && this.$refs.clipItemRefs[0]) {
          this.copyrightFocusWidth =
            this.$refs.clipItemRefs[0].getCopyrightFocusWidth()
        }
      })
    },
    reflowCopyrightFocus() {
      const elArr = this.$refs.copyrightFocusRef
      if (elArr) {
        elArr.forEach((item) => {
          item && item.reJudegeOverLine()
        })
      }
    },
    initSort() {
      if (this.disabled) return
      const table = document.querySelector('.table-body')
      this.sortableObj = Sortable.create(table, {
        handle: '.my-handle',
        onStart: (data) => {
          // 为虚拟滚动的swap做准备
          if (this.needVS) {
            const idx = data.oldIndex
            const renderList = this.$refs.vsRef.renderList
            const trueIdx = renderList[idx - 1].idx
            this.oldVideoItem = {
              arrIdx: trueIdx,
              data: this.videos[trueIdx]
            }
          }
        },
        onEnd: (data) => {
          if (this.needVS) {
            return this.sortVirtualEnd(data)
          }
          let { newIndex, oldIndex } = data
          if (newIndex === oldIndex) return
          const newList = this.videos
          // 数组的move
          // https://github.com/brownieboy/array.prototype.move/blob/master/src/array-prototype-move.js
          if (newList.length === 0) {
            return newList
          }
          while (oldIndex < 0) {
            oldIndex += newList.length
          }
          while (newIndex < 0) {
            newIndex += newList.length
          }
          if (newIndex >= newList.length) {
            let k = newIndex - newList.length
            while (k-- + 1) {
              newList.push(undefined)
            }
          }
          newList.splice(newIndex, 0, newList.splice(oldIndex, 1)[0])
          this.listSortChange(newList)
        }
      })
    },
    sortVirtualEnd(data) {
      const { newIndex } = data
      const renderList = this.$refs.vsRef.renderList
      let trueNewIdx = renderList[newIndex - 1].idx
      this.newVideoItem = {
        arrIdx: trueNewIdx,
        data: this.videos[trueNewIdx]
      }
      if (trueNewIdx === this.oldVideoItem.arrIdx) {
        return
      }
      let oldIndex = this.oldVideoItem.arrIdx
      const newList = this.videos
      // 数组的move
      // https://github.com/brownieboy/array.prototype.move/blob/master/src/array-prototype-move.js
      if (newList.length === 0) {
        return newList
      }
      while (oldIndex < 0) {
        oldIndex += newList.length
      }
      while (trueNewIdx < 0) {
        trueNewIdx += newList.length
      }
      if (trueNewIdx >= newList.length) {
        let k = trueNewIdx - newList.length
        while (k-- + 1) {
          newList.push(undefined)
        }
      }
      newList.splice(trueNewIdx, 0, newList.splice(oldIndex, 1)[0])
      this.listSortChange(newList)
    },
    tableRowClassName(row, idx) {
      const highLightFlag =
        row.highlight ||
        (this.showClipAiRisk &&
          row.aiRiskHint &&
          row.aiRiskHint?.aiTag !== '无审核提示')

      const activeFlag = idx === this.activeClipIndex
      return `${highLightFlag ? 'table-row__highlight' : ''} ${
        activeFlag ? 'table-row__active' : ''
      }`
    },
    clipModalOpened() {
      document.removeEventListener('keydown', this.keyDownHandler)
    },
    clipModalClosed() {
      document.removeEventListener('keydown', this.keyDownHandler)
      document.addEventListener('keydown', this.keyDownHandler)
    },
    // 显示下载弹窗
    showDownload(data) {
      const { cid } = data
      videoApi
        .getDownload({
          cid
        })
        .then((res) => {
          this.downloadUrl = res.data && res.data[0] && res.data[0].url
          this.showDownloadModal = true
        })
        .catch((_) => {})
    },
    // 去下载
    handleDownload() {
      window.open(rewriteUrlToHttps(this.downloadUrl), '_blank')
    },
    // 格式化video
    formatVideos(videos) {
      return videos.map((item, index) => {
        return {
          // 详细字段描述看cliplist.js文件
          index: item.index_order,
          id: item.id,
          cid: item.cid,
          filename: item.filename,
          status: item.status,
          xcode: item.xcode_state,
          srcType: item.src_type,
          eptitle: item.eptitle,
          description: item.description,
          tag: item.tag_desc,
          ...getAttrAndNote(item.attribute, item.note),
          reason: item.reason,
          passtime: item.pass_time
            ? moment(item.pass_time * 1000).format('YYYY-MM-DD HH:mm:ss')
            : '',
          oname: item.oname,
          weblink: item.web_link,
          link: genVideoLink(index, this.bvid)
        }
      })
    },
    async reloadVideoList() {
      await videoApi
        .getVideos({
          aid: this.aid
        })
        .then((res) => {
          const { videos } = res.data || {}
          // 更新数据
          const newVideos = this.formatVideos(videos)

          this.$emit('update:videos', newVideos)
          this.$forceUpdate()
        })
        .catch((_) => {})
    },
    // 去保存
    confirmSave(item) {
      if (this.useMockSave) {
        if (this.mockSaveFn && typeof this.mockSaveFn === 'function') {
          return this.mockSaveFn()
        }
      }
      this.$confirm('确认保存修改?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          this.pendingRequest = true
          const newItem = {
            cid: item.cid,
            aid: this.aid,
            eptitle: item.eptitle,
            description: item.description
          }
          await videoApi
            .editVideo(newItem)
            .then(() => {
              notify.success('修改成功')
              this.reloadVideoList()
            })
            .catch((_) => {})
        })
        .catch((_) => {})
        .finally(() => {
          this.pendingRequest = false
        })
    },
    // 删除
    confirmDelete(data) {
      if (this.useMockDelete) {
        if (this.mockDeleteFn && typeof this.mockDeleteFn === 'function') {
          return this.mockDeleteFn()
        }
      }
      const { cid } = data
      this.$confirm('确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          this.pendingRequest = true
          await videoApi
            .delVideo({
              cid,
              aid: this.aid
            })
            .then(() => {
              notify.success('删除成功')
              this.reloadVideoList()
            })
            .catch((_) => {})
          this.pendingRequest = true
        })
        .catch(() => {
          this.pendingRequest = false
        })
    },
    showWeblink(item) {
      if (this.useMockWeblink) {
        if (this.mockWeblinkFn && typeof this.mockWeblinkFn === 'function') {
          return this.mockWeblinkFn()
        }
      }
      this.weblinkForm = {
        cid: item.cid,
        aid: this.aid,
        weblink: item.weblink
      }
      this.showWeblinkModal = true
    },
    showTagsAndMonitor(item) {
      if (item) {
        this.tagsAndMonitorVisible = true
        this.tagsAndMonitorItem = item
      }
    },
    saveWeblink() {
      const form = { ...this.weblinkForm }
      videoApi
        .saveWeblink(form)
        .then(() => {
          notify.success('外链保存成功')
          this.reloadVideoList()
          this.showWeblinkModal = false
        })
        .catch((_) => {})
    },
    handleMenu({ command, data }) {
      switch (command) {
        case 'showDownload': {
          this.showDownload(data)
          break
        }
        case 'confirmSave': {
          this.confirmSave(data)
          break
        }
        case 'confirmDelete': {
          this.confirmDelete(data)
          break
        }
        case 'showWeblink': {
          this.showWeblink(data)
          break
        }
        case 'tagsAndMonitor': {
          this.showTagsAndMonitor(data)
          break
        }
      }
    },
    // 获取当前分P为第几个
    findPIndexByCid(cid) {
      const videoIndex = this.videos.findIndex((video) => video.cid === cid)
      return videoIndex > -1 ? videoIndex + 1 : null // 分p从1开始
    },
    // 显示切片弹窗
    showClipPreview(clip) {
      const { cid, pIndex, disableRedirectInPreview } = clip
      if (!cid) {
        return
      }
      this.closeVideoPreview()
      if (
        cid === this.clipPreviewData.cid &&
        this.showClipPreviewModal === true
      ) {
        this.closeClipPreview()
        return
      }
      this.clipPreviewData = {
        ...clip,
        pIndex: this.findPIndexByCid(clip.cid)
      }
      if (disableRedirectInPreview) {
        this.clipPreviewData.disableRedirectInPreview = disableRedirectInPreview
      }
      this.activeClipIndex = pIndex - 1 // pIndex从1开始
      this.showClipPreviewModal = true
    },
    // 获取视频播放地址
    async getPlayUrl(cid) {
      let isFail = false
      let res
      try {
        res = await archiveTaskApi.getPlayUrl({ cid })
        return { url: res.data.playurl, isFail }
      } catch (error) {
        if (error?.message?.includes('该视频上传已超过30天')) {
          notify.warning(
            '该视频上传已超过30天，渣清视频已过期！即将自动切换播放源！',
            1500
          )
          isFail = true
        }
        return { url: cid, isFail }
      }
    },
    // 关闭切片弹窗
    closeClipPreview() {
      this.clipPreviewData.cid = 0
      this.clipPreviewData.pIndex = 0
      this.clipPreviewData.filename = ''
      this.activeClipIndex = null
      this.showClipPreviewModal = false
      // 关闭预览弹窗
      this.$refs.cpmodalRef && this.$refs.cpmodalRef.closeFocusModal()
    },
    // 显示视频弹窗
    async showVideoPreview(clip) {
      const { eptitle, cid, id, aiRiskHint = {}, index, filename } = clip
      this.closeClipPreview()
      // ! 内容审核视频分片预览使用一审渣清视频播放器
      let clipInfo = {}
      let useRawFile = false
      if (this.isCategory) {
        useRawFile = true
        const { url, isFail } = await this.getPlayUrl(cid)
        clipInfo = {
          aid: this.aid,
          pIndex: this.findPIndexByCid(cid),
          title: eptitle,
          cid,
          videoId: id,
          rawFileSource: url,
          filename
        }
        useRawFile = !isFail // 原文件失效，优先切换成二转后的内容
      } else {
        clipInfo = {
          aid: this.aid,
          pIndex: index,
          title: eptitle,
          cid,
          videoId: id,
          rawFileSource: this.sourceFile,
          filename
        }
      }
      clipInfo.aiRiskHint = this.showClipAiRisk ? aiRiskHint : {}
      // 显示视频预览
      const newClipIndex = clipInfo.pIndex
      this.videoId = clipInfo.videoId
      this.activeClipIndex = newClipIndex - 1 // pIndex从1开始
      this.$emit('show-video-preview', {
        clipInfo,
        videoPlaylist: this.videoPlaylist,
        preferToUseRawFile: useRawFile
      })
    },
    // 关闭视频弹窗
    closeVideoPreview() {
      this.$emit('hide-video-preview')
      this.activeClipIndex = null
    },
    // 添加快捷键
    keyDownHandler(event) {
      const keyCode = event.keyCode
      if (event.target.nodeName === 'TEXTAREA') {
        return
      }
      if (keyCode === 27) {
        // ESC
        // 关闭切片 预览悬浮窗
        this.closeVideoPreview()
        this.closeClipPreview()
      }
    },
    listSortChange(list) {
      const form = {
        aid: this.aid,
        list_order: list.map((item, index) => {
          return {
            id: item.id,
            index: index + 1
          }
        })
      }
      this.hasSorted = true
      detailApi
        .setVideoOrder(form)
        .then(async () => {
          notify.success('排序成功', 1500)
          this.hasSorted = true
          await this.reloadVideoList()
          if (this.needVS) {
            this.setLargeVideos()
            this.$refs.vsRef && this.$refs.vsRef.redraw()
            this.$forceUpdate()
          }
        })
        .catch(async (_) => {
          await this.reloadVideoList()
          if (this.needVS) {
            this.setLargeVideos()
            this.$refs.vsRef && this.$refs.vsRef.redraw()
            this.$forceUpdate()
          }
        })
    },
    genVideoTitleKeywordData(copyrightKeywords) {
      return [
        ...(copyrightKeywords || []),
        ...(this.videoTitleHighlightKeywords || [])
      ]
    }
  },
  mounted() {
    this.clipModalClosed()
    if (!this.showPager) this.initSort()
    this.resizeTable()
    this.setLargeVideos()
    this.unwatch = this.$watch('collapseMenu', () => {
      this.resizeLayout()
    })
  },
  beforeDestroy() {
    this.sortableObj && this.sortableObj.destroy()
    this.sortableObj = null
    this.unwatch && this.unwatch()
    document.removeEventListener('keydown', this.keyDownHandler)
  }
}
</script>
<style lang="stylus">
.clip-list {
  .table-div {
    display: flex;
    width: 100%;
    flex-direction: column;
    position: relative;

    .table-row {
      display: flex;
      width: 100%;

      &__highlight {
        .table-cell {
          background-color: var(--green-light-1);
        }

        .custom-td {
          background-color: var(--green-light-1);
        }
      }

      &__active {
        .table-cell {
          background: rgb(227, 242, 253) !important;
        }

        .custom-td {
          background: rgb(227, 242, 253) !important;
        }
      }
    }

    .table-cell {
      vertical-align: middle;
      background: var(--content-bg-color);

      &-flex {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .table-body {
      display: flex;
      width: 100%;
      flex-direction: column;

      .table-cell {
        border-top: 2px solid #f9f9f9;
        border-bottom: 2px solid #f9f9f9;
        box-sizing: border-box;
      }
    }

    .table-body-with-pager {
      max-height: 400px;
      overflow-y: auto;
    }
  }
}
</style>
