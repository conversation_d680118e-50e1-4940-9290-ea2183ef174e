<template>
  <div class="table-row clip-item">
    <div class="table-cell">
      <div
        class="custom-td"
        :style="{ width: `${columnWidth['P序'].res}px` }"
        v-behavior-track="'audit-p-index'"
      >
        <div
          class="clip-item__rank my-handle"
          @click="openPIndex(item)"
          style="color: var(--blue-light-1)"
        >
          P{{ item.index }}
        </div>
      </div>
    </div>
    <div class="table-cell">
      <div class="custom-td" :style="{ width: `${columnWidth['cid'].res}px` }">
        <el-tooltip :content="`${item.filename}`" placement="top">
          <span>{{ item.cid }}</span>
        </el-tooltip>
      </div>
    </div>
    <div class="table-cell table-cell-flex">
      <div
        class="crash-cid-wrapper"
        :class="{ 'vertical-center-wrapper': safeCrashClips.length < 3 }"
        :style="{ width: `${columnWidth['filename'].res}px` }"
      >
        <div
          v-for="crashClip in safeCrashClips"
          :key="crashClip.cid"
          class="flex-c custom-td"
          style="height: 20px"
        >
          <a @click="jumpToArchiveDetail(crashClip.aid)" class="link">
            {{ crashClip.cid }}
          </a>
        </div>
      </div>
    </div>
    <div class="table-cell" v-if="enableAiInfo">
      <div
        class="custom-td custom-td__textover"
        :style="{
          width: `${columnWidth['AI提示'].res}px`
        }"
      >
        <AiTagCell :item="item" />
      </div>
    </div>
    <div class="table-cell">
      <div
        class="custom-td"
        :style="{ width: `${columnWidth['转码状态'].res}px` }"
      >
        <div>
          <a
            v-if="item && !item.disableJumpToVideo"
            :class="
              RELATED_STATE_MAP[item.status] &&
              RELATED_STATE_MAP[item.status].className
            "
            :href="`${videoInfoUrl}${item.cid}&aid=${aid}`"
            target="_blank"
            v-behavior-track="'audit-p-status'"
          >
            {{ STATES[item.status] }}
          </a>
          <span
            v-else
            :class="
              RELATED_STATE_MAP[item.status] &&
              RELATED_STATE_MAP[item.status].className
            "
          >
            {{ STATES[item.status] }}
          </span>
          <span>{{ formatXcodeState(item.xcode) }}</span>
        </div>
      </div>
    </div>
    <div class="table-cell">
      <div class="custom-td" :style="{ width: `${columnWidth['类型'].res}px` }">
        <div>
          <el-tag type="info">{{ item.srcType }}</el-tag>
        </div>
      </div>
    </div>

    <div class="table-cell">
      <div
        class="custom-td"
        :style="{ width: `${columnWidth['分P标题'].res}px` }"
      >
        <CopyrightFocus
          ref="copyrightFocusRef"
          v-model="item.eptitle"
          textarea
          style="width: 100%; height: 32px"
          :keywordData="genVideoTitleKeywordData(item)"
          :width="copyrightFocusWidth"
          :disabled="disabled"
          :placement="'bottom'"
          height="100px"
          v-behavior-track="'audit-p-title'"
        ></CopyrightFocus>
      </div>
    </div>
    <!-- 一审结果&建议 -->
    <FirstAuditResultCell
      :item="item"
      ref="firstAuditResultCellRef"
      :columnWidth="columnWidth['一审结果&建议'].res"
    />
    <!-- 一审时间 -->
    <div class="table-cell">
      <div
        class="custom-td"
        :style="{ width: `${columnWidth['一审时间'].res}px` }"
      >
        <div>{{ item.passtime }}</div>
      </div>
    </div>
    <!-- 一审操作人 -->
    <div class="table-cell">
      <div
        class="custom-td"
        :style="{ width: `${columnWidth['一审操作人'].res}px` }"
      >
        <div>{{ item.oname }}</div>
      </div>
    </div>
    <!-- 视频操作 -->
    <div class="table-cell">
      <div
        class="custom-td"
        :style="{ width: `${columnWidth['视频操作'].res}px` }"
      >
        <div class="clip-item__operation">
          <el-button
            type="primary"
            size="mini"
            @click="showClipPreview(item)"
            v-behavior-track="'audit-p-clip-preview'"
          >
            切片
          </el-button>
          <el-button
            type="primary"
            size="mini"
            @click="showVideoPreview(item)"
            v-behavior-track="'audit-p-video-preview'"
          >
            预览
          </el-button>
          <el-dropdown
            v-show="!disabled || enableTagsAndMonitor"
            type="primary"
            @command="(command) => handleMenu(command, item)"
            trigger="hover"
            v-if="aid && !item.hideMoreAction"
          >
            <span class="el-dropdown-link">
              <i class="el-icon-more"></i>
            </span>
            <el-dropdown-menu slot="dropdown" v-behavior-track="'audit-p-more'">
              <template v-if="!disabled">
                <el-dropdown-item command="showDownload">获取</el-dropdown-item>
                <el-dropdown-item
                  command="confirmSave"
                  :disabled="disabledMoreOper"
                >
                  保存
                </el-dropdown-item>
                <el-dropdown-item
                  command="confirmDelete"
                  :disabled="disabledMoreOper"
                >
                  删除
                </el-dropdown-item>
                <el-dropdown-item command="showWeblink">外链</el-dropdown-item>
              </template>
              <el-dropdown-item
                command="tagsAndMonitor"
                v-if="enableTagsAndMonitor"
                :disabled="item.status !== 0"
              >
                细标监控
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CopyrightFocus from '@/v2/biz-components/archive/CopyrightFocus'
import { mapState } from 'vuex'
import { genHost } from '@/v2/utils'
import { STATES } from '@/v2/data-source/config/local/constant'
import { RELATED_STATE_MAP } from '@/utils/constant'
import AiTagCell from './AiTagCell'
import FirstAuditResultCell from './FirstAuditResultCell'

export default {
  components: {
    AiTagCell,
    CopyrightFocus,
    FirstAuditResultCell
  },
  props: {
    aid: Number,
    columnWidth: {
      type: Object,
      default: () => {}
    },
    copyrightFocusWidth: String,
    disabled: {
      type: Boolean,
      default: false
    },
    disabledMoreOper: {
      type: Boolean,
      default: false
    },
    // 单个分 P 视频
    item: {
      type: Object,
      default: () => {}
    },
    videoTitleHighlightKeywords: Array,
    enableTagsAndMonitor: Boolean, // 支持细标监控操作
    enableAiInfo: Boolean // 支持AI提示
  },
  computed: {
    ...mapState({
      COMMON: (state) => state.common.COMMON
    }),
    videoInfoUrl() {
      return `${genHost()}/aegis/#/archive/archive-video-task/resource/detail?cid=`
    },
    safeCrashClips() {
      return this.item?.crashClips || []
    }
  },
  data() {
    return {
      STATES,
      RELATED_STATE_MAP
    }
  },
  methods: {
    // 提供给外部调用
    getCopyrightFocusWidth() {
      return getComputedStyle(this.$refs.copyrightFocusRef.$el).width
    },
    openPIndex(data) {
      window.open(data.link, '_blank')
    },
    formatXcodeState(xcode) {
      return this.COMMON?.xcode_state?.[xcode]
    },
    genVideoTitleKeywordData(item) {
      const copyrightKeywords = item?.copyrightKeywords || []
      const communityKeywords = item?.communityKeywords || []
      return [
        ...copyrightKeywords,
        ...communityKeywords,
        ...(this.videoTitleHighlightKeywords || [])
      ]
    },
    showClipPreview(clip) {
      this.$emit('showClipPreview', clip)
    },
    showVideoPreview(clip) {
      this.$emit('showVideoPreview', clip)
    },
    handleMenu(command, clip) {
      this.$emit('clickMenu', { command, data: clip })
    },
    jumpToArchiveDetail(aid) {
      const route = this.$router.resolve({
        path: '/v2/archive/detail',
        query: {
          business_id: 11,
          oid: aid,
          list_type: '00'
        }
      })
      window.open(route.href, '_blank')
    }
  }
}
</script>
<style lang="stylus" scoped>
.table-row {
  display: flex;
  width: 100%;
}

.clip-item {
  display: flex;
  width: 100%;

  .hl-color {
    color: var(--primary-color);
  }

  &__rank {
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    margin-right: 10px;
    color: var(--link-color);
    text-align: center;
  }

  &__operation {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .blue {
    color: var(--blue);

    &:hover {
      color: var(--blue-dark-1);
    }
  }

  .danger {
    color: var(--red);

    &:hover {
      color: var(--red-dark-1);
    }
  }

  .success {
    color: var(--green);

    &:hover {
      color: var(--green-dark-1);
    }
  }

  .table-cell {
    height: 80px;

    .crash-cid-wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
      height: calc(100% - 20px);
      padding-top: 20px;
      overflow: auto;
    }

    .vertical-center-wrapper {
      justify-content: center;
    }
  }

  .el-icon-more {
    transform: rotage;
    transform: rotate(90deg);
    font-size: 20px;
    color: var(--link-color);
  }

  .custom-td__textover-content {
    display: inline-block;
  }

  .custom-td {
    padding: 0 10px;
    box-sizing: border-box;
    font-size: 14px;
    color: var(--text-color);
    line-height: 20px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    &__textover {
      word-break: break-all;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 4;
      -webkit-box-orient: vertical;

      &-content {
        color: var(--text-color-reverse);
      }
    }

    &__textover-all {
      word-break: break-all;

      &-content {
        color: var(--text-color-reverse);
      }
    }

    .link {
      cursor: pointer;
      color: var(--link-color);
    }
  }
}
>>> .hightlght-input-disabled {
  color: var(--text-color-light);
}
</style>
