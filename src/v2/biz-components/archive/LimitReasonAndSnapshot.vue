<template>
  <div v-if="shouldRender">
    <AgFormItem
      label="理由："
      :labelWidth="labelWidthPx"
      type="flex-wrap"
    ></AgFormItem>
    <el-tabs
      v-model="tabValue"
      type="card"
      :class="{
        'evidence-tab': true,
        'only-one-tab': limitReason.length === 1
      }"
      :style="{ 'margin-left': evidenceMargin }"
    >
      <el-tab-pane
        v-for="(item, index) in limitReason"
        :key="item.tagId"
        :label="item.fullTagName"
        :name="`${index}`"
      >
        <template v-slot:default>
          <div style="min-height: 96px">
            <div class="mb-8 font-14">限流理由</div>
            <ReasonFiller
              v-if="!disabled && !item.shouldDisplayReason"
              ref="reasonFillerRefs"
              :editable="false"
              :reasonTemplate="item.reasonTemplate"
              :rows="3"
              :height="72"
              :value="item.reason"
              @input="(reason) => handleReasonChange(reason, item.tagId)"
            />
            <div v-else class="flex-ac">
              <AgTextarea
                type="textarea"
                size="small"
                placeholder="请输入内容"
                :value="item.reason"
                :rows="3"
                disabled
              />
              <el-button @click="item.shouldDisplayReason = false" class="ml-8" size="small">
                编辑
              </el-button>
            </div>
          </div>
          <div v-if="item.canTakeSnapshot">
            <div class="mt-8 mb-8 font-14">截图</div>
            <div class="flex-ac">
              <SnapshotSelector
                :disabled="item.shouldDisplayReason"
                :selectedSnapshots="item.picture"
                :emptySnapshotListMsg="emptySnapshotListMsg"
                :videoSnapshotOptions="videoSnapshotOptions"
                @select-snapshot="
                  (snapshots) => handleSnapshotChange(snapshots, item.tagId)
                "
              />
              <el-button
                v-if="item.shouldDisplayReason"
                @click="item.shouldDisplayReason = false"
                class="ml-8"
                size="small"
              >
                编辑
              </el-button>
            </div>
          </div>
        </template>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import { mapActions, mapState } from 'vuex'
import { AgFormItem, AgTextarea } from '@/v2/pure-components/ElementUpdate'
import ReasonFiller from '@/v2/pure-components/ReasonFiller'
import SnapshotSelector from '@/v2/biz-components/archive/SnapshotSelector'
import validateConsistentReasons from '@/v2/biz-utils/validateLimitReason'
import { safeJsonParse } from '@/utils'

// 标签业务的业务id
const TAG_BUSINESS_ID = {
  noteTag: 6,
  grayTag: 1
}

export default {
  name: 'LimitReasonAndSnapshot',
  components: {
    AgFormItem,
    AgTextarea,
    ReasonFiller,
    SnapshotSelector
  },
  props: {
    aid: {
      type: Number,
      default: 0
    },
    disabled: {
      type: Boolean,
      default: false
    },
    emptySnapshotListMsg: String,
    // enableFetchResult: {
    //   type: Boolean,
    //   default: true
    // },
    labelWidth: {
      type: [Number, String],
      default: 100
    },
    limitTags: {
      type: Array,
      default: () => []
    },
    tagType: {
      type: String,
      required: true,
      validator: (value) => ['noteTag', 'grayTag'].includes(value)
    },
    videoSnapshotOptions: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    ...mapState({
      perms: (state) => state.user.perms
    }),
    labelWidthPx() {
      if (typeof this.labelWidth === 'string') {
        return this.labelWidth
      } else {
        return `${this.labelWidth}px`
      }
    },
    evidenceMargin() {
      if (this.labelWidth === 45) return '45px'
      if (typeof this.labelWidth === 'string') {
        return this.labelWidth
      } else {
        return `${this.labelWidth + 12}px`
      }
    },
    shouldRender() {
      return this.limitReason.length > 0
    }
  },
  data() {
    return {
      initInProgress: false,
      tabValue: '0',
      limitReason: [],
      tagIdReasonTemplateMap: {},
      resultMap: {} // key: reason_id, value: {reason, picture} 回显用
    }
  },
  watch: {
    limitTags: {
      async handler() {
        // await this.fetchResult()
        this.initLimitReason()
      }
    },
    limitReason: {
      handler: function (newVal, oldVal) {
        if (newVal.length !== oldVal.length) {
          this.tabValue = '0'
        }
      }
    }
  },
  methods: {
    ...mapActions({
      setBatchEpSlices: 'reason/setBatchEpSlices',
      fetchArchiveLimitReasons: 'grayTag/fetchArchiveLimitReasons',
      fetchLimitTagsAndReasons: 'grayTag/fetchLimitTagsAndReasons'
    }),
    initLimitReason() {
      if (this.initInProgress) return
      this.initInProgress = true
      if (this.tagType === 'noteTag') {
        this.initByNoteTags(this.limitTags)
      }
      if (this.tagType === 'grayTag') {
        this.initByGrayTags(this.limitTags)
      }
      this.emitUpdate()
      this.$nextTick(() => { this.initInProgress = false })
    },
    initByNoteTags(noteTags) {
      const newLimitReason = []
      const oldLimitReason = this.limitReason
      noteTags.forEach((tag) => {
        const reasonObj = this.tagIdReasonTemplateMap[tag.tag_id]
        const reasonMetadata = safeJsonParse(reasonObj?.reason_metadata, {})
        const canTakeSnapshot = reasonMetadata?.can_picture || false
        if (reasonObj) {
          const oldReason = oldLimitReason.find((e) => e.tagId === tag.tag_id)
          newLimitReason.push({
            tagId: tag.tag_id,
            fullTagName: tag.pre_text,
            reasonId: reasonObj.id,
            reasonTemplate: reasonObj.reason,
            reason:
              this.resultMap[reasonObj.id]?.reason ?? oldReason?.reason ?? '',
            shouldDisplayReason: !!this.resultMap[reasonObj.id]?.reason,
            picture:
              this.resultMap[reasonObj.id]?.picture ?? oldReason?.picture ?? [],
            canTakeSnapshot
          })
        }
      })
      this.limitReason = newLimitReason
    },
    initByGrayTags(grayTagNodes) {
      const newLimitReason = []
      const oldLimitReason = this.limitReason
      grayTagNodes.forEach((node) => {
        const tagId = node.value
        const reasonObj = this.tagIdReasonTemplateMap[tagId]
        const reasonMetadata = safeJsonParse(reasonObj?.reason_metadata, {})
        const canTakeSnapshot = reasonMetadata?.can_picture || false
        if (reasonObj) {
          const oldReason = oldLimitReason.find((e) => e.tagId === tagId)
          newLimitReason.push({
            tagId,
            fullTagName: node?.pathLabels.join('/') || '',
            reasonId: reasonObj.id,
            reasonTemplate: reasonObj.reason,
            reason:
              this.resultMap[reasonObj.id]?.reason ?? oldReason?.reason ?? '',
            shouldDisplayReason: !!this.resultMap[reasonObj.id]?.reason,
            picture:
              this.resultMap[reasonObj.id]?.picture ?? oldReason?.picture ?? [],
            canTakeSnapshot
          })
        }
      })
      this.limitReason = newLimitReason
    },
    async fetchResult() {
      try {
        // 批量场景下没有单个aid，不需要回显
        if (!this.aid) return
        // if (this.limitTags.length === 0) return
        const res = await this.fetchArchiveLimitReasons({ aid: this.aid })
        const reasonList = res.data?.limit_reasons ?? []
        const newResultMap = {} // key: reason_id, value: {reason, picture}
        const sliceMap = {} // key: pIndex, value: slice[]
        reasonList.forEach((e) => {
          const pictureList = this.restoreSlices(e?.picture ?? [])
          newResultMap[e.reason_id] = {
            reason: e.reason,
            picture: pictureList
          }
          pictureList.forEach((slice) => {
            if (sliceMap[slice.pIndex]) {
              const hasDuplicate = sliceMap[slice.pIndex].find(
                (item) => item.uuid === slice.uuid
              )
              if (!hasDuplicate) {
                sliceMap[slice.pIndex].push(slice)
              }
            } else {
              sliceMap[slice.pIndex] = [slice]
            }
          })
        })
        this.resultMap = newResultMap
        if (Object.keys(sliceMap).length > 0) {
          this.setBatchEpSlices(sliceMap)
        }
      } catch (error) {
        console.error(error)
      }
    },
    async fetchTagReasonMap() {
      try {
        const business_id = this.tagType === 'noteTag'
          ? TAG_BUSINESS_ID.noteTag
          : TAG_BUSINESS_ID.grayTag
        const res = await this.fetchLimitTagsAndReasons({ business_id })
        this.tagIdReasonTemplateMap = res.data
      } catch (error) {
        console.error(error)
      } finally {
        await this.fetchResult()
        this.initLimitReason()
      }
    },
    restoreSlices(picture) {
      return picture.map((e) => {
        const isFirstAudit = !e.time.includes('｜')
        return {
          isDisplay: true,
          uuid: `${e.url}${e.time}`,
          pIndex: isFirstAudit
            ? -1
            : Number(e.time.split('P')[1].split('｜')[0]),
          text: isFirstAudit ? e.time : e.time.split('｜')[1],
          snapshotUrl: e.url
        }
      })
    },
    handleSnapshotChange(slices, tagId) {
      this.triggerUpdate('picture', slices, tagId)
    },
    handleReasonChange(reason, tagId) {
      this.triggerUpdate('reason', reason, tagId)
    },
    triggerUpdate(fieldName, fieldValue, tagId) {
      const targetIndex = this.limitReason.findIndex((e) => e.tagId === tagId)

      if (targetIndex === -1) return

      const newLimitReason = [...this.limitReason]
      newLimitReason[targetIndex] = {
        ...this.limitReason[targetIndex],
        [fieldName]: fieldValue
      }

      this.limitReason = newLimitReason
      this.emitUpdate()
    },
    emitUpdate() {
      const reasonResult = this.limitReason.map((e) => ({
        tag_id: e.tagId,
        reason: e.reason,
        picture: e.picture.map((slice) => ({
          url: slice.snapshotUrl,
          time:
            slice.pIndex > 0 ? `P${slice.pIndex}｜${slice.text}` : slice.text
        })),
        reason_id: e.reasonId,
        full_tag_name: e.fullTagName,
        editedTime: +new Date(),
        edited: !e.shouldDisplayReason
      }))
      this.$emit(`update:${this.tagType}Reason`, reasonResult)
    },
    onConsistencyError(msg) {
      this.$alert(
        msg,
        `请检查【${this.tagType === 'noteTag' ? '备注标' : '灰标'}】限流理由`
      )
    },
    // 提供校验方法供外部使用
    validateReason() {
      validateConsistentReasons(this.limitReason, this.onConsistencyError)
      const errMsg = []
      this.limitReason.forEach((rule, index) => {
        const rawMsgList =
          this.$refs.reasonFillerRefs?.[index]?.validate()?.data
        if (rawMsgList?.length) {
          const singleErrMsg = rawMsgList.map((e) => e.message).join('；')
          if (singleErrMsg) {
            errMsg.push(`标签 [${rule.fullTagName || ''}]：${singleErrMsg}；`)
          }
        }
      })
      return errMsg.join('\n')
    }
  },
  created() {
    this.fetchTagReasonMap()
  }
}
</script>
<style lang="stylus" scoped>
.evidence-tab
  margin-left 45px
  margin-bottom 10px
.only-one-tab // 单个tab的时候隐藏 el-tab-header
  >>>.el-tabs__header
    display none
</style>
