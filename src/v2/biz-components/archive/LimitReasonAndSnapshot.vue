<template>
  <div v-if="shouldRender">
    <AgFormItem
      label="理由："
      :labelWidth="labelWidthPx"
      type="flex-wrap"
    ></AgFormItem>
    <el-tabs
      v-model="tabValue"
      type="card"
      :class="{
        'evidence-tab': true,
        'only-one-tab': limitReason.length === 1
      }"
      :style="{ 'margin-left': evidenceMargin }"
    >
      <el-tab-pane
        v-for="(item, index) in limitReason"
        :key="item.tagId"
        :label="item.fullTagName"
        :name="`${index}`"
      >
        <div style="min-height: 96px">
          <div class="mb-8 font-14">限流理由</div>
          <ReasonFiller
            v-if="!disabled && !item.shouldDisplayReason"
            ref="reasonFillerRefs"
            :editable="false"
            :reasonTemplate="item.reasonTemplate"
            :rows="3"
            :height="72"
            :value="item.reason"
            @input="(reason) => handleReasonChange(reason, item.tagId)"
          />
          <div v-else class="flex-ac">
            <AgTextarea
              type="textarea"
              size="small"
              placeholder="请输入内容"
              :value="item.reason"
              :rows="3"
              disabled
            />
            <el-button @click="item.shouldDisplayReason = false" class="ml-8">
              编辑
            </el-button>
          </div>
        </div>
        <div v-if="item.canTakeSnapshot">
          <div class="mt-8 mb-8 font-14">截图</div>
          <div class="flex-ac">
            <SnapshotSelector
              :disabled="item.shouldDisplayReason"
              :selectedSnapshots="item.picture"
              :emptySnapshotListMsg="emptySnapshotListMsg"
              :videoSnapshotOptions="videoSnapshotOptions"
              @select-snapshot="
                (snapshots) => handleSnapshotChange(snapshots, item.tagId)
              "
            />
            <el-button
              v-if="item.shouldDisplayReason"
              @click="item.shouldDisplayReason = false"
              class="ml-8"
              size="small"
            >
              编辑
            </el-button>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import { mapActions, mapState } from 'vuex'
import { AgFormItem, AgTextarea } from '@/v2/pure-components/ElementUpdate'
import ReasonFiller from '@/v2/pure-components/ReasonFiller'
import SnapshotSelector from '@/v2/biz-components/archive/SnapshotSelector'
import validateConsistentReasons from '@/v2/biz-utils/validateLimitReason'
import { tagApi } from '@/api'
import { safeJsonParse } from '@/utils'

// 标签业务的业务id
const TAG_BUSINESS_ID = {
  noteTag: 6,
  grayTag: 1
}

export default {
  name: 'LimitReasonAndSnapshot',
  components: {
    AgFormItem,
    AgTextarea,
    ReasonFiller,
    SnapshotSelector
  },
  props: {
    aid: {
      type: Number,
      default: 0
    },
    disabled: {
      type: Boolean,
      default: false
    },
    emptySnapshotListMsg: String,
    // enableFetchResult: {
    //   type: Boolean,
    //   default: true
    // },
    labelWidth: {
      type: [Number, String],
      default: 100
    },
    limitTags: {
      type: Array,
      default: () => []
    },
    tagType: {
      type: String,
      required: true,
      validator: (value) => ['noteTag', 'grayTag'].includes(value)
    },
    videoSnapshotOptions: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    ...mapState({
      perms: (state) => state.user.perms
    }),
    labelWidthPx() {
      if (typeof this.labelWidth === 'string') {
        return this.labelWidth
      } else {
        return `${this.labelWidth}px`
      }
    },
    evidenceMargin() {
      if (this.labelWidth === 45) return '45px'
      if (typeof this.labelWidth === 'string') {
        return this.labelWidth
      } else {
        return `${this.labelWidth + 12}px`
      }
    },
    shouldRender() {
      return this.limitReason.length > 0
    }
  },
  data() {
    return {
      tabValue: '0',
      limitReason: [],
      tagIdReasonTemplateMap: {},
      resultMap: {}, // key: reason_id, value: {reason, picture} 回显用
      isInitializing: false // 防止重复初始化
    }
  },
  watch: {
    limitTags: {
      async handler() {
        // 防止重复初始化
        if (this.isInitializing) {
          return
        }

        this.isInitializing = true
        try {
          await this.fetchResult()
          this.initLimitReason()
        } catch (error) {
          console.error('Error in limitTags watcher:', error)
        } finally {
          this.isInitializing = false
        }
      }
    },
    limitReason: {
      handler: function (newVal, oldVal) {
        if (newVal.length !== oldVal.length) {
          this.tabValue = '0'
        }
      }
    }
  },
  methods: {
    ...mapActions({
      setEpSlices: 'reason/setEpSlices'
    }),
    async initLimitReason() {
      if (this.tagType === 'noteTag') {
        this.initByNoteTags(this.limitTags)
      }
      if (this.tagType === 'grayTag') {
        this.initByGrayTags(this.limitTags)
      }
      this.emitUpdate()
    },
    initByNoteTags(noteTags) {
      const newLimitReason = []
      const oldLimitReason = this.limitReason
      noteTags.forEach((tag) => {
        const reasonObj = this.tagIdReasonTemplateMap[tag.tag_id]
        const reasonMetadata = safeJsonParse(reasonObj?.reason_metadata, {})
        const canTakeSnapshot = reasonMetadata?.can_picture || false
        if (reasonObj) {
          const oldReason = oldLimitReason.find((e) => e.tagId === tag.tag_id)
          newLimitReason.push({
            tagId: tag.tag_id,
            fullTagName: tag.pre_text,
            reasonId: reasonObj.id,
            reasonTemplate: reasonObj.reason,
            reason:
              this.resultMap[reasonObj.id]?.reason ?? oldReason?.reason ?? '',
            shouldDisplayReason: !!this.resultMap[reasonObj.id]?.reason,
            picture:
              this.resultMap[reasonObj.id]?.picture ?? oldReason?.picture ?? [],
            canTakeSnapshot
          })
        }
      })
      this.limitReason = newLimitReason
    },
    initByGrayTags(grayTagNodes) {
      if (!Array.isArray(grayTagNodes)) {
        console.warn('initByGrayTags: grayTagNodes is not an array', grayTagNodes)
        this.limitReason = []
        return
      }

      const newLimitReason = []
      const oldLimitReason = this.limitReason
      grayTagNodes.forEach((node) => {
        if (!node || typeof node.value === 'undefined') {
          console.warn('initByGrayTags: invalid node', node)
          return
        }

        const tagId = node.value
        const reasonObj = this.tagIdReasonTemplateMap[tagId]
        const reasonMetadata = safeJsonParse(reasonObj?.reason_metadata, {})
        const canTakeSnapshot = reasonMetadata?.can_picture || false

        if (reasonObj) {
          const oldReason = oldLimitReason.find((e) => e.tagId === tagId)
          // 安全处理 pathLabels
          let fullTagName = ''
          if (node.pathLabels && Array.isArray(node.pathLabels)) {
            fullTagName = node.pathLabels.join('/')
          } else if (node.label) {
            fullTagName = node.label
          }

          newLimitReason.push({
            tagId,
            fullTagName,
            reasonId: reasonObj.id,
            reasonTemplate: reasonObj.reason,
            reason:
              this.resultMap[reasonObj.id]?.reason ?? oldReason?.reason ?? '',
            shouldDisplayReason: !!this.resultMap[reasonObj.id]?.reason,
            picture:
              this.resultMap[reasonObj.id]?.picture ?? oldReason?.picture ?? [],
            canTakeSnapshot
          })
        }
      })
      this.limitReason = newLimitReason
    },
    async fetchResult() {
      try {
        // 批量场景下没有单个aid，不需要回显
        if (!this.aid) return
        if (!Array.isArray(this.limitTags) || this.limitTags.length === 0) return

        const res = await tagApi.getArchiveLimitReasons({ aid: this.aid })
        if (!res || !res.data) {
          console.warn('fetchResult: invalid response', res)
          return
        }

        const reasonList = res.data?.limit_reasons ?? []
        if (!Array.isArray(reasonList)) {
          console.warn('fetchResult: limit_reasons is not an array', reasonList)
          return
        }

        const newResultMap = {} // key: reason_id, value: {reason, picture}
        const sliceMap = {} // key: pIndex, value: slice[]

        reasonList.forEach((e) => {
          if (!e || typeof e.reason_id === 'undefined') {
            console.warn('fetchResult: invalid reason item', e)
            return
          }

          const pictureList = this.restoreSlices(e?.picture ?? [])
          newResultMap[e.reason_id] = {
            reason: e.reason || '',
            picture: pictureList
          }

          pictureList.forEach((slice) => {
            if (!slice || typeof slice.pIndex === 'undefined') {
              return
            }

            if (sliceMap[slice.pIndex]) {
              const hasDuplicate = sliceMap[slice.pIndex].find(
                (item) => item.uuid === slice.uuid
              )
              if (!hasDuplicate) {
                sliceMap[slice.pIndex].push(slice)
              }
            } else {
              sliceMap[slice.pIndex] = [slice]
            }
          })
        })

        this.resultMap = newResultMap
        Object.keys(sliceMap).forEach((pIndex) => {
          const sliceList = sliceMap[pIndex]
          if (Array.isArray(sliceList) && sliceList.length > 0) {
            this.setEpSlices({ pIndex, slices: sliceList })
          }
        })
      } catch (error) {
        console.error('fetchResult error:', error)
        // 确保组件不会因为网络错误而崩溃
        this.resultMap = {}
      }
    },
    async fetchTagReasonMap() {
      try {
        const res = await tagApi.getLimitTagsAndReasons({
          business_id:
            this.tagType === 'noteTag'
              ? TAG_BUSINESS_ID.noteTag
              : TAG_BUSINESS_ID.grayTag
        })
        this.tagIdReasonTemplateMap = res.data
      } catch (error) {
        console.error(error)
      } finally {
        await this.fetchResult()
        this.initLimitReason()
      }
    },
    restoreSlices(picture) {
      if (!Array.isArray(picture)) {
        console.warn('restoreSlices: picture is not an array', picture)
        return []
      }

      return picture.map((e) => {
        // 安全检查
        if (!e || typeof e.time !== 'string' || typeof e.url !== 'string') {
          console.warn('restoreSlices: invalid picture item', e)
          return {
            isDisplay: true,
            uuid: `invalid_${+new Date()}`,
            pIndex: -1,
            text: '',
            snapshotUrl: ''
          }
        }

        const isFirstAudit = !e.time.includes('｜')
        let pIndex = -1
        let text = e.time

        if (!isFirstAudit) {
          try {
            const parts = e.time.split('P')
            if (parts.length > 1) {
              const subParts = parts[1].split('｜')
              if (subParts.length > 1) {
                pIndex = Number(subParts[0]) || -1
                text = subParts[1] || ''
              }
            }
          } catch (error) {
            console.warn('restoreSlices: failed to parse time format', e.time, error)
          }
        }

        return {
          isDisplay: true,
          uuid: `${e.url}${e.time}${+new Date()}`,
          pIndex,
          text,
          snapshotUrl: e.url
        }
      })
    },
    handleSnapshotChange(slices, tagId) {
      this.triggerUpdate('picture', slices, tagId)
    },
    handleReasonChange(reason, tagId) {
      this.triggerUpdate('reason', reason, tagId)
    },
    triggerUpdate(fieldName, fieldValue, tagId) {
      const newLimitReason = this.limitReason.map((e) => {
        const newElement = { ...e }
        if (e.tagId === tagId) {
          newElement[fieldName] = fieldValue
        }
        return newElement
      })
      this.limitReason = newLimitReason
      this.emitUpdate()
    },
    emitUpdate() {
      const reasonResult = this.limitReason.map((e) => ({
        tag_id: e.tagId,
        reason: e.reason,
        picture: e.picture.map((slice) => ({
          url: slice.snapshotUrl,
          time:
            slice.pIndex > 0 ? `P${slice.pIndex}｜${slice.text}` : slice.text
        })),
        reason_id: e.reasonId,
        full_tag_name: e.fullTagName,
        editedTime: +new Date(),
        edited: !e.shouldDisplayReason
      }))
      this.$emit(`update:${this.tagType}Reason`, JSON.stringify(reasonResult))
    },
    onConsistencyError(msg) {
      this.$alert(
        msg,
        `请检查【${this.tagType === 'noteTag' ? '备注标' : '灰标'}】限流理由`
      )
    },
    // 提供校验方法供外部使用
    validateReason() {
      try {
        validateConsistentReasons(this.limitReason, this.onConsistencyError)
      } catch (error) {
        console.error('validateConsistentReasons failed:', error)
        return error.message || '验证失败'
      }

      const errMsg = []
      this.limitReason.forEach((rule, index) => {
        try {
          // 安全检查 refs 数组
          const reasonFillerRefs = this.$refs.reasonFillerRefs
          if (!Array.isArray(reasonFillerRefs) || index >= reasonFillerRefs.length) {
            console.warn(`reasonFillerRefs[${index}] not found`)
            return
          }

          const rawMsgList = reasonFillerRefs[index]?.validate()?.data
          if (Array.isArray(rawMsgList) && rawMsgList.length > 0) {
            const singleErrMsg = rawMsgList
              .filter(e => e && e.message)
              .map((e) => e.message)
              .join('；')
            if (singleErrMsg) {
              errMsg.push(`标签 [${rule.fullTagName || ''}]：${singleErrMsg}；`)
            }
          }
        } catch (error) {
          console.error(`Validation error for rule ${index}:`, error)
          errMsg.push(`标签 [${rule.fullTagName || ''}]：验证出错`)
        }
      })
      return errMsg.join('\n')
    }
  },
  created() {
    this.fetchTagReasonMap()
  }
}
</script>
<style lang="stylus" scoped>
.evidence-tab
  margin-left 45px
  margin-bottom 10px
.only-one-tab // 单个tab的时候隐藏 el-tab-header
  >>>.el-tabs__header
    display none
</style>
