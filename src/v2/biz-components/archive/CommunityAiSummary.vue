<template>
  <div class="clip-ai-model-wrapper">
    <el-table :data="[{}]" border class="mb-8">
      <el-table-column label="" align="center">
        <template>稿件</template>
      </el-table-column>
      <el-table-column label="标题" align="center">
        <span
          class="three-line-max"
          :class="{ 'red-bold-text': !!archiveAiHint.title }"
        >
          {{ (archiveAiHint.title || []).join(', ') || '否' }}
        </span>
      </el-table-column>
      <el-table-column label="简介" align="center">
        <span
          class="three-line-max"
          :class="{ 'red-bold-text': !!archiveAiHint.desc }"
        >
          {{ (archiveAiHint.desc || []).join(', ') || '否' }}
        </span>
      </el-table-column>
      <el-table-column label="封面" align="center">
        <span
          class="three-line-max"
          :class="{ 'red-bold-text': !!archiveAiHint.cover }"
        >
          {{ (archiveAiHint.cover || []).join(', ') || '否' }}
        </span>
      </el-table-column>
      <el-table-column label="标签" align="center">
        <span
          class="three-line-max"
          :class="{ 'red-bold-text': !!archiveAiHint.tag }"
        >
          {{ (archiveAiHint.tag || []).join(', ') || '否' }}
        </span>
      </el-table-column>
    </el-table>
    <el-table :data="hitClipPIndexList" border empty-text="暂无命中">
      <el-table-column label="P序" align="center">
        <template v-slot="scope">P{{ scope.row + 1 }}</template>
      </el-table-column>
      <el-table-column label="标题" align="center">
        <template v-slot="scope">
          <span
            class="three-line-max"
            :class="{
              'red-bold-text': !!(getClipAiDetail(scope.row).title || []).length
            }"
          >
            {{ (getClipAiDetail(scope.row).title || []).join(', ') || '否' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="画面片段" align="center">
        <template v-slot="scope">
          <span
            class="three-line-max"
            :class="{
              'red-bold-text': !!(getClipAiDetail(scope.row).video || []).length
            }"
          >
            {{ (getClipAiDetail(scope.row).video || []).join(', ') || '否' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="文字片段" align="center">
        <template v-slot="scope">
          <span
            class="three-line-max"
            :class="{
              'red-bold-text': !!(getClipAiDetail(scope.row).text || []).length
            }"
          >
            {{ (getClipAiDetail(scope.row).text || []).join(', ') || '否' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="音频片段" align="center">
        <template v-slot="scope">
          <span
            class="three-line-max"
            :class="{
              'red-bold-text': !!(getClipAiDetail(scope.row).audio || []).length
            }"
          >
            {{ (getClipAiDetail(scope.row).audio || []).join(', ') || '否' }}
          </span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
export default {
  props: {
    communityAiSummary: {
      type: Object,
      default: () => {}
    },
    visibleClipList: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    archiveAiHint() {
      return this.communityAiSummary?.archiveAiHint || {}
    },
    aiHintByClip() {
      return this.communityAiSummary?.aiHintByClip || {}
    },
    hitClipPIndexList() {
      return this.visibleClipList
        .map((_e, i) => (this.checkIfClipHasAiResult(i) ? i : -1))
        .filter((e) => e > -1)
    }
  },
  methods: {
    getClipAiDetail(index) {
      const cid = this.visibleClipList[index]
      const rawClipAiTag = this.aiHintByClip?.[cid] || {}
      return rawClipAiTag
    },
    checkIfClipHasAiResult(index) {
      const cid = this.visibleClipList[index]
      return Object.hasOwn(this.aiHintByClip, cid)
    }
  }
}
</script>
<style lang="stylus" scoped>
.clip-ai-model-wrapper
  max-height 240px
  overflow-y auto
  .red-bold-text
    color var(--red)
    font-weight bold
  .three-line-max
    max-height 69px
    overflow-y auto
</style>
