<template>
  <div class="audit-operation">
    <el-alert
      v-if="showFreezeOperAlert"
      :title="`当前处于未开放状态，不要变更稿件状态，当前环节：${flowName}`"
      class="mb-8"
      type="warning"
      show-icon
      :closable="false"
    />
    <el-alert
      v-if="showOperAlert"
      :title="`当前环节：${flowName}`"
      class="mb-8"
      type="warning"
      show-icon
      :closable="false"
    />
    <AgFormItem label="操作：" labelWidth="45px" type="flex-wrap">
      <!-- 1.第一种形式，下拉项 -->
      <AgSelect
        v-if="!showAsButton"
        size="small"
        style="width: 120px"
        class="mr-8"
        :value="state"
        :disabled="disabled"
        @change="stateChange"
        @visible-change="dropdownVisibleChange"
        data-cy="audit-oper-select-oper"
        v-behavior-track="'audit-oper-select'"
      >
        <el-option value="" label="选择操作"></el-option>
        <el-option
          v-for="option in actionList"
          :key="option.bind_id"
          :label="option.name"
          :value="option.value"
        ></el-option>
      </AgSelect>
      <!-- 2.第二种形式，展开 -->
      <template v-if="showAsButton">
        <AuditButton
          v-for="option in actionList"
          size="small"
          style="margin-right: 4px"
          :type="getTypeByName(option.name)"
          :key="option.bind_id"
          :value="option.value"
          :active="state === option.value"
          :disabled="disabled || showFreezeOperAlert"
          @click="handleStateChange(option.value)"
          v-behavior-track="`${option.value}-audit-button`"
        >
          {{ option.name }}
        </AuditButton>
      </template>
      <el-popover
        v-if="attemptToChangeFinalState"
        ref="finalStateWarning"
        trigger="manual"
        :value="true"
        effect="light"
        placement="top"
      >
        <div class="m-20">
          当前稿件状态是
          <span class="color-error">
            【{{ FINAL_STATES[this.initArchiveState] }}】
          </span>
          <p>你变更了稿件状态，请确认是否合理</p>
        </div>
        <el-icon
          v-if="attemptToChangeFinalState"
          slot="reference"
          class="el-icon-warning ml-4 mr-12 font-20 color-warning leading-8"
        />
      </el-popover>
      <div style="margin: 0 4px; display: inline-block">
        <el-checkbox
          label="通知"
          :value="sendnotify"
          :true-label="1"
          :false-label="0"
          :disabled="disabled"
          @input="(val) => $emit('update:sendnotify', val)"
          data-cy="audit-oper-notify"
          v-behavior-track="'audit-oper-notify'"
        >
          通知
        </el-checkbox>
      </div>
      <el-button
        type="primary"
        size="small"
        class="ml-4"
        @click="hanldePreview"
        data-cy="audit-oper-preview"
        v-behavior-track="'audit-oper-preview'"
      >
        预览
      </el-button>
    </AgFormItem>
    <AgFormItem label="禁：" labelWidth="45px" type="flex">
      <template v-if="!noAuth">
        <BlockOption
          use_focus_panel
          :norank.sync="norank_local"
          :noindex.sync="noindex_local"
          :norecommend.sync="norecommend_local"
          :nohot.sync="nohot_local"
          :hot_down.sync="hot_down_local"
          :rank_down.sync="rank_down_local"
          :nosearch.sync="nosearch_local"
          :push_blog.sync="push_blog_local"
          :disabled="noReadAuth || disabled"
          :enable_six_limit="enableSixLimit"
        />
      </template>
      <template v-else>
        <span class="font-14 mr-10 ml-10">
          如需查看或操作敏感信息&nbsp;&nbsp;
          <a :href="HELP_HREF" target="_blank" class="help-text">
            点击获得帮助
          </a>
        </span>
      </template>
    </AgFormItem>
    <AgFormItem :showSlot="showSlot">
      <!-- v2理由框 -->
      <AuditReasonStruct
        ref="auditReasonStruct"
        :state="state"
        :dirty.sync="dirty"
        :reason="rejectReason"
        :listType="listType"
        :disabled="disabled"
        :reasonTags="reasonTags"
        :reasonMap="reasonMap"
        :multipleReason="multipleReason"
        :showMoreReasonBtn="showMoreReasonBtn"
        @update-reason="updateReason"
        @update-reason-id="updateReasonId"
        @clear-reason="updateReason"
        @update-multiple-reason="updateMultipleReason"
        @update-snapshots="updateSnapshots"
        v-behavior-track="'audit-oper-reason'"
      />
    </AgFormItem>
    <AgFormItem label="备注：" labelWidth="45px" type="flex">
      <!-- 分两种 -->
      <template v-if="isUserNote">
        <AgNoteTag
          ref="noteTag"
          :tags="noteTags"
          :options="noteOptionsWithRules"
          :aid="aid"
          :disabled="disabled"
          @add-note="handleAddNote"
          @delete-note="handleDeleteNote"
          v-behavior-track="'audit-oper-note'"
        />
      </template>
      <!-- 没有角色的 -->
      <template v-else>
        <AgSelect
          v-model="shortcutNote"
          style="width: 160px; margin-right: 4px"
          placeholder="选择快捷文本"
          size="small"
          :disabled="disabled"
          @change="changeNote"
          data-cy="audit-oper-select-note"
          v-behavior-track="'audit-oper-select-note'"
        >
          <el-option
            v-for="reason in REASONS"
            :key="reason"
            :label="reason"
            :value="reason"
          ></el-option>
        </AgSelect>
        <AgTextarea
          type="textarea"
          size="small"
          placeholder="填写描述"
          :rows="1"
          :value="note"
          :disabled="disabled"
          @input="(val) => $emit('update:note', val)"
          data-cy="audit-oper-textarea-note"
          v-behavior-track="'audit-oper-textarea-note'"
        />
      </template>
    </AgFormItem>
    <LimitReasonAndSnapshot
      v-if="isUserNote && noteTags.length > 0"
      ref="noteTagReasonRef"
      :aid="aid"
      :disabled="disabled"
      labelWidth="45px"
      :limitTags="noteTags"
      tagType="noteTag"
      @update:noteTagReason="relayNoteTagReasonChange"
    />
    <AgFormItem
      label="灰标："
      labelWidth="45px"
      type="flex"
      v-if="canReadGrayTags"
    >
      <el-cascader
        ref="grayTagRef"
        style="width: 100%"
        v-model="grayTagsLocal"
        :disabled="disabled || !canWriteGrayTags"
        :options="grayTagOps"
        :props="{
          expandTrigger: 'hover',
          children: 'options',
          label: 'name',
          value: 'id',
          multiple: true
        }"
        @change="handleGrayTagChange"
      />
    </AgFormItem>
    <LimitReasonAndSnapshot
      v-if="selectedGrayTagNodes.length > 0"
      :aid="aid"
      ref="grayTagReasonRef"
      :disabled="disabled"
      labelWidth="45px"
      :limitTags="selectedGrayTagNodes"
      tagType="grayTag"
      @update:grayTagReason="relayGrayTagReasonChange"
    />
  </div>
</template>
<script>
import { mapState, mapGetters, mapActions } from 'vuex'
import notify from '@/lib/notify'
import { archiveApi } from '@/api'
import {
  AgSelect,
  AgFormItem,
  AgTextarea,
  AgNoteTag
} from '@/v2/pure-components/ElementUpdate'
import {
  UAT_RECHECK_FLOW_ID,
  PROD_RECHECK_FLOW_ID,
  REASONS,
  HELP_HREF,
  FINAL_STATES
} from '@/v2/data-source/config/local/constant'
import washNoteTag from '@/karl/second-audit/note-tag.js'

import AuditButton from '@/components/element-update/AuditButton.vue'
import AuditReasonStruct from './AuditReasonStruct'
import BlockOption from '@/v2/biz-components/archive/BlockOption'
import LimitReasonAndSnapshot from '@/v2/biz-components/archive/LimitReasonAndSnapshot'
import { validateAllLimitReasons } from '@/v2/biz-utils/validateLimitReason'
import { getNodesByFullTagIds } from '@/v2/biz-utils/classifyTags'

const PRE_RELEASE_FLOW_NAMES = [
  '一审转码分发阶段',
  '二审阶段',
  '版权音乐二审',
  '三审阶段',
  '私单三审阶段',
  '四审阶段',
  '付费审核阶段'
]

export default {
  name: 'audit-operation',
  data() {
    return {
      REASONS,
      HELP_HREF,
      FINAL_STATES,
      finalStateWarningVisble: false,
      selectedAction: {},
      dirty: false,
      PRE_RELEASE_FLOW_NAMES,
      showSlot: true,
      shortcutNote: '',
      noteOptionsWithRules: [],
      reasonTags: [],
      reasonMap: {},
      finalStateWarning: null,
      reasonId: '',
      reasonTagId: '',
      categoryId: '',
      categoryText: '',
      fullPathText: '',
      fullPathId: [],
      snapshots: [],
      tempMultipleReason: null,
      selectedGrayTagNodes: [],
      noteTagReason: [], // 备注标限流理由
      grayTagReason: [] // 灰标限流理由
    }
  },
  components: {
    AuditReasonStruct,
    AgSelect,
    AgFormItem,
    AgTextarea,
    AgNoteTag,
    AuditButton,
    BlockOption,
    LimitReasonAndSnapshot
  },
  props: {
    showAsButton: {
      type: Boolean,
      default: false
    },
    showMoreReasonBtn: {
      type: Boolean,
      default: false
    },
    state: {
      type: [Number, String],
      default: ''
    },
    norank: {
      type: [Number, String],
      default: ''
    },
    hot_down: {
      type: [Number, String],
      default: ''
    },
    rank_down: {
      type: [Number, String],
      default: ''
    },
    nohot: {
      type: [Number, String],
      default: ''
    },
    noindex: {
      type: [Number, String],
      default: ''
    },
    norecommend: {
      type: [Number, String],
      default: ''
    },
    nosearch: {
      type: [Number, String],
      default: ''
    },
    push_blog: {
      type: [Number, String],
      default: ''
    },
    sendnotify: {
      type: Number,
      default: 0
    },
    multipleReason: {
      type: Array,
      default: () => []
    },
    rejectReason: {
      type: String,
      default: ''
    },
    rejectReasonId: {
      type: [String, Number],
      default: ''
    },
    flowName: {
      type: String,
      default: ''
    },
    auditState: {
      type: [Number, String],
      default: ''
    },
    bvid: {
      type: String,
      default: ''
    },
    aid: {
      type: Number,
      default: 0
    },
    disabled: {
      type: Boolean,
      default: false
    },
    note: {
      type: String,
      default: ''
    },
    actionList: {
      type: Array,
      default() {
        return []
      }
    },
    historyState: {
      type: [Number, String],
      default: ''
    },
    auditTags: {
      type: Array,
      default() {
        return []
      }
    },
    grayTags: {
      type: Array,
      default() {
        return []
      }
    },
    flowId: {
      type: Number,
      default: 0
    },
    noteOptions: {
      type: Array,
      default() {
        return []
      }
    },
    listType: {
      type: String,
      default: ''
    },
    listReview: {
      type: [Number, String],
      default: ''
    },
    noAuth: {
      type: Boolean,
      default: false
    },
    noReadAuth: {
      type: Boolean,
      default: false
    },
    enableSixLimit: {
      type: Boolean,
      default: false
    },
    initArchiveState: {
      type: String,
      default: ''
    }
  },
  computed: {
    ...mapState({
      perms: (state) => state.user.perms,
      grayTagOps: (state) => state.grayTag.grayTagOps,
      leafNodes: (state) => state.grayTag.leafNodes
    }),
    ...mapGetters({
      getEnv: 'env/getEnv'
    }),
    norank_local: {
      get() {
        return this.norank
      },
      set(val) {
        this.$emit('update:norank', val)
      }
    },
    noindex_local: {
      get() {
        return this.noindex
      },
      set(val) {
        this.$emit('update:noindex', val)
      }
    },
    norecommend_local: {
      get() {
        return this.norecommend
      },
      set(val) {
        this.$emit('update:norecommend', val)
      }
    },
    nohot_local: {
      get() {
        return this.nohot
      },
      set(val) {
        this.$emit('update:nohot', val)
      }
    },
    hot_down_local: {
      get() {
        return this.hot_down
      },
      set(val) {
        this.$emit('update:hot_down', val)
      }
    },
    rank_down_local: {
      get() {
        return this.rank_down
      },
      set(val) {
        this.$emit('update:rank_down', val)
      }
    },
    nosearch_local: {
      get() {
        return this.nosearch
      },
      set(val) {
        this.$emit('update:nosearch', val)
      }
    },
    push_blog_local: {
      get() {
        return this.push_blog
      },
      set(val) {
        this.$emit('update:push_blog', val)
      }
    },
    noteTags: {
      get() {
        return this.auditTags
      },
      set(newVal) {
        this.$emit('update:auditTags', newVal)
      }
    },
    grayTagsLocal: {
      get() {
        return this.grayTags
      },
      set(newVal) {
        this.$emit('update:grayTags', newVal)
      }
    },
    isUserNote() {
      return this.noteOptions && this.noteOptions.length > 0
    },
    showFreezeOperAlert() {
      return (
        this.listType === '00' &&
        this.flowName &&
        PRE_RELEASE_FLOW_NAMES.includes(this.flowName)
      )
    },
    showOperAlert() {
      return (
        this.listType !== '00' &&
        this.flowName &&
        PRE_RELEASE_FLOW_NAMES.includes(this.flowName)
      )
    },
    reachedFinalState() {
      return Object.keys(FINAL_STATES).includes(this.initArchiveState)
    },
    attemptToChangeFinalState() {
      return (
        this.reachedFinalState &&
        this.initArchiveState !== this.state &&
        !!this.state
      )
    },
    canReadGrayTags() {
      return this.perms.ARCH_GRAY_TAGS_READ || this.perms.ARCH_GRAY_TAGS_WRITE
    },
    canWriteGrayTags() {
      return this.perms.ARCH_GRAY_TAGS_WRITE
    },
    hasMultipleReason() {
      return Array.isArray(this.multipleReason) && this.multipleReason.length
    }
  },
  watch: {
    aid: {
      handler() {
        this.$emit('update:note', '')
      },
      immediate: true
    },
    attemptToChangeFinalState: {
      handler(val) {
        this.$nextTick(() => {
          this.$refs.finalStateWarning?.updatePopper()
        })
      },
      immediate: true
    }
  },
  methods: {
    ...mapActions({
      fetchGrayTags: 'grayTag/fetchGrayTags'
    }),
    handleAddNote(note) {
      const { value, label, remark, pre_text: preText } = note
      const existNoteTag = this.noteTags.find(
        (tag) => tag.tag_id === value && tag.pre_text === preText
      )
      if (existNoteTag && existNoteTag.canDelete) {
        notify.warning('已经存在该备注tag，只修改了备注')
        existNoteTag.remark = remark
        existNoteTag.tag_name = `${preText}【${remark}】`
      } else if (existNoteTag) {
        notify.warning('已经存在该备注tag，且无权修改')
      } else {
        this.noteTags.unshift({
          tag_id: value,
          tag_name: label,
          remark,
          pre_text: preText,
          hitState: false,
          first_init: false,
          canDelete: true
        })
      }
    },
    handleDeleteNote(note, idx) {
      this.noteTags.splice(idx, 1)
    },
    toSetBindId(state) {
      const idx = this.actionList.findIndex((item) => item.value === state)
      if (idx > -1) {
        this.$emit('update:bind_id', this.actionList[idx].bind_id)
      }
    },
    isSameFlowId() {
      const env = this.getEnv()
      const FLOW_ID_ARR =
        env === 'uat' ? UAT_RECHECK_FLOW_ID : PROD_RECHECK_FLOW_ID
      return FLOW_ID_ARR.some((item) => item === this.flowId)
    },
    getTypeByName(name) {
      let type = ''
      switch (name) {
        case '开放浏览': {
          type = 'success'
          break
        }
        case '打回': {
          type = 'warning'
          break
        }
        case '锁定': {
          type = 'danger'
          break
        }
        default: {
          type = 'success'
          break
        }
      }
      return type
    },
    dropdownVisibleChange(visible) {
      if (visible) return
      return this.stateChange(this.state)
    },
    // 按钮形式的
    handleStateChange(newState) {
      this.filterMultipleReasonByState(newState)
      this.$emit('update:state', newState)
      if (!this.hasMultipleReason) this.dirty = true
      const state = newState.toString()
      if (
        // 全部稿件列表
        this.listType === '00' ||
        // 待回查稿件  30 40 90
        (this.listReview !== '' && this.listReview >= 0) ||
        this.isSameFlowId()
      ) {
        // 系统通知联动 打回锁定
        if (state === '-2' || state === '-4') {
          this.$emit('update:sendnotify', 1)
        } else {
          this.$emit('update:sendnotify', 0)
        }
      }
      this.toSetBindId(state)
      this.fetchReason(newState)
    },
    // 下拉选项形式的
    stateChange(newState) {
      this.filterMultipleReasonByState(newState)
      this.$emit('update:state', newState)
      if (!this.hasMultipleReason) this.dirty = true
      const state = newState.toString()
      if (
        // 全部稿件列表
        this.listType === '00' ||
        // 待回查稿件  30 40 90
        (this.listReview !== '' && this.listReview >= 0) ||
        this.isSameFlowId()
      ) {
        // 系统通知联动 打回锁定
        if (state === '-2' || state === '-4') {
          this.$emit('update:sendnotify', 1)
        } else {
          this.$emit('update:sendnotify', 0)
        }
      }
      this.toSetBindId(state)
      // 只有打回/锁定需要请求理由
      this.fetchReason(newState)
    },
    filterMultipleReasonByState(newState) {
      if (newState === this.state || !this.multipleReason) return
      const activeReasons = this.multipleReason.filter(
        (reason) => reason.submit_state === newState
      )
      this.updateMultipleReason(activeReasons)
    },
    fetchReason(newState) {
      if (newState === '-2' || newState === '-4') {
        archiveApi
          .getReasonTags({
            list_type: this.listType,
            state: newState
          })
          .then((res) => {
            const { reason_map = {}, reason_tags = [] } = res.data || {}
            this.reasonTags = reason_tags || []
            this.reasonMap = reason_map
          })
          .catch((_) => {
            this.reasonTags = []
            this.reasonMap = {}
          })
      }
    },
    async fetchNoteOptWithRules() {
      try {
        const res = await archiveApi.getTagNote()
        const { noteOptions } = washNoteTag({
          note_tag: res.data
        })
        this.noteOptionsWithRules = noteOptions
      } catch (e) {
        console.error(e)
      }
    },
    async getGrayTags() {
      await this.fetchGrayTags()
      const selectedNodes = getNodesByFullTagIds(
        this.leafNodes,
        this.grayTagsLocal
      )
      this.selectedGrayTagNodes = selectedNodes
    },
    handleGrayTagChange() {
      this.$nextTick(() => {
        const selectedNodes = this.$refs.grayTagRef?.getCheckedNodes(true)
        this.selectedGrayTagNodes = selectedNodes
      })
    },
    onConsistencyError(msg) {
      this.$alert(msg, `请检查【备注标】和【灰标】限流理由`)
    },
    validateReason() {
      // 跨标签业务理由一致性校验 并用新理由覆盖旧理由
      validateAllLimitReasons(
        this.noteTagReason,
        this.grayTagReason,
        this.onConsistencyError
      )
      let msg = ''
      let msgA = ''
      let msgB = ''
      if (this.state === '-2' || this.state === '-4') {
        msg = this.$refs.auditReasonStruct.validateReason()
        msgA = this.$refs.noteTagReasonRef?.validateReason() || ''
        msgB = this.$refs.grayTagReasonRef?.validateReason() || ''
      } else {
        msgA = this.$refs.noteTagReasonRef?.validateReason() || ''
        msgB = this.$refs.grayTagReasonRef?.validateReason() || ''
      }
      const fullMsg = `${msg ? '【打锁理由】' : ''}${msg}${
        msgA ? '【备注标】限流理由' : ''
      }${msgA}${msgB ? '【灰标】限流理由' : ''}${msgB}`
      return fullMsg
    },
    updateSnapshots(snapshots) {
      const currentReason = this.tempMultipleReason
        ? this.tempMultipleReason[0]
        : this.multipleReason[0]
      this.snapshots = snapshots
      this.updateMultipleReason([{ ...currentReason, picture: snapshots }])
    },
    // 响应理由文本变化
    updateReason(reason = '') {
      if (!this.dirty) return // dirty 为 false，表示还没有操作过，则忽略
      const newReason = {
        reason,
        id: 0,
        reason_id: this.reasonId,
        category: this.categoryText,
        category_id: this.categoryId,
        reason_tag_id: this.reasonTagId,
        reason_tag: this.fullPathText,
        full_path_tag_id: this.fullPathId,
        submit_state: this.state,
        picture: this.snapshots
      }
      this.tempMultipleReason = [newReason]
      // 单条转多条时 导入单条理由的内容
      this.updateMultipleReason([newReason])
      this.$emit('update:rejectReason', reason)
    },
    // 响应理由其他数据变化
    updateReasonId(
      reasonId,
      reasonTagId,
      categoryId,
      categoryText,
      fullPathText,
      fullPathId
    ) {
      this.reasonId = reasonId
      this.reasonTagId = reasonTagId
      this.categoryId = categoryId
      this.categoryText = categoryText
      this.fullPathText = fullPathText
      this.fullPathId = fullPathId
      const newReason = {
        reason: this.rejectReason,
        id: 0,
        reason_id: reasonId,
        category: categoryText,
        category_id: categoryId,
        reason_tag_id: reasonTagId,
        reason_tag: fullPathText,
        full_path_tag_id: fullPathId,
        submit_state: this.state,
        picture: this.snapshots
      }
      this.updateMultipleReason([newReason])
    },
    updateMultipleReason(multipleReason) {
      this.$emit('update:multipleReason', multipleReason)
    },
    hanldePreview() {
      window.open(`//www.bilibili.com/video/${this.bvid}`, '_blank')
    },
    changeNote(selected) {
      let note = this.note
      if (note.length > 0) {
        note += '\n'
      }
      note += selected
      this.$emit('update:note', note)
    },
    validateNote() {
      const noteTag = this.$refs.noteTag || null
      if (this.isUserNote && noteTag) {
        const { noteIdx, desc } = noteTag
        if (noteIdx?.length || desc) {
          return '标签栏存在未录入内容，请确认'
        }
      }
      return ''
    },
    relayGrayTagReasonChange(newVal) {
      this.grayTagReason = newVal
      this.$emit('update:grayTagReason', newVal)
    },
    relayNoteTagReasonChange(newVal) {
      this.noteTagReason = newVal
      this.$emit('update:noteTagReason', newVal)
    }
  },
  mounted() {
    this.fetchReason(this.state)
    this.fetchNoteOptWithRules()
    this.getGrayTags()
  }
}
</script>
<style>
.tippy-arrow {
  color: white;
}
</style>
