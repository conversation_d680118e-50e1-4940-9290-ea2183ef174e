<template>
  <div class="advanced">
    <el-tabs v-model="curTab">
      <el-tab-pane
        label="基础编辑"
        name="basic"
        v-if="!disabled && perms?.AEGIS_VIDEO_EDIT"
      >
        <slot name="basic-edit"></slot>
      </el-tab-pane>
      <el-tab-pane label="高级操作" name="common">
        <el-form
          label-position="left"
          label-width="120px"
          size="small"
          :disabled="disabled"
        >
          <el-form-item v-if="showArctypeEditor" label="修改稿件分区：">
            <ArctypeV2Editor :aid="aid" />
          </el-form-item>
          <el-form-item label="ip限制：">
            <IpLimit
              v-if="perms.MOD_ARC_IP"
              :value="policyId"
              @input="(val) => $emit('update:policyId', val)"
            ></IpLimit>
            <HelpText v-else />
          </el-form-item>
          <el-form-item label="高级禁止：">
            <AttrTable
              v-if="perms.MOD_ARC_FLOW"
              :noReadAuth="!perms.MOD_S_FLOW_WRITE"
              :value="attrForm"
              @input="(val) => updateValue('', val)"
            />
            <HelpText v-else />
          </el-form-item>
          <el-form-item label="管控位：">
            <ControlProperty
              :aid="aid"
              :editable="perms.AEGIS_ARCHIVE_CONTROL_PROPERTY"
              :clearForm="isNewer"
            />
          </el-form-item>
          <el-form-item label="稿件属性：">
            <AttrList
              v-if="perms.MOD_ARC_ATTR"
              class="attribute-comp-detail"
              :value="attrForm"
              @input="(val) => updateValue('', val)"
            />
            <HelpText v-else />
          </el-form-item>
          <el-form-item label="充电设置：">
            <Charge
              v-if="perms.MOD_ARC_EXT"
              :aid="aid"
              :chargeState="chargeState"
            />
            <HelpText v-else />
          </el-form-item>
          <el-form-item label="稿件归属：">
            <Author v-if="perms.MOD_ARC_EXT" :aid="aid" :mid="mid" />
            <HelpText v-else />
          </el-form-item>
          <el-form-item label="阅读权限：">
            <Read v-if="perms.MOD_ARC_EXT" :access="access" :aid="aid" />
            <HelpText v-else />
          </el-form-item>
          <el-form-item label="用户组：">
            <UserGroup v-if="perms.MOD_ARC_EXT" :mid="mid" />
            <HelpText v-else />
          </el-form-item>
          <el-form-item label="其他设置：">
            <Others
              v-if="perms.MOD_ARC_EXT"
              :limitComments="limitComments"
              @update:limitComments="
                (val) => $emit('update:limitComments', val)
              "
            />
            <HelpText v-else />
          </el-form-item>
          <el-form-item label="OGV稿件跳转：">
            <OgvRedirect
              v-if="perms.OGV_JUMP"
              :aid="aid"
              :j="attrForm.j"
              :redirectUrl="redirectUrl"
              :disabled="disabled"
              @update:j="(val) => updateValue('j', val)"
              @update:redirectUrl="(val) => $emit('update:redirectUrl', val)"
            />
            <HelpText v-else />
          </el-form-item>
          <el-form-item label="盗版稿件跳转：">
            <PiracyRedirect
              v-if="perms.OGV_JUMP"
              :aid="aid"
              :disabled="disabled"
            />
            <HelpText v-else />
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane
        v-if="perms.ARC_UNRECOM_VIEW"
        label="推荐黑名单"
        name="rec_black"
      >
        <div class="wrapper">
          <BlackList :aid="aid" :disabled="disabled" />
        </div>
      </el-tab-pane>
      <el-tab-pane label="合作信息管理" name="coop">
        <div class="wrapper">
          <CoopTab :aid="aid" :hasStaff="hasStaff" :disabled="disabled" />
        </div>
      </el-tab-pane>
      <el-tab-pane label="争议标识管理" name="argue">
        <div class="wrapper">
          <ArgueTab :aid="aid" :disabled="disabled" />
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
/**
 * @component
 * @assetTitle 稿件高级设置
 * @assetDescription 展示高级设置信息，注意区分isBatch两种模式
 * @assetImportName Advanced
 * @assetTag 稿件业务组件
 */
import { mapState } from 'vuex'
import ArctypeV2Editor from '@/v2/biz-components/archive/ArctypeV2Editor'
import IpLimit from '../IpLimit.vue'
import AttrTable from '../AttrTable.vue'
import AttrList from '../AttrList.vue'
import Charge from '../Charge.vue'
import Author from '../Author.vue'
import Read from '../Read.vue'
import Others from '../Others.vue'
import BlackList from '../BlackList.vue'
import CoopTab from '../CoopTab.vue'
import UserGroup from '../UserGroup.vue'
import ArgueTab from '../ArgueTab.vue'
import OgvRedirect from '../OgvRedirect.vue'
import PiracyRedirect from '../PiracyRedirect.vue'
import { HELP_HREF } from '@/v2/data-source/config/local/constant.js'
import HelpText from '@/v2/biz-components/archive/HelpText.vue'
import ControlProperty from '../ControlProperty.vue'

export default {
  data() {
    return {
      HELP_HREF,
      curTab: this.disabled ? 'common' : 'basic' // basic, common, rec_black, coop, argue
    }
  },
  components: {
    ArctypeV2Editor,
    ArgueTab,
    AttrList,
    AttrTable,
    Author,
    BlackList,
    Charge,
    ControlProperty,
    CoopTab,
    HelpText,
    IpLimit,
    OgvRedirect,
    Others,
    PiracyRedirect,
    Read,
    UserGroup
  },
  props: {
    attrForm: {
      type: Object,
      required: true
    },
    aid: {
      type: [String, Number],
      required: true
    },
    access: {
      type: Number,
      default: 0
    },
    chargeState: {
      type: Number,
      default: 0
    },
    mid: {
      type: Number,
      required: true
    },
    hasStaff: {
      type: Boolean,
      default: false
    },
    policyId: {
      type: Number,
      default: -1
    },
    limitComments: {
      type: Number,
      default: 0
    },
    redirectUrl: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    isNewer: {
      type: Boolean,
      default: false
    },
    listReview: Number
  },
  computed: {
    ...mapState({
      perms: (state) => state.user.perms
    }),
    showArctypeEditor() {
      return [10, 37].includes(this.listReview) // 10: 排行回查 37: 高播回查
    }
  },
  methods: {
    updateValue(key, val) {
      if (key) {
        this.$emit('update:attrForm', {
          ...this.attrForm,
          [key]: val
        })
      } else {
        this.$emit('update:attrForm', {
          ...this.attrForm,
          ...val
        })
      }
    }
  }
}
</script>
<style lang="stylus" scoped>
.advanced
  padding 20px
  height calc(100% - 60px)
  width calc(100% - 20px)
  box-shadow 0 2px 12px 0 rgba(0, 0, 0, 0.1)
  border-radius 2px
  box-sizing border-box
  background var(--content-bg-color)
  overflow auto
  >>> .el-form-item
    margin-bottom 16px !important
  .wrapper
    background-color var(--content-bg-color)
    border-radius 3px
    margin-bottom 4px
    // box-shadow 0 1px 1px 0 rgba(0, 0, 0, .1)
    box-shadow initial !important
    padding 10px 0
    width calc(100% - 20px)
  .input
    display inline-block
    width 300px
    vertical-align middle
  .jumpCheckbox
    vertical-align middle
    line-height 35px
</style>
