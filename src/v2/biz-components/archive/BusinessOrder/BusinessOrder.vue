<template>
  <div class="business-order">
    <el-form inline size="small" :disabled="disabled">
      <el-row>
        <el-form-item>
          <el-checkbox
            v-if="!isBusinessOrder"
            :value="isPorder"
            :true-label="1"
            :false-label="0"
            :disabled="readOnly || businessAuthDisabled || disableIsPorder"
            @input="handleChangePorder"
            v-behavior-track="'audit-business-order'"
          >
            设为非绿洲商业稿件
          </el-checkbox>
          <span v-else>
            {{ formatedOrderInfo }}
          </span>
        </el-form-item>
        <el-form-item v-if="showJudgeInfo">
          <el-checkbox
            class="ml-20"
            :value="businessJudgeInfo && businessJudgeInfo.enable"
            :true-label="1"
            :false-label="0"
            :disabled="disableJudgeInfo || isSparkOrder"
            @input="(val) => handleUpdateJudgeInfo(val)"
          >
            逃单研判
          </el-checkbox>
          <el-select
            v-if="businessJudgeInfo && businessJudgeInfo.enable"
            class="ml-8"
            :value="transferredJudgeTypeText"
            :disabled="disableJudgeInfo"
            @change="(val) => emitBizJudgeInfo('type', val)"
          >
            <el-option
              v-for="item in businessJudgeTypeOpts"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              :disabled="item.disabled"
            />
          </el-select>
        </el-form-item>
      </el-row>
      <template v-if="isOrder">
        <el-row>
          <el-form-item>
            <el-select
              v-if="isOrder"
              v-bind:value="groupId"
              placeholder="选择流量套餐"
              :disabled="(readOnly && !isBusinessOrder) || businessAuthDisabled"
              @change="onChangeGroupId"
            >
              <el-option
                v-for="option in options.flowTags"
                :key="option.id"
                :label="option.name"
                :value="option.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-checkbox
              v-if="!isBusinessOrder"
              v-bind:value="porderNotify"
              :true-label="1"
              :false-label="0"
              :disabled="
                readOnly || businessAuthDisabled || publishedOver30Days
              "
              @input="onChangePorderNotify"
            >
              通知用户
            </el-checkbox>
          </el-form-item>
        </el-row>

        <!-- 花火商单展示：推广行业、行业=1则显示推广品牌：官方品牌、其他品牌、广告主 -->
        <template v-if="isSparkOrder">
          <el-row style="margin-top: 10px">
            <el-form-item label="推广行业" required>
              <el-select
                v-model="curIndustry"
                placeholder="选择推广行业"
                :disabled="readOnly || businessAuthDisabled"
                value-key="id"
                @change="indChange"
              >
                <el-option
                  v-for="(option, idx) in industries"
                  :key="idx"
                  :label="option.name"
                  :value="option"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="推广品牌" v-if="isMobileGame" required>
              <el-radio-group
                v-bind:value="official"
                @input="officialChange"
                :disabled="readOnly || businessAuthDisabled"
              >
                <el-radio :label="1">官方品牌</el-radio>
                <el-radio :label="0">其他品牌</el-radio>
              </el-radio-group>
            </el-form-item>
            <!-- 广告主 -->
            <el-form-item>
              <el-input
                :disabled="readOnly || businessAuthDisabled"
                :value="advertiser"
                @input="onInputAdvertiser"
                placeholder="广告主"
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row style="margin-top: 10px">
            <el-form-item>
              <el-select
                v-if="official"
                v-model="selectedBrand"
                placeholder="选择官方品牌"
                filterable
                value-key="id"
                :disabled="readOnly || businessAuthDisabled"
                @change="updateBrand"
              >
                <el-option
                  v-for="(option, idx) in allBrandOptions"
                  :key="idx"
                  :label="option.name"
                  :value="option"
                ></el-option>
              </el-select>
              <el-input
                v-else
                :value="brandName"
                @input="onInputBrandName"
                placeholder="推广品牌"
                :disabled="readOnly || businessAuthDisabled"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <div @mouseover="handleShowTip" @mouseleave="handleHideTip">
                <AgTooltip
                  :disabled="disabledShow || businessAuthDisabled"
                  placement="top"
                >
                  <el-select
                    v-model="selectedShowTypes"
                    @change="updateShowType"
                    multiple
                    placeholder="选择推广形式"
                    filterable
                    value-key="id"
                    :disabled="readOnly || businessAuthDisabled"
                    @visible-change="(show) => (showDropdown = show)"
                    @focus="focus = true"
                    @blur="focus = false"
                  >
                    <el-option
                      v-for="(option, idx) in showTypes"
                      :key="idx"
                      :label="option.name"
                      :value="option"
                    ></el-option>
                  </el-select>
                  <div slot="content">
                    {{ showTypeText }}
                  </div>
                </AgTooltip>
              </div>
            </el-form-item>
            <!-- 广告主 -->
            <el-form-item>
              <el-input
                :value="agent"
                @input="onInputAgent"
                placeholder="代理商"
                :disabled="readOnly || businessAuthDisabled"
              ></el-input>
            </el-form-item>
          </el-row>
        </template>

        <!-- 非花火商单：推广品牌 -->
        <template v-if="!isSparkOrder">
          <el-row style="margin-top: 10px">
            <el-form-item label="推广品牌" required>
              <el-select
                :value="brandId"
                placeholder="选择推广品牌"
                :disabled="readOnly || businessAuthDisabled"
                filterable
                remote
                clearable
                :remote-method="getAllBrands"
                @change="brandChange"
              >
                <el-option
                  v-for="(item, index) in brandList"
                  :key="index"
                  :label="item.brand_name"
                  :value="item.brand_id"
                ></el-option>
                <el-option
                  label="其他"
                  :value="0"
                  v-if="brandSearchValue?.length"
                ></el-option>
              </el-select>
              <el-input
                v-if="brandId === 0"
                :disabled="readOnly || businessAuthDisabled"
                style="width: 320px; margin-left: 12px"
                :value="brandName"
                @input="brandNameChange"
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row style="margin-top: 10px">
            <el-form-item>
              <div @mouseover="handleShowTip" @mouseleave="handleHideTip">
                <AgTooltip
                  :disabled="disabledShow || businessAuthDisabled"
                  placement="top"
                >
                  <el-select
                    v-model="selectedShowTypes"
                    @change="updateShowType"
                    multiple
                    placeholder="选择推广形式"
                    filterable
                    value-key="id"
                    :disabled="readOnly || businessAuthDisabled"
                    @visible-change="(show) => (showDropdown = show)"
                    @focus="focus = true"
                    @blur="focus = false"
                  >
                    <el-option
                      v-for="(option, idx) in showTypes"
                      :key="idx"
                      :label="option.name"
                      :value="option"
                    ></el-option>
                  </el-select>
                  <div slot="content">
                    {{ showTypeText }}
                  </div>
                </AgTooltip>
              </div>
            </el-form-item>
            <!-- 广告主 -->
            <el-form-item>
              <el-input
                :value="agent"
                @input="onInputAgent"
                placeholder="代理商"
                :disabled="readOnly || businessAuthDisabled"
              ></el-input>
            </el-form-item>
          </el-row>
        </template>
      </template>
    </el-form>
  </div>
</template>
<script>
/**
 * @component
 * @assetTitle 稿件商单
 * @assetDescription 展示商单信息，包括花火、私单
 * @assetImportName BusinessOrder
 * @assetTag 稿件业务组件
 */
import { businessOrderApi } from '@/v2/api'
import { AgTooltip } from '@/v2/pure-components/ElementUpdate'
import {
  JUDGE_APPLICABLE_LIST_TYPES,
  JUDGE_TYPE_LABEL,
  JUDGE_TYPES_NEW,
  JUDGE_TYPES_PENDING_LIMITED,
  JUDGE_TYPES_PENDING_IN_JUDGE_TODO,
  JUDGE_TYPES_PENDING_OUT_JUDGE_TODO
} from './constants'
import { mapState, mapActions } from 'vuex'
import moment from 'moment'

export default {
  data() {
    return {
      options: {
        flowTags: []
      },
      businessJudgeTypeOpts: [],
      businessConfigs: [],
      // 当前选中的推广行业
      curIndustry: null,
      // 当前选中的品牌
      selectedBrand: null,
      // 当前选中的推广形式
      selectedShowTypes: null,
      showTip: false,
      showDropdown: false,
      isOrder: false,
      disableIsPorder: false,
      disableJudgeInfo: false,
      watchOnce: false,
      transferOnce: false,
      // 所有品牌可选项
      brandList: [],
      // 推广品牌搜索值
      brandSearchValue: ''
    }
  },
  props: {
    advertiser: String,
    agent: String,
    aid: Number,
    brandId: [Number, String],
    brandName: String,
    businessJudgeInfo: Object,
    disabled: Boolean,
    flowPool: Number,
    groupId: [String, Number], // 兼容已删除的流量套餐21，会展示成数字，所以两个类型
    hideJudgeInfo: {
      type: Boolean,
      default: false
    },
    industryId: [String, Number],
    isBusinessOrder: Boolean,
    isPorder: Number, // 私单
    listType: String,
    listReview: Number,
    official: Number,
    orderInfo: Object,
    pool: Number,
    porderNotify: Number,
    publishTime: String,
    readOnly: Boolean,
    showBizSubmit: Boolean,
    showFront: Number,
    showType: String,
    useMockSubmit: {
      type: Boolean,
      default: false
    },
    mockSubmitFn: Function
  },
  components: {
    AgTooltip
  },
  computed: {
    ...mapState({
      perms: (state) => state.user.perms,
      adorderConfigs: (state) => state.common.adorderConfigs || [],
      allBrands: (state) => state.business.allBrands || []
    }),
    industries() {
      return this.adorderConfigs.filter((item) => item.type === 0)
    },
    showTypes() {
      return this.adorderConfigs.filter((item) => item.type === 1)
    },
    isMobileGame(id) {
      return this.curIndustry && this.curIndustry.id === 1
    },
    showTypeText() {
      return this.selectedShowTypes
        ? this.selectedShowTypes.map((item) => item.name).join(',')
        : ''
    },
    disabledShow() {
      return !(this.showTip && !this.showDropdown && this.showTypeText)
    },
    businessAuthDisabled() {
      return !this.perms.ARC_PORDER
    },
    formatedOrderInfo() {
      const { name, id } = this.orderInfo || {}
      if (name || id) return `${name}: ${id}`
      return ''
    },
    // 选择官方品牌, 依赖allBrands
    allBrandOptions() {
      if (this.readOnly || this.businessAuthDisabled) {
        return this.allBrands.filter(
          (item) => item.id === this.selectedBrand?.id
        )
      }

      return this.allBrands
    },
    showJudgeInfo() {
      return (
        JUDGE_APPLICABLE_LIST_TYPES.includes(this.listType) &&
        !this.hideJudgeInfo
      )
    },
    isSparkOrder() {
      return this.pool === 2
    },
    transferredJudgeTypeText() {
      // 需要把 judgeType 的枚举转译成中文，防止选项中没有匹配时直接展示枚举的情况
      return JUDGE_TYPE_LABEL[this.businessJudgeInfo.type] || ''
    },
    publishedOver30Days() {
      const publishMoment = moment(this.publishTime, 'YYYY-MM-DD HH:mm:ss')
      const currentMoment = moment()
      const diffInDays = currentMoment.diff(publishMoment, 'days')
      return diffInDays > 30
    }
  },
  watch: {
    // 切换任务或者刚进来时，2.如果是商单数据，则需要刷新
    aid: {
      handler() {
        this.$nextTick(() => {
          this.clearData()
          this.isOrder = this.isPorder || this.isBusinessOrder ? 1 : 0
          if (this.isOrder) {
            this.init()
          }
        })
      },
      immediate: true
    },
    businessJudgeInfo: {
      handler(val) {
        if (this.watchOnce) return
        this.watchOnce = true
        if (val?.enable && this.perms.BUSINESS_JUDGE) {
          // listType '123' 说明是逃单研判通道
          this.businessJudgeTypeOpts =
            this.listType === '123'
              ? JUDGE_TYPES_PENDING_IN_JUDGE_TODO
              : JUDGE_TYPES_PENDING_OUT_JUDGE_TODO
          this.disableIsPorder = true
        } else if (val?.enable && !this.perms.BUSINESS_JUDGE) {
          this.businessJudgeTypeOpts = JUDGE_TYPES_PENDING_LIMITED
          this.disableJudgeInfo = true
          this.disableIsPorder = true
        } else {
          this.businessJudgeTypeOpts = JUDGE_TYPES_NEW
        }
      },
      immediate: true
    },
    publishedOver30Days: {
      handler(val) {
        if (val) {
          this.onChangePorderNotify(0)
        }
      },
      immediate: true
    }
  },
  methods: {
    ...mapActions({
      fetchAdorderConfigs: 'common/fetchAdorderConfigs',
      clearAdorderConfigs: 'common/clearAdorderConfigs',
      getAllOfficialBrand: 'business/getAllOfficialBrand'
    }),
    // 清空本地勾选数据
    clearData() {
      this.options = {
        flowTags: []
      }
      this.clearAdorderConfigs()
      this.curIndustry = null
      this.selectedBrand = null
      this.selectedShowTypes = null
    },
    // 切换成商单稿件就需要，查询一堆下拉项接口数据
    handleChangePorder(val) {
      this.$emit('update:isPorder', val)
      this.isOrder = val

      if (val === 1) {
        this.init()
        this.$emit('update:porderNotify', this.publishedOver30Days ? 0 : 1)
        this.disableJudgeInfo = true
        this.emitBizJudgeInfo('enable', 0)
      } else {
        this.$emit('update:porderNotify', 0)
        this.disableJudgeInfo = false
      }
    },
    // 逃单研判与“设为非绿洲商单”互斥
    handleUpdateJudgeInfo(val) {
      this.emitBizJudgeInfo('enable', val)
      this.disableIsPorder = !!val
      if (val) this.$emit('update:isPorder', 0)
    },
    // 获取流量套餐选项
    getFlowTags() {
      businessOrderApi
        .getFlowTags({
          pool: this.flowPool === -1 ? 2 : this.flowPool,
          // 写死的state
          state: 0
        })
        .then((res) => {
          const data = res?.data?.items || []
          this.options.flowTags = data
        })
        .catch((_) => {})
    },
    indChange() {
      const official = this.isMobileGame ? 1 : 0
      let brandName = this.brandName

      // 如果是官方品牌的，则需要去拿选项列表
      if (official && this.curIndustry) {
        this.getAllOfficialBrand()
      } else {
        brandName = ''
      }

      this.$emit('update:industryId', this.curIndustry.id)
      this.$emit('update:official', official)
      this.$emit('update:brandName', brandName)
      this.$emit('update:brandId', '')
    },
    officialChange(official) {
      let groupId = this.groupId

      if (official === 0 && parseInt(this.curIndustry.id, 10) === 1) {
        // 手机游戏
        groupId = 21 // 其他手游品牌五限
      }

      this.$emit('update:official', official)
      this.$emit('update:groupId', groupId)
    },
    updateBrand(brand) {
      if (!brand) return
      const { id, name = '' } = brand

      this.$emit('update:brandId', id)
      this.$emit('update:brandName', name)
    },
    updateShowType(showTypes) {
      let newShowType

      if (!showTypes || showTypes.length === 0) {
        newShowType = ''
      } else {
        newShowType = showTypes.map((item) => item.id).join(',')
      }

      this.$emit('update:showType', newShowType)
    },
    emitBizJudgeInfo(key, value) {
      this.$emit('update:businessJudgeInfo', {
        ...this.businessJudgeInfo,
        [key]: value
      })
    },
    handleShowTip(e) {
      e && e.stopPropagation()
      this.showTip = true
    },
    handleHideTip(e) {
      e && e.stopPropagation()
      this.showTip = false
    },
    initShowType(showType) {
      if (showType) {
        const typeIds = showType.split(',').map((item) => {
          return +item
        })
        this.selectedShowTypes = this.showTypes.filter(
          (type) => typeIds.indexOf(type.id) > -1
        )
      }
    },
    initBrand(industryId, brandId) {
      let official = this.official

      if (!industryId || industryId === 0) {
        this.curIndustry = null
        official = 0
        this.$emit('update:official', official)
        return
      }

      this.curIndustry = this.industries.find((ind) => ind.id === industryId)
      if (!this.curIndustry) return
      // 官方品牌或手游行业
      if (official || this.isMobileGame) {
        this.getAllOfficialBrand().then(() => {
          const brands = this.allBrands
          let brand = null
          const newBrandId = parseInt(brandId, 10)
          if (brands) {
            brand = brands.find((brand) => brand.id === newBrandId)
          }
          this.selectedBrand = brand || null
        })
      }
    },
    initIndustrySelected(industryId) {
      const idx = this.industries.findIndex((item) => +item.id === +industryId)
      if (idx > -1) {
        this.curIndustry = this.industries[idx]
      }
    },
    onChangePorderNotify(porderNotify) {
      this.$emit('update:porderNotify', porderNotify)
    },
    onChangeGroupId(groupId) {
      this.$emit('update:groupId', groupId)
    },
    onInputBrandName(brandName) {
      this.$emit('update:brandName', brandName)
    },
    onInputAgent(agent) {
      this.$emit('update:agent', agent)
    },
    onInputAdvertiser(advertiser) {
      this.$emit('update:advertiser', advertiser)
    },
    getAllBrands(query) {
      this.brandSearchValue = query
      if (!query) {
        this.brandList = []
        return
      }
      businessOrderApi
        .getBrandList({
          query
        })
        .then((res) => {
          this.brandList = (res?.data || []).slice(0, 200)
        })
    },
    brandChange(brandId) {
      if (brandId === 0) {
        this.$emit('update:brandName', '')
        this.$emit('update:brandId', brandId)
      } else {
        const brandName = this.brandList.find(
          (item) => item.brand_id === brandId
        )?.brand_name
        this.$emit('update:brandName', brandName)
        this.$emit('update:brandId', brandId)
      }
    },
    // 其他-品牌名输入框
    brandNameChange(brandName) {
      this.$emit('update:brandName', brandName)
      this.$emit('update:brandId', 0)
    },
    initNewBrand(brandId, brandName) {
      if (brandId === 0) {
        this.brandSearchValue = '其他'
      } else {
        this.getAllBrands(brandName)
      }
    },
    async init() {
      // 1.获取数据
      await this.fetchAdorderConfigs({ pool: this.pool })
      // 2.获取流量套餐
      this.getFlowTags()
      // 3.初始化推广形式
      this.initShowType(this.showType)
      // 4.初始化推广品牌
      this.initBrand(this.industryId, this.brandId)
      // 5.初始化选中行业
      this.initIndustrySelected(this.industryId)
      // 6.初始化推广品牌(非花火商单)
      if (!this.isSparkOrder) {
        this.initNewBrand(this.brandId, this.brandName)
      }
    }
  }
}
</script>
<style lang="stylus" scoped>
.business-order {
  background: var(--content-bg-color);

  >>> .el-form-item {
    margin-bottom: 0;
  }
}
</style>
