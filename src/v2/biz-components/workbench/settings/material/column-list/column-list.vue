<template>
  <div class="column-list">
    <div class="column-item" v-for="(item, index) in columnList" :key="index">
      <div class="column-item__title">
        <el-link type="primary" @click="openInNewTab(item.jump_uri)">{{ item.title }}</el-link>
      </div>
    </div>
    <el-link type="primary" v-if="showViewMore" @click="openInNewTab(columnListUrl)">
      查看更多
    </el-link>
  </div>
</template>
<script>
export default {
  name: 'ColumnList',
  data() {
    return {}
  },
  props: {
    columnList: {
      type: Array,
      default: () => []
    },
    showViewMore: {
      type: Boolean, // 是否显示查看更多按钮
      default: false
    },
    columnListUrl: String
  },
  methods: {
    openInNewTab(url) {
      // 打开新标签页
      window.open(url, '_blank')
    }
  }
}
</script>
