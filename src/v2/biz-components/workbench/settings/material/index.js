import ArchiveOid from '@/v2/biz-components/workbench/settings/material/archive-oid'
import ArchiveTitle from '@/v2/biz-components/workbench/settings/material/archive-title'
import Arctype from '@/v2/biz-components/workbench/settings/material/arctype'
import BiliRoomReport from '@/v2/biz-components/workbench/settings/material/bili-room-report'
import Button from '@/v2/biz-components/workbench/settings/material/button'
import BvcHubLink from '@/v2/biz-components/workbench/settings/material/bvc-hub-link'
import ColorText from '@/v2/biz-components/workbench/settings/material/color-text'
import ColumnList from '@/v2/biz-components/workbench/settings/material/column-list'
import ControllerList from '@/v2/biz-components/workbench/settings/material/controller-list'
import CustomText from '@/v2/biz-components/workbench/settings/material/custom-text'
import DialogTrigger from '@/v2/biz-components/workbench/settings/material/dialog-trigger'
import Duration from '@/v2/biz-components/workbench/settings/material/duration'
import GroupInfo from '@/v2/biz-components/workbench/settings/material/group-info'
import ImgComp from '@/v2/biz-components/workbench/settings/material/img'
import Indicator from '@/v2/biz-components/workbench/settings/material/indicator'
import Link from '@/v2/biz-components/workbench/settings/material/link'
import LivePictureQaResult from '@/v2/biz-components/workbench/settings/material/live-picture-qa-result'
import LiveRoom from '@/v2/biz-components/workbench/settings/material/live-room'
import RichText from '@/v2/biz-components/workbench/settings/material/rich-text'
import StampCollection from '@/v2/biz-components/workbench/settings/material/stamp-collection'
import Tag from '@/v2/biz-components/workbench/settings/material/tag'
import Text from '@/v2/biz-components/workbench/settings/material/text'
import UpInfo from '@/v2/biz-components/workbench/settings/material/up-info'
import StreamPlayer from '@/v2/biz-components/workbench/settings/material/stream-player'
import AsrText from '@/v2/biz-components/workbench/settings/material/asr-text'
import PreviewImgList from '@/v2/biz-components/workbench/settings/material/preview-img-list'
import ReferenceInfo from '@/v2/biz-components/workbench/settings/material/reference-info'
import LiveRoomInfo from '@/v2/biz-components/workbench/settings/material/live-room-info'
import FansInfo from '@/v2/biz-components/workbench/settings/material/fans-info'
import DateText from '@/v2/biz-components/workbench/settings/material/date-text'
import ColEllipsis from '@/v2/biz-components/workbench/settings/material/col-ellipsis'
import UpLabelInfo from '@/v2/biz-components/workbench/settings/material/up-label-info'
import liveRoomInfoCard from '@/v2/biz-components/workbench/settings/material/live-room-info-card'
import LiveContentCard from '@/v2/biz-components/workbench/settings/material/live-content-card'
import LiveListCard from '@/v2/biz-components/workbench/settings/material/live-list-card'
import TextWrap from '@/v2/biz-components/workbench/settings/material/text-wrap'
import MarkingCascader from '@/v2/biz-components/workbench/settings/material/marking-cascader'
import GroupContainer from '@/v2/biz-components/workbench/settings/material/group-container'
import List from '@/v2/biz-components/workbench/settings/material/col-list'
import Rich from '@/v2/biz-components/workbench/settings/material/col-rich'
import TagList from '@/v2/biz-components/workbench/settings/material/tag-list'

export {
  ArchiveOid,
  ArchiveTitle,
  Arctype,
  BiliRoomReport,
  Button,
  BvcHubLink,
  ColorText,
  ColumnList,
  ControllerList,
  CustomText,
  DialogTrigger,
  Duration,
  GroupInfo,
  ImgComp,
  Indicator,
  Link,
  LivePictureQaResult,
  LiveRoom,
  RichText,
  StampCollection,
  Tag,
  Text,
  UpInfo,
  StreamPlayer,
  AsrText,
  PreviewImgList,
  ReferenceInfo,
  LiveRoomInfo,
  FansInfo,
  DateText,
  ColEllipsis,
  UpLabelInfo,
  liveRoomInfoCard,
  LiveContentCard,
  LiveListCard,
  TextWrap,
  MarkingCascader,
  GroupContainer,
  List,
  Rich,
  TagList
}
