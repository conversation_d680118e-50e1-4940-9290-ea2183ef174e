<template>
  <div class="video-audit-oper-v2 p-4">
    <AuditButton
      v-for="option in actionList"
      :key="option.value"
      :type="option.type"
      :active="form.status === option.value"
      :value="option.value"
      :disabled="disabled"
      style="margin-right: 8px"
      @click="onClickAduitButton(option.value)"
      v-behavior-track="`${option.value}-audit-button`"
    >
      {{ option.label }}
    </AuditButton>

    <template v-if="!isNil(form.status)">
      <!-- 1. 开放浏览 -->
      <template v-if="form.status === 0">
        <!-- 仅复审2：禁止属性 -->
        <span class="forbid-props mr-16" v-if="showForbid">
          <span>禁止属性：</span>
          <el-checkbox
            v-for="item in forbidList"
            class="mr-8"
            :key="item.value"
            :value="item.value"
            size="small"
            v-model="form[item.value]"
            :true-label="1"
            :false-label="0"
            :disabled="disabled || !perms[item.perm]"
            v-behavior-track="`${item.value}-forbid-checkbox`"
          >
            {{ item.label }}
          </el-checkbox>
        </span>
        <div class="label mt-8" v-if="loadResultOnly">
          标签：{{ form.full_path_names }}
        </div>
        <template v-else>
          <div class="label mt-8" v-if="showV2Result">
            标签：{{ resultV2Text }}
          </div>
          <SuccessTag
            v-else
            v-model="form.tids"
            :tagList="tagList"
            :disabled="disabled"
            @input="handleSuccessTagChange"
          />
        </template>
        <template v-if="!hideGrayTag">
          <!-- 使用稿件灰标 -->
          <template v-if="useArchiveGrayTags">
            <div v-if="hasArchiveGrayTagAuth" class="w-full mt-4 mb-4">
              稿件灰标：
              <el-cascader
                ref="grayTagRef"
                style="min-width: 600px"
                size="small"
                :options="grayTagOps"
                :value="form.archive_gray_tags"
                :disabled="!(perms && perms.ARCH_GRAY_TAGS_WRITE)"
                :props="{
                  expandTrigger: 'hover',
                  children: 'options',
                  label: 'name',
                  value: 'id',
                  multiple: true
                }"
                @change="handleArchiveGrayTagChange"
              ></el-cascader>
              <LimitReasonAndSnapshot
                ref="archiveTagReasonRef"
                :aid="aid"
                :disabled="!(perms && perms.ARCH_GRAY_TAGS_WRITE)"
                labelWidth="45px"
                :limitTags="selectedGrayTagNodes"
                tagType="grayTag"
                 :videoSnapshotOptions="videoSnapshotOptions"
                @update:grayTagReason="(newVal) => this.grayTagReason = newVal"
              />
            </div>
          </template>

          <!-- 使用视频灰标  -->
          <template v-else-if="hasGrayTagAuth">
            <div v-if="grayTagTree && grayTagTree.length">
              稿件灰标：
              <TagGroup
                :tagList="grayTagTree"
                multiple
                :value="form.gray_tags"
                :disabled="!(perms && perms.VIDEO_GRAY_TAGS_WRITE)"
                @input="handleGrayTagChange"
              />
              <LimitReasonAndSnapshot
                ref="grayTagReasonRef"
                :aid="aid"
                :disabled="!(perms && perms.VIDEO_GRAY_TAGS_WRITE)"
                labelWidth="45px"
                :limitTags="selectedGrayTagLabel"
                tagType="grayTag"
                :videoSnapshotOptions="videoSnapshotOptions"
                @update:grayTagReason="(newVal) => this.grayTagReason = newVal"
              />
            </div>
          </template>
        </template>
      </template>
      <!-- 2. 打回锁定 -->
      <template v-if="form.status === -2 || form.status === -4">
        <!-- 仅复审2：禁止属性 -->
        <span class="forbid-props mr-16" v-if="showForbid">
          <span>禁止属性：</span>
          <el-checkbox
            v-for="item in forbidList"
            class="mr-8"
            :key="item.value"
            :value="item.value"
            size="small"
            v-model="form[item.value]"
            :true-label="1"
            :false-label="0"
            :disabled="disabled || !perms[item.perm]"
            v-behavior-track="`${item.value}-forbid-checkbox`"
          >
            {{ item.label }}
          </el-checkbox>
        </span>
        <div v-if="showV2Result || loadResultOnly" class="flex-ac mt-8">
          <div class="label">
            位置：{{
              form.position_id
                ? VIDEO_AUDIT_POSITION_MAP[this.form.position_id]
                : ''
            }}
          </div>
        </div>
        <div class="flex-ac mt-8">
          <div class="label" v-if="loadResultOnly">
            标签：{{ form.full_path_names }}
          </div>
          <template v-else>
            <div class="label">
              标签：{{ showV2Result ? resultV2Text : '' }}
            </div>
            <el-cascader
              v-if="!showV2Result"
              v-model="resultV3"
              ref="auditTag"
              :disabled="disabled"
              :options="tagList"
              :props="{
                label: 'name',
                value: 'id',
                children: 'options',
                expandTrigger: 'hover'
              }"
              :popper-class="'video-audit-oper'"
              :placeholder="unmatchablePath"
              :class="{
                'unmatchable-text-selected': unmatchablePath !== '请选择'
              }"
              filterable
              style="width: 50%"
              @change="onChangeTag"
            />
          </template>
        </div>
        <div
          class="flex-ac mt-8"
          v-if="(form.tids && form.tids.length) || form.forward || form.reason"
        >
          <span v-if="form.tids && form.tids.length">理由：</span>
          <el-input
            v-if="form.forward"
            placeholder="请输入撞车跳转目标"
            :disabled="disabled"
            class="w-160 mr-4"
            size="small"
            v-model="form.forward"
          />
          <AgTextarea
            v-if="showV2Result || loadResultOnly"
            type="textarea"
            size="small"
            placeholder="请输入内容"
            :value="form.reason"
            :rows="2"
            :disabled="true"
          />
          <template v-else>
            <ReasonFiller
              class="flex-1"
              ref="ReasonFillerNode"
              v-if="form.tids && form.tids.length"
              v-model="form.reason"
              :reasonTemplate="form.reasonTemplate"
              :editable="perms && perms.AEGIS_REASON_INPUT_VIDEO"
            />
          </template>
        </div>
        <div
          class="flex-ac mt-8"
          v-if="
            showSuggest &&
            form.tids &&
            form.tids.length &&
            (form.suggest ||
              suggestList.length ||
              (form.suggests && form.suggests.length))
          "
        >
          <div>修改建议：</div>

          <!-- 优先回显 -->
          <div v-if="form.suggest">
            {{ form.suggest }}
          </div>

          <!-- 其次操作 -->
          <el-select
            v-else-if="suggestList.length"
            style="width: 80%"
            multiple
            v-model="form.suggests"
            :disabled="!(perms && perms.SUGGEST_WRITE)"
          >
            <el-option
              v-for="(item, idx) in suggestList"
              :key="idx"
              :label="item"
              :value="item"
            ></el-option>
          </el-select>

          <div v-else-if="form.suggests && form.suggests.length">
            {{ form.suggests.join('、') }}
          </div>
        </div>

        <div class="flex-as mt-8" v-if="showPictureSelect">
          <div style="min-width: 48px">截图：</div>
          <!-- 优先回显 -->
          <div
            v-if="form.picture && form.picture.length"
            class="flex-ac flex-wrap"
          >
            <el-popover
              v-for="(e, idx) in form.picture"
              :key="idx"
              width="240"
              trigger="hover"
            >
              <el-image
                :src="e.url"
                :preview-src-list="[e.url]"
                fit="fill"
              ></el-image>
              <el-tag
                type="info"
                slot="reference"
                class="mr-8 mb-4 cursor-pointer"
              >
                {{ e.time }}
              </el-tag>
            </el-popover>
          </div>
          <!-- 质检回显 -->
          <div
            v-else-if="
              !allScreenshots.length &&
              form.pictureList &&
              form.pictureList.length
            "
          >
            <el-popover
              v-for="(e, idx) in form.pictureList"
              :key="idx"
              width="240"
              trigger="hover"
            >
              <el-image
                :src="e.url"
                :preview-src-list="[e.url]"
                fit="fill"
              ></el-image>
              <el-tag type="info" slot="reference" class="mr-8 cursor-pointer">
                {{ e.time }}
              </el-tag>
            </el-popover>
          </div>

          <!-- 其次操作 -->
          <el-select
            v-else
            :value="form.pictureList.map((e) => e.uuid)"
            multiple
            style="width: 80%"
            @change="onChangePictureList"
            :disabled="!(perms && perms.VIDEO_PIC_WRITE)"
          >
            <el-option
              v-for="(item, idx) in allScreenshots"
              :key="idx"
              :label="`${item.time}${
                item.note ? '（内部备注：' + item.note + '）' : ''
              }`"
              :value="item.uuid"
            >
              <div class="flex-ac font-grey flex-lr">
                <div>
                  {{
                    `${item.time}${
                      item.note ? '（内部备注：' + item.note + '）' : ''
                    }`
                  }}
                </div>
                <el-popover width="240" trigger="hover">
                  <el-image
                    :src="item.url"
                    :preview-src-list="[item.url]"
                    fit="fill"
                  ></el-image>
                  <i class="el-icon-view" slot="reference" />
                </el-popover>
              </div>
            </el-option>
          </el-select>
        </div>
      </template>
    </template>

    <div class="flex-ac mt-8">
      <span>备注：</span>
      <AgSelect
        v-model="quickNote"
        class="w-120 mr-4"
        placeholder="选择快捷文本"
        size="mini"
        :disabled="disabled"
        @change="onChangeQuickNote"
        filterable
        v-behavior-track="'audit-remark-selector'"
      >
        <el-option
          v-for="reason in VIDEO_AUDIT_REASONS"
          :key="reason"
          :label="reason"
          :value="reason"
        />
      </AgSelect>
      <div style="height: 30px" class="flex-1">
        <AgTextarea
          type="textarea"
          :rows="1"
          size="mini"
          placeholder=""
          v-model="form.note"
          :disabled="disabled"
          v-behavior-track="'audit-remark-textarea'"
        />
      </div>
    </div>
    <MonitorTag
      :monitorTagId="form.monitor_tag_id"
      :showMonitorTag="form.status === 0"
      @input="handleMonitorTagChange"
    />
  </div>
</template>
<script>
import { isNil, cloneDeep, uniqBy } from 'lodash-es'
import { mapState, mapActions } from 'vuex'
import SuccessTag from './SuccessTag.vue'
import MonitorTag from './MonitorTag.vue'
import AuditButton from '@/components/element-update/AuditButton.vue'
import AgTextarea from '@/components/element-update/Textarea.vue'
import AgSelect from '@/components/element-update/Select.vue'
import ReasonFiller from '@/v2/pure-components/ReasonFiller'
import LimitReasonAndSnapshot from '@/v2/biz-components/archive/LimitReasonAndSnapshot.vue'
import notify from '@/lib/notify'
import { tagApi } from '@/api'
import {
  VIDEO_AUDIT_POSITION_MAP,
  VIDEO_AUDIT_REASONS,
  getEnvConstant
} from '@/utils/constant.js'
import TagGroup from '@/v2/pure-components/TagGroup.vue'
import { getNodesByFullTagIds } from '@/v2/biz-utils/classifyTags'

/**
 * @component
 * @assetTitle 操作模块v2
 * @assetDescription 视频详情页新版操作模块，包括操作按钮、位置、tag、理由、撞车设置、备注等。
 * @assetImportName VideoAuditOperV2
 * @assetTag 视频业务组件
 */

// 禁止属性
const forbidList = [
  {
    value: 'norank',
    label: '排行',
    perm: 'NO_RANK'
  },
  {
    value: 'noindex',
    label: '动态',
    perm: 'NO_INDEX'
  },
  {
    value: 'norecommend',
    label: '推荐',
    perm: 'NO_RECOMMEND'
  },
  {
    value: 'nosearch',
    label: '搜索',
    perm: 'NO_SEARCH'
  }
]

/**
 * TODO 注意切换任务时清空已选的tag
 */
export default {
  components: {
    AuditButton,
    AgTextarea,
    AgSelect,
    MonitorTag,
    SuccessTag,
    ReasonFiller,
    TagGroup,
    LimitReasonAndSnapshot
  },
  computed: {
    ...mapState({
      perms: (state) => state.user.perms,
      grayTagOps: (state) => state.grayTag.grayTagOps,
      leafNodes: (state) => state.grayTag.leafNodes
    }),
    // 有灰标读权限或者写权限
    hasGrayTagAuth() {
      return (
        this.perms?.VIDEO_GRAY_TAGS_READ || this.perms?.VIDEO_GRAY_TAGS_WRITE
      )
    },
    // 稿件灰标的权限
    hasArchiveGrayTagAuth() {
      return this.perms?.ARCH_GRAY_TAGS_READ || this.perms?.ARCH_GRAY_TAGS_WRITE
    },
    // 当前标签支持截图
    canPicture() {
      return !!this.currentTagObj?.extra_data?.can_picture
    },
    // 显示截图下拉选项：回显+手动选择标签
    showPictureSelect() {
      return (
        this.showScreenshot &&
        this.form.tids?.length &&
        (this.form.picture?.length ||
          this.canPicture ||
          this.form.pictureList?.length)
      )
    },
    // 当前标签支持修改建议的选项
    suggestList() {
      return this.currentTagObj?.extra_data?.suggest_enum || []
    },
    actionList() {
      const list = [
        {
          value: 0,
          type: 'success',
          label: '开放浏览'
        },
        {
          value: -2,
          type: 'warning',
          label: '打回'
        },
        {
          value: -4,
          type: 'danger',
          label: '锁定'
        }
      ]
      return this.hide_open ? list.filter((e) => e.value !== 0) : list
    },
    showV2Result() {
      return (
        Array.isArray(this.resultV2) && !!this.resultV2.length && !this.dirty
      )
    },
    tagList() {
      let tagList = []
      if (this.form.status === 0) {
        tagList = this.successTagTree
      } else if (this.form.status === -2 || this.form.status === -4) {
        tagList =
          this.form.status === -2 ? this.rejectTagTree : this.lockTagTree
      }
      // 展示0号tag的前提：特定待办-open0Tag、有权限
      return (tagList || []).map((e) => {
        return {
          ...e,
          disabled: this.getTagDisable(e.tag_id)
        }
      })
    },
    resultV2Text() {
      return (this.resultV2 || []).map((e) => e.tag_name).join('，')
    },
    unmatchablePath() {
      return this.dirty ? '请选择' : this.resultV3.join(' / ') || '请选择'
    },
    videoSnapshotOptions() {
      return this.allScreenshots?.map((e) => ({
        snapshotUrl: e.url,
        text: e.time,
        pIndex: this.pIndex,
        uuid: Math.random().toString(36).slice(2),
      })) || []
    }
  },
  watch: {
    cid(val) {
      if (val) {
        this.getTag()
      }
    },
    filename() {
      this.quickNote = ''
    },
    'form.note': {
      handler(val) {
        if (!val) {
          this.quickNote = ''
        }
      }
    }
  },
  props: {
    hideGrayTag: Boolean, // 隐藏稿件灰标
    aid: {
      type: [String, Number],
      default: ''
    },
    cid: {
      type: [String, Number],
      default: ''
    },
    // 复审2按需隐藏通过操作
    hide_open: Boolean,
    // 是否禁用
    disabled: {
      type: Boolean
    },
    // 包含所有操作项
    form: {
      type: Object,
      default() {
        return {
          status: null,
          // --- 除了复审2废弃 start ---
          norank: false,
          noindex: false,
          norecommend: false,
          nosearch: false,
          // --- end ---
          tids: [], // 审核tag
          gray_tags: [], // 灰标
          reason_id: '',
          reason: '',
          note: '',
          position_id: '', // 位置
          forward: '',
          reasonTemplate: '',
          suggests: [], // 修改建议数组
          suggest: '', // 修改建议（拼接后的用于回显）
          picture: [], // 截图（用于回显）
          pictureList: [] // 操作选中的截图列表
        }
      }
    },
    // 当前视频文件名
    filename: {
      type: String
    },
    loadResultOnly: {
      type: Boolean,
      default: false
    },
    // 是否展示禁用项
    showForbid: Boolean,
    // 是否可操作0号tag
    open0Tag: {
      type: Boolean,
      default: false
    },
    // 是否支持修改建议
    showSuggest: Boolean,
    // 是否支持违规截图
    showScreenshot: Boolean,
    allScreenshots: {
      type: Array,
      default: () => []
    }, // 视频模块所有的截图
    // 展示稿件灰标
    useArchiveGrayTags: Boolean,
    pIndex: {
      type: Number
    }
  },
  data() {
    return {
      VIDEO_AUDIT_POSITION_MAP,
      VIDEO_AUDIT_REASONS,
      forbidList,
      isNil,
      quickNote: '', // 快捷备注
      tagMapByPosition: {}, // 新数据 状态=>位置=>tagList
      initDisabledTids: [], // 初始有值但禁用的tag
      initStatus: '', // 初值有值但禁用的tag对应的状态
      // v3 操作模块
      dirty: false,
      reasonsById: {}, // key: reasonId, value: { reason: '理由模板' }
      resultV2: [],
      resultV3: [], // [ 位置 tagId, 分类 tagId, 理由 TagId] 被引用
      tagResultCache: [],
      lockTagTree: [],
      rejectTagTree: [],
      successTagTree: [],
      grayTagTree: [],
      gray_tags_before: [],
      currentTagObj: [], // 当前tag对象
      tagReady: true, // 内容分级tag result是否回来了
      tempMultipleReason: null,
      selectedGrayTagNodes: [], // 稿件灰标选中值
      selectedGrayTagLabel: [], // 视频灰标选中值
      noteTagReason: [], // 备注标限流理由
      grayTagReason: [], // 灰标限流理由
    }
  },
  created() {
    this.getTag()
    this.fetchGrayTags()
  },
  mounted() {
    this.startEventWatcher()
  },
  beforeDestroy() {
    this.stopEventWatcher()
  },
  methods: {
    ...mapActions({
      fetchGrayTags: 'grayTag/fetchGrayTags'
    }),
    async getTag() {
      if (this.loadResultOnly) {
        this.tagReady = true
        try {
          const res = await tagApi.getAuditTagsAndReasons({
            aid: this.aid,
            cid: this.cid
          })
          this.grayTagTree = (res.data?.gray_tags || []).map((e) => ({
            ...e,
            tag_id: e.id
          }))
        } catch (err) {
          console.error(err)
        }
      } else {
        this.tagReady = false
        try {
          // 获取 审核tag、候选标签树、理由
          const res = await tagApi.getAuditTagsAndReasons({
            aid: this.aid,
            cid: this.cid
          })
          const {
            result_tag1,
            result_tag61,
            tags,
            reasons,
            gray_tags,
            result_gray_tag
          } = res.data

          const result_gray_tag_ids = (result_gray_tag || []).map(
            (e) => e.tag_id
          )
          let tids = []
          let fullPathNames = []
          let _gray_tags = []
          if (this.form.status === 0) {
            tids = []
            fullPathNames = []
            _gray_tags = result_gray_tag_ids
            // 有新版结果时，优先展示新版
            if (Array.isArray(result_tag61) && result_tag61.length) {
              tids = result_tag61.map((e) => e.tag_id)
              fullPathNames = result_tag61.map((e) => e.full_names.join('/'))
            } else {
              this.resultV2 = result_tag1
              const OPEN_TAG_MIGRATION_MAP = getEnvConstant(
                'FIRST_AUDIT_OPEN_TAG'
              )
              tids = result_tag1
                .map((e) => OPEN_TAG_MIGRATION_MAP[e.tag_id])
                .filter((e) => !!e)
              fullPathNames = result_tag1
                .map((e) =>
                  OPEN_TAG_MIGRATION_MAP[e.tag_id] ? e.tag_name : ''
                )
                .filter((e) => !!e)
                .map((e) => `开放浏览/${e}`)
            }
            this.tagResultCache = [...tids]
          } else {
            this.resultV2 = result_tag1
            const fullPathIds = result_tag61[0]?.full_ids || []
            this.resultV3 = fullPathIds.filter((_e, i) => i !== 0)
            this.tagResultCache = [...this.resultV3]
            tids = []
            fullPathNames = []
            _gray_tags = []
            if (Array.isArray(result_tag61) && result_tag61.length) {
              tids = result_tag61.map((e) => e.tag_id)
              fullPathNames = result_tag61.map((e) => e.full_names.join('/'))
            }
          }
          this.reasonsById = reasons
          this.lockTagTree = tags.find((e) => e.name === '锁定')?.options || []
          this.rejectTagTree =
            tags.find((e) => e.name === '打回')?.options || []
          this.successTagTree = (
            tags.find((e) => e.name === '开放浏览')?.options || []
          ).map((e) => ({ ...e, tag_id: e.id }))
          if (this.form.status === 0) {
            // 兜底：通过状态下不能有打回的tag
            const validTids = this.successTagTree?.map((e) => e.id) || []
            this.tagResultCache = this.tagResultCache.filter((e) =>
              validTids.includes(e)
            )
            tids = this.tagResultCache
            fullPathNames = this.tagResultCache
              .map(
                (e) => this.successTagTree.find((t) => t.id === e)?.full_name
              )
              .filter((e) => !!e)
          }
          this.triggerChange({
            ...this.form,
            tids,
            full_path_names: fullPathNames.join(','),
            gray_tags: _gray_tags
          })

          this.grayTagTree = (gray_tags || []).map((e) => ({
            ...e,
            tag_id: e.id
          }))
          this.gray_tags_before = result_gray_tag_ids
          this.tagReady = true
          if(!this.useArchiveGrayTags) {
            this.selectedGrayTagLabel = _gray_tags?.map(v => {
              return {
                value: v,
                pathLabels: [this.grayTagTree.find(c => c.id === v)?.full_name]
              }
            })
          } else {
            const selectedNodes = getNodesByFullTagIds(this.leafNodes, this.form.archive_gray_tags)
            this.selectedGrayTagNodes = selectedNodes
          }
        } catch (e) {
          console.error(e)
          this.tagReady = true
        }
      }
    },
    startEventWatcher() {
      this.$EventBus.$on('select-reason-time-slice', this.handleSelectTimeSlice)
    },
    stopEventWatcher() {
      this.$EventBus.$off(
        'select-reason-time-slice',
        this.handleSelectTimeSlice
      )
    },
    // 根据驳回理由中的时间段，自动填充截图
    // 仅指定tag下有截图
    // 仅有权限的可以截图
    handleSelectTimeSlice(selectedSlices) {
      if (!(this.canPicture && this.showScreenshot)) return
      if (selectedSlices?.length) {
        let autoSelectedScreenshotItems = []
        selectedSlices.forEach((slice) => {
          const { sTime, eTime } = slice
          const matchedScreenshotItems = this.allScreenshots.filter((e) => {
            if (isNil(eTime)) {
              return e.rawSecond === sTime
            }
            return e.rawSecond >= sTime && e.rawSecond <= eTime
          })
          autoSelectedScreenshotItems = autoSelectedScreenshotItems.concat(
            matchedScreenshotItems
          )
        })
        if (autoSelectedScreenshotItems.length) {
          this.triggerChange({
            ...this.form,
            pictureList: uniqBy(
              (this.form.pictureList || []).concat(autoSelectedScreenshotItems),
              (e) => e.uuid
            )
          })
        }
      }
    },
    getAuditTags(status) {
      this.dirty = false
      if (status === -2 || status === -4) {
        this.resultV3 = this.tagResultCache
        return this.tagResultCache.slice(-1)
      }
      return this.tagResultCache
    },
    // 操作模块变更，统一对外暴露完整的操作项，开放浏览状态下部分字段置空
    triggerChange(value) {
      // 状态为开放浏览0：无reason，reason_id，forward，position_id
      const newVal = cloneDeep(value)
      if (value?.status === 0) {
        newVal.reason = ''
        newVal.reason_id = ''
        newVal.forward = ''
        // 通过时删除position_id在提交时做
        newVal.position_id = ''
        newVal.suggests = [] // 通过状态没有修改建议
        newVal.suggest = ''
        newVal.picture = [] // 通过状态没有截图
        newVal.pictureList = []
      }
      // 操作项变更
      // @arg newVal 变更后的完整操作项对象
      this.$emit('change', newVal)
    },
    // 手动改变状态。通过状态下：重新匹配tagList；其他状态，tagList置空，待位置改变后匹配；清空已选择的位置，tids，理由
    onClickAduitButton(value) {
      if (this.disabled) return
      if (!this.tagReady) {
        console.error('click-audit-button-without-tag-ready')
        this.$tracker('click-audit-button-without-tag-ready', {
          cid: this.cid,
          aid: this.aid,
          form: this.form
        })
        return
      }
      this.dirty = true
      if (this.$refs.auditTag) this.$refs.auditTag.panel.activePath = [] // 清空 el-cascader-panel 的状态，避免它报错
      this.resultV3 = []
      const disableTids =
        (this.form?.tids || []).filter(this.getTagDisable) || []
      if (!this.initDisabledTids?.length && disableTids?.length) {
        // 如果有已处理且无权限的tag，一定是第一次触发这里的
        this.initDisabledTids = disableTids
        this.initStatus = this.form.status
      }
      this.triggerChange({
        ...this.form,
        status: value,
        position_id: 1, // 默认选视频
        tids: this.clearTids(value), // 清空已选择的审核tag
        full_path_names: this.clearTids(value)?.length ? '开放浏览/涉0号' : '', // FIXME: 暂时先写死，因为一审目前能禁用的 tag 就只有 “涉0号”
        reason_id: '',
        reason: '',
        reasonTemplate: '',
        forward: '',
        suggests: [],
        suggest: '',
        picture: [],
        pictureList: [],
        gray_tags:
          value === 0 && this.form.status !== 0
            ? this.gray_tags_before
            : this.form.gray_tags
      })
    },
    // 无法操作的tag
    // 1. 权限点PARAMOUNT_LEADER_TAG：前提是待办配置了open0Tag或者是批量审核
    // 2. 权限点PARAMOUNT_NOVA_TAG：开放0 tag操作
    getTagDisable(tag_id) {
      return tag_id === getEnvConstant('LEADER_TAG_ID')
        ? !(
            (this.open0Tag && this.perms?.PARAMOUNT_LEADER_TAG) ||
            this.perms?.PARAMOUNT_NOVA_TAG
          )
        : false
    },
    // disable的标签，不能清空
    clearTids(status) {
      if (this.initStatus === status) {
        return this.initDisabledTids || []
      }
      return []
    },
    onChangeQuickNote(selected) {
      let newNote = this.form?.note || ''
      if (newNote.length > 0) {
        newNote += '\n'
      }
      newNote += selected
      this.triggerChange({
        ...this.form,
        note: newNote
      })
    },
    // 在理由模板中空的中文括号里填充位置
    replaceReason(reason, positionText) {
      return (reason || '').replace('（）', `（${positionText}）`)
    },
    onChangeTag() {
      this.$nextTick(() => {
        const selectedNodes = this.$refs?.auditTag?.getCheckedNodes() || []
        const { data: ogTagObj } = selectedNodes[0] || {}
        const reasonId = ogTagObj?.extra_data?.reason_id
        const fullPathName = ogTagObj.full_name
        if (!reasonId)
          return notify.error(
            '一审审核标签未配置对应的理由模板，请联系@审核平台小姐姐'
          )
        const { data: ogPositionObj } = selectedNodes[0]?.parent?.parent || {}
        const positionId = ogPositionObj?.extra_data?.position_id
        const positionName = ogPositionObj?.name
        const { reason } = this.reasonsById[reasonId] || ''
        const newReasonTemplate = this.replaceReason(reason, positionName)
        this.triggerChange({
          ...this.form,
          position_id: positionId,
          position_text: positionName,
          tids: this.resultV3.slice(-1),
          full_path_names: fullPathName,
          reason_id: reasonId,
          reasonTemplate: newReasonTemplate,
          suggests: [],
          suggest: '',
          picture: [],
          pictureList: []
        })
        this.currentTagObj = ogTagObj
      })
    },
    handleArchiveGrayTagChange(tagList) {
      this.$nextTick(() => {
        const selectedNodes = this.$refs.grayTagRef?.getCheckedNodes(true)
        this.selectedGrayTagNodes = selectedNodes
      })

      this.triggerChange({
        ...this.form,
        archive_gray_tags: tagList
      })
    },
    handleGrayTagChange(tagIds) {
      this.selectedGrayTagLabel = tagIds?.map(v => {
        return {
          value: v,
          pathLabels: [this.grayTagTree.find(c => c.id === v)?.full_name]
        }
      })

      const newTags = tagIds.filter(
        (e) => this.form.gray_tags.indexOf(e) === -1
      )
      let grayNote = ''
      if (newTags?.length) {
        newTags.forEach((id) => {
          const name = this.grayTagTree.find((e) => e.id === id)?.name || ''
          if (name) {
            grayNote += name
          }
        })
      }
      let newNote = this.form.note || ''
      if (grayNote) {
        if (newNote.length) {
          newNote += '\n'
        }
        newNote += grayNote
      }
      this.triggerChange({
        ...this.form,
        gray_tags: tagIds,
        note: newNote
      })
    },
    handleSuccessTagChange(tagIds) {
      const tagNames = []
      const fullPathNames = []
      tagIds.forEach((tagId) => {
        const tag = this.successTagTree.find((e) => e.id === tagId)
        tagNames.push(tag?.name)
        fullPathNames.push(tag?.full_name)
      })
      this.triggerChange({
        ...this.form,
        tids: tagIds,
        full_path_names: fullPathNames.filter((e) => !!e).join(',')
      })
    },
    handleMonitorTagChange(tagId, tagName) {
      this.triggerChange({
        ...this.form,
        monitor_tag_id: tagId,
        monitor_tag_name: tagName
      })
    },
    // @vuese
    // public 校验理由，返回有值代表不通过
    validataReason() {
      if (
        !this.form.reason &&
        (this.form.status === -2 || this.form.status === -4)
      )
        return '请添加驳回理由'
      const reasonNode = this.$refs.ReasonFillerNode
      if (reasonNode) {
        const data = reasonNode.validate()?.data
        if (data?.length) {
          return data.map((e) => e.message).join('；')
        }
      }
      return ''
    },
    onChangePictureList(val) {
      this.triggerChange({
        ...this.form,
        pictureList: val
          .map((uuid) => this.allScreenshots.find((e) => e.uuid === uuid))
          .filter((e) => !!e)
      })
    },
    validateReason() {
      return this.$refs[this.useArchiveGrayTags ? 'archiveTagReasonRef' : 'grayTagReasonRef']?.validateReason()
    }
  }
}
</script>
<style lang="stylus" scoped>
.video-audit-oper-v2 {
  .forbid-props {
    >>> .el-checkbox__label {
      padding-left: 4px;
    }
  }
}

.unmatchable-text-selected {
  >>> .el-input .el-input__inner::placeholder {
    color: var(----text-color);
  }
}

// 质检需要颜色深一点
>>> .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
  border-color: var(--text-color);
}

>>> .el-checkbox__input.is-disabled+span.el-checkbox__label {
  color: var(--text-color);
}

>>> .el-textarea.is-disabled .el-textarea__inner {
  color: var(--text-color);
}

>>> .el-input.is-disabled .el-input__inner {
  color: var(--text-color);
}
>>>.ag-form-item__label {
  font-weight: normal;
}
>>>.ag-form-item {
  margin-bottom: 5px;
}
</style>
<style lang="stylus">
.video-audit-oper {
  .el-cascader-menu__wrap {
    height: 256px; // 7行的高度
  }
}
</style>
