import { safeJsonParse } from '@/utils'

export default function validateConsistentReasons(limitReason, onFailed) {
  // 输入验证，防止无效数据导致问题
  if (!Array.isArray(limitReason)) {
    console.warn('limitReason is not an array:', limitReason)
    return true
  }

  // 防止过大的数组影响性能
  if (limitReason.length > 10000) {
    console.warn('limitReason array is too large:', limitReason.length)
    onFailed && onFailed('数据量过大，请联系技术支持')
    return false
  }

  const checkPool = {}

  for (const record of limitReason) {
    const existing = checkPool[record.reasonId]

    if (existing) {
      if (record.reason !== existing.reason) {
        onFailed(
          `标签 [${existing.fullTagName}] 和 [${record.fullTagName}] 共享模板，但理由不一致`
        )
        throw new Error(
          `标签 [${existing.fullTagName}] 和 [${record.fullTagName}] 共享模板，但理由不一致`
        )
      }

      // 优化：避免在大数组上进行昂贵的字符串操作
      const pictureA = existing.picture || []
      const pictureB = record.picture || []

      // 如果数组过大，跳过详细比较
      if (pictureA.length > 1000 || pictureB.length > 1000) {
        console.warn('Picture array too large, skipping detailed comparison')
        continue
      }

      const pictureTimeSequenceA = pictureA
        .map((e) => (e.pIndex > 0 ? `P${e.pIndex}｜${e.text}` : e.text))
        .join(',')
      const pictureTimeSequenceB = pictureB
        .map((e) => (e.pIndex > 0 ? `P${e.pIndex}｜${e.text}` : e.text))
        .join(',')

      if (pictureTimeSequenceA !== pictureTimeSequenceB) {
        onFailed(
          `标签 [${existing.fullTagName}] 和 [${record.fullTagName}] 共享模板，但截图时间不一致`
        )
        throw new Error(
          `标签 [${existing.fullTagName}] 和 [${record.fullTagName}] 共享模板，但截图时间不一致`
        )
      }
    }

    checkPool[record.reasonId] = record
  }

  return true
}

export function validateAllLimitReasons(limitReasonA, limitReasonB, onFailed) {
  const reasonListA = safeJsonParse(limitReasonA, [])
  const reasonListB = safeJsonParse(limitReasonB, [])
  const limitReason = [...reasonListA, ...reasonListB]

  // 防止过大的数组影响性能
  if (limitReason.length > 10000) {
    console.warn('Combined limitReason array is too large:', limitReason.length)
    onFailed && onFailed('数据量过大，请联系技术支持')
    return false
  }

  const checkPool = {}

  for (const record of limitReason) {
    const existing = checkPool[record.reason_id]

    if (existing) {
      const pictureTimeSequenceA = (existing.picture || [])
        .map((e) => e.time)
        .join(',')
      const pictureTimeSequenceB = (record.picture || [])
        .map((e) => e.time)
        .join(',')

      if (
        record.reason !== existing.reason ||
        pictureTimeSequenceA !== pictureTimeSequenceB
      ) {
        // 都被编辑过，要求理由一致，拦截
        if (record.edited && existing.edited) {
          onFailed(
            `标签 [${existing.full_tag_name}] 和 [${record.full_tag_name}] 共享模板，但理由或截图时间点不一致`
          )
          throw new Error(
            `标签 [${existing.fullTagName}] 和 [${record.fullTagName}] 共享模板，但理由或截图时间点不一致`
          )
        }
      }
    }

    checkPool[record.reason_id] = record
  }

  return true
}

export function overrideAllLimitReasons(limitReasonA, limitReasonB) {
  const reasonListA = safeJsonParse(limitReasonA, [])
  const reasonListB = safeJsonParse(limitReasonB, [])
  const limitReason = [...reasonListA, ...reasonListB]

  // 防止过大的数组影响性能
  if (limitReason.length > 10000) {
    console.warn('Combined limitReason array is too large:', limitReason.length)
    return [JSON.stringify(reasonListA), JSON.stringify(reasonListB)]
  }

  const checkPool = {}

  for (const record of limitReason) {
    const existing = checkPool[record.reason_id]

    if (existing) {
      const pictureTimeSequenceA = (existing.picture || [])
        .map((e) => e.time)
        .join(',')
      const pictureTimeSequenceB = (record.picture || [])
        .map((e) => e.time)
        .join(',')

      if (
        record.reason !== existing.reason ||
        pictureTimeSequenceA !== pictureTimeSequenceB
      ) {
        // 都没编辑过，理由肯定是一致的，不处理
        // 都被编辑过，要求理由一致，拦截
        // 如果其中一个被编辑过，就用编辑过的理由，覆盖没编辑过的
        if (record.edited && !existing.edited) {
          existing.reason = record.reason
          existing.picture = record.picture
        } else if (existing.edited && !record.edited) {
          record.reason = existing.reason
          record.picture = existing.picture
        }
      }
    }

    checkPool[record.reason_id] = record
  }

  return [JSON.stringify(reasonListA), JSON.stringify(reasonListB)]
}
