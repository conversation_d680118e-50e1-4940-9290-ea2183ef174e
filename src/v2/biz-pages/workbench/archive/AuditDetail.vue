<template>
  <TodoTaskDetailStruct :autoGetOpers="false" :autoFinishLoading="false">
    <template #nav>
      <StickyNav
        isWorkbench
        :aid="archive.aid"
        :bvid="archive.bvid"
        :isAdvanced="isAdvanced"
        :listType="listType"
        :cardInfo="stickyNav.cardInfo"
        :adorderType="adorderType"
        :businessLabels="stickyNav.businessLabels"
        :incrementId="stickyNav.incrementId"
        :showLimit="showLimit"
        :manualLimit="manualLimit"
        :showCancelLimit="showCancelLimit"
        :disableOper="!dataReady"
        :showTransfer="transList && transList.length > 0"
        :showReport="perms.AEGIS_APPLY_WORK_ARCHIVE"
        :isSponsored="archive.isSponsored"
        :showSubmitAndQuit="
          !!(
            dispatchConf &&
            dispatchConf.dispatch_off_release_time &&
            !dispatchConf.no_dispatch_task
          )
        "
        :classArchive="
          stickyNav.businessLabels && stickyNav.businessLabels.pugvpay
        "
        @toggle-view="enableRedBlue ? mockSubmitFn() : handleToggleView()"
        @submit="submitData"
        @reset="toResetData"
        @back="toGoBack"
        @limit="handleShowLimitDialog"
        @cancel-limit="handleShowLimitDialog"
        @show-extra-info="extraInfoVisible = true"
        @show-report-dialog="
          enableRedBlue ? mockSubmitFn() : (reportDialogVisible = true)
        "
        @transfer="pageEventBus.$emit('showTransfer')"
        @submit-and-quit="submitAndQuit"
      />
    </template>
    <div class="bff-detail">
      <!-- 主要内容 -->
      <template v-if="dataReady">
        <!-- 稿件详情 -->
        <template v-if="!isAdvanced">
          <ArchiveError v-if="showErrorPage" @back="toGoBack" />
          <template v-else>
            <div class="grid-wrap _track_fmp_ele">
              <div class="grid" style="grid-template-columns: 0.7fr 1fr 1fr">
                <div class="grid-box record-wrapper">
                  <OperationRecord :history="workbenchHistory" />
                </div>
                <div class="grid-box">
                  <ArchiveHistory :logs="history.logs"></ArchiveHistory>
                </div>
                <div class="grid-box">
                  <SubmitLog
                    :userSubmitHistory="submitLog.userSubmitHistory"
                    :userStats="submitLog.userStats"
                    :mid="archive.mid"
                  />
                </div>
              </div>
              <div class="grid" style="grid-template-columns: 1fr 1fr">
                <div class="grid-box">
                  <!-- 注意updateValueByKey为必需 -->
                  <div
                    style="
                      background: var(--content-bg-color);
                      height: 100%;
                      padding: 10px;
                      box-sizing: border-box;
                    "
                  >
                    <AuditOperationV2
                      v-if="useTag"
                      ref="auditOperation"
                      v-bind.sync="operation"
                      :aid="archive.aid"
                      :bvid="archive.bvid"
                      :flowName="archive.flowName"
                      :initArchiveState="archive.state"
                      :listType="listType"
                      :listReview="listReview"
                      :operTags="operTags"
                      :workbenchItem="workbenchItem"
                      :getExtraDataFn="genExtraDataForPeek"
                      @update:operTagInfo="(newVal) => (operTagInfo = newVal)"
                    />
                    <AuditOperation
                      v-else
                      ref="auditOperation"
                      v-bind.sync="operation"
                      :aid="archive.aid"
                      :bvid="archive.bvid"
                      :disabled="false"
                      :noindex.sync="attrForm.noindex"
                      :norank.sync="attrForm.norank"
                      :hot_down.sync="attrForm.hot_down"
                      :rank_down.sync="attrForm.rank_down"
                      :norecommend.sync="attrForm.norecommend"
                      :nohot.sync="attrForm.nohot"
                      :nosearch.sync="attrForm.nosearch"
                      :push_blog.sync="attrForm.push_blog"
                      :flowName="archive.flowName"
                      :showAsButton="todoType === 0"
                      :listType="listType"
                      :listReview="listReview"
                      :noAuth="
                        !perms.MOD_S_FLOW_READ && !perms.MOD_S_FLOW_WRITE
                      "
                      :noReadAuth="!perms.MOD_S_FLOW_WRITE"
                      :enableSixLimit="!!attrForm.only_self"
                      :initArchiveState="archive.state"
                      :showMoreReasonBtn="true"
                    />
                    <!-- 不能更改archive -->
                    <ArchiveInfo
                      v-bind="archive"
                      :disableSaveTag="disableSaveTag || coverTagClipDisabled"
                      :tagOptions="tagOptions"
                      :listReview="listReview"
                      :disabled="true"
                      :useMockSubmit="enableRedBlue"
                      :mockSubmitFn="mockSubmitFn"
                      @updateNoReprint="handleUpdateNoReprint"
                    ></ArchiveInfo>
                  </div>
                </div>
                <div class="grid-box">
                  <!-- 注意updateValueByKey为必需 -->
                  <div
                    style="
                      background: var(--content-bg-color);
                      height: 100%;
                      padding: 10px;
                      box-sizing: border-box;
                    "
                  >
                    <Warnings :warnings="warnings"></Warnings>
                    <div
                      style="display: flex; flex-wrap: wrap; margin-bottom: 5px"
                    >
                      <MissionCard
                        :id="mission.id"
                        :name="mission.name"
                        :cancelMission.sync="mission.cancelMission"
                        :isBusinessMission="mission.isBusinessMission"
                      />
                    </div>
                    <BusinessOrder
                      v-bind.sync="business"
                      :aid="archive.aid"
                      :publishTime="archive.publishTime"
                      :listType="listType"
                      class="mb-8"
                    />
                    <ArchiveCover
                      :bvid="archive.bvid"
                      :listType="listType"
                      :fullCover="archiveCover.fullCover"
                      :fullCoverV2="archiveCover.fullCoverV2"
                      :highlightCover="archiveCover.highlightCover"
                      :disabled="true"
                      :coverAiTags="archiveCover.coverAiTags"
                      :coverAiTagsV2="archiveCover.coverAiTagsV2"
                    />
                    <ArchiveSource
                      :arctypeV2="archive.arctypeV2"
                      :upFrom="archive.upFrom"
                      :material="archive.materialTemplate"
                    />
                    <!-- 仅回查展示 -->
                    <VideosCompetitor
                      v-if="todoType === 2"
                      :videosCompetitor="paginatedVideosCompetitor"
                    ></VideosCompetitor>
                    <el-tabs
                      v-if="showNoticeTabs"
                      v-model="aiTagTab"
                      type="card"
                    >
                      <el-tab-pane
                        v-if="hasCommunityAiSummary"
                        label="社区机审"
                        name="community"
                      >
                        <CommunityAiSummary
                          :communityAiSummary="communityAiSummary"
                          :visibleClipList="visibleClipList"
                        />
                      </el-tab-pane>
                      <el-tab-pane
                        v-if="showCopyright"
                        label="版权"
                        name="copyright"
                      >
                        <CopyrightInfo :aid="archive.aid" />
                      </el-tab-pane>
                    </el-tabs>
                  </div>
                </div>
              </div>
              <!-- 第三行 P序 -->
              <Playlet :playletInfo="playletInfo" />
              <div class="grid">
                <ClipList
                  :aid="archive.aid"
                  :bvid="archive.bvid"
                  :showVideoToggle="video.showVideoToggle"
                  :sourceFile="video.sourceFile"
                  :videos.sync="video.clips"
                  :disabled="true"
                  :disabledMoreOper="coverTagClipDisabled"
                  :videoTitleHighlightKeywords="videoTitleHighlightKeywords"
                  :useMockSave="enableRedBlue"
                  :mockSaveFn="mockSubmitFn"
                  :useMockDelete="enableRedBlue"
                  :mockDeleteFn="mockSubmitFn"
                  :useMockWeblink="enableRedBlue"
                  :mockWeblinkFn="mockSubmitFn"
                  :listType="listType"
                  :listReview="listReview"
                  :clipNumber="video.clipNumber"
                  :highlightArea="archive.highlightArea"
                  @updateVideosCompetitor="handleUpdateVideosCompetitor"
                  @updateVisibleClipList="(val) => (visibleClipList = val)"
                  @show-video-preview="showPreviewVideo"
                  @hide-video-preview="hidePreviewVideo"
                />
              </div>
            </div>
          </template>
        </template>
        <!-- 高级设置 -->
        <template v-else>
          <Advanced
            v-bind.sync="advanced"
            :listReview="listReview"
            :attrForm.sync="attrForm"
            :aid="archive.aid"
            :mid="archive.mid"
            :videos="video.clips"
          >
            <template #basic-edit>
              <ArchiveBasicInfo
                :archive.sync="archive"
                :video.sync="video"
                :archiveCover.sync="archiveCover"
                :aid="archive.aid"
                :key="archive.aid"
                :listReview="listReview"
                :enableRedBlue="enableRedBlue"
                :disableSaveTag="disableSaveTag"
                :coverTagClipDisabled="coverTagClipDisabled"
                :tagOptions="tagOptions"
                :listType="listType"
                :xcode2="xcode2"
                @showPreviewVideo="showPreviewVideo"
                @hidePreviewVideo="hidePreviewVideo"
                @mockSubmitFn="mockSubmitFn"
              ></ArchiveBasicInfo>
            </template>
          </Advanced>
        </template>
      </template>
      <!-- 确认弹窗 -->
      <Suppress
        :visible="suppressDialogShow"
        @close="handleCloseSuppress"
        @confirm="handleConfirmSuppress"
      />
      <!-- 限流弹窗 -->
      <LimitDialog
        ref="limitDialog"
        :log="getLimitLog"
        :aid="archive.aid"
        :visible="showLimitDialog"
        :businessJudgeInfo="business.businessJudgeInfo || {}"
        @set-visible="(val) => (showLimitDialog = val)"
        @close="showLimitDialog = false"
        @confirm="handleConfirmLimit"
      />
      <ExtraInfoDialog
        :visible.sync="extraInfoVisible"
        :extraInfoList="extraInfoList"
        :multipleReason="operation && operation.multipleReason"
      />
      <ReportDialog
        v-if="reportDialogVisible"
        :visible.sync="reportDialogVisible"
        :businessId="businessId"
        :info="reportInfo"
      ></ReportDialog>
    </div>
    <template #cache>
      <VideoPreviewModal
        :visible="!!(showVideoPreviewModal && videoPreview.pIndex)"
        :pIndex="videoPreview.pIndex"
        :aiRiskHint="videoPreview.aiRiskHint || {}"
        :title="videoPreview.title"
        :aid="videoPreview.aid"
        :cid="videoPreview.cid"
        :filename="videoPreview.filename"
        :videosrc="videoPreview.rawFileSource"
        :showExtractor="perms.VIDEO_FRAME_TOOL"
        :shouldFetchClipAiResult="shouldFetchClipAiResult"
        :sliceMap="sliceMap"
        :videoPlaylist="videoPlaylist"
        :fetchSubtitle="overseaPub"
        @close="hidePreviewVideo"
        @update:slice-map="handleSliceMapChange"
      />
    </template>
  </TodoTaskDetailStruct>
</template>
<script>
import TodoTaskDetailStruct from '@/v2/struct/TodoTaskDetailStruct'
import createPageModule from '@/v2/struct/TodoTaskDetailStruct/module'
import BasePage from '@/v2/core/base-page'
import ArchiveHistory from '@/v2/biz-components/archive/ArchiveHistory'
import ArchiveInfo from '@/v2/biz-components/archive/ArchiveInfo'
import SubmitLog from '@/v2/biz-components/archive/SubmitLog'
import StickyNav from '@/v2/biz-components/archive/StickyNav'
import MissionCard from '@/v2/biz-components/archive/MissionCard'
import Warnings from '@/v2/biz-components/archive/Warnings'
import ArchiveCover from '@/v2/biz-components/archive/ArchiveCover'
import ArchiveSource from '@/v2/biz-components/archive/ArchiveSource'
import CopyrightInfo from '@/v2/biz-components/archive/CopyrightInfo'
import BusinessOrder from '@/v2/biz-components/archive/BusinessOrder'
import AuditOperation from '@/v2/biz-components/archive/AuditOperation'
import AuditOperationV2 from '@/v2/biz-components/archive/AuditOperationV2'
import Advanced from '@/v2/biz-components/archive/Advanced'
import ClipList from '@/v2/biz-components/archive/ClipList'
import routeHelper from '@/mixins/route-helper'
import ArchiveError from '@/v2/biz-components/archive/ArchiveError'
import Playlet from '@/v2/biz-components/archive/Playlet'
import VideosCompetitor from '@/v2/biz-components/archive/VideosCompetitor.vue'
import { mapActions, mapState } from 'vuex'
import { ArchiveAuditService } from '@/v2/service/workbench/archive/audit'
import Suppress from '@/components/Common/Suppress'
import LimitDialog from '@/v2/biz-components/archive/LimitDialog'
import { getLimitLog } from '@/v2/biz-components/archive/LimitForm'
import ExtraInfoDialog from '@/components/ExtraInfoDialog.vue'
import OperationRecord from '@/pages/workbench/quality/components/OperationRecod.vue'
import { SHOW_LIMIT_BUTTON } from '@/v2/data-source/config/local/constant.js'
import auditHotkeysMixin from '@/v2/biz-pages/workbench/archive/mixins/audit-hotkeys.js'
import { workbenchApi } from '@/v2/api'
import { archiveApi } from '@/api/index'
import { safeJsonParse, report, logger } from '@/utils'
import notify from '@/lib/notify'
import ReportDialog from '@/v2/biz-components/archive/ReportDialog.vue'
import VideoPreviewModal from '@/v2/biz-components/archive/VideoPreviewModal'
import CommunityAiSummary from '@/v2/biz-components/archive/CommunityAiSummary'
import ArchiveBasicInfo from '@/v2/biz-components/archive/ArchiveBasicInfo.vue'

export default {
  extends: BasePage,
  mixins: [routeHelper, auditHotkeysMixin],
  components: {
    TodoTaskDetailStruct,
    Advanced,
    ArchiveHistory,
    ArchiveInfo,
    CommunityAiSummary,
    SubmitLog,
    StickyNav,
    MissionCard,
    Warnings,
    ArchiveCover,
    ArchiveSource,
    CopyrightInfo,
    ArchiveError,
    AuditOperationV2,
    AuditOperation,
    BusinessOrder,
    ClipList,
    Suppress,
    LimitDialog,
    ExtraInfoDialog,
    OperationRecord,
    Playlet,
    VideosCompetitor,
    VideoPreviewModal,
    ReportDialog,
    ArchiveBasicInfo
  },
  beforeRouteLeave(to, from, next) {
    this.pageEventBus.$emit('beforeRouteLeave', to, from, next)
  },
  props: {
    pageService: {
      type: Object,
      default() {
        return {
          archiveAuditService: ArchiveAuditService
        }
      }
    },
    pageModule: {
      type: Object,
      default() {
        return createPageModule()
      }
    }
  },
  data() {
    return {
      aiTagTab: 'copyright',
      communityAiSummary: {},
      showErrorPage: false,
      dataReady: false,
      isAdvanced: false,
      // 重新压制视频的弹窗
      suppressDialogShow: false,
      // 社区高粉负向操作
      communityNegativeOperConfirmed: false,
      xcode2: undefined,
      // 限流弹窗
      showLimitDialog: false,
      manualLimit: false, // 手动限流通知
      // 基础信息
      listType: '00',
      listReview: -1,
      // 下方是数据
      // 高级设置
      advanced: {},
      // 稿件信息
      archive: {},
      // 稿件封面
      archiveCover: {},
      // AI版权识别
      copyrightDetection: {},
      // 稿件历史
      history: {},
      // 任务模块
      mission: {},
      // 操作栏模块
      operation: {},
      // tag化操作信息
      operTagInfo: {},
      // 导航栏模块
      stickyNav: {},
      // 视频模块
      video: {},
      // 警告栏
      warnings: {
        // TODO:找不到有推广位信息的数据，请产品确认
        promotePos: [],
        deviceWarning: [],
        userWarning: []
      },
      // 操作日志
      submitLog: {},
      // 商单模块
      business: {},
      // 重置用的数据
      resetData: {},
      // 属性位和禁止项表单
      attrForm: {},
      // 接口返回的部分属性位和禁止项
      resetUpdateAttr: {},
      // 稿件tag选项
      tagOptions: [],
      // 提取信息卡片
      extraInfoVisible: false,
      extraInfoList: [],
      // 提示
      tips: [],
      // 子提交参数，一次性数据，得清空
      subSubmitParams: {},
      dialogShow: false,
      // 服务端快照
      serverSnapshot: {},
      videosCompetitor: [],
      paginatedVideosCompetitor: [],
      // 报备弹窗
      reportDialogVisible: false,
      reportInfo: null,
      // 是否启用红蓝模式
      enableRedBlue: false,
      // 进通道时间
      ctime: null,
      // 微短剧信息
      playletInfo: {},
      // 正在预览的分 p 视频
      videoPreview: {},
      // 是否显示分 p 预览弹窗
      showVideoPreviewModal: false,
      sliceMap: {},
      videoPlaylist: [],
      visibleClipList: [], // 当前可见的分p列表
      cacheValidateReasonRes: '',
      cacheValidateNoteRes: '',
      overseaPub: false,
      operTags: {},
      workbenchItem: {}
    }
  },
  watch: {
    // FMP
    dataReady(val) {
      if (val) {
        this.paginatedVideosCompetitor = this.videosCompetitor
      }
    },
    hasCommunityAiSummary: {
      handler(val) {
        if (val) this.aiTagTab = 'community'
      },
      immediate: true
    }
  },
  computed: {
    ...mapState({
      arctypeMap: (state) => state.arctype.arctypeMap,
      childTypes: (state) => state.arctype.childTypes,
      mode: (state) => state.pageState.mode,
      todoId: (state) => state.pageState.todoId,
      todoType: (state) => state.pageState.todoType,
      todoName: (state) => state.pageState.todoName,
      businessId: (state) => state.pageState.businessId,
      bizConfig: (state) => state.pageState.bizConfig,
      todoConfig: (state) => state.pageState.todoConfig,
      workbenchHistory: (state) => state.pageState.history,
      transList: (state) => state.pageState.transList,
      task: (state) => state.pageState.task,
      mixinTodoConfig: (state) => state.pageState.mixinTodoConfig,
      videoTitleHighlightKeywords() {
        return this.archive?.highlightKeyword?.video_title || []
      },
      dispatchConf: (state) => state.pageState.dispatchConf
    }),
    // 日志
    getLimitLog() {
      return getLimitLog(this.operation?.auditTags, this.attrForm)
    },
    needSuppressToast() {
      // 自制变转载且有权限就会弹
      if (
        this.perms.XCODE2 &&
        this.resetData.archive.copyright === 1 &&
        this.archive.copyright === 2
      ) {
        return true
      } else {
        return false
      }
    },
    showLimit() {
      return this.perms.LIMIT_NOTIFY
    },
    showCopyright() {
      return this.perms.ARCHIVE_COPYRIGHT
    },
    hasCommunityAiSummary() {
      if (!this.perms.COMMUNITY_AI_KNOWLEDGE || !this.communityAiSummary)
        return false
      if (this.communityAiSummary?.archiveAiHint) return true
      return !!this.communityAiSummary?.aiHintByClip
    },
    shouldFetchClipAiResult() {
      const currentCid = this.videoPreview?.cid
      if (!currentCid) return false
      return !!this.communityAiSummary?.aiHintByClip?.[currentCid]
    },
    showNoticeTabs() {
      return this.showCopyright || this.hasCommunityAiSummary
    },
    showCancelLimit() {
      return (
        this.perms.LIMIT_NOTIFY &&
        this.archive.limitNotify &&
        SHOW_LIMIT_BUTTON.includes(this.listType)
      )
    },
    coverTagClipDisabled() {
      return this.todoConfig?.coverTagClipDisabled
    },
    useTag() {
      return this.todoConfig?.useTag
    },
    communityHighFollowerThreshold() {
      return this.todoConfig?.communityHighFollowerThreshold || -1
    },
    disableSaveTag() {
      // note: 是pgc或者特定待办
      return this.attrForm.is_pgc === 1
    },
    adorderType() {
      return this.business?.orderInfo?.type || ''
    },
    disableShortcutSubmit() {
      return this.bizConfig.disableShortcutSubmitTodoList.includes(this.todoId)
    }
  },
  methods: {
    ...mapActions({
      getArctype: 'arctype/getArctype',
      fetchCommon: 'common/fetchCommon',
      resetEpSlices: 'reason/resetEpSlices'
    }),
    handleUpdateVideosCompetitor(newVideosCompetitor) {
      this.paginatedVideosCompetitor = newVideosCompetitor
    },
    handleUpdateNoReprint(val) {
      this.attrForm.no_reprint = val
    },
    mockSubmitFn() {
      notify.error('没有权限')
    },
    async getTodoConfig(todoId) {
      try {
        const { data } = await workbenchApi.getBusinessConfig({
          business_id: this.businessId,
          todo_id: todoId,
          view_type: 39
        })

        return safeJsonParse(data?.[0]?.config, null)
      } catch (e) {
        console.error(e)
      }
    },
    async afterGetResource(err, resource, res, loadingFinishedCb) {
      if (!err && resource) {
        const { audit_mode } = resource
        if (audit_mode === 2) this.enableRedBlue = true
        else this.enableRedBlue = false
        this.communityNegativeOperConfirmed = false
        this.resetEpSlices()
        this.sliceMap = {}
        this.xcode2 = undefined
        const parent_todo_id =
          res?.data?.single_dispatch?.parent_todo_id ||
          res?.data?.parent_todo_id
        let todoConfig = this.todoConfig
        const isMixinTodo = [
          601356, 601357, 420817, 662120, 602726, 602305
        ].includes(this.todoId)

        // TODO: 后续服务支持下发review，可去除此处获取配置逻辑
        // 流转待办场景，需要依赖原始待办的id, 来传递review
        if (parent_todo_id) {
          todoConfig = await this.getTodoConfig(parent_todo_id)

          if (isMixinTodo) {
            logger.error({
              type: 'mixin_has_parent_todo_id'
            })
          }
        }

        // 混排逻辑，部分先同步多调用一次，辅助分析获取配置异常的问题
        if (isMixinTodo) {
          // 再拉取一次, 保证正确性
          todoConfig = await this.getTodoConfig(this.todoId)

          if (todoConfig) {
            if (
              this.mixinTodoConfig &&
              todoConfig.from_review !== this.mixinTodoConfig.from_review
            ) {
              logger.error({
                type: 'different-todo-config',
                todoId: this.todoId,
                parent_todo_id,
                from_review: todoConfig.from_review,
                error_from_review: this.mixinTodoConfig.from_review
              })
            }
          } else {
            logger.error({
              type: 'none-todo-config',
              todoId: this.todoId
            })
          }
        }

        // DEBUG埋点: 回查没有todoConfig
        if (
          [
            660529, 660530, 601356, 1080444, 573906, 420817, 450706, 390975
          ].includes(this.todoId) &&
          (!todoConfig || !todoConfig.from_review)
        ) {
          const errorParams = {
            todoId: this.todoId,
            todoName: this.todoName,
            path: this.$route.fullPath,
            todoConfig,
            parent_todo_id
          }
          report('review-without-todo-config', errorParams)
          logger.error(errorParams)
        }

        if (todoConfig) {
          this.listType = todoConfig.list_type
          this.listReview = todoConfig.from_review
        }

        const opers = await this.getOpers(resource?.archive)
        this.operTags = opers?.oper_tags || {}
        this.serverSnapshot = {
          arcInfoSnapshot: resource.arc_info_before_recheck_json
        }
        await this.archiveAuditService.getArchive(resource, {
          opers: opers?.audit_single,
          flowId: opers?.audit_single?.[0]?.flow_id?.[0]
        })
        this.ctime = resource?.work_item?.ctime
        const metas = safeJsonParse(resource?.work_item?.metadata, {})
        this.overseaPub = metas.oversea_pub === 1
        this.workbenchItem = resource.work_item
      }
      this.toastTips()
      loadingFinishedCb()
      if (this.perms.LIMIT_NOTIFY) {
        await this.getLimitNotify()
      }
    },
    // hook: 提交
    async submitArchive(params, autoQuit = false) {
      // 红蓝需要的提交参数
      const redBlueInfo = {}
      if (this.enableRedBlue) {
        const {
          typeid: secondaryTypeId,
          note,
          noteTagContent
        } = params.form || {}
        const priamryTyepId = this.childTypes[secondaryTypeId]?.pid
        if (secondaryTypeId && priamryTyepId) {
          const secondaryType = this.arctypeMap[secondaryTypeId] || ''
          const primaryType = this.arctypeMap[priamryTyepId] || ''
          redBlueInfo.type_name = `${primaryType}/${secondaryType}`
        }
        const useStructuredNote = this.operation.noteOptions?.length > 0
        if (useStructuredNote) {
          redBlueInfo.note_text = noteTagContent || ''
        } else {
          redBlueInfo.note_text = note || ''
        }
        delete params.form.noteTagContent
      }
      // 分成两步
      // 1.校验
      const { form, operTagInfo, snapshot, subSubmitParams } = params
      this.subSubmitParams = subSubmitParams
      const multipleReason = this.operation?.multipleReason || []
      const data = {
        resource_result: {
          note: form.note,
          reject_reason: form.reject_reason,
          reason_id: Number(form.reject_reason_id),
          full_tag_ids: this.useTag
            ? (operTagInfo.incremental_tag_rules || []).map(
                (tagRule) => tagRule.fullPath
              )
            : multipleReason?.map((e) => {
                return [e.category_id, ...(e.full_path_tag_id || [])]
              }) || [],
          full_tag_names: this.useTag
            ? (operTagInfo.incremental_tag_rules || []).map((tagRule) =>
                tagRule.reasonLabel.split(' / ')
              )
            : multipleReason?.map((e) => {
                return [e.category, ...(e.reason_tag?.split(' / ') || [])]
              }) || []
        },
        extra_data: {
          aid: form.id,
          action: 'audit',
          arc_info_before_recheck_json: this.serverSnapshot.arcInfoSnapshot,
          ...form,
          ...redBlueInfo,
          auditversion: this.archive?.auditversion
        },
        binds: form.bind_id,
        qa_snapshot_data: {
          oid: form.id,
          content: form.title,
          mid: form.mid,
          extra2s: this.archive.arctypeV1,
          task_gtime: this.task?.gtime,
          extra3: Number(this.archive?.state)
        },
        qa_snapshot_detail: snapshot
      }
      if (this.useTag) data.oper_tag_info = operTagInfo
      // 2.接口返回处理
      // 3.特殊处理https://info.bilibili.co/pages/viewpage.action?pageId=406596398
      if (this.todoConfig?.flag_disable_base) {
        data.extra_data.flag_disable_base = this.todoConfig?.flag_disable_base
      }
      // 根据通道配置 决定是否触发强制限流结算 https://www.tapd.bilibili.co/20065591/prong/stories/view/1120065591003061185
      if (this.todoConfig?.must_settle_tag) {
        data.extra_data.must_settle_tag = this.todoConfig?.must_settle_tag
      }

      if (this.hardCodeConfigFromReviewNotMatch(data.extra_data.from_review)) {
        logger.error({
          type: 'submit_error_from_review',
          from_review: data.extra_data.from_review,
          todoId: this.todoId,
          todoConfig: this.todoConfig,
          mixinTodoConfig: this.mixinTodoConfig
        })
      }
      this.pageEventBus.$emit('submit', data, false, autoQuit)
    },
    async validTaskSubmit(tips) {
      // 针对双盲提示
      let valid = true
      const h = this.$createElement
      const message =
        typeof tips === 'string'
          ? h('p', null, tips)
          : h(
              'table',
              {
                attrs: {
                  align: 'left'
                },
                style: 'line-height:20px;'
              },
              tips.map((e) =>
                h(
                  'tr',
                  {
                    // class: 'flex-as'
                  },
                  [
                    h(
                      'td',
                      {
                        // class: 'mr-20'
                        attrs: {
                          valign: 'top'
                        },
                        class: 'pr-16 pb-16',
                        style: 'line-height:20px'
                      },
                      e.name
                    ),
                    h(
                      'td',
                      {
                        class: 'break-all pb-16',
                        attrs: {
                          valign: 'top'
                        },
                        style: 'line-height:20px'
                      },
                      e.oper
                    )
                  ]
                )
              )
            )
      await this.$msgbox({
        title: '消息',
        message,
        showCancelButton: true,
        confirmButtonText: '我知道了',
        cancelButtonText: '取消'
      }).catch(() => {
        // do nothing
        // 取消直接返回
        valid = false
      })
      return valid
    },
    // hook: 提交后（进行小提交）
    async afterSubmit(err, res, params, autoGetNext, autoQuit) {
      if (err) {
        // 双盲
        if (err.code === 92149) {
          let valid
          if (err.data?.tips) {
            const tips = JSON.parse(err.data.tips)
            if (!tips?.length) {
              // code正确但是无tips，不影响审核流程，兜底文案
              valid = await this.validTaskSubmit(
                '首次操作与二次操作结果不一致，确认是否按当前操作提交'
              )
            } else {
              valid = await this.validTaskSubmit(tips)
            }
          } else {
            // code正确但是无tips，不影响审核流程，兜底文案
            valid = await this.validTaskSubmit(
              '首次操作与二次操作结果不一致，确认是否按当前操作提交'
            )
          }
          if (valid) {
            this.submitData({
              ignoreCheckRound2: true,
              skip: true // 避免重新触发展示是否压制弹窗的逻辑，造成无限循环弹窗提示
            })
          }
        }
        return
      }
      this.videoPreview = {}
      this.showVideoPreviewModal = false
      if (Object.keys(this.subSubmitParams).length > 0) {
        await this.archiveAuditService
          .appendSubSumit(this.subSubmitParams)
          .then((_) => {
            this.clearSubmitParams()
            if (this.mode === 'Detail') {
              this.toGoBack()
            } else {
              if (!autoQuit) {
                this.pageEventBus.$emit('getResource')
              }
            }
          })
          .catch((_) => {})
      } else {
        this.clearSubmitParams()
        if (this.mode === 'Detail') {
          this.toGoBack()
        } else {
          if (!autoQuit) {
            this.pageEventBus.$emit('getResource')
          }
        }
      }
    },
    /**
     * 获取操作项
     */
    async getOpers(archive) {
      let err, res, opers
      try {
        // TODO: 待网关统一切换后，可统一至getOpers接口上
        res = await workbenchApi.getOpers({
          business_id: this.businessId,
          todo_id: this.todoId,
          mode: 2,
          page_detail: 1,
          oid: archive.aid
        })
        opers = res.data
      } catch (error) {
        err = error
        console.error(error)
      }

      this.pageEventBus.$emit('afterGetOpers', err, opers, res)
      return opers
    },
    clearSubmitParams() {
      this.subSubmitParams = {}
    },
    toastTips() {
      const { warningsProtectNote } = this.archive
      const { tips } = this
      if (warningsProtectNote) {
        notify.warning(warningsProtectNote, 0)
      }
      if (tips && tips.length > 0) {
        tips.forEach((tip) => {
          notify.info(tip, 1000)
        })
      }
    },
    toResetData() {
      this.archiveAuditService.resetArchive()
    },
    toGoBack() {
      this.pageEventBus.$emit('backToList')
    },
    handleCloseSuppress() {
      this.suppressDialogShow = false
      this.xcode2 = undefined
    },
    handleConfirmSuppress(val) {
      this.suppressDialogShow = false
      if (val === 1) {
        this.xcode2 = 1
      } else {
        this.xcode2 = undefined
      }
      // 跳过校验
      this.submitData({
        skip: true
      })
    },
    async getLimitNotify() {
      const { data } = await archiveApi.getLimitNotify({
        aid: this.archive.aid
      })
      this.manualLimit = data?.limit_state || false
    },
    async handleConfirmLimit(data) {
      if (this.enableRedBlue) {
        return this.mockSubmitFn()
      }
      const params = { ...data }
      this.ctime && (params.enter_time = this.ctime)
      try {
        const msg = await this.archiveAuditService.confirmLimit(params)
        notify.success(msg)
        this.getLimitNotify()
      } catch (e) {
        console.error(e)
      }
    },
    handleShowLimitDialog(ifLimit) {
      this.$refs.limitDialog && this.$refs.limitDialog.openDialog(ifLimit)
    },
    setDialogShow(payload) {
      this.dialogShow = payload
    },
    async bizOrderConfirmCb() {
      let confirmed = true
      this.setDialogShow(true)
      await this.$confirm(
        '稿件为<b style="color: red">商业稿件</b>，确认是否进行操作？',
        '请注意',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          confirmed = true
        })
        .catch(() => {
          confirmed = false
        })
      this.setDialogShow(false)
      return confirmed
    },
    // 推荐稿件弹窗显示及回调
    async recommendConfirmCb() {
      let confirmTipCall = true
      this.setDialogShow(true)
      await this.$confirm('稿件处于推荐中，是否确认提交？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          confirmTipCall = true
        })
        .catch(() => {
          confirmTipCall = false
        })
      this.setDialogShow(false)
      return confirmTipCall
    },
    // 高粉提交弹窗显示及回调
    submitConfirmCb(warnings, force) {
      return new Promise((resolve) =>
        setTimeout(() => {
          const h = this.$createElement
          this.setDialogShow(true)
          this.$msgbox({
            title: '提示',
            message: h('p', null, [
              h(
                'i',
                {
                  style: 'color: teal;margin-right: 8px;color: var(--orange);',
                  class: 'el-icon-warning'
                },
                ''
              ),
              h('span', null, `${warnings.join('，')}，是否确认提交？`)
            ]),
            $type: 'confirm',
            showCancelButton: true,
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            beforeClose: (action, instance, done) => {
              // done是用来关闭弹窗的
              done()
              this.setDialogShow(false)
            }
          })
            .then((action) => {
              this.setDialogShow(false)
              // 当既是高粉稿件（或者有提示），又稿件已被删除时的极端情况，需要带上force（如果原来有的话）
              resolve({
                type: 'success',
                data: {
                  action: 'retry',
                  data: {
                    isForce: force ? true : undefined,
                    isConfirm: true
                  }
                }
              })
            })
            .catch((_) => {
              resolve({
                type: 'error',
                data: {
                  action: 'normal',
                  data: `${warnings.join('，')}，弹窗选择了关闭`
                }
              })
            })
        }, 0)
      )
    },
    doubleConfirmCb(warnings, force) {
      return new Promise((resolve) =>
        setTimeout(() => {
          const h = this.$createElement
          this.setDialogShow(true)
          this.$msgbox({
            title: '提示',
            message: h('p', null, [
              h(
                'i',
                {
                  style: 'color: teal;margin-right: 8px;color: var(--orange);',
                  class: 'el-icon-warning'
                },
                ''
              ),
              h('span', null, warnings)
            ]),
            $type: 'confirm',
            showCancelButton: true,
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            beforeClose: (action, instance, done) => {
              // done是用来关闭弹窗的
              this.setDialogShow(false)
              done()
            }
          })
            .then((action) => {
              resolve({
                type: 'success',
                data: {
                  action: 'retry',
                  data: {
                    isForce: force ? true : undefined,
                    isConfirm: true
                  }
                }
              })
            })
            .catch((_) => {
              resolve({
                type: 'error',
                data: {
                  action: 'normal',
                  data: `${warnings}，弹窗选择了关闭`
                }
              })
            })
        }, 0)
      )
    },
    // 强制覆盖弹窗显示及回调
    forceDialogCb() {
      return new Promise((resolve) => {
        setTimeout(() => {
          const h = this.$createElement
          this.setDialogShow(true)
          this.$msgbox({
            title: '提示',
            message: h('p', null, [
              h(
                'i',
                {
                  style: 'color: teal;margin-right: 8px;color: var(--orange);',
                  class: 'el-icon-warning'
                },
                ''
              ),
              h(
                'span',
                null,
                `稿件已删除，是否覆盖？\n若确认覆盖，请确保该稿件下视频状态可播放。`
              )
            ]),
            $type: 'confirm',
            showCancelButton: true,
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            beforeClose: (action, instance, done) => {
              // done是用来关闭弹窗的
              this.setDialogShow(false)
              done()
            }
          })
            .then((action) => {
              resolve({
                type: 'error',
                data: {
                  action: 'retry',
                  data: {
                    isForce: true
                  }
                }
              })
            })
            .catch((e) => {
              resolve({
                type: 'error',
                data: {
                  action: 'donothing',
                  data: {}
                }
              })
            })
        }, 0)
      })
    },
    async communityNegativeOperLimitCb() {
      try {
        await this.$alert(
          '当前稿件为高粉投稿，如需限制请流转至延迟通道处理',
          '提示'
        )
        this.communityNegativeOperConfirmed = true
      } catch (_) {}
    },
    handleToggleView() {
      this.cacheValidateReasonRes = this.validateReason()
      this.cacheValidateNoteRes = this.validateNote()
      this.isAdvanced = !this.isAdvanced
    },
    validateReason() {
      const auditOperationRef = this.$refs.auditOperation
      return auditOperationRef
        ? auditOperationRef.validateReason()
        : this.cacheValidateReasonRes
    },
    validateNote() {
      const auditOperationRef = this.$refs.auditOperation
      return auditOperationRef
        ? auditOperationRef.validateNote()
        : this.cacheValidateNoteRes
    },
    async reasonOnErrorCb(reasonError) {
      await this.$alert(reasonError.replace(/\n/g, '<br>'), '请补充理由', {
        dangerouslyUseHTMLString: true
      })
    },
    noteOnErrorCb(noteError) {
      setTimeout(async () => {
        await this.$alert(noteError, '提示')
      }, 100)
    },
    async submitAndQuit() {
      await this.$confirm(
        `<div class="flex-as">
          <div class="el-icon-warning" style="font-size:24px;color:var(--warning-color);margin-right:8px;"></div>
          <div>
            <div style="line-height:24px;">请注意！！</div>
            <div style="color:var(--error-color)">「提交且退出」后将退出通道，将不再分配任务</div>
          </div>
        </div>`,
        '',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确认退出',
          cancelButtonText: '取消'
        }
      )
      this.submitData(undefined, true)
    },
    genExtraDataForPeek() {
      return this.archiveAuditService.genExtraDataForPeek()
    },
    // 在提交之前
    async submitData(appendOptions, autoQuit = false) {
      // 1.拼接form参数
      let params = {}
      try {
        const { type = '', data } = await this.archiveAuditService.submit(
          appendOptions
        )
        if (type === 'success' && data.action === 'params') {
          const { form, operTagInfo, snapshot, subSubmitParams } = data.data
          params = {
            form,
            operTagInfo,
            snapshot,
            subSubmitParams
          }
          // 校验成功继续发请求
          this.submitArchive(params, autoQuit)
        } else {
          this.clearSubmitParams()
          const { action, data: actionData } = data
          switch (action) {
            case 'message': {
              notify.all(actionData)
              break
            }
            case 'retry': {
              this.submitData(actionData)
              break
            }
            case 'donothing': {
              break
            }
            default: {
              console.error(actionData)
            }
          }
        }
      } catch (e) {
        this.clearSubmitParams()
        console.error(e)
      }
    },
    hotkeysSubmit() {
      if (
        this.dialogShow ||
        this.suppressDialogShow ||
        this.showLimitDialog ||
        this.disableShortcutSubmit
      )
        return
      this.submitData()
    },
    // 本地配置，和服务配置进行对比
    hardCodeConfigFromReviewNotMatch(fromReview) {
      const CONFIG_MAP = {
        602726: 42,
        601357: 31,
        601356: 3,
        420817: 36
      }
      const hit = CONFIG_MAP[this.todoId]

      if (hit) {
        return hit !== fromReview
      } else {
        return false
      }
    },
    showPreviewVideo(payload) {
      const { clipInfo, videoPlaylist } = payload
      if (
        clipInfo.pIndex === this.videoPreview.pIndex &&
        this.showVideoPreviewModal
      ) {
        this.showVideoPreviewModal = false
      } else {
        this.videoPreview = clipInfo
        this.videoPlaylist = videoPlaylist
        this.showVideoPreviewModal = true
      }
    },
    hidePreviewVideo() {
      this.showVideoPreviewModal = false
    },
    handleSliceMapChange({ pIndex, slices }) {
      this.$set(this.sliceMap, pIndex, slices)
    }
  },
  created() {
    this.getArctype()
    this.fetchCommon()
    this.pageEventBus.$on('afterGetResource', this.afterGetResource)
    this.pageEventBus.$on('afterSubmit', this.afterSubmit)
  },
  beforeDestroy() {
    this.pageEventBus.$off('afterGetResource', this.afterGetResource)
    this.pageEventBus.$off('afterSubmit', this.afterSubmit)
  }
}
</script>

<style lang="stylus" scoped>
.bff-detail
  height 100% !important
  width 100%
  box-shadow 0 2px 12px 0 rgba(0, 0, 0, 0.1)
  border-radius 2px
  box-sizing border-box
  background var(--content-bg-color)
  >>>.el-form-item__label
    line-height 30px
  >>>.el-form-item__content
    line-height 30px
  >>>.el-textarea textarea
    font-family 'Avenir', Helvetica, Arial, sans-serif
  .base-row
    height 60px
    padding 10px 18px
    font-size 14px
    z-index 20
    line-height 3
    box-shadow 0 2px 4px 0 rgba(0, 0, 0, 0.1)
    .base-div
      display flex
      .default-base-info
        line-height 2.6
        .el-button
          vertical-align top
        .task-info
          color var(--text-color-dark-1)
          margin-right 10px
          em
            color var(--text-color)
            font-weight bold
        .delay-num > em
          color var(--red)
  .grid
    display grid
    margin-bottom 5px
    background var(--bg-color)
    padding 8px
    &:last-child
      margin-bottom 0px
  .grid-wrap
    overflow auto
    // 减去base-row高度
    height calc(100% - 60px)
    padding 10px
    box-sizing border-box
  .custom-operation-wrapper
    margin-right: 20px
    .custom-operation
      background: var(--content-bg-color)
      margin-bottom 10px
      &:last-child
        margin-bottom 0px
</style>
