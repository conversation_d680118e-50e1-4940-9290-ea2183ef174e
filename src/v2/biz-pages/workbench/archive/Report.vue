<template>
  <TodoTaskDetailStruct defaultMode="Pull" :enableDetailTrans="true">
    <template #nav>
      <StickyNav
        v-if="archive"
        isWorkbench
        :aid="archive.aid"
        :bvid="archive.bvid"
        :isAdvanced="showAdvanced"
        :isSponsored="archive.isSponsored"
        :cardInfo="stickyNav.cardInfo"
        :adorderType="adorderType"
        :businessLabels="stickyNav.businessLabels"
        :incrementId="stickyNav.incrementId"
        :showExtractInfo="false"
        :showLimit="false"
        :customOperation="true"
        @toggle-view="showAdvanced = !showAdvanced"
        @back="pageEventBus.$emit('backToList')"
      >
        <template #operation>
          <el-button
            v-if="effectOper"
            class="ml-8"
            type="success"
            size="small"
            @click="openEffectDialog"
            :disabled="disableOperation"
          >
            {{ effectOper.ch_name }}
          </el-button>
          <el-button
            class="ml-8"
            type="danger"
            size="small"
            @click="submitInvalid(invalidOper)"
            :disabled="disableOperation"
          >
            {{ invalidOper.ch_name }}
          </el-button>
          <el-button
            v-if="transList && transList.length > 0"
            size="small"
            type="primary"
            plain
            @click="pageEventBus.$emit('showTransfer')"
          >
            流转
          </el-button>
          <el-button
            v-if="perms.AEGIS_APPLY_WORK_ARCHIVE"
            @click="reportDialogVisible = true"
            size="small"
            :disabled="disableOperation"
          >
            报备
          </el-button>
          <el-button
            v-if="archive"
            type="text"
            class="ml-12"
            size="small"
            @click="routeToArchiveDetail"
          >
            跳转稿件详情
          </el-button>
          <div v-if="isReview" class="ml-20">
            <el-button type="primary" size="small" @click="openLimitDialog">
              限流通知/解限
            </el-button>
          </div>
        </template>
      </StickyNav>
    </template>

    <Advanced
      v-if="showAdvanced"
      v-bind="advanced"
      :attrForm="attrForm"
      :aid="archive.aid"
      :mid="archive.mid"
      :videos="video.clips"
      :disabled="true"
    />
    <div class="archive-inform-content" v-else>
      <div class="main">
        <div class="left" style="margin-right: 10px">
          <GridDescription class="archive-logs-grid-desc">
            <GridDescriptionItem label="操作记录">
              <HistoryLog
                class="archive-inform-log-container"
                :history="history"
              ></HistoryLog>
            </GridDescriptionItem>
            <GridDescriptionItem label="稿件历史">
              <ArchiveHistory
                class="archive-inform-log-container"
                :logs="archiveHistory"
                :showTitle="false"
              ></ArchiveHistory>
            </GridDescriptionItem>
          </GridDescription>
          <ArchiveInfo
            v-if="archive"
            :archive="archive"
            :coverAiTags="archiveCover.coverAiTags"
            :coverAiTagsV2="archiveCover.coverAiTagsV2"
            :cover="archiveCover.fullCover"
            :coverV2="archiveCover.fullCoverV2"
            :mission="mission"
            :business="business"
            :attrForm="attrForm"
            :pay="pay"
          />
        </div>
        <div class="right">
          <SubmitLog
            v-if="submitLog"
            class="submit-log-container"
            :userSubmitHistory="submitLog.userSubmitHistory"
            :userStats="submitLog.userStats"
            :mid="archive.mid"
          />
          <Warnings v-if="warnings" :warnings="warnings"></Warnings>
          <ReportInfo :key="oid" :oid="oid" :rid="rid"></ReportInfo>
        </div>
      </div>
      <ClipList
        v-if="video"
        :aid="archive.aid"
        :bvid="archive.bvid"
        :showVideoToggle="video.showVideoToggle"
        :videoTitleHighlightKeywords="videoTitleHighlightKeywords"
        :sourceFile="video.sourceFile"
        :videos="video.clips"
        :clipNumber="video.clipNumber"
        :highlightArea="archive.highlightArea"
        :disabled="true"
        :enableTagsAndMonitor="!isReview"
        :enableAiInfo="!isReview"
        @show-video-preview="showPreviewVideo"
        @hide-video-preview="hidePreviewVideo"
      />
    </div>
    <LimitDialog
      v-if="archive && isReview"
      ref="limitDialog"
      :log="getLimitLog"
      :visible="showLimitDialog"
      :businessJudgeInfo="business.businessJudgeInfo || {}"
      :aid="archive.aid"
      @set-visible="(val) => (showLimitDialog = val)"
      @close="showLimitDialog = false"
      @confirm="handleConfirmLimit"
    />
    <EffectDialog
      v-if="archive"
      ref="effect"
      :aid="archive.aid || 0"
      :auditversion="archive.auditversion"
      :key="`effect-${task && task.id}`"
      :informTagId="informTagId"
      :todoConfig="todoConfig"
      :isBusinessOrder="isBusinessOrder"
      @submit="submit"
    />
    <ReportDialog
      v-if="reportDialogVisible"
      :visible.sync="reportDialogVisible"
      :businessId="businessId"
      :info="reportInfo"
    ></ReportDialog>
    <template #cache>
      <VideoPreviewModal
        :visible="!!(showVideoPreviewModal && videoPreview.pIndex)"
        :pIndex="videoPreview.pIndex"
        :aiRiskHint="videoPreview.aiRiskHint || {}"
        :title="videoPreview.title"
        :aid="videoPreview.aid"
        :cid="videoPreview.cid"
        :videosrc="videoPreview.rawFileSource"
        :showExtractor="perms.VIDEO_FRAME_TOOL"
        :sliceMap="sliceMap"
        :videoPlaylist="videoPlaylist"
        @close="hidePreviewVideo"
        @update:slice-map="handleSliceMapChange"
      />
    </template>
  </TodoTaskDetailStruct>
</template>

<script>
import { mapGetters, mapState, mapActions } from 'vuex'
import TodoTaskDetailStruct, {
  createPageModule
} from '@/v2/struct/TodoTaskDetailStruct'
import BasePage from '@/v2/core/base-page'
import StickyNav from '@/v2/biz-components/archive/StickyNav'
import Warnings from '@/v2/biz-components/archive/Warnings'
import SubmitLog from '@/v2/biz-components/archive/SubmitLog'
import ClipList from '@/v2/biz-components/archive/ClipList'
import Advanced from '@/v2/biz-components/archive/Advanced'
import HistoryLog from '@/components/Common/history-log'
import ArchiveHistory from '@/v2/biz-components/archive/ArchiveHistory'
import LimitDialog from '@/v2/biz-components/archive/LimitDialog'
import { getLimitLog } from '@/v2/biz-components/archive/LimitForm'
import EffectDialog from '@/pages/workbench/dialogConfig/EffectDialog.vue'
// 先复用客服反馈的组件
import ArchiveInfo from '@/v2/biz-pages/workbench/customer-feedback/components/ArchiveInfo.vue'
import GridDescription from '@/v2/pure-components/GridDescription/GridDescription.vue'
import GridDescriptionItem from '@/v2/pure-components/GridDescription/GridDescriptionItem.vue'
import ReportInfo from './components/ReportInfo.vue'
import ReportDialog from '@/v2/biz-components/archive/ReportDialog.vue'
import VideoPreviewModal from '@/v2/biz-components/archive/VideoPreviewModal'
import { archiveApi } from '@/v2/api'
import notify from '@/lib/notify'
import { genStandardQaSnapshotData } from '@/v2/biz-utils/checkAutoQa'

import {
  genArchive,
  genArchiveCover,
  genHistory,
  genVideo,
  genSubmitLog,
  genWarnings,
  genAttrForm,
  genBusiness,
  genPay,
  genMission,
  genStickyNav,
  genAdvanced
} from '@/v2/data-source/archive/info/adapter'
import { ArchiveDetailService } from '@/v2/service/archive/archive-detail'

export default {
  extends: BasePage,
  props: {
    pageModule: {
      type: Object,
      default() {
        return createPageModule()
      }
    }
  },
  components: {
    TodoTaskDetailStruct,
    EffectDialog,
    LimitDialog,
    StickyNav,
    Warnings,
    SubmitLog,
    ClipList,
    Advanced,
    ArchiveInfo,
    ArchiveHistory,
    HistoryLog,
    ReportInfo,
    GridDescription,
    GridDescriptionItem,
    ReportDialog,
    VideoPreviewModal
  },
  data() {
    return {
      effectOper: null,
      invalidOper: null,
      showAdvanced: false,
      archive: null,
      archiveHistory: null,
      archiveCover: null,
      advanced: null,
      video: null,
      submitLog: null,
      attrForm: null,
      business: null,
      mission: null,
      pay: null,
      stickyNav: null,
      warnings: [],
      // 父工单id
      oid: '',
      // 父工单状态
      parentOrderStatus: null,
      // 举报tagId
      informTagId: null,
      // 资源处理状态 0待处理，1已处理
      workItemState: 0,
      // 流转自哪个待办
      parentTodoId: undefined,
      metaTime: {},
      // 报备弹窗
      reportDialogVisible: false,
      reportInfo: null,
      showLimitDialog: false,
      // 进入待办时间
      ctime: null,
      auditTags: [],
      // 正在预览的分 p 视频
      videoPreview: {},
      // 是否显示分 p 预览弹窗
      showVideoPreviewModal: false,
      sliceMap: {},
      videoPlaylist: [],
      qaSnapshotData: null
    }
  },
  computed: {
    ...mapGetters({
      getEnv: 'env/getEnv'
    }),
    ...mapState({
      perms: (state) => state.user.perms,
      arctypeMap: (state) => state.arctype.arctypeMap,
      username: (state) => state.user.username
    }),
    ...mapState('pageState', [
      'mode',
      'task',
      'todoId',
      'todoName',
      'history',
      'businessId',
      'itemId',
      'todoConfig',
      'transList'
    ]),
    disableOperation() {
      // 资源模式
      // 有效状态，并且资源状态不是待处理, 则禁用操作项
      return (
        this.mode === 'Detail' &&
        this.parentOrderStatus === 1 &&
        this.workItemState !== 0
      )
    },
    isReview() {
      return this.todoConfig?.isReview ?? false
    },
    rid() {
      // 0-全部 1-审核 2-回查
      if (this.parentTodoId) {
        return 0
      }

      return this.isReview ? 2 : 1
    },
    videoTitleHighlightKeywords() {
      return this.archive?.highlightKeyword?.video_title || []
    },
    adorderType() {
      return this.business?.orderInfo?.type || ''
    },
    getLimitLog() {
      return getLimitLog(this.auditTags, this.attrForm)
    },
    isBusinessOrder() {
      return this.business?.isBusinessOrder
    }
  },
  beforeRouteLeave(to, from, next) {
    this.pageEventBus.$emit('beforeRouteLeave', to, from, next)
  },
  created() {
    this.getArctype()
    this.fetchCommon()
    this.pageEventBus.$on('afterGetResource', this.afterGetResource)
    this.pageEventBus.$on('afterSubmit', this.afterSubmit)
    this.pageEventBus.$on('afterGetOpers', this.afterGetOpers)
  },
  beforeDestroy() {
    this.pageEventBus.$off('afterGetResource', this.afterGetResource)
    this.pageEventBus.$off('afterSubmit', this.afterSubmit)
    this.pageEventBus.$off('afterGetOpers', this.afterGetOpers)
  },
  methods: {
    ...mapActions({
      getArctype: 'arctype/getArctype',
      fetchCommon: 'common/fetchCommon',
      resetEpSlices: 'reason/resetEpSlices'
    }), 
    afterGetOpers(err, opers) {
      if (!err && opers) {
        const audit_single = opers.audit_single || []

        this.effectOper = audit_single.find((item) => item.ch_name === '有效')
        this.invalidOper = audit_single.find((item) => item.ch_name === '无效')
      }
    },
    afterSubmit() {
      this.videoPreview = {}
      this.showVideoPreviewModal = false
    },
    afterGetResource(err, resource, res) {
      if (!err && resource) {
        this.parentTodoId =
          res?.data?.single_dispatch?.parent_todo_id ||
          res?.data?.parent_todo_id
        this.oid = resource.oid

        if (resource.work_resource) {
          this.parentOrderStatus = +resource.work_resource.extra4
          this.informTagId = resource.work_resource.extra5
          const { mtime, audit_mtime, recheck_mtime } =
            resource.work_resource.metas || {}

          this.metaTime = {
            mtime,
            audit_mtime,
            recheck_mtime
          }
        }

        if (resource.work_item) {
          this.workItemState = resource.work_item.state
          this.ctime = resource.work_item.ctime
        }

        this.archive = genArchive(resource)
        this.archiveCover = genArchiveCover(resource)
        this.archiveHistory = genHistory(resource)?.logs ?? []
        this.video = genVideo(resource, {}, { env: this.getEnv() })
        this.submitLog = genSubmitLog(resource)
        this.warnings = genWarnings(resource, {
          showSenUserGroup: this.perms.SENSITIVE_USERGROUP ? 1 : 0
        })
        this.attrForm = genAttrForm(resource, {})
        this.business = genBusiness(resource)
        this.mission = genMission(resource)
        this.pay = genPay(resource)
        this.stickyNav = genStickyNav(resource)
        this.advanced = genAdvanced(resource)
        this.reportInfo = ArchiveDetailService.genReportInfo(
          this.archive,
          this.arctypeMap,
          this.warnings
        )
        this.qaSnapshotData = genStandardQaSnapshotData(resource.work_item)
        this.resetEpSlices()
        this.sliceMap = {}
      }
    },
    routeToArchiveDetail() {
      const route = this.$router.resolve({
        path: '/v2/archive/detail',
        query: {
          business_id: 11,
          oid: this.archive.aid,
          listType: '00'
        }
      })
      window.open(route.href, '_blank')
    },
    openLimitDialog() {
      this.$refs.limitDialog.openDialog()
    },
    async handleConfirmLimit(data) {
      try {
        const params = {
          ...data,
          aid: this.archive.aid
        }
        this.ctime && (params.enter_time = this.ctime)
        await archiveApi.sendLimitNotify(params)
        // 无需提示
        this.showLimitDialog = false
        if (data.type === 3) {
          notify.success('限流成功')
        } else {
          notify.success('解限成功')
        }
      } catch (e) {
        console.error(e)
      }
    },
    openEffectDialog() {
      this.$refs.effect.openDialog(this.effectOper)
    },
    closeEffectDialog() {
      this.$refs.effect.close()
    },
    // 无效提交
    submitInvalid(oper) {
      this.$confirm(`此操作将对选定资源执行无效操作, 是否继续?`, '无效', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const params = {
            resource_result: {},
            blind_diff_info: [{ label: '审核结果', value: '无效' }],
            qa_snapshot_data: this.qaSnapshotData,
            qa_snapshot_detail: this.genSnapshot()
          }
          this.submit(params, oper)
        })
        .catch((e) => {})
    },
    submit(params, oper, operationSnapshot) {
      this.closeEffectDialog()
      const data = {
        binds: oper.bind_id_list,
        qa_snapshot_data: {
          ...this.qaSnapshotData,
          task_gtime: this.task?.gtime
        },
        qa_snapshot_detail: this.genSnapshot(operationSnapshot),
        ...params,
        extra_data: {
          ...(params.extra_data || {}),
          ...this.metaTime,
          aid: this.archive.aid,
          rid: this.isReview ? 2 : 1 // 提交时，全部的逻辑交给服务处理，只需提交审核/回查
        }
      }
      this.pageEventBus.$emit('submit', data)
    },
    genSnapshot(operationSnapshot) {
      const snapshot = {
        todoName: this.todoName,
        oid: this.oid,
        archive: this.archive,
        archiveCover: this.archiveCover,
        archiveHistory: this.archiveHistory,
        video: this.video,
        submitLog: this.submitLog,
        warnings: this.warnings,
        attrForm: this.attrForm,
        business: this.business,
        mission: this.mission,
        pay: this.pay,
        stickyNav: this.stickyNav,
        advanced: this.advanced,
        rid: this.rid,
        todoConfig: this.todoConfig,
        username: this.username
      }
      if (operationSnapshot) {
        snapshot.formOperation = operationSnapshot.formOperation
        snapshot.snapshotNoteTags = operationSnapshot.snapshotNoteTags
      }
      return JSON.stringify(snapshot)
    },
    showPreviewVideo(payload) {
      const { clipInfo, videoPlaylist } = payload
      if (
        clipInfo.pIndex === this.videoPreview.pIndex &&
        this.showVideoPreviewModal
      ) {
        this.showVideoPreviewModal = false
      } else {
        this.videoPreview = clipInfo
        this.videoPlaylist = videoPlaylist
        this.showVideoPreviewModal = true
      }
    },
    hidePreviewVideo() {
      this.showVideoPreviewModal = false
    },
    handleSliceMapChange({ pIndex, slices }) {
      this.$set(this.sliceMap, pIndex, slices)
    }
  }
}
</script>
<style lang="stylus" scoped>
.archive-logs-grid-desc {
  border-bottom 0
  grid-template-columns: repeat(2, 1fr)
  grid-template-rows: 200px
  .archive-inform-log-container {
    height: 190px
    overflow: scroll

    >>> p {
      font-size: 12px
      line-height: 1.5
    }
    >>> .sl__card {
      height: 100%
      padding-right: 0
    }
    >>> .sl__body {
      padding 0
    }
    >>> .ag-card__header {
      margin-bottom: 0
    }
  }
}
.archive-inform-content {
  display: flex
  flex-direction: column
  padding: 5px
  .main {
    display: flex
    flex-direction: row
    margin-bottom: 5px
    .left {
      flex: 3
    }
    .right {
      flex: 2
      .submit-log-container {
        border: 1px solid var(--border-color-light-2)
        margin-bottom: 10px
      }
    }
  }
}
</style>
