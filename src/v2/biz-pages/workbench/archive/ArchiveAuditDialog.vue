<template>
  <el-dialog
    :visible="dialogVisible"
    fullscreen
    :show-close="false"
    custom-class="archive-audit-dialog"
  >
    <template #title>
      <TaskNav
        :todoName="todoName"
        :task="{ timeCount: taskTimeCount }"
        @quit="onQuitDialog"
      >
        <StickyNav
          isWorkbench
          :aid="archive.aid"
          :bvid="archive.bvid"
          :isAdvanced="isAdvanced"
          :listType="listType"
          :cardInfo="stickyNav.cardInfo"
          :adorderType="adorderType"
          :businessLabels="stickyNav.businessLabels"
          :incrementId="stickyNav.incrementId"
          :showLimit="showLimit"
          :manualLimit="manualLimit"
          :showCancelLimit="showCancelLimit"
          :disableOper="!dataReady"
          :showTransfer="transList && transList.length > 0"
          :showReport="perms.AEGIS_APPLY_WORK_ARCHIVE"
          :isSponsored="archive.isSponsored"
          :showSubmitAndQuit="isLastTask"
          :classArchive="
            stickyNav.businessLabels && stickyNav.businessLabels.pugvpay
          "
          @toggle-view="handleToggleView"
          @submit="submitData"
          @reset="toResetData"
          @back="toGoBack"
          @limit="handleShowLimitDialog"
          @cancel-limit="handleShowLimitDialog"
          @show-extra-info="extraInfoVisible = true"
          @show-report-dialog="reportDialogVisible = true"
          @transfer="$emit('show-transfer')"
          @submit-and-quit="submitAndQuit"
        />
      </TaskNav>
    </template>
    <div class="bff-detail">
      <!-- 主要内容 -->
      <template v-if="dataReady">
        <!-- 稿件详情 -->
        <template v-if="!isAdvanced">
          <ArchiveError v-if="showErrorPage" @back="toGoBack" />
          <template v-else>
            <div class="grid-wrap _track_fmp_ele">
              <div class="grid" style="grid-template-columns: 0.7fr 1fr 1fr">
                <div class="grid-box record-wrapper">
                  <OperationRecord :history="workbenchHistory" />
                </div>
                <div class="grid-box">
                  <ArchiveHistory :logs="history.logs"></ArchiveHistory>
                </div>
                <div class="grid-box">
                  <SubmitLog
                    :userSubmitHistory="submitLog.userSubmitHistory"
                    :userStats="submitLog.userStats"
                    :mid="archive.mid"
                  />
                </div>
              </div>
              <div class="grid" style="grid-template-columns: 1fr 1fr">
                <div class="grid-box">
                  <!-- 注意updateValueByKey为必需 -->
                  <div
                    style="
                      background: var(--content-bg-color);
                      height: 100%;
                      padding: 10px;
                      box-sizing: border-box;
                    "
                  >
                    <AuditOperationV2
                      v-if="useTag"
                      ref="auditOperation"
                      v-bind.sync="operation"
                      :aid="archive.aid"
                      :bvid="archive.bvid"
                      :flowName="archive.flowName"
                      :initArchiveState="archive.state"
                      :listType="listType"
                      :listReview="listReview"
                      :operTags="operTags"
                      :workbenchItem="workbenchItem"
                      :getExtraDataFn="genExtraDataForPeek"
                      @update:operTagInfo="(newVal) => operTagInfo = newVal"
                    />
                    <AuditOperation
                      v-else
                      ref="auditOperation"
                      v-bind.sync="operation"
                      :aid="archive.aid"
                      :bvid="archive.bvid"
                      :disabled="false"
                      :noindex.sync="attrForm.noindex"
                      :norank.sync="attrForm.norank"
                      :hot_down.sync="attrForm.hot_down"
                      :rank_down.sync="attrForm.rank_down"
                      :norecommend.sync="attrForm.norecommend"
                      :nohot.sync="attrForm.nohot"
                      :nosearch.sync="attrForm.nosearch"
                      :push_blog.sync="attrForm.push_blog"
                      :flowName="archive.flowName"
                      :showAsButton="todoType === 0"
                      :listType="listType"
                      :listReview="listReview"
                      :noAuth="
                        !perms.MOD_S_FLOW_READ && !perms.MOD_S_FLOW_WRITE
                      "
                      :noReadAuth="!perms.MOD_S_FLOW_WRITE"
                      :enableSixLimit="!!attrForm.only_self"
                      :initArchiveState="archive.state"
                      :showMoreReasonBtn="true"
                    />
                    <ArchiveInfo
                      v-bind="archive"
                      :disableSaveTag="disableSaveTag || coverTagClipDisabled"
                      :tagOptions="tagOptions"
                      :listReview="listReview"
                      :disabled="true"
                      @updateNoReprint="handleUpdateNoReprint"
                    ></ArchiveInfo>
                  </div>
                </div>
                <div class="grid-box">
                  <!-- 注意updateValueByKey为必需 -->
                  <div
                    style="
                      background: var(--content-bg-color);
                      height: 100%;
                      padding: 10px;
                      box-sizing: border-box;
                    "
                  >
                    <Warnings :warnings="warnings"></Warnings>
                    <div
                      style="display: flex; flex-wrap: wrap; margin-bottom: 5px"
                    >
                      <MissionCard
                        :id="mission.id"
                        :name="mission.name"
                        :cancelMission.sync="mission.cancelMission"
                        :isBusinessMission="mission.isBusinessMission"
                      />
                    </div>
                    <BusinessOrder
                      v-bind.sync="business"
                      :aid="archive.aid"
                      :publishTime="archive.publishTime"
                      :listType="listType"
                      class="mb-8"
                    />
                    <ArchiveCover
                      :bvid="archive.bvid"
                      :listType="listType"
                      :fullCover="archiveCover.fullCover"
                      :fullCoverV2="archiveCover.fullCoverV2"
                      :highlightCover="archiveCover.highlightCover"
                      :coverAiTags="archiveCover.coverAiTags"
                      :coverAiTagsV2="archiveCover.coverAiTagsV2"
                      :disabled="true"
                    />
                    <div style="display: flex">
                      <ArchiveSource
                        :upFrom="archive.upFrom"
                        :material="archive.materialTemplate"
                      />
                    </div>
                    <!-- 仅回查展示 -->
                    <VideosCompetitor
                      v-if="todoType === 2"
                      :videosCompetitor="paginatedVideosCompetitor"
                    ></VideosCompetitor>
                    <CopyrightInfo v-if="showCopyright" :aid="archive.aid" />
                  </div>
                </div>
              </div>
              <!-- 第三行 P序 -->
              <Playlet :playletInfo="playletInfo" />
              <div class="grid">
                <ClipList
                  :aid="archive.aid"
                  :bvid="archive.bvid"
                  :showVideoToggle="video.showVideoToggle"
                  :sourceFile="video.sourceFile"
                  :videos.sync="video.clips"
                  :disabled="true"
                  :disabledMoreOper="coverTagClipDisabled"
                  :videoTitleHighlightKeywords="videoTitleHighlightKeywords"
                  :listType="listType"
                  :listReview="listReview"
                  :clipNumber="video.clipNumber"
                  :highlightArea="archive.highlightArea"
                  @updateVideosCompetitor="handleUpdateVideosCompetitor"
                  @show-video-preview="showPreviewVideo"
                  @hide-video-preview="hidePreviewVideo"
                />
              </div>
            </div>
          </template>
        </template>
        <!-- 高级设置 -->
        <template v-else>
          <Advanced
            v-bind.sync="advanced"
            :attrForm.sync="attrForm"
            :aid="archive.aid"
            :mid="archive.mid"
            :videos="video.clips"
            :listReview="listReview"
          >
            <template #basic-edit>
              <ArchiveBasicInfo
                :archive.sync="archive"
                :video.sync="video"
                :archiveCover.sync="archiveCover"
                :aid="archive.aid"
                :key="archive.aid"
                :listReview="listReview"
                :enableRedBlue="false"
                :disableSaveTag="disableSaveTag"
                :coverTagClipDisabled="coverTagClipDisabled"
                :tagOptions="tagOptions"
                :listType="listType"
                :xcode2="xcode2"
                @showPreviewVideo="showPreviewVideo"
                @hidePreviewVideo="hidePreviewVideo"
              />
            </template>
          </Advanced>
        </template>
      </template>
      <!-- 确认弹窗 -->
      <Suppress
        :visible="suppressDialogShow"
        @close="handleCloseSuppress"
        @confirm="handleConfirmSuppress"
      />
      <!-- 限流弹窗 -->
      <LimitDialog
        ref="limitDialog"
        :log="getLimitLog"
        :aid="archive.aid"
        :visible="showLimitDialog"
        :businessJudgeInfo="business.businessJudgeInfo || {}"
        @set-visible="(val) => (showLimitDialog = val)"
        @close="showLimitDialog = false"
        @confirm="handleConfirmLimit"
      />
      <ExtraInfoDialog
        :visible.sync="extraInfoVisible"
        :extraInfoList="extraInfoList"
        :multipleReason="operation && operation.multipleReason"
        :modal="false"
      />
      <ReportDialog
        v-if="reportDialogVisible"
        :visible.sync="reportDialogVisible"
        :businessId="businessId"
        :info="reportInfo"
        :modal="false"
      ></ReportDialog>
      <VideoPreviewModal
        :visible="!!(showVideoPreviewModal && videoPreview.pIndex)"
        :pIndex="videoPreview.pIndex"
        :aiRiskHint="videoPreview.aiRiskHint || {}"
        :title="videoPreview.title"
        :aid="videoPreview.aid"
        :cid="videoPreview.cid"
        :videosrc="videoPreview.rawFileSource"
        :showExtractor="perms.VIDEO_FRAME_TOOL"
        :sliceMap="sliceMap"
        :videoPlaylist="videoPlaylist"
        @close="hidePreviewVideo"
        @update:slice-map="handleSliceMapChange"
      />
    </div>
  </el-dialog>
</template>
<script>
import { mapActions, mapState } from 'vuex'
import BasePage from '@/v2/core/base-page'
import ArchiveHistory from '@/v2/biz-components/archive/ArchiveHistory'
import ArchiveInfo from '@/v2/biz-components/archive/ArchiveInfo'
import SubmitLog from '@/v2/biz-components/archive/SubmitLog'
import StickyNav from '@/v2/biz-components/archive/StickyNav'
import MissionCard from '@/v2/biz-components/archive/MissionCard'
import Warnings from '@/v2/biz-components/archive/Warnings'
import ArchiveCover from '@/v2/biz-components/archive/ArchiveCover'
import ArchiveSource from '@/v2/biz-components/archive/ArchiveSource'
import CopyrightInfo from '@/v2/biz-components/archive/CopyrightInfo'
import BusinessOrder from '@/v2/biz-components/archive/BusinessOrder'
import AuditOperation from '@/v2/biz-components/archive/AuditOperation'
import AuditOperationV2 from '@/v2/biz-components/archive/AuditOperationV2'
import Advanced from '@/v2/biz-components/archive/Advanced'
import ClipList from '@/v2/biz-components/archive/ClipList'
import routeHelper from '@/mixins/route-helper'
import ArchiveError from '@/v2/biz-components/archive/ArchiveError'
import Playlet from '@/v2/biz-components/archive/Playlet'
import VideosCompetitor from '@/v2/biz-components/archive/VideosCompetitor'
import VideoPreviewModal from '@/v2/biz-components/archive/VideoPreviewModal'
import { ArchiveAuditService } from '@/v2/service/workbench/archive/audit'
import Suppress from '@/components/Common/Suppress'
import LimitDialog from '@/v2/biz-components/archive/LimitDialog'
import { getLimitLog } from '@/v2/biz-components/archive/LimitForm'
import ExtraInfoDialog from '@/components/ExtraInfoDialog'
import OperationRecord from '@/pages/workbench/quality/components/OperationRecod'
import { SHOW_LIMIT_BUTTON } from '@/v2/data-source/config/local/constant'
import trackMixin from '@/mixins/track.mixin'
import auditHotkeysMixin from '@/v2/biz-pages/workbench/archive/mixins/audit-hotkeys'
import { workbenchApi } from '@/v2/api'
import { archiveApi } from '@/api/index'
import notify from '@/lib/notify'
import ReportDialog from '@/v2/biz-components/archive/ReportDialog'
import TaskNav from '@/v2/struct/TodoTaskDetailStruct/components/TaskNav'
import ArchiveBasicInfo from '@/v2/biz-components/archive/ArchiveBasicInfo.vue'

export default {
  name: 'ArchiveAuditDialog',
  extends: BasePage,
  mixins: [routeHelper, trackMixin, auditHotkeysMixin],
  components: {
    Advanced,
    ArchiveHistory,
    ArchiveInfo,
    SubmitLog,
    StickyNav,
    MissionCard,
    Warnings,
    ArchiveCover,
    ArchiveSource,
    CopyrightInfo,
    ArchiveError,
    AuditOperation,
    AuditOperationV2,
    BusinessOrder,
    ClipList,
    Suppress,
    LimitDialog,
    ExtraInfoDialog,
    OperationRecord,
    Playlet,
    VideosCompetitor,
    VideoPreviewModal,
    ReportDialog,
    TaskNav,
    ArchiveBasicInfo
  },
  props: {
    pageService: {
      type: Object,
      default() {
        return {
          archiveAuditService: ArchiveAuditService
        }
      }
    },
    value: {
      type: Boolean,
      default: false
    },
    aid: Number,
    // 进通道时间
    ctime: {
      type: Number,
      default: null
    },
    isLastTask: {
      type: Boolean,
      default: false
    },
    rawArchiveInfo: {
      type: Object,
      default: () => {}
    },
    taskTimeCount: Number,
    workbenchHistory: {
      type: Array,
      default: () => []
    },
    workbenchItem: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      showErrorPage: false,
      dataReady: false,
      isAdvanced: false,
      // 重新压制视频的弹窗
      suppressDialogShow: false,
      xcode2: undefined,
      // 限流弹窗
      showLimitDialog: false,
      manualLimit: false, // 手动限流通知
      // 基础信息
      listType: '00',
      listReview: -1,
      // 下方是数据
      // 高级设置
      advanced: {},
      // 稿件信息
      archive: {},
      // 稿件封面
      archiveCover: {},
      // AI版权识别
      copyrightDetection: {},
      // 稿件历史
      history: {},
      // 任务模块
      mission: {},
      // 操作栏模块
      operation: {},
      // 导航栏模块
      stickyNav: {},
      // 视频模块
      video: {},
      // 警告栏
      warnings: {
        // TODO:找不到有推广位信息的数据，请产品确认
        promotePos: [],
        deviceWarning: [],
        userWarning: []
      },
      // 操作日志
      submitLog: {},
      // 商单模块
      business: {},
      // 重置用的数据
      resetData: {},
      // 属性位和禁止项表单
      attrForm: {},
      // 接口返回的部分属性位和禁止项
      resetUpdateAttr: {},
      // 稿件tag选项
      tagOptions: [],
      // 提取信息卡片
      extraInfoVisible: false,
      extraInfoList: [],
      // 提示
      tips: [],
      // 子提交参数，一次性数据，得清空
      subSubmitParams: {},
      dialogShow: false,
      // 服务端快照
      serverSnapshot: {},
      videosCompetitor: [],
      paginatedVideosCompetitor: [],
      // 报备弹窗
      reportDialogVisible: false,
      reportInfo: null,
      // 微短剧信息
      playletInfo: {},
      // 正在预览的分 p 视频
      videoPreview: {},
      // 是否显示分 p 预览弹窗
      showVideoPreviewModal: false,
      sliceMap: {},
      videoPlaylist: [],
      cacheValidateReasonRes: '',
      cacheValidateNoteRes: '',
      operTags: {}
    }
  },
  watch: {
    // FMP
    dataReady(val) {
      if (val) {
        this.$nextTick(() => {
          this.trackFMP()
        })
        this.paginatedVideosCompetitor = this.videosCompetitor
      }
    }
  },
  computed: {
    ...mapState({
      arctypeMap: (state) => state.arctype.arctypeMap,
      todoId: (state) => state.pageState.todoId,
      todoType: (state) => state.pageState.todoType,
      todoName: (state) => state.pageState.todoName,
      businessId: (state) => state.pageState.businessId,
      bizConfig: (state) => state.pageState.bizConfig,
      todoConfig: (state) => state.pageState.todoConfig,
      transList: (state) => state.pageState.transList,
      task: (state) => state.pageState.task,
      videoTitleHighlightKeywords() {
        return this.archive?.highlightKeyword?.video_title || []
      }
    }),
    dialogVisible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    // 日志
    getLimitLog() {
      return getLimitLog(this.operation?.auditTags, this.attrForm)
    },
    needSuppressToast() {
      // 自制变转载且有权限就会弹
      if (
        this.perms.XCODE2 &&
        this.resetData.archive.copyright === 1 &&
        this.archive.copyright === 2
      ) {
        return true
      } else {
        return false
      }
    },
    showLimit() {
      return this.perms.LIMIT_NOTIFY
    },
    showCopyright() {
      return this.perms.ARCHIVE_COPYRIGHT
    },
    showCancelLimit() {
      return (
        this.perms.LIMIT_NOTIFY &&
        this.archive.limitNotify &&
        SHOW_LIMIT_BUTTON.includes(this.listType)
      )
    },
    coverTagClipDisabled() {
      return this.todoConfig?.coverTagClipDisabled
    },
    useTag() {
      return this.todoConfig?.useTag
    },
    disableSaveTag() {
      // note: 是pgc或者特定待办
      return this.attrForm.is_pgc === 1
    },
    adorderType() {
      return this.business?.orderInfo?.type || ''
    },
    disableShortcutSubmit() {
      return this.bizConfig.disableShortcutSubmitTodoList.includes(this.todoId)
    }
  },
  methods: {
    ...mapActions({
      getArctype: 'arctype/getArctype',
      fetchCommon: 'common/fetchCommon',
      resetEpSlices: 'reason/resetEpSlices'
    }),
    handleUpdateVideosCompetitor(newVideosCompetitor) {
      this.paginatedVideosCompetitor = newVideosCompetitor
    },
    handleUpdateNoReprint(val) {
      this.attrForm.no_reprint = val
    },
    onQuitDialog() {
      this.dialogVisible = false
      this.$emit('clear-selection')
    },
    async getArchiveData() {
      this.resetEpSlices()
      this.sliceMap = {}
      this.xcode2 = undefined

      if (this.todoConfig) {
        this.listType = this.todoConfig.list_type
        this.listReview = this.todoConfig.from_review
      }

      const opers = await this.getOpers(this.aid)
      this.operTags = opers?.oper_tags || {}
      // TODO: 获取工作台日志

      await this.archiveAuditService.getArchive(this.rawArchiveInfo, {
        opers: opers?.audit_single,
        flowId: opers?.audit_single?.[0]?.flow_id?.[0]
      })
      this.serverSnapshot = {
        arcInfoSnapshot: this.archive.arcInfoBeforeRecheckJson
      }

      this.toastTips()
      if (this.perms.LIMIT_NOTIFY) {
        await this.getLimitNotify()
      }
    },
    genExtraDataForPeek() {
      return this.archiveAuditService.genExtraDataForPeek()
    },
    // 大提交
    async submitArchive(params, autoQuit = false) {
      // 红蓝需要的提交参数
      const redBlueInfo = {}
      // 分成两步
      // 1.校验
      const { form, snapshot, subSubmitParams } = params
      this.subSubmitParams = subSubmitParams
      const data = {
        resource_result: {
          note: form.note,
          reject_reason: form.reject_reason,
          reason_id: Number(form.reject_reason_id)
        },
        extra_data: {
          aid: form.id,
          action: 'audit',
          arc_info_before_recheck_json: this.serverSnapshot.arcInfoSnapshot,
          ...form,
          ...redBlueInfo,
          auditversion: this.archive?.auditversion
        },
        binds: form.bind_id,
        qa_snapshot_data: {
          oid: form.id,
          content: form.title,
          mid: form.mid,
          extra1s: String(form.typeid),
          task_gtime: this.task?.gtime,
          extra3: Number(this.archive?.state)
        },
        qa_snapshot_detail: snapshot
      }
      // 2.接口返回处理
      // 3.特殊处理https://info.bilibili.co/pages/viewpage.action?pageId=406596398
      if (this.todoConfig?.flag_disable_base) {
        data.extra_data.flag_disable_base = this.todoConfig?.flag_disable_base
      }
      // 根据通道配置 决定是否触发强制限流结算 https://www.tapd.bilibili.co/20065591/prong/stories/view/1120065591003061185
      if (this.todoConfig?.must_settle_tag) {
        data.extra_data.must_settle_tag = this.todoConfig?.must_settle_tag
      }

      await this.submitArchiveForbids()
      this.$emit('submit-audit-result', { result: data, autoQuit })
      this.videoPreview = {}
      this.showVideoPreviewModal = {}
      this.clearSubmitParams()
    },
    // 小提交
    async submitArchiveForbids() {
      if (Object.keys(this.subSubmitParams).length > 0) {
        try {
          await this.archiveAuditService.appendSubSumit(this.subSubmitParams)
        } catch (error) {
          console.error(error)
        }
      }
    },
    /**
     * 获取操作项
     */
    async getOpers(aid) {
      let res, opers
      try {
        // TODO: 待网关统一切换后，可统一至getOpers接口上
        res = await workbenchApi.getOpers({
          business_id: this.businessId,
          todo_id: this.todoId,
          mode: 2,
          page_detail: 1,
          oid: aid
        })
        opers = res.data
      } catch (error) {
        console.error(error)
      }
      return opers
    },
    clearSubmitParams() {
      this.subSubmitParams = {}
    },
    toastTips() {
      const { warningsProtectNote } = this.archive
      const { tips } = this
      if (warningsProtectNote) {
        notify.warning(warningsProtectNote, 0)
      }
      if (tips && tips.length > 0) {
        tips.forEach((tip) => {
          notify.info(tip, 1000)
        })
      }
    },
    toResetData() {
      this.archiveAuditService.resetArchive()
    },
    toGoBack() {
      this.dialogVisible = false
    },
    handleCloseSuppress() {
      this.suppressDialogShow = false
      this.xcode2 = undefined
    },
    handleConfirmSuppress(val) {
      this.suppressDialogShow = false
      if (val === 1) {
        this.xcode2 = 1
      } else {
        this.xcode2 = undefined
      }
      // 跳过校验
      this.submitData({
        skip: true
      })
    },
    async getLimitNotify() {
      const { data } = await archiveApi.getLimitNotify({
        aid: this.archive.aid
      })
      this.manualLimit = data?.limit_state || false
    },
    async handleConfirmLimit(data) {
      const params = { ...data }
      this.ctime && (params.enter_time = this.ctime)
      try {
        const msg = await this.archiveAuditService.confirmLimit(params)
        notify.success(msg)
        this.getLimitNotify()
      } catch (e) {
        console.error(e)
      }
    },
    handleShowLimitDialog(ifLimit) {
      this.$refs.limitDialog && this.$refs.limitDialog.openDialog(ifLimit)
    },
    setDialogShow(payload) {
      this.dialogShow = payload
    },
    async bizOrderConfirmCb() {
      let confirmed = true
      this.setDialogShow(true)
      await this.$confirm(
        '稿件为<b style="color: red">商业稿件</b>，确认是否进行操作？',
        '请注意',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          confirmed = true
        })
        .catch(() => {
          confirmed = false
        })
      this.setDialogShow(false)
      return confirmed
    },
    // 推荐稿件弹窗显示及回调
    async recommendConfirmCb() {
      let confirmTipCall = true
      this.setDialogShow(true)
      await this.$confirm('稿件处于推荐中，是否确认提交？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          confirmTipCall = true
        })
        .catch(() => {
          confirmTipCall = false
        })
      this.setDialogShow(false)
      return confirmTipCall
    },
    // 高粉提交弹窗显示及回调
    submitConfirmCb(warnings, force) {
      return new Promise((resolve) =>
        setTimeout(() => {
          const h = this.$createElement
          this.setDialogShow(true)
          this.$msgbox({
            title: '提示',
            message: h('p', null, [
              h(
                'i',
                {
                  style: 'color: teal;margin-right: 8px;color: var(--orange);',
                  class: 'el-icon-warning'
                },
                ''
              ),
              h('span', null, `${warnings.join('，')}，是否确认提交？`)
            ]),
            $type: 'confirm',
            showCancelButton: true,
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            beforeClose: (action, instance, done) => {
              // done是用来关闭弹窗的
              done()
              this.setDialogShow(false)
            }
          })
            .then((action) => {
              this.setDialogShow(false)
              // 当既是高粉稿件（或者有提示），又稿件已被删除时的极端情况，需要带上force（如果原来有的话）
              resolve({
                type: 'success',
                data: {
                  action: 'retry',
                  data: {
                    isForce: force ? true : undefined,
                    isConfirm: true
                  }
                }
              })
            })
            .catch((_) => {
              resolve({
                type: 'error',
                data: {
                  action: 'normal',
                  data: `${warnings.join('，')}，弹窗选择了关闭`
                }
              })
            })
        }, 0)
      )
    },
    doubleConfirmCb(warnings, force) {
      return new Promise((resolve) =>
        setTimeout(() => {
          const h = this.$createElement
          this.setDialogShow(true)
          this.$msgbox({
            title: '提示',
            message: h('p', null, [
              h(
                'i',
                {
                  style: 'color: teal;margin-right: 8px;color: var(--orange);',
                  class: 'el-icon-warning'
                },
                ''
              ),
              h('span', null, warnings)
            ]),
            $type: 'confirm',
            showCancelButton: true,
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            beforeClose: (action, instance, done) => {
              // done是用来关闭弹窗的
              this.setDialogShow(false)
              done()
            }
          })
            .then((action) => {
              resolve({
                type: 'success',
                data: {
                  action: 'retry',
                  data: {
                    isForce: force ? true : undefined,
                    isConfirm: true
                  }
                }
              })
            })
            .catch((_) => {
              resolve({
                type: 'error',
                data: {
                  action: 'normal',
                  data: `${warnings}，弹窗选择了关闭`
                }
              })
            })
        }, 0)
      )
    },
    // 强制覆盖弹窗显示及回调
    forceDialogCb() {
      return new Promise((resolve) => {
        setTimeout(() => {
          const h = this.$createElement
          this.setDialogShow(true)
          this.$msgbox({
            title: '提示',
            message: h('p', null, [
              h(
                'i',
                {
                  style: 'color: teal;margin-right: 8px;color: var(--orange);',
                  class: 'el-icon-warning'
                },
                ''
              ),
              h(
                'span',
                null,
                `稿件已删除，是否覆盖？\n若确认覆盖，请确保该稿件下视频状态可播放。`
              )
            ]),
            $type: 'confirm',
            showCancelButton: true,
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            beforeClose: (action, instance, done) => {
              // done是用来关闭弹窗的
              this.setDialogShow(false)
              done()
            }
          })
            .then((action) => {
              resolve({
                type: 'error',
                data: {
                  action: 'retry',
                  data: {
                    isForce: true
                  }
                }
              })
            })
            .catch((e) => {
              resolve({
                type: 'error',
                data: {
                  action: 'donothing',
                  data: {}
                }
              })
            })
        }, 0)
      })
    },
    handleToggleView() {
      this.cacheValidateReasonRes = this.validateReason()
      this.cacheValidateNoteRes = this.validateNote()
      this.isAdvanced = !this.isAdvanced
    },
    validateReason() {
      const auditOperationRef = this.$refs.auditOperation
      return auditOperationRef
        ? auditOperationRef.validateReason()
        : this.cacheValidateReasonRes
    },
    validateNote() {
      const auditOperationRef = this.$refs.auditOperation
      return auditOperationRef
        ? auditOperationRef.validateNote()
        : this.cacheValidateNoteRes
    },
    async reasonOnErrorCb(reasonError) {
      await this.$alert(reasonError, '请补充驳回理由')
    },
    noteOnErrorCb(noteError) {
      setTimeout(async () => {
        await this.$alert(noteError, '提示')
      }, 100)
    },
    async submitAndQuit() {
      await this.$confirm(
        `<div class="flex-as">
          <div class="el-icon-warning" style="font-size:24px;color:var(--warning-color);margin-right:8px;"></div>
          <div>
            <div style="line-height:24px;">请注意！！</div>
            <div style="color:var(--error-color)">「提交且退出」后将退出通道，将不再分配任务</div>
          </div>
        </div>`,
        '',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确认退出',
          cancelButtonText: '取消'
        }
      )
      this.submitData(undefined, true)
    },
    // 在提交之前
    async submitData(appendOptions, autoQuit = false) {
      // 1.拼接form参数
      let params = {}
      try {
        const { type = '', data } = await this.archiveAuditService.submit(
          appendOptions
        )
        if (type === 'success' && data.action === 'params') {
          const { form, snapshot, subSubmitParams } = data.data
          params = {
            form,
            snapshot,
            subSubmitParams
          }
          // 校验成功继续发请求
          this.submitArchive(params, autoQuit)
        } else {
          this.clearSubmitParams()
          const { action, data: actionData } = data
          switch (action) {
            case 'message': {
              notify.all(actionData)
              break
            }
            case 'retry': {
              this.submitData(actionData)
              break
            }
            case 'donothing': {
              break
            }
            default: {
              console.error(actionData)
            }
          }
        }
      } catch (e) {
        this.clearSubmitParams()
        console.error(e)
      }
    },
    showPreviewVideo(payload) {
      const { clipInfo, videoPlaylist } = payload
      if (
        clipInfo.pIndex === this.videoPreview.pIndex &&
        this.showVideoPreviewModal
      ) {
        this.showVideoPreviewModal = false
      } else {
        this.videoPreview = clipInfo
        this.videoPlaylist = videoPlaylist
        this.showVideoPreviewModal = true
      }
    },
    hidePreviewVideo() {
      this.showVideoPreviewModal = false
    },
    handleSliceMapChange({ pIndex, slices }) {
      this.$set(this.sliceMap, pIndex, slices)
    },
    hotkeysSubmit() {
      if (
        this.dialogShow ||
        this.suppressDialogShow ||
        this.showLimitDialog ||
        this.disableShortcutSubmit
      )
        return
      this.submitData()
    }
  },
  created() {
    this.getArchiveData()
  },
  beforeDestroy() {}
}
</script>

<style lang="stylus">
.archive-audit-dialog
  .el-dialog__body
    padding-top 0
  .todo-struct-task-nav
    .time-span
      width 80px
      margin-left 16px
      margin-right 8px
</style>
<style lang="stylus" scoped>
.bff-detail
  height 100% !important
  width 100%
  box-shadow 0 2px 12px 0 rgba(0, 0, 0, 0.1)
  border-radius 2px
  box-sizing border-box
  background var(--content-bg-color)
  >>>.el-form-item__label
    line-height 30px
  >>>.el-form-item__content
    line-height 30px
  >>>.el-textarea textarea
    font-family 'Avenir', Helvetica, Arial, sans-serif
  .base-row
    height 60px
    padding 10px 18px
    font-size 14px
    z-index 20
    line-height 3
    box-shadow 0 2px 4px 0 rgba(0, 0, 0, 0.1)
    .base-div
      display flex
      .default-base-info
        line-height 2.6
        .el-button
          vertical-align top
        .task-info
          color var(--text-color-dark-1)
          margin-right 10px
          em
            color var(--text-color)
            font-weight bold
        .delay-num > em
          color var(--red)
  .grid
    display grid
    margin-bottom 5px
    background var(--bg-color)
    padding 8px
    &:last-child
      margin-bottom 0px
  .grid-wrap
    overflow auto
    // 减去base-row高度
    height calc(100% - 60px)
    padding 10px
    box-sizing border-box
  .custom-operation-wrapper
    margin-right: 20px
    .custom-operation
      background: var(--content-bg-color)
      margin-bottom 10px
      &:last-child
        margin-bottom 0px
</style>
