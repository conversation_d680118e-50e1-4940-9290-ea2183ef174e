<template>
  <TodoTaskDetailStruct :autoGetOpers="false">
    <template #nav>
      <StickyNav
        class="mr-10"
        isWorkbench
        :aid="archive.aid"
        :bvid="archive.bvid"
        :isAdvanced="isAdvanced"
        :listType="listType"
        :cardInfo="stickyNav.cardInfo"
        :businessLabels="stickyNav.businessLabels"
        :adorderType="adorderType"
        :incrementId="stickyNav.incrementId"
        :isSponsored="archive.isSponsored"
        :showLimit="false"
        :disableOper="!dataReady"
        :upNotify.sync="upNotify"
        :showExtractInfo="showExtractInfo"
        :showLimitFromCategory="pushList.length ? false : true"
        :showTransfer="transList && transList.length > 0"
        @toggle-view="isAdvanced = !isAdvanced"
        @submit="submitData"
        @reset="toResetData"
        @back="toGoBack"
        @show-extra-info="extraInfoVisible = true"
        @transfer="pageEventBus.$emit('showTransfer')"
      />
    </template>
    <div class="bff-detail">
      <!-- 主要内容 -->
      <template v-if="dataReady">
        <!-- 稿件详情 -->
        <template v-if="!isAdvanced">
          <ArchiveError v-if="showErrorPage" @back="toGoBack" />
          <template v-else>
            <div class="grid-wrap _track_fmp_ele">
              <div
                class="grid"
                :style="{
                  'grid-template-columns': isQaRecall
                    ? todoConfig && todoConfig.showSubmitLog
                      ? '0.7fr 1fr 1fr 1fr'
                      : '0.7fr 1fr 1fr'
                    : todoConfig && todoConfig.showSubmitLog
                    ? '1fr 1fr 1fr'
                    : '1fr 1fr'
                }"
              >
                <div class="grid-box record-wrapper">
                  <OperationRecord :history="workbenchHistory" />
                </div>
                <div class="grid-box" v-if="isQaRecall">
                  <OperationRecord title="质检操作记录" :history="qaHistory" />
                </div>
                <div class="grid-box">
                  <ArchiveHistory :logs="history.logs"></ArchiveHistory>
                </div>
                <div
                  class="grid-box"
                  v-if="todoConfig && todoConfig.showSubmitLog"
                >
                  <SubmitLog
                    :mid="archive.mid || 0"
                    :userSubmitHistory="userSubmitHistory"
                    :userStats="userStats"
                    @clickTitle="$emit('clickTitle')"
                  />
                </div>
              </div>
              <!-- 第三行 P序 -->
              <div class="grid" v-if="todoConfig && todoConfig.showClipList">
                <ClipList
                  :aid="archive.aid"
                  :bvid="archive.bvid"
                  :showVideoToggle="true"
                  :sourceFile="video.sourceFile"
                  :isCategory="true"
                  :videos.sync="video.clips"
                  :disabled="disabled"
                  @show-video-preview="showPreviewVideo"
                  @hide-video-preview="hidePreviewVideo"
                />
              </div>
              <div class="grid" style="grid-template-columns: 1fr 1fr">
                <div class="grid-box">
                  <!-- 注意updateValueByKey为必需 -->
                  <div
                    style="
                      background: var(--content-bg-color);
                      height: 100%;
                      padding: 10px;
                      box-sizing: border-box;
                    "
                  >
                    <Warnings :warnings="warnings"></Warnings>
                    <AgFormItem
                      label="禁止项："
                      labelWidth="56px"
                      v-if="todoConfig && todoConfig.showBlockOption"
                    >
                      <BlockOption
                        disabled
                        :norank.sync="block_ops.norank"
                        :noindex.sync="block_ops.noindex"
                        :norecommend.sync="block_ops.norecommend"
                        :nohot.sync="block_ops.nohot"
                        :nosearch.sync="block_ops.nosearch"
                      />
                    </AgFormItem>
                    <ArchiveInfo
                      v-bind.sync="archive"
                      :disableSaveTag="true"
                      :tagOptions="tagOptions"
                      :listReview="listReview"
                      :disabled="disabled"
                      @updateNoReprint="handleUpdateNoReprint"
                    ></ArchiveInfo>
                    <ArchiveCover
                      style="width: calc(100% - 40px); margin-left: 40px"
                      :fullCover.sync="archiveCover.fullCover"
                      :fullCoverV2="archiveCover.fullCoverV2"
                      :highlightCover="archiveCover.highlightCover"
                      :preview="true"
                    />
                    <MissionCard
                      style="width: calc(100% - 40px); margin-left: 40px"
                      :id="mission.id"
                      :name="mission.name"
                      :cancelMission.sync="mission.cancelMission"
                      :isBusinessMission="mission.isBusinessMission"
                    />
                    <BusinessOrder
                      v-bind.sync="business"
                      style="width: calc(100% - 40px); margin-left: 40px"
                      :aid="archive.aid"
                      :listType="listType"
                      :publishTime="archive.publishTime"
                      hideJudgeInfo
                      class="mb-8"
                    />
                    <div
                      style="
                        display: flex;
                        width: calc(100% - 40px);
                        margin-left: 40px;
                      "
                    >
                      <ArchiveSource
                        :upFrom="archive.upFrom"
                        :arctypeV2="archive.arctypeV2"
                      />
                    </div>
                  </div>
                </div>
                <div class="grid-box">
                  <!-- 注意updateValueByKey为必需 -->
                  <div
                    style="
                      background: var(--content-bg-color);
                      height: 100%;
                      padding: 10px;
                      box-sizing: border-box;
                    "
                  >
                    <CategoryOperation
                      ref="operationRef"
                      :tagList.sync="operations.tagList"
                      :remark.sync="operations.remark"
                      :isGrayTag="isGrayTag"
                      :aid="archive.aid"
                      @updateTagList="handleUpdateTagList"
                      @update:grayTagReason="handleUpdateGrayTagReason"
                    />
                    <div v-if="hotWord" class="form-row">
                      <label class="form-label">热搜词：</label>
                      <p
                        v-highlight-config="hotWordAreaName"
                        data-area-style="color: white; background: var(--orange);"
                        :key="hotWord"
                      >
                        {{ hotWord }}
                      </p>
                    </div>
                    <!-- push审核相关 -->
                    <div class="pushData mt-30" v-if="pushList.length">
                      <div
                        class="pushItem mb-20"
                        v-for="(item, index) in pushList"
                        :key="index"
                      >
                        <div class="flex">
                          <strong class="push_title">
                            {{ item.style }}主标题：
                          </strong>
                          <div>{{ item.title }}</div>
                        </div>
                        <div class="flex">
                          <strong class="push_title">
                            {{ item.style }}副标题：
                          </strong>
                          <div>{{ item.subtitle }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 第三行 P序 -->
              <div class="grid" v-if="!todoConfig || !todoConfig.showClipList">
                <ClipList
                  :aid="archive.aid"
                  :bvid="archive.bvid"
                  :showVideoToggle="true"
                  :sourceFile="video.sourceFile"
                  :isCategory="true"
                  :videos.sync="video.clips"
                  :disabled="disabled"
                  @show-video-preview="showPreviewVideo"
                  @hide-video-preview="hidePreviewVideo"
                />
              </div>
            </div>
          </template>
        </template>
        <!-- 高级设置 -->
        <template v-else>
          <Advanced
            v-bind.sync="advanced"
            :attrForm.sync="attrForm"
            :aid="archive.aid"
            :mid="archive.mid"
            :videos="video.clips"
            :disabled="disabled"
          />
        </template>
      </template>
    </div>
    <ExtraInfoDialog
      :visible.sync="extraInfoVisible"
      :extraInfoList="extraInfoList"
    />
    <template #cache>
      <VideoPreviewModal
        :toggle="true"
        :preferToUseRawFile="preferToUseRawFile"
        :visible="!!(showVideoPreviewModal && videoPreview.pIndex)"
        :pIndex="videoPreview.pIndex"
        :aiRiskHint="videoPreview.aiRiskHint || {}"
        :title="videoPreview.title"
        :aid="videoPreview.aid"
        :cid="videoPreview.cid"
        :videosrc="videoPreview.rawFileSource"
        :showExtractor="perms.VIDEO_FRAME_TOOL"
        :sliceMap="sliceMap"
        :videoPlaylist="videoPlaylist"
        @close="hidePreviewVideo"
        @update:slice-map="handleSliceMapChange"
      />
    </template>
  </TodoTaskDetailStruct>
</template>
<script>
import TodoTaskDetailStruct from '@/v2/struct/TodoTaskDetailStruct'
import createPageModule from '@/v2/struct/TodoTaskDetailStruct/module'
import BasePage from '@/v2/core/base-page'
import ArchiveHistory from '@/v2/biz-components/archive/ArchiveHistory'
import ArchiveInfo from '@/v2/biz-components/archive/ArchiveInfo'
import StickyNav from '@/v2/biz-components/archive/StickyNav'
import MissionCard from '@/v2/biz-components/archive/MissionCard'
import Warnings from '@/v2/biz-components/archive/Warnings'
import ArchiveCover from '@/v2/biz-components/archive/ArchiveCover'
import ArchiveSource from '@/v2/biz-components/archive/ArchiveSource'
import BusinessOrder from '@/v2/biz-components/archive/BusinessOrder'
import Advanced from '@/v2/biz-components/archive/Advanced'
import ClipList from '@/v2/biz-components/archive/ClipList'
import routeHelper from '@/mixins/route-helper'
import ArchiveError from '@/v2/biz-components/archive/ArchiveError'
import CategoryOperation from '@/v2/biz-components/workbench/CategoryOperation.vue'
import VideoPreviewModal from '@/v2/biz-components/archive/VideoPreviewModal'
import { mapActions, mapState } from 'vuex'
import { ArchiveAuditService } from '@/v2/service/workbench/archive/audit'
import OperationRecord from '@/pages/workbench/quality/components/OperationRecod.vue'
import trackMixin from '@/mixins/track.mixin'
import auditHotkeysMixin from '@/v2/biz-pages/workbench/archive/mixins/audit-hotkeys.js'
import { workbenchApi, workbenchDetailApi } from '@/v2/api'
import { safeJsonParse } from '@/utils'
import notify from '@/lib/notify'
import {
  washOperation,
  getSelectedTagFromTagList,
  getDataFromTagList,
  getSelectedTagNames
} from '@/pages/workbench/archive/wash/util.js'
import ExtraInfoDialog from '@/components/ExtraInfoDialog.vue'
import SubmitLog from '@/v2/biz-components/archive/SubmitLog'
import { AgFormItem } from '@/v2/pure-components/ElementUpdate'
import BlockOption from '@/v2/biz-components/archive/BlockOption'
import { genSubmitLog } from '@/v2/data-source/archive/info/adapter'
import { cloneDeep } from 'lodash-es'
import { sensitiveWordMixin } from '@/mixins/workbench-config'

const QaRecallTodoIds = [
  390003, // 内容分级 - 审核召回
  420005, // 内容分级 - 审核召回 - 延迟
  390004, // 内容分级 - 回查召回
  390005 // 内容分级 - 回查召回 - 延迟
]

export default {
  extends: BasePage,
  mixins: [routeHelper, trackMixin, auditHotkeysMixin, sensitiveWordMixin],
  components: {
    VideoPreviewModal,
    TodoTaskDetailStruct,
    Advanced,
    ArchiveHistory,
    ArchiveInfo,
    StickyNav,
    MissionCard,
    Warnings,
    ArchiveCover,
    ArchiveSource,
    ArchiveError,
    BusinessOrder,
    ClipList,
    OperationRecord,
    CategoryOperation,
    ExtraInfoDialog,
    SubmitLog,
    BlockOption,
    AgFormItem
  },
  beforeRouteLeave(to, from, next) {
    this.pageEventBus.$emit('beforeRouteLeave', to, from, next)
  },
  props: {
    pageService: {
      type: Object,
      default() {
        return {
          archiveAuditService: ArchiveAuditService
        }
      }
    },
    pageModule: {
      type: Object,
      default() {
        return createPageModule()
      }
    }
  },
  data() {
    return {
      showErrorPage: false,
      dataReady: false,
      isAdvanced: false,
      // 重新压制视频的弹窗
      suppressDialogShow: false,
      xcode2: undefined,
      // 限流弹窗
      showLimitDialog: false,
      // 基础信息
      listType: '00',
      listReview: -1,
      // 下方是数据
      // 高级设置
      advanced: {},
      // 稿件信息
      archive: {},
      // 稿件封面
      archiveCover: {},
      // 稿件历史
      history: {},
      // 任务模块
      mission: {},
      // 导航栏模块
      stickyNav: {},
      // 视频模块
      video: {},
      // 警告栏
      warnings: {
        // TODO:找不到有推广位信息的数据，请产品确认
        promotePos: [],
        deviceWarnings: [],
        userWarning: []
      },
      // 操作日志
      submitLog: {},
      // 商单模块
      business: {},
      // 重置用的数据
      resetData: {},
      // 属性位和禁止项表单
      attrForm: {},
      // 接口返回的部分属性位和禁止项
      resetUpdateAttr: {},
      // 稿件tag选项
      tagOptions: [],
      // 提取信息卡片
      extraInfoVisible: false,
      extraInfoList: [],
      // 提示
      tips: [],
      // 子提交参数，一次性数据，得清空
      subSubmitParams: {},
      dialogShow: false,
      // 服务端快照
      serverSnapshot: {},
      videosCompetitor: [],
      // 限流通知
      upNotify: false,
      // 分类操作栏模块
      operations: {
        tagList: [],
        remark: '',
        annotation: {},
        grayTagReason: []
      },
      resetOperations: {
        tagList: [],
        remark: '',
        annotation: {},
        grayTagReason: []
      },
      disabled: true,
      qaHistory: [],
      qa_business_id: undefined,
      qa_id: undefined,
      videoPreview: {}, // 正在预览的分 p 视频
      showVideoPreviewModal: false, // 是否显示分 p 预览弹窗
      sliceMap: {},
      videoPlaylist: [],
      preferToUseRawFile: true,
      userSubmitHistory: [], // 投稿历史
      userStats: {}, // 概览数据
      block_ops: {}, // 禁止项
      hotWord: '', // 热搜词
      pushList: [], // push样式列表
      isGrayTag: false
    }
  },
  watch: {
    // FMP
    dataReady(val) {
      if (val) {
        this.$nextTick(() => {
          this.trackFMP()
        })
      }
    }
  },
  computed: {
    ...mapState({
      arctypeMap: (state) => state.arctype.arctypeMap,
      mode: (state) => state.pageState.mode,
      todoId: (state) => state.pageState.todoId,
      itemId: (state) => state.pageState.itemId,
      todoType: (state) => state.pageState.todoType,
      businessId: (state) => state.pageState.businessId,
      todoConfig: (state) => state.pageState.todoConfig,
      workbenchHistory: (state) => state.pageState.history,
      transList: (state) => state.pageState.transList
    }),
    // 判断是否是质检召回
    isQaRecall() {
      return QaRecallTodoIds.includes(this.todoId)
    },
    showExtractInfo() {
      return (
        this.perms.ABSTRACT_ARC_INFORMATION &&
        this.todoConfig?.showExtractInfo === true
      )
    },
    hotWordAreaName() {
      return this.todoConfig.hotWordAreaName
    },
    adorderType() {
      return this.business?.orderInfo?.type || ''
    }
  },
  methods: {
    ...mapActions({
      getArctype: 'arctype/getArctype',
      fetchCommon: 'common/fetchCommon'
    }),
    handleUpdateNoReprint(val) {
      this.attrForm.no_reprint = val
    },
    async getTodoConfig(todoId) {
      try {
        const { data } = await workbenchApi.getBusinessConfig({
          business_id: this.businessId,
          todo_id: todoId,
          view_type: 39
        })

        return safeJsonParse(data?.[0]?.config, null)
      } catch (e) {
        console.error(e)
      }
    },
    afterSubmit() {
      this.videoPreview = {}
      this.showVideoPreviewModal = false
    },
    async afterGetResource(err, resource, res) {
      if (!err && resource) {
        const parent_todo_id =
          res?.data?.single_dispatch?.parent_todo_id ||
          res?.data?.parent_todo_id
        let todoConfig = this.todoConfig
        // TODO: 后续服务支持下发review，可去除此处获取配置逻辑
        // 流转待办场景，需要依赖原始待办的id, 来传递review
        if (parent_todo_id) {
          todoConfig = await this.getTodoConfig(parent_todo_id)
        }

        if (todoConfig) {
          this.listType = todoConfig.list_type
          this.listReview = todoConfig.from_review
        }

        const opers = await this.getOpers(resource?.archive, res?.data)
        this.serverSnapshot = {
          arcInfoSnapshot: resource.arc_info_before_recheck_json
        }
        // 这里拿到都是空
        await this.archiveAuditService.getArchive(resource, {
          opers: opers?.audit_single,
          flowId: opers?.audit_single?.[0]?.flow_id?.[0]
        })
        const itemContent = resource?.item_content
        if (itemContent) {
          this.qa_business_id = itemContent.qa_business_id
          this.qa_id = itemContent.qa_id
          const hotWord = itemContent.hot_word
          if (hotWord) {
            this.hotWord = hotWord
            this.$nextTick(() => this.$EventBus.$emit('sensitiveWordChange'))
          }
        }
        this.setExtraInfoList()
        this.getHistoryData()
        // 投稿历史记录处理&投稿历史记录概览数据处理
        const submitLog = genSubmitLog(resource) || {}
        this.userSubmitHistory = submitLog.userSubmitHistory || []
        this.userStats = submitLog.userStats || {}
        // 禁止项数据处理
        const { norank, norecommend, nosearch, noindex, nohot } =
          resource.arc_flow_attributes
        this.block_ops = { norank, norecommend, nosearch, noindex, nohot }
        // push数据
        const { work_item } = resource || {}
        const pushData = safeJsonParse(work_item?.metadata, {})
        this.pushList = pushData?.push_cls_info?.list || []
        this.sliceMap = {}
      } else {
        this.clearData()
      }
      this.toastTips()
    },
    setExtraInfoList() {
      const { archive, arctypeMap } = this
      this.extraInfoList = [
        {
          name: '视频',
          value: archive.bvid
        },
        {
          name: '标题',
          value: archive?.title
        },
        {
          name: '分区',
          value: arctypeMap[archive.typeid]
        },
        {
          name: 'up主',
          value: archive.author?.name
        },
        {
          name: '粉丝数',
          value: archive.author?.followerNum || 0
        },
        {
          name: '内容涉及分层标注标签',
          value: ''
        },
        {
          name: '处理方式',
          value: ''
        }
      ]
    },
    checkHighFollowerOrView() {
      if (this.todoConfig) {
        const { followerThreshold, viewThreshold } = this.todoConfig
        if (
          this.archive.author.followerNum >= followerThreshold ||
          this.stickyNav.cardInfo.view >= viewThreshold
        ) {
          return new Promise((resolve, reject) => {
            setTimeout(() => {
              this.$confirm(
                '当前稿件命中 <span style="color: var(--red)">高粉丝/高播放</span> 阈值，点击【确认】提交将按标签关联禁止项进行生效，点击【取消】关闭弹窗',
                '提交',
                {
                  dangerouslyUseHTMLString: true
                }
              )
                .then((_) => {
                  resolve()
                })
                .catch((error) => {
                  reject(error)
                })
            }, 0)
          })
        }
      }
    },
    clearData() {
      const resetData = this.$options.data()
      // TODO: 要补充其他值吗
      this.qaHistory = resetData.qaHistory
      this.qa_business_id = resetData.qa_business_id
      this.qa_id = resetData.qa_id
      this.up_notify = resetData.up_notify
      this.itemId = resetData.itemId
      this.extraInfoList = []
      this.handleLimitCheck(this.operations.tagList)
    },
    // hook: 提交
    async submitData() {
      // 分成两步
      // 1.校验
      try {
        await this.checkHighFollowerOrView()
      } catch (error) {
        console.error(error)
        return
      }
      const msg = this.$refs.operationRef.validateReason()
      if (msg) {
        this.$alert(msg, `请检查【灰标】限流理由`)
        return
      }
      const annotation = this.getAnnotation()

      // 待办配置中 story内容标注，支持annotation为空
      if (
        !this.todoConfig?.supportEmptyAnnotation &&
        Object.keys(annotation).length === 0
      ) {
        notify.info('分类必选', 1500)
        return
      }
      // 分类特有参数
      const params = {
        annotation,
        remark: this.operations.remark,
        extra_data: {
          up_notify: this.upNotify
        },
        ...this.genQaParams()
      }
      if (this.isGrayTag) {
        const tagReason = this.operations.grayTagReason || []
        params.extra_data.tag_reason = JSON.stringify(tagReason)
      }
      // 2.接口返回处理
      this.pageEventBus.$emit('submit', params)
    },
    /**
     * 获取操作项
     */
    async getOpers(archive, resource) {
      let err, res, opers
      try {
        // TODO: 待网关统一切换后，可统一至getOpers接口上
        res = await workbenchApi.getOpers({
          business_id: this.businessId,
          todo_id: this.todoId,
          mode: 2,
          page_detail: 1,
          oid: archive.aid
        })
        opers = res.data
      } catch (error) {
        err = error
        console.error(error)
      }
      this.pageEventBus.$emit('afterGetOpers', err, opers, resource)
      return opers
    },
    // 获取历史记录
    async getHistoryData() {
      if (this.isQaRecall && this.qa_business_id && this.qa_id) {
        this.getQaHistory()
      }
    },
    // 获取质检历史记录
    async getQaHistory() {
      try {
        const res = await workbenchDetailApi.getQaLog({
          business_id: this.qa_business_id,
          qa_id: this.qa_id
        })
        this.qaHistory = res.data.logs || []
      } catch (error) {
        this.qaHistory = []
        console.error(error)
      }
    },
    afterGetOpers(err, opers, resource) {
      const needClearResult = this.isQaRecall && resource.item_state === 0
      if (err) {
        console.error(err)
        this.operations = {
          tagList: [],
          remark: '',
          annotation: {},
          grayTagReason: []
        }
      } else {
        const { tagList, remark, annotation } = washOperation({
          operation: opers.annotation,
          result: needClearResult ? {} : resource.result,
          remarkData: needClearResult ? '' : resource.remark
        })
        this.operations = {
          tagList: tagList || [],
          remark: remark || '',
          annotation: annotation || {},
          grayTagReason: []
        }
        // 分类标注打得标签不一定是灰标 需要通过标签业务id判断一次
        this.isGrayTag = opers?.oper_tags?.tag_business_id === 1
      }
      this.handleLimitCheck(this.operations.tagList)
      this.resetOperations = cloneDeep(this.operations)
    },
    handleUpdateGrayTagReason(newVal) {
      this.operations = {
        ...this.operations,
        grayTagReason: newVal
      }
    },
    handleUpdateTagList({ newTagList, selectedTag }) {
      const newVal = newTagList
      const oldVal = this.operations.tagList
      // 1.找出当前所有勾选的选项
      // 2.检查是否有其中一个有up_notify: true
      const newTags = getSelectedTagFromTagList(newVal)
      const newUpNotifyTags = newTags.filter((item) => item.up_notify === true)
      const newHasNotifyTag = newUpNotifyTags.length > 0

      const oldTags = getSelectedTagFromTagList(oldVal)
      const oldUpNotifyTags = oldTags.filter((item) => item.up_notify === true)
      const oldHasNotifyTag = oldUpNotifyTags.length > 0

      // 只比较有up_notify医疗资质的tag的变化
      // 1. 新的有医疗资质tag，旧的没有 = 新增了医疗资质tag，结果：勾选
      if (newHasNotifyTag && !oldHasNotifyTag) {
        this.upNotify = true
      } else if (!newHasNotifyTag && oldHasNotifyTag) {
        // 2. 新的没有医疗资质tag，旧的有 = 删除了医疗资质tag，结果：取消勾选
        this.upNotify = false
      } else if (!newHasNotifyTag && !oldHasNotifyTag) {
        // 3. 新旧都没有医疗资质tag，结果： 什么都不动
      } else {
        // 4.新旧都有医疗资质tag，判断前后变化
        if (selectedTag.up_notify && selectedTag.value === 1) {
          this.upNotify = true
        }
      }
      this.operations = {
        ...this.operations,
        tagList: newVal
      }
    },
    handleLimitCheck(tagList) {
      const data = getSelectedTagFromTagList(tagList)
      this.upNotify = data.some((item) => item.up_notify === true)
    },
    // 获取标签选择
    getAnnotation() {
      return getDataFromTagList(this.operations.tagList)
    },
    genQaParams() {
      let {
        advanced,
        archive,
        archiveCover,
        mission,
        stickyNav,
        video,
        warnings,
        business,
        attrForm,
        tagOptions,
        topic,
        resetUpdateAttr,
        operations,
        history,
        submitLog,
        tips,
        userSubmitHistory, // 投稿历史
        userStats, // 概览数据
        block_ops, // 禁用项
        pushList // push样式列表
      } = this

      // 稿件历史、提交记录、用户信息使用实时信息
      archive = cloneDeep(archive)

      const snapshotStat = JSON.stringify({
        advanced,
        archive,
        archiveCover,
        mission,
        stickyNav,
        video,
        warnings,
        business,
        attrForm,
        tagOptions,
        topic,
        resetUpdateAttr,
        operations,
        history,
        submitLog,
        tips,
        snapshotVersion: '2.0',
        userSubmitHistory, // 投稿历史
        userStats, // 概览数据
        block_ops, // 禁用项
        pushList
      })

      return {
        qa_snapshot_data: {
          oid: this.archive.aid, // aid稿件id
          mid: this.archive.mid, // 稿件发布人up主
          content: this.archive.title,
          extra1: parseInt(this.archive.state, 10), // 稿件审核状态
          extra4: this.itemId,
          extra1s: String(this.archive.typeid), // 分区id
          extra2s: getSelectedTagNames(this.operations.tagList), // 分级结果完整细标
          extra3s: this.operations.remark // 分级备注
        },
        qa_snapshot_detail: snapshotStat
      }
    },
    clearSubmitParams() {
      this.subSubmitParams = {}
    },
    toastTips() {
      const { warningsProtectNote } = this.archive
      const { tips } = this
      if (warningsProtectNote) {
        notify.warning(warningsProtectNote, 0)
      }
      if (tips && tips.length > 0) {
        tips.forEach((tip) => {
          notify.info(tip, 1000)
        })
      }
    },
    toResetData() {
      this.archiveAuditService.resetArchive()
      this.operations = cloneDeep(this.resetOperations)
    },
    toGoBack() {
      this.pageEventBus.$emit('backToList')
    },
    setDialogShow(payload) {
      this.dialogShow = payload
    },
    hotkeysSubmit() {
      if (this.dialogShow || this.suppressDialogShow || this.showLimitDialog)
        return
      this.submitData()
    },
    showPreviewVideo(payload) {
      console.log('show VideoPreviewModal')
      const { clipInfo, videoPlaylist, preferToUseRawFile } = payload
      if (
        clipInfo.pIndex === this.videoPreview.pIndex &&
        this.showVideoPreviewModal
      ) {
        this.showVideoPreviewModal = false
      } else {
        this.videoPreview = clipInfo
        this.videoPlaylist = videoPlaylist
        this.showVideoPreviewModal = true
        this.preferToUseRawFile = preferToUseRawFile
      }
    },
    hidePreviewVideo() {
      this.showVideoPreviewModal = false
    },
    handleSliceMapChange({ pIndex, slices }) {
      this.$set(this.sliceMap, pIndex, slices)
    }
  },
  created() {
    this.getArctype()
    this.fetchCommon()
    this.pageEventBus.$on('afterGetResource', this.afterGetResource)
    this.pageEventBus.$on('afterSubmit', this.afterSubmit)
    this.pageEventBus.$on('afterGetOpers', this.afterGetOpers)
  },
  beforeDestroy() {
    this.pageEventBus.$off('afterGetResource', this.afterGetResource)
    this.pageEventBus.$off('afterSubmit', this.afterSubmit)
    this.pageEventBus.$off('afterGetOpers', this.afterGetOpers)
  }
}
</script>

<style lang="stylus" scoped>
.bff-detail
  height 100% !important
  width 100%
  box-shadow 0 2px 12px 0 rgba(0, 0, 0, 0.1)
  border-radius 2px
  box-sizing border-box
  background var(--content-bg-color)
  >>>.el-form-item__label
    line-height 30px
  >>>.el-form-item__content
    line-height 30px
  >>>.el-textarea textarea
    font-family 'Avenir', Helvetica, Arial, sans-serif
  .base-row
    height 60px
    padding 10px 18px
    font-size 14px
    z-index 20
    line-height 3
    box-shadow 0 2px 4px 0 rgba(0, 0, 0, 0.1)
    .base-div
      display flex
      .default-base-info
        line-height 2.6
        .el-button
          vertical-align top
        .task-info
          color var(--text-color-dark-1)
          margin-right 10px
          em
            color var(--text-color)
            font-weight bold
        .delay-num > em
          color var(--red)
  .grid
    display grid
    margin-bottom 5px
    background var(--bg-color)
    padding 8px
    &:last-child
      margin-bottom 0px
  .grid-wrap
    overflow auto
    // 减去base-row高度
    height calc(100% - 60px)
    padding 10px
    box-sizing border-box
  .custom-operation-wrapper
    margin-right 20px
    .custom-operation
      background var(--content-bg-color)
      margin-bottom 10px
      &:last-child
        margin-bottom 0px
  .form-row
    display flex
    line-height 40px
  .form-label
    font-size 14px
    color var(--text-color)
    padding 0 12px 0 0
    -webkit-box-sizing border-box
    box-sizing border-box
    width 80px
  .push_title
    flex-shrink 0
    display inline-block
    width 180px
    text-align right
  .flex
    display flex
</style>
