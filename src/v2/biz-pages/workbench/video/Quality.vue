<template>
  <!-- 稿件视频 审核 -->
  <TodoTaskDetailStruct defaultMode="Pull" class="wkb-video-quality-v2">
    <template #nav>
      <div class="nav-submit flex-lr flex-1">
        <div class="flex-ac">
          <span class="ml-8">/</span>
          <span class="ml-8">cid: {{ video && video.cid }}</span>

          <NavModule
            class="ml-80"
            :hideIncrement="true"
            :archive="{
              aid,
              bvid,
              increment_id
            }"
          />
          <template v-if="archiveState && archiveState.text">
            ：
            <span :style="`color:${archiveState.color};`">
              {{ archiveState.text }}
            </span>
          </template>

          <div class="ml-280">
            <el-button
              type="primary"
              size="small"
              @click="goToVideoDetail"
              :disabled="!resource"
            >
              跳转视频详情
            </el-button>
            <el-button
              size="small"
              @click="qualityPannelVisible = true"
              :disabled="!resource"
            >
              质检面板
            </el-button>
            <el-button
              v-if="transList && transList.length > 0"
              size="small"
              type="primary"
              plain
              @click="pageEventBus.$emit('showTransfer')"
            >
              延迟/流转
            </el-button>
          </div>

          <BusinessLabels
            style="padding-bottom: 5px"
            vertical
            :value="businessLabels"
          />
        </div>

        <NavFlow :aid="aid" :cid="video && video.cid" />
      </div>
    </template>
    <VideoQualityPannel
      v-if="qaOpers && !!resource && qualityPannelVisible"
      :visible.sync="qualityPannelVisible"
      :qaOpers="qaOpers"
      :qa="qa"
      rememberPosition="video-auidt-quality-pannel"
      @submit="onSubmit"
      :quickRemarks="todoConfig && todoConfig.quickRemarks"
      :mistake_levels="bizConfig && bizConfig.mistake_levels"
      :bizConfig="bizConfig"
      :todoConfig="todoConfig"
    />
    <Detail
      :title="title"
      :highlight="highlight"
      :highlightKeyword="highlightKeyword"
      :videoHighlightKeyword="videoHighlightKeyword"
      :eptitle="eptitle"
      :desc="desc"
      :cover="cover"
      :cover2="cover2"
      :tag="tag"
      :copyright="copyright"
      :author="author"
      :userWarnings="userWarnings"
      :content="content"
      :dynamic="dynamic"
      :missionRule="missionRule"
      :arctypeV1="arctypeV1"
      :source="source"
      :ctime="ctime"
      :mission="mission"
      :topic="topic"
      :mode="mode"
      :aiTagList="aiTagList"
      :videoCopyright="videoCopyright"
      :aiMark="aiMark"
      :cid="video && video.cid"
      :playerData="playerData"
      :archiveAddit="archiveAddit"
      :todo_id="todoId"
      :tipList="tipList"
      :filename="video && video.filename"
      :aid="video && video.aid"
      :relatedVideos="relatedVideos"
      :relatedVideoStats="relatedVideoStats"
      :relatedVideoSize="relatedVideoSize"
      :videoshots="videoshots"
      :previewContent="previewContent"
      :disabled="true"
      :showToggle="true"
      :area="area"
      @showBigModal="showBigModal"
      :userStats="userStats"
      :userSubmitHistory="userSubmitHistory"
      @clickArchiveStatsTitle="onClickArchiveStatsTitle"
      :ai_img_urls="ai_img_urls"
      :showCancelMission="form.status === 0"
      :cancel_mission="form.cancel_mission"
      @changeMission="onChangeMission"
      :mission_check="mission_check"
      :toggle_video_src="toggle_video_src"
      :ai_cover_info="ai_cover_info"
      :sourceFileExpired="sourceFileExpired"
      enableExternalVideoBlock
      @detail-mounted="positionVideoBlock"
    >
      <template #audit-log="{ styles }">
        <el-descriptions-item
          label="操作记录"
          :contentStyle="{
            ...styles.contentStyle,
            padding: 0
          }"
        >
          <div class="operation-log-v2">
            <!-- 1、工作台历史 实时-->
            <HistoryLog :history="history" class="workbench-log" />
            <!-- 2、视频历史 快照 -->
            <VideoLog
              class="video-log font-13"
              :videoHistory="videoHistory"
            />
            <!-- 3、撞车提示 兼容旧快照 -->
            <CrashWarningsOld
              :crashWarnings="crashWarnings"
              class="crash-warnings"
              v-if="isOldCrash"
            />
            <CrashWarnings
              :crashWarnings="crashWarnings"
              class="crash-warnings"
              :showPassOper="false"
              :showRejectOper="false"
              :cid="video && video.cid"
              v-else
            />
          </div>
        </el-descriptions-item>
      </template>
      <template #audit-operation="{ styles }">
        <el-descriptions-item
          label="审核操作"
          :contentStyle="styles.contentStyle"
        >
          <template v-if="!!resource">
            <VideoAuditOperV2
              :aid="aid"
              :cid="video && video.cid"
              :disabled="true"
              :form="form"
              :filename="video && video.filename"
              :showForbid="showForbid"
              :loadResultOnly="true"
              :showSuggest="true"
              :showScreenshot="true"
              @change="onChangeForm"
            />
          </template>
        </el-descriptions-item>
      </template>
    </Detail>
    <template #cache>
      <VideoBlock
        v-show="videoBlockVisible"
        style="margin-top: 12px"
        :style="videoBlockStyle"
        ref="videoBlock"
        :aid="aid"
        :cid="video && video.cid"
        :filename="video && video.filename"
        :playerData="playerData"
        :archiveAddit="archiveAddit"
        :mode="mode"
        :aiTagList="aiTagList"
        :videoCopyright="videoCopyright"
        :aiMark="aiMark"
        :todo_id="todoId"
        :tipList="tipList"
        :toggle_video_src="toggle_video_src"
        :ai_cover_info="ai_cover_info"
        :sourceFileExpired="sourceFileExpired"
      />
    </template>
  </TodoTaskDetailStruct>
</template>
<script>
import { mapState, mapActions } from 'vuex'
import TodoTaskDetailStruct from '@/v2/struct/TodoTaskDetailStruct'
import Detail from './Detail.vue'
import VideoBlock from './blocks/VideoBlock.vue'
import BasePage from '@/v2/core/base-page'
import createPageModule from '@/v2/struct/TodoTaskDetailStruct/module'
import {
  BusinessLabels,
  NavModule,
  NavFlow,
  VideoAuditOperV2,
  HistoryLog,
  CrashWarnings,
  CrashWarningsOld,
  VideoLog,
  VideoQualityPannel
} from '@/v2/biz-components/workbench/index'
import { archiveApi, workbenchDetailApi, archiveTaskApi } from '@/api/index'
import { changeHttpProtocol, genQaOpers, getQualityData } from './util'
import { genHost } from '@/api/utils'
import { cloneDeep } from 'lodash-es'
import trackMixin from '@/mixins/track.mixin'
import notify from '@/lib/notify'
import trackerFunc from '@/utils/report'

export default {
  extends: BasePage,
  components: {
    BusinessLabels,
    TodoTaskDetailStruct,
    Detail,
    NavModule,
    NavFlow,
    VideoAuditOperV2,
    HistoryLog,
    VideoLog,
    CrashWarnings,
    CrashWarningsOld,
    VideoQualityPannel,
    VideoBlock
  },
  mixins: [trackMixin],
  props: {
    pageModule: {
      type: Object,
      default() {
        return createPageModule()
      }
    }
  },
  data() {
    return {
      disabled: false,
      resource: null,

      video: {},
      aid: '',
      bvid: '',
      increment_id: '',

      highlight: {},
      highlightKeyword: {},
      title: '',
      eptitle: '', // 分p标题
      desc: '', // 分p简介
      cover: '',
      cover2: '',
      author: {},
      tag: '',
      videoHighlightKeyword: {}, // 视频敏感词
      userWarnings: [],
      content: '', // 简介
      dynamic: '',
      missionRule: {},
      mission: null,
      arctypeV1: '', // UP 对接方
      source: '', // 转载来源
      ctime: null, // 投稿时间
      topic: null, // 话题信息
      aiTagList: [],
      videoCopyright: {},
      aiMark: '',
      crashWarnings: [],
      tipList: [],
      ai_cover_info: {},

      // 播放器
      playerData: {
        playurl: '',
        xcode_state: '',
        mediaInfo: {},
        watermarkState: ''
      },
      archiveAddit: null,
      businessLabels: {},

      videoHistory: [],
      relatedVideos: [],
      relatedVideoStats: {},
      relatedVideoSize: 0,
      videoshots: [],
      previewContent: [],
      form: {
        status: null,
        norank: false,
        noindex: false,
        norecommend: false,
        nosearch: false,
        tids: [], // 审核tag
        gray_tags: [], // 灰标
        reason_id: '',
        reason: '',
        reasonTemplate: '',
        note: '',
        forward: '', // 撞车设置
        position_id: '', // 位置
        cancel_mission: false, // 取消活动是否勾选
        suggests: [], // 修改建议数组
        suggest: '', // 修改建议（拼接后的用于回显）
        picture: [], // 截图（用于回显）
        pictureList: [] // 操作选中的截图列表
      }, // 审核操作
      initForm: {},
      initAttr: {},
      auditSingle: [],
      annotationOpers: [],
      noteRequired: true,
      copyright: '',
      warningsProtect: {},

      qualityPannelVisible: true, // #
      qaOpers: null, // qa操作项 #
      qa: {
        tags: [],
        remark: '',
        mistake_level: '',
        model_identify: '',
        risk_level: ''
      }, // qa结果 #
      archiveState: {},
      area: '', // 投稿地区
      showForbid: false, // 取快照

      showBigModalFlag: false, // 标记当前任务或者详情页是否点击过查看大图
      clickArchiveStatsFlag: false, // 标记当前任务或者详情页是否点击过投稿记录的标题

      userStats: {},
      userSubmitHistory: [],
      ai_img_urls: [],
      mission_check: null,
      toggle_video_src: false,
      sourceFileExpired: false,
      videoBlockStyle: {},
      videoBlockVisible: false
    }
  },
  watch: {
    showCrashSet: {
      handler(visible) {
        if (!visible) {
          this.form.forward = ''
        }
      }
    }
  },
  computed: {
    ...mapState('pageState', [
      'task',
      'mode',
      'todoId',
      'businessId',
      'todoConfig',
      'history',
      'transList',
      'bizConfig'
    ]),
    ...mapState({
      arctypes: (state) => state.arctype.arctypes,
      perms: (state) => state.user.perms
    }),
    showCrashSet() {
      // 仅当审核选择锁定操作，且驳回理由id196时展示该模块
      return (
        this.form &&
        String(this.form.status) === '-4' &&
        String(this.form.reason_id) === '196'
      )
    },
    // 撞车模块升级后需兼容旧快照
    isOldCrash() {
      if (!this.crashWarnings?.length) return false // 空的用新的
      return this.crashWarnings.find(
        (e) => e.filename_stored || e.filename_upload
      ) // 用字段区分
    }
  },
  methods: {
    ...mapActions({
      getArctype: 'arctype/getArctype'
    }),
    positionVideoBlock() {
      this.$nextTick(() => {
        this.videoBlockVisible = true
        const dummyVideoBlock = document.querySelector(
          '#video-block-placeholder'
        )
        if (dummyVideoBlock) {
          const { top: rawTop, width } = dummyVideoBlock.getBoundingClientRect()
          const minTop = 475
          const top = rawTop > minTop ? rawTop : minTop
          this.videoBlockStyle = {
            top: `${top}px`,
            left: '16px',
            width: `${width}px`,
            position: 'absolute'
          }
        }
      })
    },
    async afterGetResource(err, resource) {
      if (err) {
        console.log(err)
      }
      if (!resource) return
      this.showBigModalFlag = false // 新获取到数据后 初始化
      this.clickArchiveStatsFlag = false

      const formatData = getQualityData(resource || {}) || {}
      for (const key in formatData) {
        this[key] = formatData[key]
      }
      const { snapshotDataVersion, taskForm } = formatData
      if (this.video?.cid) {
        // 利用cid获取其他信息
        this.getExtraInfoByCid(this.video, snapshotDataVersion)
      }
      this.form = cloneDeep(taskForm)
      this.resource = resource

      // 弹窗： 稿件保护信息提示
      if (this.warningsProtect?.note) {
        // 提示一直在，直到页面销毁前关闭
        const toaster = notify.warning(this.warningsProtect.note, 0)
        this.$once('hook:beforeDestroy', () => {
          toaster.close()
        })
      }
    },
    afterGetOpers(err, opers) {
      if (err) {
        console.log(err)
      }
      const { qa = [] } = opers || {}
      this.qaOpers = genQaOpers(qa) // #
    },
    submit(val) {
      this.pageEventBus.$emit('submit', val)
      this.$refs?.videoBlock?.pause()
      this.$refs?.videoBlock?.clearFrameList()
      this.videoBlockVisible = false
      this.$refs?.videoBlock?.disableShortcut()
    },
    getExtraInfoByCid(video, snapshotDataVersion) {
      const { cid = '' } = video
      this.getPlayUrl(cid)
      //  1.1 以上版本不发请求
      if (!(Number(snapshotDataVersion) >= 1.1)) {
        this.getAiMark(cid, video.aid)
        this.getCrashWarnings(cid)
      }

      // 2.0 以上版本不发
      if (!(Number(snapshotDataVersion) >= 2)) {
        this.getHighlightKeywords(this.aid, cid, this.title, this.eptitle)
      }
    },
    getHighlightKeywords(aid, cid, title, eptitle) {
      const params = {
        aids: { [aid]: title },
        cids: { [cid]: eptitle }
      }
      workbenchDetailApi
        .getCopyright({
          data: JSON.stringify(params)
        })
        .then((res) => {
          const data = res.data
          this.videoHighlightKeyword = {
            title: data.cids[cid] || []
          }

          // 标题版权关键词
          const titleCopyrightKeywords = data?.aids[aid] || []
          this.highlightKeyword = {
            ...this.archiveFilter
          }
          if (titleCopyrightKeywords?.length) {
            this.highlightKeyword.title = [
              ...titleCopyrightKeywords,
              ...(this.archiveFilter.title || [])
            ]
          }
          // 从快照拿 #
        })
        .catch((e) => {
          this.videoHighlightKeyword = {
            title: []
          }
          this.highlightKeyword = {
            ...this.archiveFilter
          }
        })
    },
    getAiMark(cid, aid) {
      if (aid && cid) {
        archiveTaskApi
          .getAiMark({
            aid,
            cid
          })
          .then((res) => {
            if (res && res.data && res.data[cid]) {
              this.aiMark = res.data[cid].ai_score
            }
          })
          .catch((_) => {})
      }
    },
    // 获取视频播放地址
    getPlayUrl(cid) {
      workbenchDetailApi
        .getPlayUrl({
          cid
        })
        .then((res) => {
          this.playerData.playurl = changeHttpProtocol(
            res.data && res.data.playurl
          )
          this.toggle_video_src = !!res.data.tips
        })
        .catch((e) => {
          this.playerData.playurl = ''
          this.sourceFileExpired = e.code === 21521
        })
    },
    getCrashWarnings(cid) {
      archiveApi
        .getCrashInfo({
          cid
        })
        .then((res) => {
          const result = res.data || ''
          if (!result) return Promise.reject(new Error('撞车获取失败'))
          this.crashWarnings = result.crash || []
          this.tipList = (result.ai_knowledge || {}).data || []
          this.aiTagList = (result.ai_knowledge || {}).ai_tag || []
        })
        .catch(() => {
          this.crashWarnings = []
          this.tipList = []
          this.aiTagList = []
        })
    },
    /**
     * 点击查看大图
     * 埋点
     */
    showBigModal() {
      if (!this.showBigModalFlag) {
        // 多次点击只上报一次
        this.trackClick('videoshot_click', 1)
        this.showBigModalFlag = true // 当前任务下点击过 查看大图了
      }
    },
    /**
     * 点击投稿记录的标题
     */
    onClickArchiveStatsTitle() {
      if (!this.clickArchiveStatsFlag) {
        this.trackClick('upload_video_history_click', 1)
        this.clickArchiveStatsFlag = true // 当前任务下点击过 点击过标题了
      }
    },
    trackClick(name, oper) {
      trackerFunc(name, {
        cid: this.cid,
        type: 'audit',
        mode: this.mode,
        uid: this.uid,
        oper
      })
    },
    onChangeForm(val) {
      this.form = val
    },
    /**
     * 提交质检结果 #
     */
    onSubmit(qa) {
      // 提交前 还未点击查看大图 则上报
      if (!this.showBigModalFlag) {
        this.trackClick('videoshot_click', 0)
      }
      if (!this.clickArchiveStatsFlag) {
        this.trackClick('upload_video_history_click', 0)
      }
      this.qa = qa
      const params = {
        qa_tags: this.qa.tags,
        remark: this.qa.remark,
        qa_extra: {
          mistake_level: this.qa.mistake_level,
          model_identify: this.qa.model_identify,
          risk_level: this.qa.risk_level
        }
      }
      this.submit(params)
      this.qualityPannelVisible = false
      this.qa = {
        tags: [],
        remark: '',
        mistake_level: '',
        model_identify: '',
        risk_level: ''
      }
      setTimeout(() => {
        this.qualityPannelVisible = true
      }, 100)
    },
    goToVideoDetail() {
      const { cid = '', aid = '' } = this.video
      const business_id = +this.businessId
      const { fullPath } = this.$route

      window.open(
        `${genHost()}/aegis/#/archive/archive-video-task/resource/detail?cid=${cid}&aid=${aid}&business_id=${business_id}&back=${encodeURIComponent(
          fullPath
        )}`
      )
    },
    onChangeMission(val) {
      this.form.cancel_mission = val
    }
  },
  beforeRouteLeave(to, from, next) {
    this.pageEventBus.$emit('beforeRouteLeave', to, from, next)
  },
  created() {
    // 获取分区
    this.getArctype()
    this.pageEventBus.$on('afterGetResource', this.afterGetResource)
    this.pageEventBus.$on('afterGetOpers', this.afterGetOpers)
  },
  beforeDestroy() {
    this.pageEventBus.$off('afterGetResource', this.afterGetResource)
    this.pageEventBus.$off('afterGetOpers', this.afterGetOpers)
  }
}
</script>
<style lang="stylus" scoped>
.nav-submit {
  display: flex;
  flex-direction: row;
  align-items: center;

  >>> .info-row .ids {
    width: auto;
  }
}

.operation-log-v2 {
  display: flex;
  flex-direction: row;

  .workbench-log {
    flex: 1;
    border-right: 1px solid var(--border-color-light-2);
    height: 128px;
    overflow: auto;
    padding: 2px;

    >>> p {
      line-height: 1.5;
      font-size: 13px;
      margin-bottom: 4px;
    }
  }

  .video-log {
    flex: 1;
    border-right: 1px solid var(--border-color-light-2);
    display: flex;
    flex-direction: column;
    overflow: auto;
    height: 128px;
    padding: 2px;

    >>> li {
      margin-bottom: 4px;
    }
  }

  .crash-warnings {
    flex: 1;
    height: 128px;
    overflow: auto;
    padding: 2px;
  }
}
</style>
<style lang="stylus">
.wkb-video-quality-v2 {
  .el-form-item__label {
    line-height: 30px;
  }

  .el-form-item__content {
    line-height: 30px;
  }
}
</style>
