<template>
  <div>
    <div class="video-block-v2 flex-lr">
      <VideoPlayerContainer
        ref="videoContainer"
        :playerData="playerData"
        :cid="cid"
        :filename="filename"
        :fetchSubtitle="fetchSubtitle"
        :seek="archiveAddit && archiveAddit.act_upfrom_jump"
        :seekPause="true"
        :options="{
          toggle: VIDEO_SECOND_TODO_IDS.includes(todo_id) || toggle_video_src
        }"
        :hideInfo="true"
        :showToggle="showToggle"
        :sourceFileExpired="sourceFileExpired"
        getFrameRateEnabled
        @nano-canplay="handleCanplay"
        @nano-played="handleOnPlay"
        @nano-loaded-data="handleMediaLoaded"
        @loadeddata="handleMediaLoaded"
        @nano-paused="$emit('video-paused')"
        @toggle-fullscreen="(e) => $emit('toggle-fullscreen', e)"
      />
      <el-descriptions
        class="ml-8 flex-1"
        :colon="false"
        border
        :column="1"
        :labelStyle="labelStyle"
        :contentStyle="contentStyle"
      >
        <el-descriptions-item :span="1" label="AI提示">
          <div class="content ai-hint">
            <AiTag
              :list="aiTagList"
              :score="aiMark"
              :cid="cid"
              :reportBiz="mode === 'Push' ? 'push' : 'receive'"
              :ai_cover_info="ai_cover_info"
              :trackInfo="{
                event: PAGE_TRACK_KEY,
                value: { loc: TRACK_MAP.AIWARNING, cid, trackType: '曝光' }
              }"
            />
          </div>
        </el-descriptions-item>
        <el-descriptions-item :span="1" label="版权提示">
          <div class="content copyright-hint">
            <CopyrightInfo
              :aid="aid"
              :cid="cid"
              :minHeight="0"
              :trackInfo="{
                event: PAGE_TRACK_KEY,
                value: { loc: TRACK_MAP.VIDEOCOPYRIGHT, cid, trackType: '曝光' }
              }"
            />
          </div>
        </el-descriptions-item>
        <el-descriptions-item :span="1" label="知识库提示">
          <div class="content knowledge-hint">
            <AiLibrary
              :tips="tipList"
              :hideLabel="true"
              :trackInfo="{
                event: PAGE_TRACK_KEY,
                value: { loc: TRACK_MAP.AILIBRARY, cid, trackType: '曝光' }
              }"
              @click-library="onClickLibrary"
            />
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <div class="flex-as">
      <div>
        <div class="flex-as">
          <div
            class="font-14 font-bold"
            style="color: var(--text-color); width: 70px; line-height: 20px"
          >
            取时间：
          </div>
          <div>
            <el-switch v-model="showTimeExtractor"></el-switch>
          </div>
        </div>
        <VideoTimeExtractor
          ref="videoTimeExtractor"
          :cid="cid"
          :mediaReady="mediaReady"
          style="flex: 1 0 auto"
          v-if="showTimeExtractor"
          class="mt-8"
          @player-seek="seekTime"
          :getCurrentTime="getCurrentTime"
        ></VideoTimeExtractor>
      </div>

      <div class="ml-8" v-if="perms && perms.VIDEO_PIC_WRITE">
        <el-button type="primary" size="medium" @click="onClickScreenshot">
          截图
        </el-button>
        <InfoList
          class="mt-8"
          :frames="
            allScreenshots.map((e) => {
              return {
                start_time: e.rawSecond,
                start_url: e.url,
                note: e.note
              }
            })
          "
          @remove-card="removeCard"
          @player-seek="seekTime"
        ></InfoList>
      </div>
    </div>

    <ImageAnnotator
      ref="imageAnnotator"
      :imageDataUri="screenshotForm.imageDataUri"
      :toolbarConfig="['rect']"
      :lineColor="'#ee5037'"
      :width="'55%'"
      customClass="video-image-annotator"
      @export="handleExport"
    >
      <div slot="metadata" class="flex-ac flex-lr mb-8">
        <span>截图时间：{{ screenshotForm.time }}</span>
        <span class="flex-ac flex-lr">
          <span style="flex: 1 0 auto">内部备注：</span>
          <el-input v-model="screenshotForm.note" :max-length="16" />
          <el-tooltip placement="top">
            <div slot="content">
              在选择理由时用来区分是什么问题。
              <br />
              不会透传到用户，仅在本次操作中生效。
            </div>
            <i class="el-icon-info ml-8" />
          </el-tooltip>
        </span>
      </div>
    </ImageAnnotator>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import {
  AiTag,
  AiLibrary,
  VideoPlayerContainer
} from '@/v2/biz-components/workbench/index.js'
import { VIDEO_SECOND_TODO_IDS } from '../util'
import VideoTimeExtractor from '@/v2/biz-components/workbench/VideoTimeExtractor'
import { TRACK_MAP, PAGE_TRACK_KEY } from './ArcInfo.vue'
import { secondTimeFormat, DataURIToBlob } from '@/utils'
import { videoApi } from '@/api/index'
import ImageAnnotator from '@/v2/biz-components/archive/ImageAnnotator'
import { uploadImageByFile } from '@/plugins/bvcflow-upload'
import InfoList from '@/v2/biz-components/workbench/VideoTimeExtractor/InfoList.vue'
import CopyrightInfo from '@/v2/biz-components/archive/CopyrightInfo'
import notify from '@/lib/notify'
import { v4 as uuidv4 } from 'uuid'

export default {
  components: {
    AiTag,
    CopyrightInfo,
    AiLibrary,
    VideoPlayerContainer,
    VideoTimeExtractor,
    ImageAnnotator,
    InfoList
  },
  data() {
    return {
      VIDEO_SECOND_TODO_IDS,
      labelStyle: {
        width: '84px',
        minWidth: '84px',
        color: 'var(--text-color)',
        padding: '5px',
        background: 'var(--label-bg)'
      },
      contentStyle: {
        minWidth: '160px',
        color: 'var(--text-color)',
        padding: '5px'
      },
      mediaReady: false,
      showTimeExtractor: false,
      TRACK_MAP,
      PAGE_TRACK_KEY,
      screenshotForm: {},
      timer: null, // 轮询
      count: 1,
      playEventReported: false,
      exposureEventReported: false
    }
  },
  props: {
    aid: {
      type: [Number, String]
    },
    cid: {
      type: [Number, String],
      default: ''
    },
    filename: {
      type: String,
      default: ''
    },
    fetchSubtitle: {
      type: Boolean,
      default: false
    },
    playerData: {
      type: Object
    },
    archiveAddit: {
      type: Object
    },
    watermark: {
      type: Object
    },
    todo_id: {
      type: Number
    },
    aiTagList: {
      type: [Array, String],
      default() {
        return []
      }
    },
    videoCopyright: {
      type: Object,
      default() {
        return {}
      }
    },
    aiMark: {
      type: [String, Number],
      default: ''
    },
    mode: {
      type: String
    },
    tipList: {
      type: Array,
      default() {
        return []
      }
    },
    showToggle: Boolean,
    toggle_video_src: Boolean,
    ai_cover_info: Object,
    allScreenshots: {
      type: Array,
      default: () => []
    },
    sourceFileExpired: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapState({
      perms: (state) => state.user.perms
    }),
    hasVideoCopyright() {
      return (
        this.videoCopyright?.VideoDetailInfo?.length ||
        this.videoCopyright?.MusicDetailInfo?.length ||
        this.videoCopyright?.InterveneStyle?.Value
      )
    }
  },
  watch: {
    cid() {
      this.playEventReported = false
      this.exposureEventReported = false
    }
  },
  methods: {
    handleMediaLoaded() {
      this.mediaReady = true
    },
    pause() {
      this.$refs?.videoContainer?.pause()
    },
    disableShortcut() {
      this.$refs?.videoContainer?.disableShortcut()
    },
    clearFrameList() {
      this.$refs?.videoTimeExtractor?.clearFrameList()
      this.showTimeExtractor = false
    },
    seekTime(time) {
      try {
        if (window.nanoPlayer) {
          window.nanoPlayer.currentTime = time
        } else {
          const videoElement =
            window.player || document.getElementById('player')
          if (videoElement) videoElement.currentTime = time
        }
      } catch (err) {
        console.error('空降失败', err)
      }
    },
    getCurrentTime() {
      return window.nanoPlayer?.getCurrentTime()
    },
    onClickLibrary() {
      this.$tracker(PAGE_TRACK_KEY, {
        loc: TRACK_MAP.AILIBRARY,
        cid: this.cid,
        trackType: '点击'
      })
    },
    // 播放器可以播放的状态
    handleCanplay() {
      if (this.exposureEventReported) return
      this.$tracker(PAGE_TRACK_KEY, {
        loc: TRACK_MAP.VIDEOPLAYER,
        cid: this.cid,
        trackType: '曝光'
      })
      this.exposureEventReported = true
    },
    // 播放器播放的行为
    handleOnPlay() {
      if (this.playEventReported) return
      this.$tracker(PAGE_TRACK_KEY, {
        loc: TRACK_MAP.VIDEOPLAYER,
        cid: this.cid,
        trackType: '播放'
      })
      this.playEventReported = true
      this.$emit('video-played')
    },
    // 点击截图
    async onClickScreenshot() {
      if (!this.mediaReady) {
        notify.error('视频未加载完成，请稍后再试')
        return
      }
      if (this.allScreenshots?.length >= 30) {
        notify.error('当前截图已超过30张，请先删除已有截图再继续添加')
        return
      }
      try {
        let rawSecond
        console.log('截图start：', Date.now())
        window.nanoPlayer && window.nanoPlayer.pause()
        rawSecond =
          this.getCurrentTime() < 0.5
            ? Math.floor(this.getCurrentTime())
            : Math.ceil(this.getCurrentTime())
        const imageDataUri = await window.nanoPlayer.readFrameAsDataURL()
        console.log('截图end：', Date.now())
        const time = secondTimeFormat(rawSecond)
        this.screenshotForm = {
          rawSecond,
          time,
          imageDataUri
        }
        this.$refs.imageAnnotator.showModal()
      } catch (err) {
        console.error('从播放器获取截图失败', err)
        notify.error(err)
        this.resetScreenshotForm()
      }
    },
    async handleExport(editedImage) {
      try {
        const imageFile = DataURIToBlob(editedImage)
        const url = await uploadImageByFile(imageFile, {
          bucket: 'reason_picture'
        })
        if (url) {
          this.screenshotForm.url = url
          this.screenshotForm.uuid = uuidv4()
          this.$emit(
            'updateScreenshot',
            (this.allScreenshots || []).concat(this.screenshotForm)
          )
        }
        this.resetScreenshotForm()
      } catch (err) {
        console.error('上传截图失败', err)
        notify.error('上传截图失败')
        this.resetScreenshotForm()
      }
    },
    resetScreenshotForm() {
      this.screenshotForm = {
        imageDataUri: '', // 原始截图
        time: '',
        rawSecond: 0,
        url: '', // 标注后的截图
        note: '', // 内部备注
        uuid: ''
      }
    },
    removeCard(idx) {
      this.$emit(
        'updateScreenshot',
        (this.allScreenshots || []).filter((e, index) => index !== idx)
      )
    }
  },
  mounted() {
    this.resetScreenshotForm()
  }
}
</script>
<style lang="stylus" scoped>
.content
  max-height 120px
  min-height 40px
  min-width 176px
  overflow auto
  word-break break-word
.ai-hint
  content-visibility auto
  contain-intrinsic-size auto 176px auto 40px
.video-block-v2
  .copyright-hint
    content-visibility auto
    contain-intrinsic-size auto 176px auto 40px
    max-height 200px
.knowledge-hint
content-visibility auto
  contain-intrinsic-size auto 176px auto 120px
</style>
<style lang="stylus">
.video-image-annotator {
  .el-dialog__body {
    max-height: calc(100vh - 200px)
    overflow: auto
  }
}
</style>
