<template>
  <div class="wkb-vd-dt-v2">
    <div class="wkb-vd-dt-v2-l">
      <ArcInfo
        :cid="cid"
        :size="size"
        :highlight="highlight"
        :highlightKeyword="highlightKeyword"
        :videoHighlightKeyword="videoHighlightKeyword"
        :title="title"
        :cover="cover"
        :cover2="cover2"
        :eptitle="eptitle"
        :disabled="disabled"
        :tag="tag"
        :author="author"
        :userWarnings="userWarnings"
        :content="content"
        :dynamic="dynamic"
        :arctypeV1="arctypeV1"
        :source="source"
        :ctime="ctime"
        :copyright="copyright"
        :area="area"
        :upClickable="upClickable"
        :cancel_mission="cancel_mission"
        @changeEptitle="onChangeEptitle"
        :ai_cover_info="ai_cover_info"
        :showEpViews="showEpViews"
        :epViews="epViews"
      />
      <div
        id="video-block-placeholder"
        v-if="enableExternalVideoBlock"
        style="height: 519px; min-width: 920px"
      ></div>
      <VideoBlock
        v-else
        style="margin-top: 12px"
        :aid="aid"
        :cid="cid"
        :filename="filename"
        :playerData="playerData"
        :archiveAddit="archiveAddit"
        :mode="mode"
        :aiTagList="aiTagList"
        :videoCopyright="videoCopyright"
        :aiMark="aiMark"
        :todo_id="todo_id"
        :tipList="tipList"
        :showToggle="showToggle"
        :toggle_video_src="toggle_video_src"
        :allScreenshots="allScreenshots"
        @updateScreenshot="updateScreenshot"
        :ai_cover_info="ai_cover_info"
        :sourceFileExpired="sourceFileExpired"
      />
    </div>
    <div class="wkb-vd-dt-v2-r">
      <AuditBlock
        :cid="cid"
        :filename="filename"
        :relatedVideos="relatedVideos"
        :relatedVideoStats="relatedVideoStats"
        :relatedVideoSize="relatedVideoSize"
        :clickable="!!(perms && perms.AEGIS_ARCHIVEINFO_JUMP)"
        :aid="aid"
        :userStats="userStats"
        :userSubmitHistory="userSubmitHistory"
        @clickTitle="$emit('clickArchiveStatsTitle')"
      >
        <template #log="{ styles }">
          <slot name="audit-log" :styles="styles" />
        </template>
        <template #operation="{ styles }">
          <slot name="audit-operation" :styles="styles" />
        </template>
        <template #crash="{ styles }">
          <slot name="crash-target" :styles="styles" />
        </template>
      </AuditBlock>

      <div class="flex-lr">
        <ClipPreivew
          :videoshots="videoshots"
          :content="previewContent"
          :ai_img_urls="ai_img_urls"
          :maxWidth="640"
          class="mr-8"
          :trackInfo="{
            cid: this.cid,
            aid: this.aid,
            business_id: $route.query.business_id,
            todo_id: this.todo_id
          }"
          @showBigModal="showBigModal"
          @click-clip-preview="clickClipPreview"
        />
        <div class="font-14 mt-8">
          <div class="flex-col">
            <div class="flex-lr label">
              <p>活动信息</p>
              <div>
                <el-checkbox
                  :disabled="
                    !showCancelMission ||
                    (archiveAddit && archiveAddit.mission_id === 0)
                  "
                  :value="cancel_mission"
                  @change="onChangeMission"
                >
                  取消活动
                </el-checkbox>
                <el-tooltip
                  effect="dark"
                  content="勾选“取消活动”且提交，则视频所在的稿件取消活动关联。"
                  placement="top-start"
                >
                  <i
                    class="el-icon-warning-outline"
                    style="vertical-align: top; font-size: 10px"
                  ></i>
                </el-tooltip>
              </div>
            </div>
            <MissionInfo
              :mission="mission"
              :missionRule="missionRule"
              :arctypeMap="arctypeMap"
              :mission_check="mission_check"
              :disabled="archiveAddit && archiveAddit.mission_id === 0"
              class="break-word cell flex-1"
              style="min-height: 24px"
              :trackInfo="{
                event: PAGE_TRACK_KEY,
                value: { loc: TRACK_MAP.MISSION, cid, trackType: '曝光' }
              }"
            />
          </div>
          <!-- 第三行：话题信息 -->
          <div>
            <div class="label">话题信息</div>
            <div
              style="
                max-height: 64px;
                min-height: 24px;
                border-bottom: 1px solid #ebeef5;
              "
              class="break-word overflow-auto cell"
            >
              <TopicInfo
                :topic="topic"
                :trackInfo="{
                  event: PAGE_TRACK_KEY,
                  value: { loc: TRACK_MAP.TOPIC, cid, trackType: '曝光' }
                }"
              />
            </div>
          </div>
          <el-descriptions
            class="mt-8"
            :colon="false"
            border
            :column="1"
            :labelStyle="labelStyle"
            :contentStyle="contentStyle"
            :size="size"
          >
            <el-descriptions-item :span="1" label="用户维度">
              <UserStats :userStats="userStats" />
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import ArcInfo, { TRACK_MAP, PAGE_TRACK_KEY } from './blocks/ArcInfo.vue'
import VideoBlock from './blocks/VideoBlock.vue'
import AuditBlock from './blocks/AuditBlock.vue'
import {
  ClipPreivew,
  UserStats,
  MissionInfo,
  TopicInfo
} from '@/v2/biz-components/workbench/index'

export default {
  components: {
    ArcInfo,
    VideoBlock,
    AuditBlock,
    ClipPreivew,
    UserStats,
    MissionInfo,
    TopicInfo
  },
  data() {
    return {
      PAGE_TRACK_KEY,
      TRACK_MAP,
      labelStyle: {
        width: '80px',
        minWidth: '80px',
        color: 'var(--text-color)',
        padding: '5px',
        background: 'var(--label-bg)'
      },
      contentStyle: {
        minWidth: '160px',
        // width: '200px',
        color: 'var(--text-color)',
        padding: '5px'
      }
    }
  },
  computed: {
    ...mapState({
      arctypeMap: (state) => state.arctype.arctypeMap,
      perms: state => state.user.perms
    })
  },
  props: {
    size: {
      type: String,
      default: ''
    },
    highlight: {
      type: Object,
      default() {}
    },
    highlightKeyword: {
      type: Object,
      default() {}
    },
    videoHighlightKeyword: {
      type: Object,
      default() {
        return {}
      }
    },
    title: {
      type: String,
      default: ''
    },
    cover: {
      type: String,
      default: ''
    },
    cover2: {
      type: String,
      default: ''
    },
    eptitle: {
      // 分p标题
      type: String,
      default: ''
    },
    highlightKeywords: {
      type: Array,
      default() {
        return []
      }
    },
    disabled: {
      type: Boolean,
      default: false
    },
    tag: {
      type: String,
      default: ''
    },
    author: {
      type: Object,
      default() {
        return {}
      }
    },
    userWarnings: {
      type: Array,
      default() {
        return []
      }
    },
    content: {
      type: [String, Object, Array]
    }, // 简介
    dynamic: {
      type: [String, Object, Array]
    },
    missionRule: {
      type: Object,
      default() {
        return {}
      }
    },
    copyright: {
      type: [Number, String]
    },
    arctypeV1: {
      type: String
    },
    source: {
      type: String
    },
    ctime: {
      type: Number
    },
    mission: {
      type: Object
    },
    topic: {
      type: Object
    },
    mode: {
      type: String
    },
    aiTagList: {
      type: [Array, String],
      default() {
        return []
      }
    },
    videoCopyright: {
      type: Object,
      default() {
        return {}
      }
    },
    aiMark: {
      type: [String, Number],
      default: ''
    },
    archiveAddit: {
      type: Object
    },
    cid: {
      type: [Number, String],
      default: ''
    },
    playerData: {
      type: Object
    },
    watermark: {
      type: Object
    },
    todo_id: {
      type: Number
    },
    tipList: {
      type: Array
    },
    filename: {
      type: String
    },
    aid: {
      type: [String, Number],
      default: ''
    },
    relatedVideos: {
      type: Array,
      default() {
        return []
      }
    },
    relatedVideoStats: {
      type: Object,
      default: () => {}
    },
    relatedVideoSize: {
      type: Number,
      default: 0
    },
    videoshots: {
      type: Array,
      default() {
        return []
      }
    },
    previewContent: {
      type: Array,
      default() {
        return []
      }
    },
    showToggle: Boolean,
    userStats: {
      type: Object,
      default() {
        return {}
      }
    },
    area: {
      type: String,
      default: ''
    },
    upClickable: {
      type: Boolean,
      default: false
    },
    userSubmitHistory: {
      type: Array,
      default() {
        return []
      }
    },
    ai_img_urls: {
      type: Array,
      default() {
        return []
      }
    },
    showCancelMission: {
      type: Boolean
    },
    cancel_mission: {
      type: Boolean,
      default() {
        return false
      }
    },
    mission_check: {
      type: Object
    },
    toggle_video_src: Boolean, // 是否使用二审播放源
    ai_cover_info: {
      type: Object,
      default() {
        return {}
      }
    },
    showEpViews: Boolean,
    epViews: {
      type: [Number, String],
      default: ''
    },
    allScreenshots: {
      type: Array,
      default: () => []
    },
    enableExternalVideoBlock: {
      type: Boolean,
      default: false
    },
    sourceFileExpired: {
      type: Boolean,
      default: false
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.$emit('detail-mounted')
    })
  },
  methods: {
    updateScreenshot(val) {
      this.$emit('updateScreenshot', val)
    },
    onChangeEptitle(val) {
      this.$emit('changeEptitle', val)
    },
    showBigModal() {
      this.$emit('showBigModal')
    },
    onChangeMission(val) {
      this.$emit('changeMission', val)
    },
    clickClipPreview({ time, total, currentIndex }) {
      this.$tracker(PAGE_TRACK_KEY, {
        loc: TRACK_MAP.VIDEOCLIP,
        cid: this.cid,
        trackType: '点击',
        time,
        total,
        currentIndex
      })
    }
  }
}
</script>
<style lang="stylus" scoped>
.wkb-vd-dt-v2 {
  height: 100%;
  display: flex;
  flex-direction: row;
  background: var(--content-bg-color);
  padding: 16px;

  &-l {
    flex: 4;
  }

  &-r {
    flex: 3;
    margin-left: 12px;
    min-width: 880px;
    .label {
      background: var(--label-bg);
      color: var(--text-color);
      padding: 5px;
      border: 1px solid var(--border-color-light-2);
      // border-left: none;
    }

    .cell {
      padding: 5px;
      border: 1px solid var(--border-color-light-2);
      line-height: 1.5;
    }
  }
}
</style>
