<template>
  <!-- 稿件视频 新人模式 -->
  <TodoTaskDetailStruct>
    <template #nav>
      <div class="nav-submit flex-1 flex-lr">
        <div class="flex-ac">
          <span class="ml-8">/</span>
          <span class="ml-8">cid: {{ video && video.cid }}</span>
          <NavModule
            :hideIncrement="true"
            class="ml-80"
            :archive="{
              aid,
              bvid,
              increment_id
            }"
          />
          <template v-if="archiveState && archiveState.text">
            ：
            <span :style="`color:${archiveState.color};`">
              {{ archiveState.text }}
            </span>
          </template>

          <div style="margin-left: 280px">
            <el-button size="small" type="primary" @click="onSubmit">
              提交
            </el-button>
            <el-button
              size="small"
              @click="
                resetForm(
                  video,
                  initAttr,
                  question,
                  remark,
                  videoAudit,
                  forward
                )
              "
            >
              重置
            </el-button>
            <el-button
              v-if="transList && transList.length > 0"
              size="small"
              type="primary"
              plain
              @click="pageEventBus.$emit('showTransfer')"
            >
              延迟/流转
            </el-button>
            <BusinessLabels
              style="padding-bottom: 5px"
              vertical
              :value="businessLabels"
            />
          </div>
        </div>

        <NavFlow :aid="aid" :cid="video && video.cid" :hideInfo="true" />
      </div>
    </template>
    <Detail
      :title="title"
      :highlight="highlight"
      :highlightKeyword="highlightKeyword"
      :videoHighlightKeyword="videoHighlightKeyword"
      :eptitle="eptitle"
      :desc="desc"
      :cover="cover"
      :cover2="cover2"
      :tag="tag"
      :copyright="copyright"
      :author="author"
      :userWarnings="userWarnings"
      :content="content"
      :dynamic="dynamic"
      :missionRule="missionRule"
      :arctypeV1="arctypeV1"
      :source="source"
      :ctime="ctime"
      :mission="mission"
      :topic="topic"
      :mode="mode"
      :aiTagList="aiTagList"
      :videoCopyright="videoCopyright"
      :aiMark="aiMark"
      :cid="video && video.cid"
      :playerData="playerData"
      :archiveAddit="archiveAddit"
      :todo_id="todoId"
      :tipList="tipList"
      @changeEptitle="onChangeEptitle"
      @changeDesc="onChangeDesc"
      :filename="video && video.filename"
      :aid="aid"
      :relatedVideos="relatedVideos"
      :relatedVideoStats="relatedVideoStats"
      :relatedVideoSize="relatedVideoSize"
      :videoshots="videoshots"
      :previewContent="previewContent"
      :area="area"
      :disabled="true"
      @showBigModal="showBigModal"
      :userStats="userStats"
      :userSubmitHistory="userSubmitHistory"
      @clickArchiveStatsTitle="onClickArchiveStatsTitle"
      :ai_img_urls="ai_img_urls"
      :showCancelMission="form.status === 0"
      :cancel_mission="form.cancel_mission"
      @changeMission="onChangeMission"
      :mission_check="mission_check"
      :toggle_video_src="toggle_video_src"
      :ai_cover_info="ai_cover_info"
      :sourceFileExpired="sourceFileExpired"
    >
      <template #audit-log="{ styles }">
        <el-descriptions-item
          label="操作记录"
          :contentStyle="{
            ...styles.contentStyle,
            padding: 0
          }"
        >
          <div class="operation-log-v2">
            <!-- 1、工作台历史 -->
            <HistoryLog :history="history" class="workbench-log" />
            <!-- 2、视频历史 -->
            <VideoLog
              class="video-log"
              :videoHistory="videoHistory"
            />
            <!-- 3、撞车提示 兼容旧快照 -->
            <CrashWarningsOld
              :crashWarnings="crashWarnings"
              class="crash-warnings"
              v-if="isOldCrash"
            />

            <CrashWarnings
              :crashWarnings="crashWarnings"
              class="crash-warnings"
              :showPassOper="true"
              :showRejectOper="true"
              :cid="video && video.cid"
              v-else
            />
          </div>
        </el-descriptions-item>
      </template>
      <template #audit-operation="{ styles }">
        <el-descriptions-item
          label="审核操作"
          :contentStyle="styles.contentStyle"
        >
          <VideoAuditOperV2
            :aid="aid"
            :cid="video && video.cid"
            :disabled="disabled"
            :form="form"
            :filename="video && video.filename"
            :showForbid="showForbid"
            ref="operNode"
            @change="onChangeForm"
          />
        </el-descriptions-item>
      </template>
    </Detail>
  </TodoTaskDetailStruct>
</template>
<script>
import { mapState, mapActions } from 'vuex'
import TodoTaskDetailStruct from '@/v2/struct/TodoTaskDetailStruct'
import Detail from './Detail.vue'

import BasePage from '@/v2/core/base-page'
import createPageModule from '@/v2/struct/TodoTaskDetailStruct/module'
import {
  BusinessLabels,
  NavModule,
  NavFlow,
  HistoryLog,
  VideoLog,
  VideoAuditOperV2,
  CrashWarnings,
  CrashWarningsOld
} from '@/v2/biz-components/workbench/index'
import { workbenchDetailApi, archiveTaskApi } from '@/api/index'
import { isVideoRecallTodo } from '@/pages/workbench/constants'
import {
  TASK_ATTR_FORM_FIELDS_TEMP,
  VIDEO_AUDIT_RECHECK_CODE_LIST
} from '@/utils/constant'
import auditHotkeysMixin from '@/v2/biz-pages/workbench/video/mixins/audit-hotkeys.js'
import submitRecheckMixin from '@/v2/biz-pages/workbench/video/mixins/submit-recheck.js'
import {
  changeHttpProtocol,
  AUDIT_MAP,
  getNewerData,
  washResource
} from './util'
import { cloneDeep } from 'lodash-es'
import moment from 'moment'
import trackMixin from '@/mixins/track.mixin'
import { isNil } from 'lodash'
import notify from '@/lib/notify'
import { getEnvConstant } from '@/utils/constant.js'
import trackerFunc from '@/utils/report'

export default {
  extends: BasePage,
  components: {
    BusinessLabels,
    TodoTaskDetailStruct,
    Detail,
    NavModule,
    NavFlow,
    VideoAuditOperV2,
    HistoryLog,
    VideoLog,
    CrashWarnings,
    CrashWarningsOld
  },
  mixins: [trackMixin, auditHotkeysMixin, submitRecheckMixin],
  props: {
    pageModule: {
      type: Object,
      default() {
        return createPageModule()
      }
    }
  },
  data() {
    return {
      dirty: false,
      resource: null,

      video: {},
      aid: '',
      bvid: '',
      increment_id: '',

      highlight: {},
      highlightKeyword: {},
      title: '',
      eptitle: '', // 分p标题
      desc: '', // 分p简介
      cover: '',
      cover2: '',
      author: {},
      tag: '',
      videoHighlightKeyword: {}, // 视频敏感词
      disabled: false,
      userWarnings: [],
      content: '', // 简介
      dynamic: '',
      missionRule: {},
      mission: null,
      arctypeV1: '', // UP 对接方
      source: '', // 转载来源
      ctime: null, // 投稿时间
      topic: null, // 话题信息
      aiTagList: [],
      videoCopyright: {},
      aiMark: '',
      crashWarnings: [],
      tipList: [],
      ai_cover_info: {},

      // 播放器
      playerData: {
        playurl: '',
        xcode_state: '',
        mediaInfo: {},
        watermarkState: ''
      },
      archiveAddit: null,
      businessLabels: {},

      videoHistory: [],
      relatedVideos: [],
      relatedVideoStats: {},
      relatedVideoSize: 0,
      allTags: [], // 新版操作项不用
      videoshots: [],
      previewContent: [],
      form: {
        status: null,
        norank: false,
        noindex: false,
        norecommend: false,
        nosearch: false,
        tids: [], // 审核tag
        gray_tags: [], // 灰标
        reason_id: '',
        reason: '',
        reasonTemplate: '',
        note: '',
        forward: '', // 撞车设置
        position_id: '', // 位置
        cancel_mission: false // 取消活动是否勾选
      }, // 审核操作
      initForm: {},
      initAttr: {},
      auditSingle: [],
      annotationOpers: [],
      noteRequired: true,
      copyright: '',
      warningsProtect: {},

      resetFlag: false,
      remark: '', // 备注
      videoAudit: {},
      question: {}, // #
      archiveState: {},
      forward: '', // 用于回源展示
      area: '', // 投稿地区
      second_check: [], // 二次确认
      tagListV2: [], // v2版操作模块 页面上的标签列表

      showBigModalFlag: false, // 标记当前任务或者详情页是否点击过查看大图
      clickArchiveStatsFlag: false, // 标记当前任务或者详情页是否点击过投稿记录的标题

      userStats: {},
      userSubmitHistory: [],
      ai_img_urls: [],
      mission_check: null,
      toggle_video_src: false,
      sourceFileExpired: false,
    }
  },
  computed: {
    ...mapState('pageState', [
      'task',
      'mode',
      'todoId',
      'transList',
      'todoConfig',
      'bizConfig'
    ]),
    ...mapState({
      arctypes: (state) => state.arctype.arctypes,
      perms: (state) => state.user.perms,
      uid: (state) => state.user.uid
    }),
    showCrashSet() {
      // v2操作组件不用
      return (
        this.form &&
        String(this.form.status) === '-4' &&
        String(this.form.reason_id) ===
          String(getEnvConstant('CRASH_REASON_ID'))
      )
    },
    showForbid() {
      return !!this.todoConfig?.showForbid
    },
    // 撞车模块升级后需兼容旧快照
    isOldCrash() {
      if (!this.crashWarnings?.length) return false // 空的用新的
      return this.crashWarnings.find(
        (e) => e.filename_stored || e.filename_upload
      ) // 用字段区分
    },
  },
  watch: {
    showCrashSet: {
      handler(visible) {
        if (!visible) {
          this.form.forward = ''
        }
      }
    },
    'form.status': {
      handler(status) {
        if (status !== 0) {
          this.form.cancel_mission = false
        }
      },
      // immediate: true,
      deep: true
    }
  },
  methods: {
    ...mapActions({
      getArctype: 'arctype/getArctype'
    }),
    hotkeysSubmit() {
      this.onSubmit()
    },
    async afterGetResource(err, resource, res) {
      if (err) {
        console.log(err)
      }
      if (!resource) return
      this.showBigModalFlag = false // 新获取到数据后 初始化
      this.clickArchiveStatsFlag = false

      const { remark = '' } = res?.data || {} // 备注
      this.remark = remark
      this.resource = getNewerData(resource || {}) // 全部用快照中的数据
      this.videoAudit = this.resource.video_audit
      this.history = this.resource.snapshotHistory || {} // 工作台历史（用快照，不用当前任务的）
      const formatData = washResource(this.resource)
      for (const key in formatData) {
        this[key] = formatData[key]
      }
      this.question = (resource || {}).question
      this.resetForm(
        this.video,
        this.initAttr,
        this.question,
        this.remark,
        this.videoAudit,
        this.forward
      ) // tids不赋值 TODO 是否需要上报快照 #
      if (this.video?.cid) {
        // 利用cid获取其他信息
        this.getExtraInfoByCid(this.video.cid)
      }

      // 弹窗： 稿件保护信息提示
      if (this.warningsProtect?.note) {
        // 提示一直在，直到页面销毁前关闭
        const toaster = notify.warning(this.warningsProtect.note, 0)
        this.$once('hook:beforeDestroy', () => {
          toaster.close()
        })
      }
    },
    afterGetOpers(err, opers) {
      if (err) {
        console.log(err)
      }
      let { audit_single: auditSingle = [], annotation: annotationOpers = [] } =
        opers
      auditSingle = auditSingle.map((item) => {
        return {
          ...item,
          name: item.ch_name,
          value: (AUDIT_MAP[item.ch_name] || {}).value
        }
      })
      this.auditSingle = auditSingle || []
      this.annotationOpers = annotationOpers || []
    },
    submit(val) {
      this.pageEventBus.$emit('submit', val)
    },
    /**
     * 用快照中的cid获取的实时数据 #
     */
    getExtraInfoByCid(cid) {
      this.getPlayUrl(cid)
      this.getAiMark(cid, this.aid)
    },
    getAiMark(cid, aid) {
      if (aid && cid) {
        archiveTaskApi
          .getAiMark({
            aid,
            cid
          })
          .then((res) => {
            if (res && res.data && res.data[cid]) {
              this.aiMark = res.data[cid].ai_score
            }
          })
          .catch((_) => {})
      }
    },
    // 获取视频播放地址
    getPlayUrl(cid) {
      workbenchDetailApi
        .getPlayUrl({
          cid
        })
        .then((res) => {
          this.playerData.playurl = changeHttpProtocol(
            res.data && res.data.playurl
          )
          this.toggle_video_src = !!res.data.tips
        })
        .catch((e) => {
          console.error(e)
          this.playerData.playurl = ''
          this.sourceFileExpired = e.code === 21521
        })
    },
    onChangeEptitle(val) {
      this.eptitle = val
    },
    onChangeDesc(val) {
      this.desc = val
    },
    /**
     * 点击查看大图
     * 埋点
     */
    showBigModal() {
      if (!this.showBigModalFlag) {
        // 多次点击只上报一次
        this.trackClick('videoshot_click', 1)
        this.showBigModalFlag = true // 当前任务下点击过 查看大图了
      }
    },
    /**
     * 点击投稿记录的标题
     */
    onClickArchiveStatsTitle() {
      if (!this.clickArchiveStatsFlag) {
        this.trackClick('upload_video_history_click', 1)
        this.clickArchiveStatsFlag = true // 当前任务下点击过 点击过标题了
      }
    },
    trackClick(name, oper) {
      trackerFunc(name, {
        cid: this.cid,
        type: 'newer',
        mode: this.mode,
        uid: this.uid,
        oper
      })
    },
    onChangeForm(val) {
      this.form = val
    },
    // newVersion: 1 v2版本操作项仅需要tids初始值
    getTags(status, newVersion) {
      if (isNil(status)) return
      let group = 0
      if (status === -2 || status === -4) {
        group = 1
      }
      archiveTaskApi
        .getContentClassifyTagList({
          group,
          cid: this.video?.cid,
          aid: this.aid
        })
        .then((res) => {
          const { tags } = res.data
          //   this.form.tids = (result || []).join(',') // 标签 #
          if (!newVersion) {
            this.allTags = tags
          }
        })
        .catch((_) => {})
    },
    resetForm(video, initAttr, question, remark, videoAudit, forward) {
      const {
        norank,
        noindex,
        norecommend,
        nosearch,
        oversea_block,
        push_blog
      } = initAttr
      this.initForm = {
        status:
          this.mode === 'Detail' && question?.submit_result
            ? Number(question.submit_result)
            : null, // #
        reason: video.reason || videoAudit.reason,
        reasonTemplate: '',
        note: remark,
        reason_id: '',
        norank: parseInt(norank, 10),
        noindex: parseInt(noindex, 10),
        norecommend: parseInt(norecommend, 10),
        nosearch: parseInt(nosearch, 10),
        oversea_block: parseInt(oversea_block, 10),
        push_blog: parseInt(push_blog, 10),
        tids: '', // tag接口的result，新人里面不赋值
        gray_tags: [],
        forward,
        position_id: isNil(videoAudit.position_id)
          ? ''
          : videoAudit.position_id,
        cancel_mission: false,
        monitor_tag_id: null,
        monitor_tag_name: ''
      }
      // 改了status，组件里面会发请求，然后异步修改reason和reason_id，这里的赋值失效了

      this.resetFlag = true
      // 初始化form会触发form.status的改变，将自动初始化审核tag

      this.form = cloneDeep(this.initForm)
    },
    // 提交前校验表单
    validateTaskForm(form, noteRequired) {
      if ([0, -2, -4].indexOf(form.status) === -1) {
        return '请选择审核操作'
      }

      // 新版操作项 打回锁定 需选择位置
      if ((form.status === -2 || form.status === -4) && !form.position_id) {
        return '请选择审核标签'
      }

      if (form.status === -2 && form.tids.length === 0) {
        return '请选择打回审核标签'
      }

      if (form.status === -4 && form.tids.length === 0) {
        return '请选择锁定审核标签'
      }

      if (
        (noteRequired && this.validForbidChange(this.initAttr, form)) ||
        form.status === '10000' // 会员可见 需要备注
      ) {
        if (form.note.length === 0) {
          return '禁止属性变动，请补充备注'
        }
      }

      if (this.$refs.operNode) {
        return this.$refs.operNode.validataReason()
      }

      return ''
    },
    // 检查禁止项是否有变化
    validForbidChange(initAttr, form) {
      const forbidAttrChanged =
        TASK_ATTR_FORM_FIELDS_TEMP.find((key) => {
          return form[key] !== initAttr[key]
        }) || false
      return forbidAttrChanged
    },
    async afterSubmit(error, res) {
      if (error) {
        if (parseInt(error.code, 10) === 21121) {
          this.$confirm('视频已删除，是否覆盖？', '', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(() => {
              this.onSubmit(true)
            })
            .catch((_) => {})
        }
        const recheckInfo = VIDEO_AUDIT_RECHECK_CODE_LIST.find(
          (item) => item.code === Number(error.code)
        )

        if (recheckInfo) {
          const valid = await this.validTaskSubmit(recheckInfo.msg)
          if (valid) {
            this.onSubmit(false, true)
          }
        }
      }
    },
    async validTaskSubmit(msg) {
      // 注意只校验复审2提交
      let valid = true
      await this.$msgbox({
        title: '消息',
        message: this.$createElement('p', null, msg),
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).catch(() => {
        // do nothing
        // 取消直接返回
        valid = false
      })
      return valid
    },
    onSubmit(isForce, ignoreCheckRound2) {
      // 提交前 还未点击查看大图 则上报
      if (!this.showBigModalFlag) {
        this.trackClick('videoshot_click', 0)
      }
      if (!this.clickArchiveStatsFlag) {
        this.trackClick('upload_video_history_click', 0)
      }
      const validateResult = this.validateTaskForm(
        cloneDeep(this.form),
        this.noteRequired
      )

      if (validateResult.length !== 0) {
        notify.error(validateResult, 1500)
        return false
      }

      const params = this.getParams(isForce, ignoreCheckRound2)
      this.recheckConfirm(isForce, ignoreCheckRound2, params)
    },
    getResourceResult() {
      const resourceResult = {
        note: this.form.note,
        reject_reason: this.form.reason,
        reason_id: +this.form.reason_id
      }
      if (this.form.status !== -2 && this.form.status !== -4) {
        delete resourceResult.reject_reason
        delete resourceResult.reason_id
      }
      return resourceResult
    },
    getExtraData(isForce, ignoreCheckRound2) {
      const fullPathNameStr = this.form.full_path_names || ''
      // 新人为了兼容历史，只取最后一级
      const leafTagNodeText = fullPathNameStr.split('/').slice(-1).pop()
      const extraData = {
        aid: this.aid,
        cid: this.video.cid,
        tid: '',
        tids: Array.isArray(this.form.tids)
          ? this.form.tids.join(',')
          : this.form.tids,
        gray_tags: (this.form.gray_tags || []).join(','),
        gray_tags_before: (this.$refs.operNode?.gray_tags_before || []).join(
          ','
        ),
        rsc_count: this.video.duration,
        noindex: this.form.noindex,
        norank: this.form.norank,
        norecommend: this.form.norecommend,
        nosearch: this.form.nosearch,
        oversea_block: this.form.oversea_block,
        push_blog: this.form.push_blog,
        arctypeV1: this.arctypeV1,
        // 普通为：  'video_todo_submit' 回查则改为： 'video_recall_submit'
        submit_from: isVideoRecallTodo.includes(this.todoId)
          ? 'video_recall_submit'
          : 'video_todo_submit',
        // 后端【乐文】新增禁用项日志历史字段
        norank_before: this.initForm.norank,
        noindex_before: this.initForm.noindex,
        norecommend_before: this.initForm.norecommend,
        nosearch_before: this.initForm.nosearch,
        push_blog_before: this.initForm.push_blog,
        oversea_block_before: this.initForm.oversea_block,
        disable_tag_text: leafTagNodeText,
        tag_names: this.form.full_path_names,
        // --稿件信息处 v-model---
        eptitle: this.eptitle,
        description: this.desc,
        forward: this.form.forward,
        position_id: this.form.position_id,
        position_text: this.form.position_text,
        cancel_mission: this.form.cancel_mission ? 1 : ''
      }

      if (
        !(
          String(this.form.status) === '-4' &&
          String(this.form.reason_id) ===
            String(getEnvConstant('CRASH_REASON_ID'))
        )
      ) {
        delete extraData.forward
      }
      if (this.video?.mtime) {
        extraData.mtime = moment(this.video.mtime * 1000)
          .utcOffset('+08:00')
          .format('YYYY-MM-DD HH:mm:ss')
      }

      // 强制覆盖状态
      if (isForce === true) {
        extraData.force = 1
      }

      // 强制不走提交结果比对
      if (ignoreCheckRound2) {
        extraData.ignore_check_round2 = true
      }

      // 新人特有 在大提交中 撞车
      extraData.forward = this.form.forward
      return extraData || {}
    },
    getParams(isForce, ignoreCheckRound2) {
      // 获取bindList
      const el =
        this.auditSingle?.find((item) => +item.value === +this.form.status) ||
        {}

      const extraParams = {}
      if (`${this.form.status}` === '0') {
        extraParams.monitor_tag_id = this.form.monitor_tag_id
        extraParams.monitor_tag_name = this.form.monitor_tag_name
      }

      return {
        resource_result: this.getResourceResult(),
        extra_data: this.getExtraData(isForce, ignoreCheckRound2),
        binds: el.bind_id_list || ''
        // ...extraParams
        // ...this.genQaParams() // #
      }
    },
    onChangeMission(val) {
      this.form.cancel_mission = val
    }
  },
  beforeRouteLeave(to, from, next) {
    this.pageEventBus.$emit('beforeRouteLeave', to, from, next)
  },
  created() {
    // 获取分区
    this.getArctype()
    this.pageEventBus.$on('afterGetResource', this.afterGetResource)
    this.pageEventBus.$on('afterGetOpers', this.afterGetOpers)
    this.pageEventBus.$on('afterSubmit', this.afterSubmit)
  },
  beforeDestroy() {
    this.pageEventBus.$off('afterGetResource', this.afterGetResource)
    this.pageEventBus.$off('afterGetOpers', this.afterGetOpers)
    this.pageEventBus.$off('afterSubmit', this.afterSubmit)
  }
}
</script>
<style lang="stylus" scoped>
.nav-submit {
  display: flex;
  flex-direction: row;
  align-items: center;

  >>> .info-row .ids {
    width: auto;
  }
}

.operation-log-v2 {
  display: flex;
  flex-direction: row;

  .workbench-log {
    flex: 1;
    border-right: 1px solid var(--border-color-light-2);
    height: 128px;
    overflow: auto;
    padding: 2px;

    >>> p {
      line-height: 1.5;
      font-size: 13px;
      margin-bottom: 4px;
    }
  }

  .video-log {
    flex: 1;
    border-right: 1px solid var(--border-color-light-2);
    display: flex;
    flex-direction: column;
    overflow: auto;
    height: 128px;
    font-size: 13px;
    padding: 2px;

    >>> li {
      margin-bottom: 4px;
    }
  }

  .crash-warnings {
    flex: 1;
    height: 128px;
    overflow: auto;
    padding: 2px;
  }
}
</style>
