<template>
  <!-- 稿件视频 审核 -->
  <TodoTaskDetailStruct defaultMode="Push" :needUpgradeConfig="true">
    <!-- TODO 二审标志 -->
    <template #nav>
      <div class="nav-submit flex-1 flex-lr">
        <div class="flex-ac">
          <span class="ml-8">/</span>
          <span class="ml-8">cid: {{ video && video.cid }}</span>

          <NavModule
            class="ml-80"
            :hideIncrement="true"
            :archive="{
              aid,
              bvid,
              increment_id
            }"
          />
          <template v-if="archiveState && archiveState.text">
            ：
            <span :style="`color:${archiveState.color};`">
              {{ archiveState.text }}
            </span>
          </template>

          <el-button
            size="small"
            type="primary"
            @click="onSubmit"
            style="margin-left: 280px"
            v-behavior-track="'submit-btn'"
          >
            提交
          </el-button>
          <el-button
            size="small"
            @click="resetForm(video, initAttr, remark, videoAudit, forward)"
            v-behavior-track="'reset-btn'"
          >
            重置
          </el-button>
          <el-button
            v-if="transList && transList.length > 0"
            size="small"
            type="primary"
            plain
            @click="pageEventBus.$emit('showTransfer')"
            v-behavior-track="'task-trans-btn'"
          >
            延迟/流转
          </el-button>
          <el-button
            size="small"
            type="primary"
            @click="submitAndQuit"
            v-behavior-track="'submit-quit-btn'"
            v-if="
              dispatchConf &&
              dispatchConf.dispatch_off_release_time &&
              !dispatchConf.no_dispatch_task
            "
          >
            提交且退出
          </el-button>
          <!-- 标签 -->
          <BusinessLabels
            style="padding-bottom: 5px"
            vertical
            :value="businessLabels"
          />
        </div>
        <div>
          <el-button
            type="info"
            size="small"
            @click="onClickExtract"
            class="mr-10"
          >
            提取信息
          </el-button>
          <NavFlow
            :aid="aid"
            :cid="video && video.cid"
            :filename="video && video.filename"
          />
        </div>
      </div>
    </template>
    <Detail
      :title="title"
      :highlight="highlight"
      :highlightKeyword="highlightKeyword"
      :videoHighlightKeyword="videoHighlightKeyword"
      :eptitle="eptitle"
      :desc="desc"
      :cover="cover"
      :cover2="cover2"
      :tag="tag"
      :copyright="copyright"
      :author="author"
      :userWarnings="userWarnings"
      :content="content"
      :dynamic="dynamic"
      :missionRule="missionRule"
      :arctypeV1="arctypeV1"
      :source="source"
      :ctime="ctime"
      :mission="mission"
      :topic="topic"
      :mode="mode"
      :aiTagList="isRedBlue && aiTagListIsEmpty ? '视频无审核提示' : aiTagList"
      :videoCopyright="videoCopyright"
      :aiMark="aiMark"
      :cid="video && video.cid"
      :playerData="playerData"
      :archiveAddit="archiveAddit"
      :todo_id="todoId"
      :tipList="tipList"
      @changeEptitle="onChangeEptitle"
      @changeDesc="onChangeDesc"
      :filename="video && video.filename"
      :aid="aid"
      :relatedVideos="relatedVideos"
      :relatedVideoStats="relatedVideoStats"
      :relatedVideoSize="relatedVideoSize"
      :videoshots="videoshots"
      :previewContent="previewContent"
      :userStats="userStats"
      class="_track_fmp_ele"
      :area="area"
      :disabled="true"
      @showBigModal="showBigModal"
      :userSubmitHistory="userSubmitHistory"
      @clickArchiveStatsTitle="onClickArchiveStatsTitle"
      :ai_img_urls="ai_img_urls"
      :showCancelMission="form.status === 0"
      :cancel_mission="form.cancel_mission"
      @changeMission="onChangeMission"
      :mission_check="mission_check"
      :toggle_video_src="toggle_video_src"
      :ai_cover_info="ai_cover_info"
      :allScreenshots="allScreenshots"
      @updateScreenshot="updateScreenshot"
      :sourceFileExpired="sourceFileExpired"
      enableExternalVideoBlock
      @detail-mounted="positionVideoBlock"
    >
      <template #audit-log="{ styles }">
        <el-descriptions-item
          label="操作记录"
          :contentStyle="{
            ...styles.contentStyle,
            padding: 0
          }"
        >
          <div class="operation-log-v2">
            <!-- 1、工作台历史 -->
            <HistoryLog :history="history" class="workbench-log" />
            <!-- 2、视频历史，视频抽样回查通道隐藏视频log -->
            <VideoLog
              v-if="!(todoConfig && todoConfig.hideVideoLog)"
              class="video-log"
              :videoHistory="videoHistory"
            />
            <!-- 3、撞车提示 TODO showRejectOper -->
            <CrashWarnings
              :crashWarnings="crashWarnings"
              class="crash-warnings"
              :showPassOper="true"
              :showRejectOper="true"
              :cid="video && video.cid"
              v-track.impression="{
                event: PAGE_TRACK_KEY,
                value: {
                  loc: TRACK_MAP.CRASHWARNING,
                  cid: video && video.cid,
                  trackType: '曝光'
                }
              }"
              @click-crash="onClickCrash"
            />
          </div>
        </el-descriptions-item>
      </template>
      <template #audit-operation="{ styles }">
        <el-descriptions-item
          label="审核操作"
          :contentStyle="styles.contentStyle"
        >
          <template v-if="useTagOper">
            <!-- 待办配置：use_tag_oper -->
            <!-- key为cid，切换资源更新 -->
            <VideoTagOper
              ref="tagOperNode"
              v-if="oper_tags && oper_tags.length"
              :oper_tags="oper_tags"
              :tag_business_id="tag_business_id"
              :resource="resource"
              :disabled="disabled"
              :form="form"
              :allScreenshots="allScreenshots"
              :getExtraData="getExtraData"
              :key="video && video.cid"
              @change="onChangeForm"
            ></VideoTagOper>
          </template>
          <template v-else>
            <VideoOperTip
              v-if="
                !(
                  (todoConfig && todoConfig.isVideoRecallTodo) ||
                  isVideoRecallTodo.includes(todoId) ||
                  isDoubleBlindSecond
                )
              "
              :video="video"
            />
            <VideoAuditOperV2
              :aid="aid"
              :cid="video && video.cid"
              :disabled="disabled"
              :form="form"
              :filename="video && video.filename"
              :showForbid="showForbid"
              :hide_open="hide_open"
              :open0Tag="!!(todoConfig && todoConfig.open0Tag)"
              ref="operNode"
              :showSuggest="true"
              :showScreenshot="true"
              :allScreenshots="allScreenshots"
              :pIndex="-1"
              :useArchiveGrayTags="useArchiveGrayTags"
              @change="onChangeForm"
            />
          </template>
        </el-descriptions-item>
      </template>
    </Detail>

    <ExtraInfoDialog
      :visible.sync="extraInfoVisible"
      :extraInfoList="extraInfoList"
    />
    <template #cache>
      <VideoBlock
        v-show="videoBlockVisible"
        style="margin-top: 12px"
        :style="videoBlockStyle"
        ref="videoBlock"
        :aid="aid"
        :cid="video && video.cid"
        :filename="video && video.filename"
        :fetchSubtitle="overseaPub"
        :playerData="playerData"
        :archiveAddit="archiveAddit"
        :mode="mode"
        :aiTagList="aiTagList"
        :videoCopyright="videoCopyright"
        :aiMark="aiMark"
        :todo_id="todoId"
        :tipList="tipList"
        :toggle_video_src="toggle_video_src"
        :allScreenshots="allScreenshots"
        @updateScreenshot="updateScreenshot"
        :ai_cover_info="ai_cover_info"
        :sourceFileExpired="sourceFileExpired"
        @video-played="onVideoPlayed"
        @video-paused="onVideoPaused"
        @toggle-fullscreen="onVideoToggleFullscreen"
      />
    </template>
  </TodoTaskDetailStruct>
</template>
<script>
import { mapState, mapActions } from 'vuex'
import TodoTaskDetailStruct from '@/v2/struct/TodoTaskDetailStruct'
import Detail from './Detail.vue'
import VideoBlock from './blocks/VideoBlock.vue'

import BasePage from '@/v2/core/base-page'
import createPageModule from '@/v2/struct/TodoTaskDetailStruct/module'
import {
  BusinessLabels,
  NavModule,
  NavFlow,
  VideoAuditOperV2,
  HistoryLog,
  CrashWarnings,
  VideoLog
} from '@/v2/biz-components/workbench/index'
import VideoTagOper from '@/v2/biz-components/workbench/VideoAuditOperV2/VideoTagOper.vue'
import { workbenchDetailApi, archiveTaskApi, videoApi } from '@/api/index'
import { isVideoRecallTodo } from '@/pages/workbench/constants'
import {
  TASK_ATTR_FORM_FIELDS_TEMP,
  VIDEO_AUDIT_RECHECK_CODE_LIST
} from '@/utils/constant'
import auditHotkeysMixin from '@/v2/biz-pages/workbench/video/mixins/audit-hotkeys.js'
import submitRecheckMixin from '@/v2/biz-pages/workbench/video/mixins/submit-recheck.js'
import trackTaskAHTMixin from '@/v2/struct/TodoTaskDetailStruct/trackTaskAHTMixin.js'

import {
  changeHttpProtocol,
  AUDIT_MAP,
  washResource,
  getPromotePos,
  genExtraInfoList,
  getSuggest,
  getPicture
} from './util'
import { cloneDeep } from 'lodash-es'
import moment from 'moment'
import trackMixin from '@/mixins/track.mixin'
import { genHost } from '@/v2/utils'
import { isNil } from 'lodash'
import notify from '@/lib/notify'
import { getEnvConstant } from '@/utils/constant.js'
import trackerFunc from '@/utils/report'
import ExtraInfoDialog from '@/components/ExtraInfoDialog.vue'
import VideoOperTip from '@/v2/biz-components/workbench/VideoOperTip.vue'
import { TRACK_MAP, PAGE_TRACK_KEY } from './blocks/ArcInfo.vue'
import devtools from '@/mixins/devtools'
import { techReportPb } from '@bilibili/bili-mirror'
import { EVENT_NAME_MAP } from '@/utils/track-AHT/report'
import { tagApi } from '@/api'

export default {
  extends: BasePage,
  components: {
    BusinessLabels,
    TodoTaskDetailStruct,
    Detail,
    NavModule,
    NavFlow,
    VideoAuditOperV2,
    HistoryLog,
    VideoLog,
    CrashWarnings,
    ExtraInfoDialog,
    VideoOperTip,
    VideoBlock,
    VideoTagOper
  },
  mixins: [
    trackMixin,
    auditHotkeysMixin,
    submitRecheckMixin,
    devtools,
    trackTaskAHTMixin
  ],
  props: {
    pageModule: {
      type: Object,
      default() {
        return createPageModule()
      }
    }
  },
  data() {
    return {
      dirty: false,
      resource: null,

      video: {},
      aid: '',
      bvid: '',
      increment_id: '',

      highlight: {},
      highlightKeyword: {},
      title: '',
      eptitle: '', // 分p标题
      desc: '', // 分p简介
      cover: '',
      cover2: '',
      author: {},
      tag: '',
      videoHighlightKeyword: {}, // 视频敏感词
      disabled: false,
      userWarnings: [],
      content: '', // 简介
      dynamic: '',
      missionRule: {},
      mission: null,
      arctypeV1: '', // UP 对接方
      source: '', // 转载来源
      ctime: null, // 投稿时间
      topic: null, // 话题信息
      aiTagList: [],
      videoCopyright: {},
      aiMark: '',
      crashWarnings: [],
      tipList: [],
      ai_cover_info: {},
      overseaPub: false, // 是否海外投稿

      // 播放器
      playerData: {
        playurl: '',
        xcode_state: '',
        mediaInfo: {},
        watermarkState: ''
      },
      archiveAddit: null,
      businessLabels: {},

      videoHistory: [],
      relatedVideos: [],
      relatedVideoStats: {},
      relatedVideoSize: 0,
      videoshots: [],
      previewContent: [],
      form: {
        status: null,
        norank: false,
        noindex: false,
        norecommend: false,
        nosearch: false,
        tids: [], // 审核tag
        gray_tags: [], // 灰标
        reason_id: '',
        reason: '',
        reasonTemplate: '',
        note: '',
        forward: '', // 撞车设置
        position_id: '', // 位置
        cancel_mission: false, // 取消活动是否勾选
        suggests: [], // 修改建议数组
        suggest: '', // 修改建议（拼接后的用于回显）
        picture: [], // 截图（用于回显）
        pictureList: [], // 操作选中的截图列表
        archive_gray_tags: [], // 稿件灰标[][]
        tags: [], // 标签化改造后的tag
        tagNames: []
      }, // 审核操作
      initForm: {},
      initAttr: {},
      auditSingle: [],
      annotationOpers: [],
      noteRequired: true,
      copyright: '',
      warningsProtect: {},

      resetFlag: false,
      remark: '', // 备注
      videoAudit: {},
      snapshotData: {}, // 快照
      state: '', // 稿件状态
      hasShowBlindTip: false, // 是否展示过双盲提示
      archiveState: {}, // {color, text} 稿件状态
      userStats: {},
      forward: '', // 用于回源展示
      showCrashSet: false, // v2操作组件不用
      resetReasonIdFlag: false,
      area: '', // 投稿地区
      second_check: [], // 二次确认

      showBigModalFlag: false, // 标记当前任务或者详情页是否点击过查看大图
      clickArchiveStatsFlag: false, // 标记当前任务或者详情页是否点击过投稿记录的标题

      userSubmitHistory: [],
      extraInfoVisible: false, // 提取信息弹窗
      extraInfoList: [],
      ai_img_urls: [],
      mission_check: null,
      hide_open: false, // 隐藏通过操作
      toggle_video_src: false,
      isVideoRecallTodo,
      TRACK_MAP,
      PAGE_TRACK_KEY,
      item_state: undefined, // 待办状态：0待审，1已处理
      allScreenshots: [], // 视频模块的截图列表
      sourceFileExpired: false,
      videoBlockStyle: {},
      videoBlockVisible: false,
      gray_tag: [], // 稿件灰标回显

      oper_tags: [], // 标签化操作
      tag_business_id: undefined
    }
  },
  computed: {
    ...mapState('pageState', [
      'task',
      'mode',
      'todoId',
      'history',
      'isBlind',
      'businessId',
      'bizConfig',
      'itemId',
      'todoConfig',
      'transList',
      'dispatchConf',
      'upgradeConfig'
    ]),
    ...mapState({
      arctypes: (state) => state.arctype.arctypes,
      perms: (state) => state.user.perms,
      uid: (state) => state.user.uid
    }),
    monitorWhitelist() {
      return this.bizConfig?.monitorWhitelist || []
    },
    // 复审2相关待办
    showForbid() {
      return !!this.todoConfig?.showForbid
    },
    isRedBlue() {
      return this.resource?.audit_mode === 2
    },
    // 双盲+非升级待办+未处理
    isDoubleBlindSecond() {
      return (
        this.resource?.audit_mode === 1 &&
        !(
          (this.upgradeConfig?.upgrade_todo_ids || []).indexOf(this.todoId) > -1
        ) &&
        this.item_state !== 1
      )
    },
    aiTagListIsEmpty() {
      return Array.isArray(this.aiTagList)
        ? !this.aiTagList.length
        : !!this.aiTagList
    },
    useArchiveGrayTags() {
      return !!this.todoConfig?.useArchiveGrayTags
    },
    useTagOper() {
      return !!this.todoConfig?.use_tag_oper
    }
  },
  watch: {
    showCrashSet: {
      handler(visible) {
        if (!visible) {
          this.form.forward = ''
        }
      }
    },
    'form.status': {
      handler(status) {
        if (status !== 0) {
          this.form.cancel_mission = false
        } else if (this.mission) {
          const cancelMission = this.mission_check?.conclusion === 2 // 不符合 勾选【取消活动】，符合 & 未知 不勾选【取消活动】
          this.form.cancel_mission = cancelMission
        }
      },
      deep: true
    }
  },
  methods: {
    ...mapActions({
      getArctype: 'arctype/getArctype',
      resetEpSlices: 'reason/resetEpSlices'
    }),
    onVideoPlayed() {
      this.recordTaskAHT(EVENT_NAME_MAP.Play_video)
    },
    onVideoPaused() {
      this.recordTaskAHT(EVENT_NAME_MAP.Pause_video)
    },
    onVideoToggleFullscreen({ isFullScreen, isWebFull }) {
      this.recordTaskAHT(
        isFullScreen || isWebFull
          ? EVENT_NAME_MAP.Open_video_player
          : EVENT_NAME_MAP.Close_video_player
      )
    },
    hotkeysSubmit() {
      this.onSubmit()
    },
    positionVideoBlock() {
      this.$nextTick(() => {
        this.videoBlockVisible = true
        const dummyVideoBlock = document.querySelector(
          '#video-block-placeholder'
        )
        if (dummyVideoBlock) {
          const { top: rawTop, width } = dummyVideoBlock.getBoundingClientRect()
          const minTop = 475
          const top = rawTop > minTop ? rawTop : minTop
          this.videoBlockStyle = {
            top: `${top}px`,
            left: '16px',
            width: `${width}px`,
            position: 'absolute'
          }
        }
      })
    },
    async afterGetResource(err, resource, res) {
      this.allScreenshots = []
      if (err) {
        console.log(err)
      }
      if (!resource) return
      this.resetEpSlices()
      this.showBigModalFlag = false // 新获取到数据后 初始化
      this.clickArchiveStatsFlag = false

      const { remark = '', item_state } = res?.data || {} // 备注
      this.item_state = item_state // 任务模式为undefined，资源模式为0或1
      this.remark = remark
      this.resource = resource || {}
      this.videoAudit = this.resource.video_audit || {}
      const formatData = washResource(this.resource)
      for (const key in formatData) {
        this[key] = formatData[key]
      }
      this.resetForm(
        this.video,
        this.initAttr,
        this.remark,
        this.videoAudit,
        this.forward
      )
      // 利用cid获取其他信息
      if (this.video?.cid) {
        this.getExtraInfoByCid(this.video.cid)
      }
      this.snapshotData = {
        ...cloneDeep(resource || {}),
        topic: this.topic
      }

      // TODO
      // 三、判断当前是不是复审REVIEW
      // let isReview = false
      // let reviewInit = false
      // if (+taskResult.round === 1) {
      //   isReview = true
      //   reviewInit = false
      // }

      // 弹窗： 稿件保护信息提示
      if (this.warningsProtect?.note) {
        // 提示一直在，直到页面销毁前关闭
        const toaster = notify.warning(this.warningsProtect.note, 0)
        this.$once('hook:beforeDestroy', () => {
          toaster.close()
        })
      }
      this.hasShowBlindTip = false
    },
    afterGetOpers(err, opers) {
      if (err) {
        console.log(err)
      }
      let { audit_single: auditSingle = [], annotation: annotationOpers = [] } =
        opers
      auditSingle = auditSingle.map((item) => {
        return {
          ...item,
          name: item.ch_name,
          value: (AUDIT_MAP[item.ch_name] || {}).value
        }
      })
      this.auditSingle = auditSingle || []
      this.annotationOpers = annotationOpers || []
      this.oper_tags = opers?.oper_tags?.tag_list || []
      this.tag_business_id = opers?.oper_tags?.tag_business_id
    },
    submit(params, submitAndQuit = false) {
      let autoGetNext, autoQuit
      if (submitAndQuit) {
        autoGetNext = false
        autoQuit = true
      } else {
        autoGetNext = true
        autoQuit = false
      }

      // 提交cid不匹配的场景，在给到pageEventBus之前拦截
      try {
        if (
          params?.extra_data?.cid &&
          params.extra_data.cid !== this.video?.cid
        ) {
          techReportPb({
            type: 'error',
            eventId: 'main.aegis.ERROR.videoSubmitCidError',
            msg: {
              aid: this.aid,
              cid: this.video?.cid,
              extra_data_cid: params.extra_data.cid,
              uid: this.uid,
              task: this.task,
              itemId: this.itemId
            }
          })
          notify.error('提交错误，请刷新页面重试')
          return
        }
      } catch (err) {
        console.error(err)
      }

      this.pageEventBus.$emit('submit', params, autoGetNext, autoQuit)
    },
    /**
     * 用快照中的cid获取的实时数据 #
     */
    getExtraInfoByCid(cid) {
      this.getPlayUrl(cid)
      this.getAiMark(cid, this.aid)
    },
    getAiMark(cid, aid) {
      if (aid && cid) {
        archiveTaskApi
          .getAiMark({
            aid,
            cid
          })
          .then((res) => {
            if (res && res.data && res.data[cid]) {
              this.aiMark = res.data[cid].ai_score
            }
          })
          .catch((_) => {})
      }
    },
    // 获取视频播放地址
    getPlayUrl(cid) {
      workbenchDetailApi
        .getPlayUrl({
          cid
        })
        .then((res) => {
          this.playerData.playurl = changeHttpProtocol(
            res.data && res.data.playurl
          )
          this.toggle_video_src = !!res.data.tips
        })
        .catch((e) => {
          console.error(e)
          this.playerData.playurl = ''
          this.sourceFileExpired = e.code === 21521
        })
    },
    onChangeEptitle(val) {
      this.eptitle = val
    },
    onChangeDesc(val) {
      this.desc = val
    },
    /**
     * 点击查看大图
     * 埋点
     */
    showBigModal() {
      if (!this.showBigModalFlag) {
        // 多次点击只上报一次
        this.trackClick('videoshot_click', 1)
        this.showBigModalFlag = true // 当前任务下点击过 查看大图了
      }
    },
    /**
     * 点击投稿记录的标题
     */
    onClickArchiveStatsTitle() {
      if (!this.clickArchiveStatsFlag) {
        this.trackClick('upload_video_history_click', 1)
        this.clickArchiveStatsFlag = true // 当前任务下点击过 点击过标题了
      }
    },
    trackClick(name, oper) {
      trackerFunc(name, {
        cid: this.video?.cid,
        type: 'audit',
        mode: this.mode,
        uid: this.uid,
        oper
      })
    },
    onChangeForm(val) {
      this.form = val
    },
    resetForm(video, initAttr, remark, videoAudit, forward) {
      const {
        norank,
        noindex,
        norecommend,
        nosearch,
        oversea_block,
        push_blog
      } = initAttr
      // 不显示初始操作状态的场景（或）：
      // 1. 待办配置了hideDefaultState
      // 2. 是双盲且不是升级待办
      const hideDefaultState =
        !!this.todoConfig?.hideDefaultState || this.isDoubleBlindSecond
      this.initForm = {
        status: hideDefaultState ? null : video.status,
        reason: video.reason || videoAudit?.reason,
        reasonTemplate: '',
        note: this.isDoubleBlindSecond ? '' : remark,
        reason_id: '', // TODO 新版初值（服务端暂不支持）
        norank: parseInt(norank, 10),
        noindex: parseInt(noindex, 10),
        norecommend: parseInt(norecommend, 10),
        nosearch: parseInt(nosearch, 10),
        oversea_block: parseInt(oversea_block, 10),
        push_blog: parseInt(push_blog, 10),
        tids: this.$refs.operNode?.getAuditTags(video.status),
        forward: hideDefaultState ? '' : forward,
        position_id: isNil(videoAudit?.position_id)
          ? ''
          : videoAudit.position_id,
        cancel_mission: false,
        suggest: videoAudit?.suggest || '',
        picture: videoAudit?.picture || [],
        monitor_tag_id: null, // 没有初始值
        monitor_tag_name: '',
        gray_tags: this.$refs.operNode?.gray_tags_before || [],
        archive_gray_tags: this.gray_tag?.map((e) => e.full_ids) || [],
        oper_tag_info: {}, // 不回显
        tags: this.$refs.tagOperNode?.originalTags || [] // 标签化改造后的tag
      }
      // 改了status，组件里面会发请求，然后异步修改reason和reason_id，这里的赋值失效了

      this.resetFlag = true
      // 初始化form会触发form.status的改变，将自动初始化审核tag

      // 重置&初始化时，依据forward判断是否显示撞车设置模块
      if (forward) {
        this.showCrashSet = true
      }
      this.resetReasonIdFlag = true

      this.form = cloneDeep(this.initForm)
    },
    // 提交前校验表单
    validateTaskForm(form, noteRequired) {
      if ([0, -2, -4].indexOf(form.status) === -1) {
        return '请选择审核操作'
      }

      // 新版操作项 打回锁定 需选择位置
      if ((form.status === -2 || form.status === -4) && !form.position_id) {
        return '请选择审核标签'
      }

      if (form.status === -2 && form.tids.length === 0) {
        return '请选择打回审核标签'
      }

      if (form.status === -4 && form.tids.length === 0) {
        return '请选择锁定审核标签'
      }

      if (
        (noteRequired && this.validForbidChange(this.initAttr, form)) ||
        form.status === '10000' // 会员可见 需要备注
      ) {
        if (form.note.length === 0) {
          return '禁止属性变动，请补充备注'
        }
      }

      if (
        form.status === -4 &&
        form.reason_id === getEnvConstant('CRASH_REASON_ID')
      ) {
        // 撞车目标必填 且 必须是字母和数字
        if (!form.forward?.trim()) {
          return '请输入撞车跳转目标'
        }

        const re = /^[0-9a-zA-Z]*$/
        if (!re.test(form.forward?.trim())) {
          return '撞车跳转目标格式不正确'
        }
      }

      if (this.$refs.operNode) {
        return this.$refs.operNode.validataReason()
      }

      return ''
    },
    validateTagDisabled() {
      try {
        // FIXME: 暂时只校验通过状态，针对0号标
        if (this.form.status === 0 && this.video.status === 0) {
          const oldTids = this.$refs.operNode.getAuditTags(0) || []
          // 初始值中有没权限的标 => 保持选中
          const oldDisabledTags = oldTids.filter(
            (e) =>
              this.$refs.operNode?.getTagDisable(e) &&
              !this.form.tids.includes(e)
          )
          if (oldDisabledTags.length) {
            techReportPb({
              type: 'error',
              eventId: 'main.aegis.ERROR.videoSubmitErrorTids',
              msg: {
                aid: this.aid,
                cid: this.video?.cid,
                tids: this.form.tids,
                uid: this.uid,
                task: this.task,
                itemId: this.itemId,
                initFormTid: this.initForm.tids,
                oldTids
              }
            })
            this.form.tids = this.form.tids.concat(oldDisabledTags)
          }
          // 无权限&初始值中没有 => 去掉
          // this.form.tids = this.form.tids.filter(e => !(this.$refs.operNode?.getTagDisable(e) && !oldTids.includes(e)))
        }
      } catch (err) {
        console.error(err)
      }
    },
    // 检查禁止项是否有变化
    validForbidChange(initAttr, form) {
      const forbidAttrChanged =
        TASK_ATTR_FORM_FIELDS_TEMP.find((key) => {
          return form[key] !== initAttr[key]
        }) || false
      return forbidAttrChanged
    },
    async afterSubmit(error, res) {
      if (error) {
        if (parseInt(error.code, 10) === 21121) {
          this.$confirm('视频已删除，是否覆盖？', '', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(() => {
              this.onSubmit(true)
            })
            .catch((_) => {})
        }
        const recheckInfo = VIDEO_AUDIT_RECHECK_CODE_LIST.find(
          (item) => item.code === Number(error.code)
        )

        if (recheckInfo) {
          const valid = await this.validTaskSubmit(recheckInfo.msg)
          if (valid) {
            this.onSubmit(false, true)
          }
        }
      } else {
        this.$refs?.videoBlock?.pause()
        this.$refs?.videoBlock?.clearFrameList()
        this.videoBlockVisible = false
        this.$refs?.videoBlock?.disableShortcut()
      }
    },
    async validTaskSubmit(msg) {
      // 注意只校验复审2提交
      let valid = true
      await this.$msgbox({
        title: '消息',
        message: this.$createElement('p', null, msg),
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).catch(() => {
        // do nothing
        // 取消直接返回
        valid = false
      })
      return valid
    },
    async submitAndQuit() {
      await this.$confirm(
        `<div class="flex-as">
          <div class="el-icon-warning" style="font-size:24px;color:var(--warning-color);margin-right:8px;"></div>
          <div>
            <div style="line-height:24px;">请注意！！</div>
            <div style="color:var(--error-color)">「提交且退出」后将退出通道，将不再分配任务</div>
          </div>
        </div>`,
        '',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确认退出',
          cancelButtonText: '取消'
        }
      )
      this.$tracker(PAGE_TRACK_KEY, {
        loc: TRACK_MAP.SUBMITANDQUIT,
        cid: this.video?.cid,
        trackType: '点击'
      })
      await this.onSubmit(false, false, true)
    },
    async onSubmit(isForce, ignoreCheckRound2, submitAndQuit) {
      // 提交前 还未点击查看大图 则上报
      if (!this.showBigModalFlag) {
        this.trackClick('videoshot_click', 0)
      }
      if (!this.clickArchiveStatsFlag) {
        this.trackClick('upload_video_history_click', 0)
      }

      if (
        !this.hasShowBlindTip &&
        this.isBlind &&
        String(this.state) === '-4'
      ) {
        try {
          await this.$confirm(
            ' 当前稿件已锁定，如需改为开放/打回需先解锁稿件',
            '',
            {
              confirmButtonText: '确认前往',
              cancelButtonText: '取消',
              type: 'warning'
            }
          )

          const url = `${genHost()}/aegis/#/audit/tasks/detail/11?business_id=11&oid=${
            this.aid
          }&list_type=00`
          window.open(url, '_blank')
          this.hasShowBlindTip = true
        } catch (error) {
          this.hasShowBlindTip = true
        }
        return
      }

      if (this.useTagOper) {
        if (!this.form.tags?.length) {
          notify.error('请选择标签')
          return false
        }
        if (!this.form.oper_tag_info) {
          notify.error('请选择标签并预结算')
          return false
        }
        // 标签化
        const isValid = this.$refs.tagOperNode?.validatePeekTags()
        if (!isValid) {
          notify.error('请重新预结算')
          return false
        }
        if (![0, -2, -4].includes(this.form.oper_tag_info.rsc_state)) {
          notify.error('结算状态出错了')
          return false
        }
        const validateResult = this.$refs.tagOperNode?.validateReason()
        if (validateResult?.length !== 0) {
          notify.error(validateResult, 3000)
          return false
        }
      } else {
        const validateResult = this.validateTaskForm(
          cloneDeep(this.form),
          this.noteRequired
        )

        if (validateResult.length !== 0) {
          notify.error(validateResult, 1500)
          return false
        }

        // 校验disabled的标tids有没有变化
        this.validateTagDisabled()

        // 校验限流理由&截图
        this.$refs.operNode?.validateReason()
      }

      const params = this.getParams(isForce, ignoreCheckRound2)
      const isBusinessOrder = this.businessLabels?.adorder
      this.recheckConfirm(
        isForce,
        ignoreCheckRound2,
        params,
        submitAndQuit,
        isBusinessOrder
      )
    },
    getResourceResult() {
      let resourceResult
      if (this.useTagOper) {
        const tagMap = this.$refs.tagOperNode?.tagsMap || {}
        const full_tag_names = []
        this.form.tags?.forEach((e) => {
          const arr = []
          e.forEach((ee) => {
            arr.push(tagMap[ee].name)
          })
          full_tag_names.push(arr)
        })
        resourceResult = {
          note: this.form.note,
          full_tag_ids: this.form.tags || [],
          full_tag_names
        }
      } else {
        resourceResult = {
          note: this.form.note,
          reject_reason: this.form.reason,
          reason_id: +this.form.reason_id,
          full_tag_ids: this.$refs.operNode?.resultV3?.length
            ? [this.$refs.operNode?.resultV3]
            : [],
          full_tag_names: this.form.full_path_names?.split('/')?.slice(1)
            ?.length
            ? [this.form.full_path_names.split('/').slice(1)]
            : []
        }
        if (this.form.status !== -2 && this.form.status !== -4) {
          delete resourceResult.reject_reason
          delete resourceResult.reason_id
        }
      }
      return resourceResult
    },
    getExtraData(isForce, ignoreCheckRound2) {
      let extraData
      if (this.useTagOper) {
        const tagMap = this.$refs.tagOperNode?.tagsMap || {}
        const full_tag_names = []
        this.form.tags?.forEach((e) => {
          const arr = []
          e.forEach((ee) => {
            arr.push(tagMap[ee])
          })
          full_tag_names.push(arr)
        })
        extraData = {
          aid: this.aid,
          cid: this.video.cid,
          rsc_count: this.video.duration,
          noindex: this.form.noindex,
          norank: this.form.norank,
          norecommend: this.form.norecommend,
          nosearch: this.form.nosearch,
          oversea_block: this.form.oversea_block,
          push_blog: this.form.push_blog,
          norank_before: this.initForm.norank,
          noindex_before: this.initForm.noindex,
          norecommend_before: this.initForm.norecommend,
          nosearch_before: this.initForm.nosearch,
          push_blog_before: this.initForm.push_blog,
          oversea_block_before: this.initForm.oversea_block,
          submit_from:
            isVideoRecallTodo.includes(this.todoId) ||
            this.todoConfig?.isVideoRecallTodo
              ? 'video_recall_submit'
              : 'video_todo_submit',
          eptitle: this.eptitle,
          description: this.desc,
          cancel_mission:
            this.form.cancel_mission && this.form.status === 0 ? 1 : ''
        }
      } else {
        let gray_tags = ''
        let gray_tags_before = ''
        if (this.useArchiveGrayTags) {
          gray_tags =
            this.form.archive_gray_tags
              ?.map((e) => e.at(-1))
              ?.filter((e) => !!e)
              ?.join(',') || ''
          gray_tags_before =
            this.gray_tag?.map((e) => e.tag_id)?.join(',') || ''
        } else {
          gray_tags = this.form.gray_tags?.join(',') || ''
          gray_tags_before = (this.$refs.operNode?.gray_tags_before || []).join(
            ','
          )
        }
        extraData = {
          aid: this.aid,
          cid: this.video.cid,
          tid: '',
          tids: Array.isArray(this.form.tids)
            ? this.form.tids.join(',')
            : this.form.tids,
          gray_tags,
          gray_tags_before,
          rsc_count: this.video.duration,
          noindex: this.form.noindex,
          norank: this.form.norank,
          norecommend: this.form.norecommend,
          nosearch: this.form.nosearch,
          oversea_block: this.form.oversea_block,
          push_blog: this.form.push_blog,
          // 普通为：  'video_todo_submit' 回查则改为： 'video_recall_submit'
          submit_from:
            isVideoRecallTodo.includes(this.todoId) ||
            this.todoConfig?.isVideoRecallTodo
              ? 'video_recall_submit'
              : 'video_todo_submit',
          // 后端【乐文】新增禁用项日志历史字段
          norank_before: this.initForm.norank,
          noindex_before: this.initForm.noindex,
          norecommend_before: this.initForm.norecommend,
          nosearch_before: this.initForm.nosearch,
          push_blog_before: this.initForm.push_blog,
          oversea_block_before: this.initForm.oversea_block,
          disable_tag_text: this.form.full_path_names,
          tag_names: this.form.full_path_names,
          is_tag61: true,
          // --稿件信息处 v-model---
          eptitle: this.eptitle,
          description: this.desc,
          forward: this.form.forward?.trim(),
          position_id: this.form.position_id,
          position_text: this.form.position_text,
          cancel_mission:
            this.form.cancel_mission && this.form.status === 0 ? 1 : '',
          suggest: this.form.status === 0 ? '' : getSuggest(this.form), // 通过没有修改建议
          picture:
            this.form.status === 0
              ? ''
              : getPicture({
                  pictureList: this.form.pictureList,
                  picture: this.form.picture,
                  allScreenshots: this.allScreenshots
                }), // 通过没有截图
          gray_tag_reason: JSON.stringify(this.$refs.operNode?.grayTagReason || [])
        }
        if (
          !(
            String(this.form.status) === '-4' &&
            String(this.form.reason_id) ===
              String(getEnvConstant('CRASH_REASON_ID'))
          )
        ) {
          delete extraData.forward
        }
      }

      if (`${this.form.status}` === '0') {
        extraData.monitor_tag_id = this.form.monitor_tag_id
        extraData.monitor_tag_name = this.form.monitor_tag_name
      }

      if (this.video?.mtime) {
        extraData.mtime = moment(this.video.mtime * 1000)
          .utcOffset('+08:00')
          .format('YYYY-MM-DD HH:mm:ss')
      }

      // 强制覆盖状态
      if (isForce === true) {
        extraData.force = 1
      }

      // 强制不走提交结果比对
      if (ignoreCheckRound2) {
        extraData.ignore_check_round2 = true
      }

      return extraData || {}
    },
    getParams(isForce, ignoreCheckRound2) {
      let el
      // 获取bindList
      if (this.useTagOper) {
        el =
          this.auditSingle?.find(
            (item) => +item.value === this.form.oper_tag_info?.rsc_state
          ) || {}
      } else {
        el =
          this.auditSingle?.find((item) => +item.value === +this.form.status) ||
          {}
      }

      return {
        resource_result: this.getResourceResult(),
        extra_data: this.getExtraData(isForce, ignoreCheckRound2),
        binds: el.bind_id_list || '',
        oper_tag_info: this.useTagOper
          ? this.getOperTagInfo(this.form.oper_tag_info)
          : null,
        ...this.genQaParams()
      }
    },
    // 标签化提交
    getOperTagInfo(_oper_tag_info) {
      const new_oper_tag_info = cloneDeep(_oper_tag_info)
      new_oper_tag_info?.incremental_tag_rules?.forEach((e) => {
        const position_tag_id = this.form.tags.find(
          (t) => t.at(-1) === e.tag_id
        )?.[0] // 这个标签对应的位置
        const position_text = this.oper_tags.find(
          (e) => e.id === position_tag_id
        )?.name
        const position_id = this.oper_tags.find((e) => e.id === position_tag_id)
          ?.extra_data?.position_id
        // 1. 补齐修改建议
        e.suggest =
          _oper_tag_info.rsc_state === 0
            ? ''
            : getSuggest({
                suggest: e.suggest,
                position_text,
                suggests: e.suggests
              }) // 通过没有修改建议
        delete e.suggests
        // 2.补齐position_id
        e.position_id = position_id
        // 3. 补齐截图
        e.picture =
          this.form.status === 0
            ? ''
            : getPicture({
                pictureList: e.pictureList,
                picture: e.picture,
                allScreenshots: this.allScreenshots
              })
        delete e.pictureList
        // 4.补齐tag_name
        if (this.$refs.tagOperNode) {
          const tagsMap = this.$refs.tagOperNode.tagsMap || {}
          e.tag_name = tagsMap[e.tag_id]?.full_name
        }
      })

      return new_oper_tag_info
    },
    genQaParams() {
      if (+this.todoId === 180021) return {}
      return {
        qa_snapshot_data: {
          oid: this.video?.cid, // cid
          mid: this.author?.mid, // 稿件发布人up主
          content: this.title, // 稿件标题
          extra1s: String(this.aid), // 稿件aid
          extra2s: this.video?.filename, // filename
          extra4s: this.arctypeV1
        },
        qa_snapshot_detail: JSON.stringify({
          ...this.snapshotData,
          snapshotHistory: this.history,
          // ---发请求拿到的数据都上报---
          aiMark: this.aiMark,
          crashWarnings: this.crashWarnings,
          tipList: this.tipList,
          aiTagList: this.aiTagList,
          taskForm: cloneDeep(this.form),
          playerData: this.playerData,
          // 2.1新增
          eptitle: this.eptitle,
          desc: this.desc,
          // --- 2.3新增 ---
          videoAduitOperVersion: 1, // 操作模块版本
          showForbid: this.showForbid, // 是否展示禁止项
          forward_bvid: this.showCrashSet ? this.forward_bvid : '', // 撞车目标 v2操作组件不用

          version: '2.3'
        })
      }
    },
    syncShowCrashSet() {
      this.showCrashSet =
        this.form &&
        String(this.form.status) === '-4' &&
        String(this.form.reason_id) ===
          String(getEnvConstant('CRASH_REASON_ID'))
    },
    onClickExtract() {
      // 发请求获取web推广位信息
      // getPromotePos
      // genExtraInfoList
      videoApi
        .getPromotePosInfo({
          aid: this.aid
        })
        .then((res) => {
          const promotePos = getPromotePos(res.data)
          this.extraInfoList = genExtraInfoList({
            bvid: this.bvid,
            title: this.title,
            arctypeV1: this.arctypeV1,
            author: this.author,
            promotePos
          })
          this.extraInfoVisible = true
        })
    },
    onChangeMission(val) {
      this.form.cancel_mission = val
    },
    updateScreenshot(val) {
      this.allScreenshots = val
    },
    onClickCrash() {
      this.$tracker(PAGE_TRACK_KEY, {
        loc: TRACK_MAP.CRASHWARNING,
        cid: this.video?.cid,
        trackType: '点击'
      })
    },
    trackQuitClick() {
      this.$tracker(PAGE_TRACK_KEY, {
        loc: TRACK_MAP.QUIT,
        cid: this.video?.cid,
        trackType: '点击'
      })
    }
  },
  beforeRouteLeave(to, from, next) {
    this.pageEventBus.$emit('beforeRouteLeave', to, from, next)
  },
  created() {
    // 获取分区
    this.getArctype()
    this.pageEventBus.$on('afterGetResource', this.afterGetResource)
    this.pageEventBus.$on('afterGetOpers', this.afterGetOpers)
    this.pageEventBus.$on('afterSubmit', this.afterSubmit)
    this.pageEventBus.$on('trackQuitClick', this.trackQuitClick)
  },
  beforeDestroy() {
    this.pageEventBus.$off('afterGetResource', this.afterGetResource)
    this.pageEventBus.$off('afterGetOpers', this.afterGetOpers)
    this.pageEventBus.$off('afterSubmit', this.afterSubmit)
    this.pageEventBus.$off('trackQuitClick', this.trackQuitClick)
  }
}
</script>
<style lang="stylus" scoped>
.nav-submit {
  display: flex;
  flex-direction: row;
  align-items: center;

  >>> .info-row .ids {
    width: auto;
  }
}

.operation-log-v2 {
  display: flex;
  flex-direction: row;

  .workbench-log {
    flex: 1;
    border-right: 1px solid var(--border-color-light-2);
    height: 128px;
    overflow: auto;
    padding: 2px;

    >>> p {
      line-height: 1.5;
      font-size: 13px;
      margin-bottom: 4px;
    }
  }

  .video-log {
    flex: 1;
    border-right: 1px solid var(--border-color-light-2);
    display: flex;
    flex-direction: column;
    overflow: auto;
    height: 128px;
    font-size: 13px;
    padding: 2px;

    >>> li {
      margin-bottom: 4px;
    }
  }

  .crash-warnings {
    flex: 1;
    height: 128px;
    overflow: auto;
    padding: 2px;
  }
}
</style>
