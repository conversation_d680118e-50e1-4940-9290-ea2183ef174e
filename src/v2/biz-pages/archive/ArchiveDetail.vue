<template>
  <div class="bff-detail">
    <el-row type="flex" justify="space-between" class="base-row">
      <div class="base-div" style="flex: 1">
        <StickyNav
          :aid="archive.aid"
          :bvid="archive.bvid"
          :isAdvanced="isAdvanced"
          :listType="listType"
          :cardInfo="stickyNav.cardInfo"
          :adorderType="adorderType"
          :businessLabels="stickyNav.businessLabels"
          :incrementId="stickyNav.incrementId"
          :showLimit="showLimit"
          :manualLimit="manualLimit"
          :disableOper="!dataReady"
          :isSponsored="archive.isSponsored"
          :showReport="perms.AEGIS_APPLY_WORK_ARCHIVE"
          :classArchive="
            stickyNav.businessLabels && stickyNav.businessLabels.pugvpay
          "
          @show-report-dialog="reportDialogVisible = true"
          @toggle-view="handleToggleView"
          @submit="submitData"
          @reset="toResetData"
          @back="toGoBack"
          @limit="handleShowLimitDialog"
          @show-extra-info="extraInfoVisible = true"
        />
      </div>
    </el-row>
    <!-- 主要内容 -->
    <template v-if="dataReady">
      <!-- 稿件详情 -->
      <template v-if="!isAdvanced">
        <ArchiveError v-if="showErrorPage" @back="toGoBack" />
        <template v-else>
          <div class="grid-wrap _track_fmp_ele">
            <div class="grid" style="grid-template-columns: 1fr 1fr">
              <div class="grid-box">
                <ArchiveHistory :logs="history.logs"></ArchiveHistory>
              </div>
              <div class="grid-box">
                <SubmitLog
                  :userSubmitHistory="submitLog.userSubmitHistory"
                  :userStats="submitLog.userStats"
                  :mid="archive.mid"
                />
              </div>
            </div>
            <div class="grid" style="grid-template-columns: 1fr 1fr">
              <div class="grid-box">
                <!-- 注意updateValueByKey为必需 -->
                <div
                  style="
                    background: var(--content-bg-color);
                    height: 100%;
                    padding: 10px;
                    box-sizing: border-box;
                  "
                >
                  <AuditOperation
                    ref="auditOperation"
                    v-bind.sync="operation"
                    :actionList="actionList"
                    :aid="archive.aid"
                    :bvid="archive.bvid"
                    :disabled="false"
                    :noindex.sync="attrForm.noindex"
                    :norank.sync="attrForm.norank"
                    :hot_down.sync="attrForm.hot_down"
                    :rank_down.sync="attrForm.rank_down"
                    :norecommend.sync="attrForm.norecommend"
                    :nohot.sync="attrForm.nohot"
                    :nosearch.sync="attrForm.nosearch"
                    :push_blog.sync="attrForm.push_blog"
                    :flowName="archive.flowName"
                    :listType="listType"
                    :listReview="listReview"
                    :noAuth="!perms.MOD_S_FLOW_READ && !perms.MOD_S_FLOW_WRITE"
                    :noReadAuth="!perms.MOD_S_FLOW_WRITE"
                    :enableSixLimit="!!attrForm.only_self"
                    :initArchiveState="archive.state"
                    :showMoreReasonBtn="true"
                  />
                  <!-- 不能更改archive -->
                  <ArchiveInfo
                    v-bind="archive"
                    :disableSaveTag="disableSaveTag"
                    :tagOptions="tagOptions"
                    :listReview="listReview"
                    :disabled="true"
                    @updateNoReprint="handleUpdateNoReprint"
                  ></ArchiveInfo>
                </div>
              </div>
              <div class="grid-box">
                <!-- 注意updateValueByKey为必需 -->
                <div
                  style="
                    background: var(--content-bg-color);
                    height: 100%;
                    padding: 10px;
                    box-sizing: border-box;
                  "
                >
                  <Warnings :warnings="warnings"></Warnings>
                  <div
                    style="display: flex; flex-wrap: wrap; margin-bottom: 5px"
                  >
                    <MissionCard
                      :id="mission.id"
                      :name="mission.name"
                      :cancelMission.sync="mission.cancelMission"
                      :isBusinessMission="mission.isBusinessMission"
                    />
                  </div>
                  <TopicCard v-bind.sync="topic" />
                  <!--  -->
                  <BusinessOrder
                    v-bind.sync="business"
                    :aid="archive.aid"
                    :publishTime="archive.publishTime"
                    :listType="listType"
                  />
                  <ArchiveCover
                    :bvid="archive.bvid"
                    :listType="listType"
                    :fullCover="archiveCover.fullCover"
                    :fullCoverV2="archiveCover.fullCoverV2"
                    :highlightCover="archiveCover.highlightCover"
                    :coverAiTags="archiveCover.coverAiTags"
                    :coverAiTagsV2="archiveCover.coverAiTagsV2"
                    :disabled="true"
                  />
                  <ArchiveSource
                    :arctypeV2="archive.arctypeV2"
                    :upFrom="archive.upFrom"
                    :material="archive.materialTemplate"
                  />
                  <!-- 仅回查展示 -->
                  <VideosCompetitor
                    v-if="listReview >= 0"
                    :videosCompetitor="paginatedVideosCompetitor"
                  ></VideosCompetitor>
                  <el-tabs v-if="showNoticeTabs" v-model="aiTagTab" type="card">
                    <el-tab-pane
                      v-if="hasCommunityAiSummary"
                      label="社区机审"
                      name="community"
                    >
                      <CommunityAiSummary
                        :communityAiSummary="communityAiSummary"
                        :visibleClipList="visibleClipList"
                      />
                    </el-tab-pane>
                    <el-tab-pane
                      v-if="showCopyright"
                      label="版权"
                      name="copyright"
                    >
                      <CopyrightInfo
                        v-if="aiTagTab === 'copyright'"
                        :aid="archive.aid"
                      />
                    </el-tab-pane>
                  </el-tabs>
                </div>
              </div>
            </div>
            <!-- 第三行 P序 -->
            <Playlet :playletInfo="playletInfo" />
            <div class="grid">
              <ClipList
                :aid="archive.aid"
                :bvid="archive.bvid"
                :showVideoToggle="video.showVideoToggle"
                :sourceFile="video.sourceFile"
                :videos.sync="video.clips"
                :disabled="true"
                :videoTitleHighlightKeywords="videoTitleHighlightKeywords"
                :listType="listType"
                :listReview="listReview"
                :clipNumber="video.clipNumber"
                :highlightArea="archive.highlightArea"
                @updateVideosCompetitor="handleUpdateVideosCompetitor"
                @updateVisibleClipList="(val) => (visibleClipList = val)"
                @show-video-preview="showPreviewVideo"
                @hide-video-preview="hidePreviewVideo"
              />
            </div>
          </div>
        </template>
      </template>
      <!-- 高级设置 -->
      <template v-else>
        <!-- 稿件侧负责联动 @updateNoReprint="handleUpdateNoReprint" -->
        <!-- 可变更archive -->
        <Advanced
          v-bind.sync="advanced"
          :attrForm.sync="attrForm"
          :aid="archive.aid"
          :mid="archive.mid"
          :videos="video.clips"
          :listReview="listReview"
        >
          <template #basic-edit>
            <ArchiveBasicInfo
              :archive.sync="archive"
              :video.sync="video"
              :archiveCover.sync="archiveCover"
              :aid="archive.aid"
              :key="archive.aid"
              :listReview="listReview"
              :enableRedBlue="false"
              :disableSaveTag="disableSaveTag"
              :coverTagClipDisabled="coverTagClipDisabled"
              :tagOptions="tagOptions"
              :listType="listType"
              :xcode2="xcode2"
              @showPreviewVideo="showPreviewVideo"
              @hidePreviewVideo="hidePreviewVideo"
            />
          </template>
        </Advanced>
      </template>
    </template>
    <!-- 确认弹窗 -->
    <Suppress
      :visible="suppressDialogShow"
      @close="handleCloseSuppress"
      @confirm="handleConfirmSuppress"
    />
    <!-- 限流弹窗 -->
    <LimitDialog
      ref="limitDialog"
      :log="getLimitLog"
      :aid="archive.aid"
      :visible="showLimitDialog"
      :businessJudgeInfo="business.businessJudgeInfo || {}"
      @set-visible="(val) => (showLimitDialog = val)"
      @close="showLimitDialog = false"
      @confirm="handleConfirmLimit"
    />
    <ExtraInfoDialog
      :visible.sync="extraInfoVisible"
      :extraInfoList="extraInfoList"
      :multipleReason="operation && operation.multipleReason"
    />
    <ReportDialog
      v-if="reportDialogVisible"
      :visible.sync="reportDialogVisible"
      :businessId="businessId"
      :info="reportInfo"
    ></ReportDialog>
    <VideoPreviewModal
      :visible="!!(showVideoPreviewModal && videoPreview.pIndex)"
      :pIndex="videoPreview.pIndex"
      :aiRiskHint="videoPreview.aiRiskHint || {}"
      :title="videoPreview.title"
      :aid="videoPreview.aid"
      :cid="videoPreview.cid"
      :videosrc="videoPreview.rawFileSource"
      :showExtractor="perms.VIDEO_FRAME_TOOL"
      :shouldFetchClipAiResult="shouldFetchClipAiResult"
      :sliceMap="sliceMap"
      :videoPlaylist="videoPlaylist"
      @close="hidePreviewVideo"
      @update:slice-map="handleSliceMapChange"
    />
  </div>
</template>
<script>
import BasePage from '@/v2/core/base-page'
import ArchiveHistory from '@/v2/biz-components/archive/ArchiveHistory'
import ArchiveInfo from '@/v2/biz-components/archive/ArchiveInfo'
import SubmitLog from '@/v2/biz-components/archive/SubmitLog'
import StickyNav from '@/v2/biz-components/archive/StickyNav'
import MissionCard from '@/v2/biz-components/archive/MissionCard'
import TopicCard from '@/v2/biz-components/archive/TopicCard.vue'
import Warnings from '@/v2/biz-components/archive/Warnings'
import ArchiveCover from '@/v2/biz-components/archive/ArchiveCover'
import ArchiveSource from '@/v2/biz-components/archive/ArchiveSource'
import BusinessOrder from '@/v2/biz-components/archive/BusinessOrder'
import AuditOperation from '@/v2/biz-components/archive/AuditOperation.vue'
import Advanced from '@/v2/biz-components/archive/Advanced'
import ClipList from '@/v2/biz-components/archive/ClipList'
import CopyrightInfo from '@/v2/biz-components/archive/CopyrightInfo'
import VideosCompetitor from '@/v2/biz-components/archive/VideosCompetitor.vue'
import routeHelper from '@/mixins/route-helper'
import ArchiveError from '@/v2/biz-components/archive/ArchiveError'
import Playlet from '@/v2/biz-components/archive/Playlet'
import { mapActions, mapState } from 'vuex'
import { ArchiveAuditService } from '@/v2/service/archive/archive-audit'
import { getBackUrl } from '@/utils'
import Suppress from '@/components/Common/Suppress'
import LimitDialog from '@/v2/biz-components/archive/LimitDialog'
import { getLimitLog } from '@/v2/biz-components/archive/LimitForm'
import ExtraInfoDialog from '@/components/ExtraInfoDialog.vue'
import { SHOW_LIMIT_BUTTON } from '@/v2/data-source/config/local/constant.js'
import trackMixin from '@/mixins/track.mixin'
import auditHotkeysMixin from '@/v2/biz-pages/workbench/archive/mixins/audit-hotkeys.js'
import notify from '@/lib/notify'
import trackResourceTimeMinxin from '@/mixins/workbench/trackResourceTime.js'
import ReportDialog from '@/v2/biz-components/archive/ReportDialog.vue'
import VideoPreviewModal from '@/components/TaskDetail/archive/VideoPreviewModal'
import CommunityAiSummary from '@/v2/biz-components/archive/CommunityAiSummary'
import { archiveApi } from '@/api/index'
import ArchiveBasicInfo from '@/v2/biz-components/archive/ArchiveBasicInfo.vue'

export default {
  extends: BasePage,
  mixins: [routeHelper, trackMixin, auditHotkeysMixin, trackResourceTimeMinxin],
  components: {
    Advanced,
    ArchiveCover,
    ArchiveError,
    ArchiveHistory,
    ArchiveInfo,
    ArchiveSource,
    AuditOperation,
    BusinessOrder,
    ClipList,
    CommunityAiSummary,
    CopyrightInfo,
    ExtraInfoDialog,
    LimitDialog,
    MissionCard,
    Playlet,
    ReportDialog,
    StickyNav,
    SubmitLog,
    Suppress,
    TopicCard,
    VideoPreviewModal,
    VideosCompetitor,
    Warnings,
    ArchiveBasicInfo
  },
  props: {
    pageService: {
      type: Object,
      default() {
        return {
          archiveAuditService: ArchiveAuditService
        }
      }
    }
  },
  watch: {
    // FMP
    dataReady(val) {
      if (val) {
        this.$nextTick(() => {
          this.trackFMP()
        })
        this.paginatedVideosCompetitor = this.videosCompetitor
      }
    },
    hasCommunityAiSummary: {
      handler(val) {
        if (val) this.aiTagTab = 'community'
      },
      immediate: true
    }
  },
  data() {
    return {
      actionList: [
        {
          name: '开放浏览',
          value: '0'
        },
        {
          name: '打回',
          value: '-2'
        },
        {
          name: '锁定',
          value: '-4'
        }
      ],
      aiTagTab: 'copyright',
      showErrorPage: false,
      dataReady: false,
      isAdvanced: false,
      // 重新压制视频的弹窗
      suppressDialogShow: false,
      xcode2: undefined,
      // 限流弹窗
      showLimitDialog: false,
      manualLimit: false, // 手动限流通知
      // 基础信息
      aid: '',
      listType: '00',
      listReview: -1,
      businessId: '',
      // 下方是数据
      // 高级设置
      advanced: {},
      // 稿件信息
      archive: {},
      // 稿件封面
      archiveCover: {},
      // 社区机审
      communityAiSummary: {},
      // 稿件历史
      history: {},
      // 任务模块
      mission: {},
      // 操作栏模块
      operation: {},
      // 导航栏模块
      stickyNav: {},
      // 视频模块
      video: {},
      // 警告栏
      warnings: {
        // TODO:找不到有推广位信息的数据，请产品确认
        promotePos: [],
        deviceWarning: [],
        userWarning: []
      },
      // 操作日志
      submitLog: {},
      // 商单模块
      business: {},
      // 重置用的数据
      resetData: {},
      // 属性位和禁止项表单
      attrForm: {},
      // 接口返回的部分属性位和禁止项
      resetUpdateAttr: {},
      // 稿件tag选项
      tagOptions: [],
      // 提取信息卡片
      extraInfoVisible: false,
      extraInfoList: [],
      // 提示
      tips: [],
      dialogShow: false,
      // 话题
      topic: {},
      paginatedVideosCompetitor: [],
      // 微短剧信息
      playletInfo: {},
      // 报备弹窗
      reportDialogVisible: false,
      reportInfo: null,
      // 是否显示分 p 预览弹窗
      showVideoPreviewModal: false,
      sliceMap: {},
      // 视频竞品识别
      videosCompetitor: [],
      // 正在预览的分 p 视频
      videoPreview: {},
      videoPlaylist: [],
      visibleClipList: [], // 当前可见的分p列表
      cacheValidateReasonRes: '',
      cacheValidateNoteRes: ''
    }
  },
  computed: {
    ...mapState({
      arctypeMap: (state) => state.arctype.arctypeMap
    }),
    // 日志
    getLimitLog() {
      return getLimitLog(this.operation?.auditTags, this.attrForm)
    },
    needSuppressToast() {
      // 自制变转载且有权限就会弹
      if (
        this.perms.XCODE2 &&
        this.resetData.archive.copyright === 1 &&
        this.archive.copyright === 2
      ) {
        return true
      } else {
        return false
      }
    },
    showLimit() {
      return this.perms.LIMIT_NOTIFY
    },
    showCancelLimit() {
      return (
        this.perms.LIMIT_NOTIFY &&
        this.archive.limitNotify &&
        SHOW_LIMIT_BUTTON.includes(this.listType)
      )
    },
    showCopyright() {
      return this.perms.ARCHIVE_COPYRIGHT
    },
    hasCommunityAiSummary() {
      if (!this.perms.COMMUNITY_AI_KNOWLEDGE || !this.communityAiSummary)
        return false
      if (this.communityAiSummary?.archiveAiHint) return true
      return !!this.communityAiSummary?.aiHintByClip
    },
    shouldFetchClipAiResult() {
      const currentCid = this.videoPreview?.cid
      if (!currentCid) return false
      return !!this.communityAiSummary?.aiHintByClip?.[currentCid]
    },
    showNoticeTabs() {
      return this.showCopyright || this.hasCommunityAiSummary
    },
    disableSaveTag() {
      return this.attrForm.is_pgc === 1
    },
    videoTitleHighlightKeywords() {
      return this.archive?.highlightKeyword?.video_title || []
    },
    adorderType() {
      return this.business?.orderInfo?.type || ''
    },
    disableShortcutSubmit() {
      return ['31', '34', '39'].includes(this.listType)
    }
  },
  methods: {
    ...mapActions({
      getArctype: 'arctype/getArctype',
      fetchCommon: 'common/fetchCommon',
      resetEpSlices: 'reason/resetEpSlices'
    }),
    handleUpdateVideosCompetitor(newVideosCompetitor) {
      this.paginatedVideosCompetitor = newVideosCompetitor
    },
    handleUpdateNoReprint(val) {
      this.attrForm.no_reprint = val
    },
    getQuery() {
      const query = this.$route.query

      this.businessId = +query.business_id || 0
      this.aid = query.oid
      this.listType = query.list_type
      const listReview = parseInt(query.review || '', 10)
      this.listReview = Number.isNaN(listReview) ? -1 : listReview
      this.back = query.back || ''
    },
    toastTips() {
      const { warningsProtectNote } = this.archive
      const { tips } = this
      if (warningsProtectNote) {
        notify.warning(warningsProtectNote, 0)
      }
      if (tips && tips.length > 0) {
        tips.forEach((tip) => {
          notify.info(tip, 1500)
        })
      }
    },
    async fetchData() {
      this.getQuery()
      await this.archiveAuditService.getArchive()
      this.trackStart()
      this.toastTips()
    },
    toResetData() {
      this.archiveAuditService.resetArchive()
    },
    toGoBack() {
      const url = getBackUrl(this.back)
      if (url) {
        window.location.href = url
      } else {
        this.$router.push({
          path: '/'
        })
      }
    },
    handleCloseSuppress() {
      this.suppressDialogShow = false
      this.xcode2 = undefined
    },
    handleConfirmSuppress(val) {
      this.suppressDialogShow = false
      if (val === 1) {
        this.xcode2 = 1
      } else {
        this.xcode2 = undefined
      }
      // 跳过校验
      this.submitData({
        skip: true
      })
    },
    async getLimitNotify() {
      const { data } = await archiveApi.getLimitNotify({
        aid: this.$route.query.oid
      })
      this.manualLimit = data?.limit_state || false
    },
    handleConfirmLimit(data) {
      this.archiveAuditService
        .confirmLimit(data)
        .then((msg) => {
          notify.success(msg)
          this.getLimitNotify()
        })
        .catch((e) => {
          console.error(e)
        })
    },
    handleShowLimitDialog(ifLimit) {
      this.$refs.limitDialog && this.$refs.limitDialog.openDialog(ifLimit)
    },
    setDialogShow(payload) {
      this.dialogShow = payload
    },
    async bizOrderConfirmCb() {
      let confirmed = true
      this.setDialogShow(true)
      await this.$confirm(
        '稿件为<b style="color: red">商业稿件</b>，确认是否进行操作？',
        '请注意',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          confirmed = true
        })
        .catch(() => {
          confirmed = false
        })
      this.setDialogShow(false)
      return confirmed
    },
    // 推荐稿件弹窗显示及回调
    async recommendConfirmCb() {
      let confirmTipCall = true
      this.setDialogShow(true)
      await this.$confirm('稿件处于推荐中，是否确认提交？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          confirmTipCall = true
        })
        .catch(() => {
          confirmTipCall = false
        })
      this.setDialogShow(false)
      return confirmTipCall
    },
    // 高粉提交弹窗显示及回调
    submitConfirmCb(warnings, force) {
      return new Promise((resolve) =>
        setTimeout(() => {
          const h = this.$createElement
          this.setDialogShow(true)
          this.$msgbox({
            title: '提示',
            message: h('p', null, [
              h(
                'i',
                {
                  style: 'color: teal;margin-right: 8px;color: var(--orange);',
                  class: 'el-icon-warning'
                },
                ''
              ),
              h('span', null, `${warnings.join('，')}，是否确认提交？`)
            ]),
            $type: 'confirm',
            showCancelButton: true,
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            beforeClose: (action, instance, done) => {
              // done是用来关闭弹窗的
              this.setDialogShow(false)
              done()
            }
          })
            .then((action) => {
              // 当既是高粉稿件（或者有提示），又稿件已被删除时的极端情况，需要带上force（如果原来有的话）
              resolve({
                type: 'success',
                data: {
                  action: 'retry',
                  data: {
                    isForce: force ? true : undefined,
                    isConfirm: true
                  }
                }
              })
            })
            .catch((_) => {
              resolve({
                type: 'error',
                data: {
                  action: 'normal',
                  data: `${warnings.join('，')}，弹窗选择了关闭`
                }
              })
            })
        }, 0)
      )
    },
    doubleConfirmCb(warnings, force) {
      return new Promise((resolve) =>
        setTimeout(() => {
          const h = this.$createElement
          this.setDialogShow(true)
          this.$msgbox({
            title: '提示',
            message: h('p', null, [
              h(
                'i',
                {
                  style: 'color: teal;margin-right: 8px;color: var(--orange);',
                  class: 'el-icon-warning'
                },
                ''
              ),
              h('span', null, warnings)
            ]),
            $type: 'confirm',
            showCancelButton: true,
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            beforeClose: (action, instance, done) => {
              // done是用来关闭弹窗的
              this.setDialogShow(false)
              done()
            }
          })
            .then((action) => {
              resolve({
                type: 'success',
                data: {
                  action: 'retry',
                  data: {
                    isForce: force ? true : undefined,
                    isConfirm: true
                  }
                }
              })
            })
            .catch((_) => {
              resolve({
                type: 'error',
                data: {
                  action: 'normal',
                  data: `${warnings}，弹窗选择了关闭`
                }
              })
            })
        }, 0)
      )
    },
    // 强制覆盖弹窗显示及回调
    forceDialogCb() {
      return new Promise((resolve) => {
        setTimeout(() => {
          const h = this.$createElement
          this.setDialogShow(true)
          this.$msgbox({
            title: '提示',
            message: h('p', null, [
              h(
                'i',
                {
                  style: 'color: teal;margin-right: 8px;color: var(--orange);',
                  class: 'el-icon-warning'
                },
                ''
              ),
              h(
                'span',
                null,
                `稿件已删除，是否覆盖？\n若确认覆盖，请确保该稿件下视频状态可播放。`
              )
            ]),
            $type: 'confirm',
            showCancelButton: true,
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            beforeClose: (action, instance, done) => {
              // done是用来关闭弹窗的
              this.setDialogShow(false)
              done()
            }
          })
            .then((action) => {
              resolve({
                type: 'error',
                data: {
                  action: 'retry',
                  data: {
                    isForce: true
                  }
                }
              })
            })
            .catch((e) => {
              resolve({
                type: 'error',
                data: {
                  action: 'donothing',
                  data: {}
                }
              })
            })
        }, 0)
      })
    },
    handleToggleView() {
      this.cacheValidateReasonRes = this.validateReason()
      this.cacheValidateNoteRes = this.validateNote()
      this.isAdvanced = !this.isAdvanced
    },
    validateReason() {
      const auditOperationRef = this.$refs.auditOperation
      return auditOperationRef
        ? auditOperationRef.validateReason()
        : this.cacheValidateReasonRes
    },
    validateNote() {
      const auditOperationRef = this.$refs.auditOperation
      return auditOperationRef
        ? auditOperationRef.validateNote()
        : this.cacheValidateNoteRes
    },
    async reasonOnErrorCb(reasonError) {
      await this.$alert(reasonError, '请补充驳回理由')
    },
    noteOnErrorCb(noteError) {
      setTimeout(async () => {
        await this.$alert(noteError, '提示')
      }, 100)
    },
    async submitData(appendOptions) {
      // 1.拼接form参数
      try {
        const { type = 'success', data } =
          await this.archiveAuditService.submit(appendOptions)
        if (type === 'success') {
          const { data: actionData } = data
          notify.success(actionData.message || '提交成功')
          this.trackEnd()
          this.toGoBack()
        } else {
          this.resetEnd()
          const { action, data: actionData } = data
          switch (action) {
            case 'message': {
              notify.all(actionData)
              break
            }
            case 'retry': {
              this.submitData(actionData)
              break
            }
            case 'donothing': {
              break
            }
            default: {
              console.error(actionData)
            }
          }
        }
      } catch (e) {
        this.resetEnd()
        console.error(e)
      }
    },
    hotkeysSubmit() {
      if (
        this.dialogShow ||
        this.suppressDialogShow ||
        this.showLimitDialog ||
        this.disableShortcutSubmit
      )
        return
      this.submitData()
    },
    showPreviewVideo(payload) {
      const { clipInfo, videoPlaylist } = payload
      if (
        clipInfo.pIndex === this.videoPreview.pIndex &&
        this.showVideoPreviewModal
      ) {
        this.showVideoPreviewModal = false
      } else {
        this.videoPreview = clipInfo
        this.videoPlaylist = videoPlaylist
        this.showVideoPreviewModal = true
      }
    },
    hidePreviewVideo() {
      this.showVideoPreviewModal = false
    },
    handleSliceMapChange({ pIndex, slices }) {
      this.$set(this.sliceMap, pIndex, slices)
    }
  },
  created() {
    this.fetchData()
    this.getArctype()
    this.resetEpSlices()
    this.fetchCommon()
    this.perms.LIMIT_NOTIFY && this.getLimitNotify()
  }
}
</script>

<style lang="stylus" scoped>
.bff-detail
  height 100% !important
  width 100%
  box-shadow 0 2px 12px 0 rgba(0, 0, 0, 0.1)
  border-radius 2px
  box-sizing border-box
  background var(--content-bg-color)
  >>>.el-form-item__label
    line-height 30px
  >>>.el-form-item__content
    line-height 30px
  >>>.el-textarea textarea
    font-family 'Avenir', Helvetica, Arial, sans-serif
  .base-row
    height 60px
    padding 10px 18px
    font-size 14px
    z-index 20
    line-height 3
    box-shadow 0 2px 4px 0 rgba(0, 0, 0, 0.1)
    .base-div
      display flex
      .default-base-info
        line-height 2.6
        .el-button
          vertical-align top
        .task-info
          color var(--text-color-dark-1)
          margin-right 10px
          em
            color var(--text-color)
            font-weight bold
        .delay-num > em
          color var(--red)
  .grid
    display grid
    margin-bottom 5px
    background var(--bg-color)
    padding 8px
    &:last-child
      margin-bottom 0px
  .grid-wrap
    overflow auto
    // 减去base-row高度
    height calc(100% - 60px)
    padding 10px
    box-sizing border-box
  .custom-operation-wrapper
    margin-right: 20px
    .custom-operation
      background: var(--content-bg-color)
      margin-bottom 10px
      &:last-child
        margin-bottom 0px
</style>
