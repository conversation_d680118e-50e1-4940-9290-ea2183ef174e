<template>
  <!-- 一审视频列表-视频详情页 -->
  <div class="video-detail-v2">
    <Nav
      :aid="aid"
      :cid="cid"
      :bvid="bvid"
      :increment_id="increment_id"
      :filename="video && video.filename"
      :archiveState="archiveState"
      @quit="quit"
    >
      <div class="flex-lr flex-1 flex-ac">
        <div style="margin-left: 40px">
          <el-button size="small" type="primary" @click="onSubmit">
            提交
          </el-button>
          <el-button
            size="small"
            @click="resetForm(video, initAttr, videoAudit, forward)"
          >
            重置
          </el-button>
        </div>
        <BusinessLabels
          style="padding-bottom: 5px"
          vertical
          :value="businessLabels"
        />

        <div>
          <el-button
            type="info"
            size="small"
            @click="onClickExtract"
            class="mr-10 ml-10"
          >
            提取信息
          </el-button>
          <NavFlow
            slot="extra"
            :aid="aid"
            :cid="cid"
            :filename="video && video.filename"
          />
        </div>
      </div>
    </Nav>
    <Detail
      v-if="resource"
      :title="title"
      :highlight="highlight"
      :highlightKeyword="highlightKeyword"
      :videoHighlightKeyword="videoHighlightKeyword"
      :eptitle="eptitle"
      :desc="desc"
      :cover="cover"
      :cover2="cover2"
      :tag="tag"
      :copyright="copyright"
      :author="author"
      :userWarnings="userWarnings"
      :content="content"
      :dynamic="dynamic"
      :missionRule="missionRule"
      :arctypeV1="arctypeV1"
      :source="source"
      :ctime="ctime"
      :mission="mission"
      :topic="topic"
      mode="Detail"
      :aiTagList="aiTagList"
      :videoCopyright="videoCopyright"
      :aiMark="aiMark"
      :cid="video && video.cid"
      :playerData="playerData"
      :archiveAddit="archiveAddit"
      :tipList="tipList"
      :filename="video && video.filename"
      :aid="aid"
      :relatedVideos="relatedVideos"
      :relatedVideoStats="relatedVideoStats"
      :relatedVideoSize="relatedVideoSize"
      :videoshots="videoshots"
      :previewContent="previewContent"
      :disabled="true"
      :userStats="userStats"
      class="detail-layout _track_fmp_ele"
      :area="area"
      :userSubmitHistory="userSubmitHistory"
      :ai_img_urls="ai_img_urls"
      :showCancelMission="form.status === 0"
      :cancel_mission="form.cancel_mission"
      @changeMission="onChangeMission"
      :mission_check="mission_check"
      :toggle_video_src="toggle_video_src"
      :ai_cover_info="ai_cover_info"
      :allScreenshots="allScreenshots"
      @updateScreenshot="updateScreenshot"
    >
      <template #audit-log="{ styles }">
        <el-descriptions-item
          label="操作记录"
          :contentStyle="{
            ...styles.contentStyle,
            padding: 0
          }"
        >
          <div class="operation-log-v2">
            <!-- 1、工作台历史 -->
            <!-- 2、视频历史 -->
            <VideoLog class="video-log" :videoHistory="videoHistory" />
            <!-- 3、撞车提示 TODO showRejectOper  -->
            <CrashWarnings
              :crashWarnings="crashWarnings"
              class="crash-warnings"
              :showPassOper="true"
              :showRejectOper="true"
              :cid="video && video.cid"
              v-track.impression="{
                event: PAGE_TRACK_KEY,
                value: {
                  loc: TRACK_MAP.CRASHWARNING,
                  cid: video && video.cid,
                  trackType: '曝光'
                }
              }"
              @click-crash="onClickCrash"
            />
          </div>
        </el-descriptions-item>
      </template>
      <template #audit-operation="{ styles }">
        <el-descriptions-item
          label="审核操作"
          :contentStyle="styles.contentStyle"
        >
          <VideoOperTip :video="video" />
          <VideoAuditOperV2
            ref="operNode"
            :aid="aid"
            :cid="video && video.cid"
            :disabled="disabled"
            :form="form"
            :filename="video && video.filename"
            :showForbid="showForbid"
            :hide_open="hide_open"
            @change="onChangeForm"
            @changeTagList="onChangeTagList"
            :showSuggest="true"
            :showScreenshot="true"
            :pIndex="-1"
            :allScreenshots="allScreenshots"
          />
        </el-descriptions-item>
      </template>
    </Detail>

    <ExtraInfoDialog
      :visible.sync="extraInfoVisible"
      :extraInfoList="extraInfoList"
    />
  </div>
</template>
<script>
import { mapState, mapActions } from 'vuex'
import { archiveResourceApi, archiveTaskApi, videoApi } from '@/api/index'

import trackerFunc from '@/utils/report'
import {
  washResource,
  getPromotePos,
  genExtraInfoList,
  getSuggest,
  getPicture
} from '@/v2/biz-pages/workbench/video/util.js'
import { cloneDeep } from 'lodash-es'
import {
  BusinessLabels,
  VideoAuditOperV2,
  CrashWarnings,
  VideoLog,
  NavFlow
} from '@/v2/biz-components/workbench/index'
import Detail from '@/v2/biz-pages/workbench/video/Detail.vue'
import Nav from './Nav.vue'
import { getBackUrl } from '@/utils'
import { report } from '@/utils/index'
import trackMixin from '@/mixins/track.mixin'
import { TASK_ATTR_FORM_FIELDS_TEMP } from '@/utils/constant'
import auditHotkeysMixin from '@/v2/biz-pages/workbench/video/mixins/audit-hotkeys.js'
import submitRecheckMixin from '@/v2/biz-pages/workbench/video/mixins/submit-recheck.js'
import { isNil } from 'lodash'
import notify from '@/lib/notify'
import { getEnvConstant } from '@/utils/constant.js'
import ExtraInfoDialog from '@/components/ExtraInfoDialog.vue'
import VideoOperTip from '@/v2/biz-components/workbench/VideoOperTip.vue'
import {
  TRACK_MAP,
  PAGE_TRACK_KEY
} from '@/v2/biz-pages/workbench/video/blocks/ArcInfo.vue'
import devtools from '@/mixins/devtools'

export default {
  components: {
    BusinessLabels,
    Detail,
    VideoAuditOperV2,
    CrashWarnings,
    VideoLog,
    Nav,
    NavFlow,
    ExtraInfoDialog,
    VideoOperTip
  },
  computed: {
    ...mapState({
      uid: (state) => state.user.uid
    }),
    showForbid() {
      return false
    }
  },
  mixins: [trackMixin, auditHotkeysMixin, submitRecheckMixin, devtools],
  watch: {
    showCrashSet: {
      handler(visible) {
        if (!visible) {
          this.form.forward = ''
        }
      }
    },
    'form.status': {
      handler(status) {
        if (status !== 0) {
          this.form.cancel_mission = false
        } else if (this.mission) {
          const cancelMission = this.mission_check?.conclusion === 2 // 不符合 勾选【取消活动】，符合 & 未知 不勾选【取消活动】
          this.form.cancel_mission = cancelMission
        }
      },
      deep: true
    },
    'form.reason_id': {
      handler() {
        if (!this.resetReasonIdFlag) {
          // 操作
          this.syncShowCrashSet()
        } else {
          // 初始化&重置
          this.resetReasonIdFlag = false
        }
      },
      deep: true
    }
  },
  data() {
    return {
      dirty: false,
      cid: '',
      resource: null,

      video: {},
      aid: '',
      bvid: '',
      increment_id: '',

      highlight: {},
      highlightKeyword: {},
      title: '',
      eptitle: '', // 分p标题
      desc: '', // 分p简介
      cover: '',
      cover2: '',
      author: {},
      tag: '',
      videoHighlightKeyword: {}, // 视频敏感词
      disabled: false,
      userWarnings: [],
      content: '', // 简介
      dynamic: '',
      missionRule: {},
      mission: null,
      arctypeV1: '', // UP 对接方
      source: '', // 转载来源
      ctime: null, // 投稿时间
      topic: null, // 话题信息
      aiTagList: [],
      videoCopyright: {},
      aiMark: '',
      crashWarnings: [],
      tipList: [],
      ai_cover_info: {},

      // 播放器
      playerData: {
        playurl: '',
        xcode_state: '',
        mediaInfo: {},
        watermarkState: ''
      },
      archiveAddit: null,
      businessLabels: {},

      videoHistory: [],
      relatedVideos: [],
      relatedVideoStats: {},
      relatedVideoSize: 0,
      videoshots: [],
      previewContent: [],
      form: {
        status: null,
        norank: false,
        noindex: false,
        norecommend: false,
        nosearch: false,
        tids: [], // 审核tag
        gray_tags: [], // 灰标
        reason_id: '',
        reason: '',
        reasonTemplate: '',
        note: '',
        forward: '', // 撞车设置
        position_id: '', // 位置
        cancel_mission: false, // 取消活动是否勾选
        suggests: [], // 修改建议数组
        suggest: '', // 修改建议（拼接后的用于回显）
        picture: [], // 截图（用于回显）
        pictureList: [] // 操作选中的截图列表
      }, // 审核操作
      initForm: {},
      initAttr: {},
      auditSingle: [],
      annotationOpers: [],
      noteRequired: true,
      copyright: '',
      resetFlag: false,
      remark: '', // 备注
      archiveState: {},
      userStats: {},

      forward: '', // 用于回源展示
      showCrashSet: false, // v2操作组件不用
      resetReasonIdFlag: false,
      area: '', // 投稿地区
      second_check: [], // 二次确认
      tagListV2: [], // v2版操作模块 页面上的标签列表

      userSubmitHistory: [],
      extraInfoVisible: false, // 提取信息弹窗
      extraInfoList: [],
      ai_img_urls: [],
      mission_check: null,
      hide_open: false, // 隐藏通过操作
      toggle_video_src: false,
      TRACK_MAP,
      PAGE_TRACK_KEY,
      allScreenshots: [] // 视频模块的截图列表
    }
  },
  methods: {
    ...mapActions({
      fetchCommon: 'common/fetchCommon',
      getArctype: 'arctype/getArctype',
      resetEpSlices: 'reason/resetEpSlices'
    }),
    hotkeysSubmit() {
      this.onSubmit()
    },
    quit() {
      this.$tracker(PAGE_TRACK_KEY, {
        cid: this.cid,
        loc: TRACK_MAP.QUIT,
        trackType: '点击'
      })
      const back = this.$route.query.back || ''
      const url = getBackUrl(back)

      if (url) {
        window.location.href = url
      } else {
        this.$router.push({
          path: '/archive/archive-video-task/resource/list'
        })
      }
    },
    // 提交前校验表单
    validateTaskForm(form, noteRequired) {
      if ([0, -2, -4].indexOf(form.status) === -1) {
        return '请选择审核操作'
      }

      // 新版操作项 打回锁定 需选择位置
      if ((form.status === -2 || form.status === -4) && !form.position_id) {
        return '请选择审核标签'
      }

      if (form.status === -2 && form.tids.length === 0) {
        return '请选择打回审核标签'
      }

      if (form.status === -4 && form.tids.length === 0) {
        return '请选择锁定审核标签'
      }

      if (
        (noteRequired && this.validForbidChange(this.initAttr, form)) ||
        form.status === '10000' // 会员可见 需要备注
      ) {
        if (form.note.length === 0) {
          return '禁止属性变动，请补充备注'
        }
      }

      if (
        form.status === -4 &&
        form.reason_id === getEnvConstant('CRASH_REASON_ID')
      ) {
        // 撞车目标必填 且 必须是字母和数字
        if (!form.forward?.trim()) {
          return '请输入撞车跳转目标'
        }

        const re = /^[0-9a-zA-Z]*$/
        if (!re.test(form.forward?.trim())) {
          return '撞车跳转目标格式不正确'
        }
      }

      if (this.$refs.operNode) {
        return this.$refs.operNode.validataReason()
      }
      return ''
    },
    // 检查禁止项是否有变化
    validForbidChange(initAttr, form) {
      const forbidAttrChanged =
        TASK_ATTR_FORM_FIELDS_TEMP.find((key) => {
          return form[key] !== initAttr[key]
        }) || false
      return forbidAttrChanged
    },
    onSubmit(isForce) {
      const validateResult = this.validateTaskForm(
        cloneDeep(this.form),
        this.noteRequired
      )

      if (validateResult.length !== 0) {
        notify.error(validateResult, 1500)
        return false
      }

      const params = this.getParams(isForce)
      const isBusinessOrder = this.businessLabels?.adorder
      this.recheckConfirm(isForce, false, params, false, isBusinessOrder)
    },
    submit(params) {
      archiveResourceApi
        .submitDetail(params)
        .then(() => {
          notify.success('审核成功')
          this.quit()
        })
        .catch((e) => {
          const msg = e.message || e.msg
          const { code } = e
          if (!msg) {
            notify.error('提交失败')
          } else if (parseInt(code, 10) === 21121) {
            this.$confirm('视频已删除，是否覆盖？', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
              .then(() => {
                return this.onSubmit(true)
              })
              .catch((_) => {})
          } else if (parseInt(code, 10) === 21168) {
            this.$confirm(
              '<span>本视频命中复审2，须回查团队判断复核，如需提交</span><span style="color: var(--red);">请联系回查组长确认</span>',
              '提示',
              {
                dangerouslyUseHTMLString: true,
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }
            )
              .then(() => {
                return this.onSubmit(true)
              })
              .catch((_) => {})
          } else {
            notify.error(msg)
          }
        })
    },
    getParams(isForce) {
      const params = {
        status: this.form.status,
        id: this.video.id,
        cid: this.cid,
        aid: this.aid,
        mid: this.author?.mid,
        eptitle: this.eptitle,
        description: this.desc,
        note: this.form.note,
        reason_id: +this.form.reason_id,
        reason: this.form.reason,
        tids: Array.isArray(this.form.tids)
          ? this.form.tids.join(',')
          : this.form.tids,
        gray_tags: (this.form.gray_tags || []).join(','),
        gray_tag_reason: JSON.stringify(this.$refs.operNode?.grayTagReason),
        gray_tags_before: (this.$refs.operNode?.gray_tags_before || []).join(
          ','
        ),
        norank: this.form.norank,
        noindex: this.form.noindex,
        push_blog: this.form.push_blog,
        norecommend: this.form.norecommend,
        nosearch: this.form.nosearch,
        oversea_block: this.form.oversea_block,
        forward: this.form.forward?.trim(),
        position_id: this.form.position_id,
        position_text: this.form.position_text,
        cancel_mission:
          this.form.cancel_mission && this.form.status === 0 ? 1 : '',
        is_tag61: true,
        tag_names: this.form.full_path_names,
        disable_tag_text: this.form.full_path_names,
        suggest: this.form.status === 0 ? '' : getSuggest(this.form),
        picture:
          this.form.status === 0
            ? ''
            : getPicture({
                pictureList: this.form.pictureList,
                picture: this.form.picture,
                allScreenshots: this.allScreenshots
              })
      }

      if (`${this.form.status}` === '0') {
        params.monitor_tag_id = this.form.monitor_tag_id
        params.monitor_tag_name = this.form.monitor_tag_name
      }

      if (
        !(
          String(this.form.status) === '-4' &&
          String(this.form.reason_id) ===
            String(getEnvConstant('CRASH_REASON_ID'))
        )
      ) {
        delete params.forward
      }

      if (params.status !== -2 && params.status !== -4) {
        delete params.reason
        delete params.reason_id
      }
      // 强制覆盖状态
      if (isForce === true) {
        params.force = 1
      }

      report('submitDetail', { params })
      return params
    },
    onChangeForm(val) {
      this.form = val
    },
    syncShowCrashSet() {
      this.showCrashSet =
        this.form &&
        String(this.form.status) === '-4' &&
        String(this.form.reason_id) ===
          String(getEnvConstant('CRASH_REASON_ID'))
    },
    getQuery() {
      const query = this.$route.query
      this.back = query.back || ''
      this.cid = query.cid || ''
      this.aid = query.aid || ''
    },
    // 获取播放地址
    getPlayUrl(cid) {
      return archiveResourceApi
        .getPlayUrl({ cid })
        .then((res) => {
          this.playerData.playurl = res.data.playurl
          this.toggle_video_src = !!res.data.tips
        })
        .catch((_) => {})
    },
    getAiMark(cid, aid) {
      if (aid && cid) {
        archiveTaskApi
          .getAiMark({
            aid,
            cid
          })
          .then((res) => {
            if (res && res.data && res.data[cid]) {
              this.aiMark = res.data[cid].ai_score
            }
          })
          .catch((_) => {})
      }
    },
    // 获取信息
    async getInfo(cid, aid) {
      await archiveResourceApi
        .getInfo({
          cid
          // aid
        })
        .then((res) => {
          this.allScreenshots = []
          try {
            this.resource = res.data || {}
            const { video_audit = {} } = this.resource
            this.videoAudit = video_audit
            const formatData = washResource(this.resource)
            for (const key in formatData) {
              this[key] = formatData[key]
            }
            this.cid = this.cid || this.video?.cid
            this.resetForm(
              this.video,
              this.initAttr,
              this.videoAudit,
              this.forward
            )

            if (this.cid) {
              this.getPlayUrl(this.cid)
              this.getAiMark(cid, this.aid)
            }
          } catch (e) {
            console.error(e)
            trackerFunc('first-audit-wash-error', { res })
          }
        })
        .catch((_) => {})
    },
    resetForm(video, initAttr, videoAudit, forward) {
      const {
        norank,
        noindex,
        norecommend,
        nosearch,
        oversea_block,
        push_blog
      } = initAttr
      this.initForm = {
        status: video.status,
        reason: videoAudit.reason,
        reasonTemplate: '',
        note: videoAudit.note,
        reason_id: '',
        norank: parseInt(norank, 10),
        noindex: parseInt(noindex, 10),
        norecommend: parseInt(norecommend, 10),
        nosearch: parseInt(nosearch, 10),
        oversea_block: parseInt(oversea_block, 10),
        push_blog: parseInt(push_blog, 10),
        tids: this.$refs.operNode?.getAuditTags(video.status),
        forward,
        position_id: isNil(videoAudit.position_id)
          ? ''
          : videoAudit.position_id,
        cancel_mission: false,
        suggest: videoAudit?.suggest || '',
        picture: videoAudit?.picture || [],
        monitor_tag_id: null, // 没有初始值
        monitor_tag_name: '',
        gray_tags: this.$refs.operNode?.gray_tags_before || []
      }
      this.resetFlag = true
      // 初始化form会触发form.status的改变，将自动初始化重新一审和审核tag

      // 重置&初始化时，依据forward判断是否显示撞车设置模块
      if (forward) {
        this.showCrashSet = true
      }
      this.resetReasonIdFlag = true

      this.form = cloneDeep(this.initForm)
    },
    onChangeTagList(val) {
      this.tagListV2 = val || []
    },
    onClickExtract() {
      // 发请求获取web推广位信息
      // getPromotePos
      // genExtraInfoList
      videoApi
        .getPromotePosInfo({
          aid: this.aid
        })
        .then((res) => {
          const promotePos = getPromotePos(res.data)
          this.extraInfoList = genExtraInfoList({
            bvid: this.bvid,
            title: this.title,
            author: this.author,
            promotePos
          })
          this.extraInfoVisible = true
        })
    },
    onChangeMission(val) {
      this.form.cancel_mission = val
    },
    onClickCrash() {
      this.$tracker(PAGE_TRACK_KEY, {
        loc: TRACK_MAP.CRASHWARNING,
        cid: this.cid,
        trackType: '点击'
      })
    },
    updateScreenshot(val) {
      this.allScreenshots = val
    }
  },
  async created() {
    this.getQuery()
    this.getArctype()
    this.resetEpSlices()

    await this.fetchCommon().catch((_) => {})
    // 2.获取信息
    if (this.cid) {
      await this.getInfo(this.cid, this.aid)
    }
  }
}
</script>
<style lang="stylus" scoped>
>>> .nav-module .info-row .ids {
  width: auto;
}

.video-detail-v2 {
  width: 100%;
}

.operation-log-v2 {
  display: flex;
  flex-direction: row;

  .workbench-log {
    flex: 1;
    border-right: 1px solid var(--border-color-light-2);
    height: 128px;
    overflow: auto;
    padding: 2px;

    >>> p {
      line-height: 1.5;
      font-size: 13px;
      margin-bottom: 4px;
    }
  }

  .video-log {
    flex: 1;
    border-right: 1px solid var(--border-color-light-2);
    display: flex;
    flex-direction: column;
    overflow: auto;
    height: 128px;
    font-size: 13px;
    padding: 2px;

    >>> li {
      margin-bottom: 4px;
    }
  }

  .crash-warnings {
    flex: 1;
    height: 128px;
    overflow: auto;
    padding: 2px;
  }
}
</style>
