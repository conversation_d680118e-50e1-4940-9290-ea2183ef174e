import { BaseService } from '@/v2/core/base-service.js'
import { evalExpValue } from '@/pages/workbench/common.js'
import { get } from 'lodash-es'
import Vue from 'vue'
import { flatTagTree } from '@/v2/biz-utils/handleTagTree'
import {
  ArchiveOid,
  ArchiveTitle,
  Arctype,
  BiliRoomReport,
  Button,
  BvcHubLink,
  ColorText,
  ColumnList,
  ControllerList,
  CustomText,
  DialogTrigger,
  Duration,
  GroupInfo,
  ImgComp,
  Indicator,
  Link,
  LivePictureQaResult,
  LiveRoom,
  RichText,
  StampCollection,
  Tag,
  Text,
  UpInfo,
  StreamPlayer,
  AsrText,
  PreviewImgList,
  ReferenceInfo,
  LiveRoomInfo,
  FansInfo,
  DateText,
  ColEllipsis,
  UpLabelInfo,
  liveRoomInfoCard,
  LiveContentCard,
  LiveListCard,
  TextWrap,
  MarkingCascader,
  GroupContainer,
  List,
  Rich,
  TagList
} from '@/v2/biz-components/workbench/settings/material'

import {
  ArchiveOidProps,
  ArchiveTitleProps,
  ArctypeProps,
  BiliRoomReportProps,
  ButtonProps,
  BvcHubLinkProps,
  CascadingTextProps,
  ColorTextProps,
  ColumnListProps,
  ControllerListProps,
  CustomTextProps,
  DialogTriggerProps,
  DurationProps,
  GroupInfoProps,
  ImgCompProps,
  IndicatorProps,
  LinkProps,
  LivePictureQaResultProps,
  LiveRoomProps,
  RichTextProps,
  StampCollectionProps,
  TagProps,
  TextProps,
  UpInfoProps,
  StreamPlayerProps,
  AsrTextProps,
  PreviewImgListProps,
  ReferenceInfoProps,
  LiveRoomInfoProps,
  FansInfoProps,
  DateTextProps,
  ColEllipsisProps,
  UpLabelInfoProps,
  liveRoomInfoCardProps,
  LiveContentCardProps,
  LiveListCardProps,
  TextWrapProps,
  MarkingCascaderProps,
  GroupContainerProps,
  ListProps,
  RichProps,
  TagListProps
} from '@/v2/biz-components/workbench/settings/material/props.js'

// 缓存下来，避免重复计算
let qaOperList = null

export class TemplateService extends BaseService {
  enumData
  extra
  reasonTags
  originOpers

  getProps(schema, data) {
    const { type, prop } = schema
    const value = evalExpValue(prop, data)
    let propMap
    switch (type) {
      case 'upInfo': {
        propMap = UpInfoProps(schema, value, data)
        break
      }
      case 'groupInfo': {
        propMap = GroupInfoProps(schema, value, data)
        break
      }
      // TODO: 数据源那边去掉依赖content_config
      case 'richText': {
        propMap = RichTextProps(schema, value, data)
        break
      }
      case 'customText': {
        propMap = CustomTextProps(schema, value, data)
        break
      }
      case 'enumText':
      case 'tag': {
        propMap = TagProps(schema, value, data, {
          enums: {
            ...this.enumData,
            ...this.extra
          }
        })
        break
      }
      case 'cascadingText':
        propMap = CascadingTextProps(schema, value, data, {
          enums: this.enumData
        })
        break
      case 'colorText': {
        propMap = ColorTextProps(schema, value, data)
        break
      }
      case 'columnList': {
        propMap = ColumnListProps(schema, value, data)
        break
      }
      case 'controllerList': {
        propMap = ControllerListProps(schema, value, data)
        break
      }
      case 'operation': {
        propMap = ButtonProps(schema, value, data, {
          reasonTags: this.reasonTags
        })
        break
      }
      case 'img': {
        propMap = ImgCompProps(schema, value, data)
        break
      }
      case 'link': {
        propMap = LinkProps(schema, value, data)
        break
      }
      case 'archiveTitle': {
        propMap = ArchiveTitleProps(schema, value, data)
        break
      }
      case 'arctype': {
        propMap = ArctypeProps(schema, value, data)
        break
      }
      case 'archiveOid': {
        propMap = ArchiveOidProps(schema, value, data)
        break
      }
      case 'stampCollection': {
        propMap = StampCollectionProps(schema, value, data)
        break
      }
      case 'indicator': {
        propMap = IndicatorProps(schema, value, data)
        break
      }
      case 'dialogTrigger': {
        propMap = DialogTriggerProps(schema, value, data)
        break
      }
      case 'duration': {
        propMap = DurationProps(schema, value, data)
        break
      }
      case 'liveRoom': {
        propMap = LiveRoomProps(schema, value, data)
        break
      }
      case 'streamPlayer': {
        propMap = StreamPlayerProps(schema, value, data)
        break
      }
      case 'asrText': {
        propMap = AsrTextProps(schema, value, data)
        break
      }
      case 'preview-img-list': {
        propMap = PreviewImgListProps(schema, value, data)
        break
      }
      case 'reference-info': {
        propMap = ReferenceInfoProps(schema, value, data)
        break
      }
      case 'live-room-info': {
        propMap = LiveRoomInfoProps(schema, value, data)
        break
      }
      case 'fans-info': {
        propMap = FansInfoProps(schema, value, data)
        break
      }
      case 'date-text': {
        propMap = DateTextProps(schema, value, data)
        break
      }
      case 'col-ellipsis': {
        propMap = ColEllipsisProps(schema, value, data)
        break
      }
      case 'up-label-info': {
        propMap = UpLabelInfoProps(schema, value, data)
        break
      }
      case 'live-room-info-card': {
        propMap = liveRoomInfoCardProps(schema, value, data)
        break
      }
      case 'live-content-card': {
        propMap = LiveContentCardProps(schema, value, data)
        break
      }
      case 'live-list-card': {
        propMap = LiveListCardProps(schema, value, data)
        break
      }
      case 'text-wrap': {
        propMap = TextWrapProps(schema, value, data)
        break
      }
      case 'marking-cascader': {
        propMap = MarkingCascaderProps(schema, value, data)
        break
      }
      case 'group-container': {
        propMap = GroupContainerProps(schema, value, data)
        break
      }
      case 'bvc-hub-link': {
        propMap = BvcHubLinkProps(schema, value, data)
        break
      }
      case 'live-picture-qa-result': {
        if (!qaOperList) {
          qaOperList = flatTagTree(this.originOpers?.qa || [])
        }
        propMap = LivePictureQaResultProps(schema, value, data, {
          qaOperList
        })
        break
      }
      case 'bili-room-report': {
        propMap = BiliRoomReportProps(schema, value, data)
        break
      }
      case 'list': {
        propMap = ListProps(schema, value, data)
        break
      }
      case 'rich': {
        propMap = RichProps(schema, value, data)
        break
      }
      case 'tag-list': {
        propMap = TagListProps(schema, value, data)
        break
      }
      default: {
        propMap = TextProps(schema, value, data)
        break
      }
    }
    return {
      ...propMap
    }
  }

  getEnumText(mapping, value) {
    if (!mapping) {
      return value
    }
    if (typeof mapping !== 'string') return value
    const enums = get(this, mapping)
    return get(enums, `enum.${value}`) || get(enums, value)
  }

  getRenderComponent(schema) {
    const { type } = schema
    let comp = ''
    switch (type) {
      case 'upInfo': {
        comp = UpInfo
        break
      }
      case 'groupInfo': {
        comp = GroupInfo
        break
      }
      case 'richText': {
        comp = RichText
        break
      }
      case 'customText': {
        comp = CustomText
        break
      }
      case 'cascadingText': {
        comp = Text
        break
      }
      case 'enumText': {
        comp = Text
        break
      }
      case 'colorText': {
        comp = ColorText
        break
      }
      case 'columnList': {
        comp = ColumnList
        break
      }
      case 'controllerList': {
        comp = ControllerList
        break
      }
      case 'tag': {
        comp = Tag
        break
      }
      case 'operation': {
        comp = Button
        break
      }
      case 'img': {
        comp = ImgComp
        break
      }
      case 'link': {
        comp = Link
        break
      }
      case 'archiveTitle': {
        comp = ArchiveTitle
        break
      }
      case 'arctype': {
        comp = Arctype
        break
      }
      case 'archiveOid': {
        comp = ArchiveOid
        break
      }
      case 'stampCollection': {
        comp = StampCollection
        break
      }
      case 'indicator': {
        comp = Indicator
        break
      }
      case 'dialogTrigger': {
        comp = DialogTrigger
        break
      }
      case 'duration': {
        comp = Duration
        break
      }
      case 'liveRoom': {
        comp = LiveRoom
        break
      }
      case 'streamPlayer': {
        comp = StreamPlayer
        break
      }
      case 'asrText': {
        comp = AsrText
        break
      }
      case 'preview-img-list': {
        comp = PreviewImgList
        break
      }
      case 'reference-info': {
        comp = ReferenceInfo
        break
      }
      case 'live-room-info': {
        comp = LiveRoomInfo
        break
      }
      case 'fans-info': {
        comp = FansInfo
        break
      }
      case 'date-text': {
        comp = DateText
        break
      }
      case 'col-ellipsis': {
        comp = ColEllipsis
        break
      }
      case 'up-label-info': {
        comp = UpLabelInfo
        break
      }
      case 'live-room-info-card': {
        comp = liveRoomInfoCard
        break
      }
      case 'live-content-card': {
        comp = LiveContentCard
        break
      }
      case 'live-list-card': {
        comp = LiveListCard
        break
      }
      case 'text-wrap': {
        comp = TextWrap
        break
      }
      case 'marking-cascader': {
        comp = MarkingCascader
        break
      }
      case 'group-container': {
        comp = GroupContainer
        break
      }
      case 'bvc-hub-link': {
        comp = BvcHubLink
        break
      }
      case 'live-picture-qa-result': {
        comp = LivePictureQaResult
        break
      }
      case 'bili-room-report': {
        comp = BiliRoomReport
        break
      }
      case 'list': {
        comp = List
        break
      }
      case 'rich': {
        comp = Rich
        break
      }
      case 'tag-list': {
        comp = TagList
        break
      }
      default: {
        comp = Text
        break
      }
    }
    return comp ? Vue.extend(comp) : ''
  }
}
