import {
  ATTR_FIELDS,
  FORBID_ATTR_SUBMIT,
  UPDATE_ATTR_SUBMIT,
  NEED_NOTE_ATTR,
  FROM_LIST_MAP,
  FORBID_ATTR_DELETE,
  VIDEO_STATES,
  FORBID_TEXT_MAP
} from '@/v2/data-source/config/local/constant.js'
import { confirmClassArchive } from '@/v2/biz-utils/classVideoConfirm.js'
import { detailApi, archiveApi } from '@/v2/api'
import { cloneDeep } from 'lodash-es'
import { BaseService } from '@/v2/core/base-service'
import { richToRawText, richToServiceV2 } from '@/utils/rich'
import { ArchiveDetailService } from '@/v2/service/archive/archive-detail'
import genOperationTags from '@/v2/biz-utils/archiveOperationTags'
import { overrideAllLimitReasons } from '@/v2/biz-utils/validateLimitReason'
import notify from '@/lib/notify'
import store from '@/store'
import qs from 'qs'

// 稿件审核的service
export class ArchiveAuditService extends BaseService {
  // 显式声明需要使用的vm属性, 初始化时会对声明的属性进行代理转发, 用于明确service所依赖的属性
  // 分离纯ui属性 和 数据逻辑处理的属性
  // 稿件信息
  advanced
  archive
  archiveCover
  communityAiSummary
  history
  mission
  operation
  stickyNav
  video
  warnings
  submitLog
  business
  attrForm
  tagOptions
  resetData
  //
  listReview
  forceDialogCb
  resetUpdateAttr
  arcAttributes
  submitConfirmCb
  recommendConfirmCb
  bizOrderConfirmCb
  suppressDialogShow
  xcode2
  showLimitDialog
  needSuppressToast
  listType
  topic
  videosCompetitor
  doubleConfirmCb
  rawAttr
  validateReason
  validateNote
  reasonOnErrorCb
  noteOnErrorCb
  enableRedBlue
  playletInfo
  communityNegativeOperLimitCb
  // 权限
  perms

  // service内部使用的变量scope, 纯业务逻辑
  service$ = {
    local: 0
  }

  // 依赖的service，默认值为Service Class, 当前class实例化时会自动实例化依赖service
  archiveDetailService = ArchiveDetailService

  async getArchive() {
    const res = await this.archiveDetailService.getArchive()

    this.resetData = cloneDeep(res?.data)
  }

  resetArchive() {
    this.archiveDetailService.applyData(cloneDeep(this.resetData))
  }

  // 获取数据
  extractFormData() {
    const {
      aid: id,
      title,
      mid,
      mtime,
      copyright,
      publishTime: ptime,
      dynamic,
      content,
      descFormatId: desc_format_id,
      source,
      delay,
      delayTime,
      tags,
      neutralMark,
      auditversion
    } = this.archive
    const {
      rejectReason: reject_reason,
      rawRejectReasonId,
      multipleReason,
      actionList,
      state,
      sendnotify,
      auditTags,
      grayTags: frontendGrayTags = [],
      note
    } = this.operation
    const reject_reason_id = multipleReason?.[0]?.reason_id || rawRejectReasonId
    const { cancelMission: cancel_mission } = this.mission
    const { cancelTopic: cancel_topic } = this.topic

    const { fullCover: cover } = this.archiveCover
    const {
      isPorder: is_porder,
      industryId: industry_id,
      brandId: brand_id,
      brandName: brand_name,
      official,
      showType: show_type,
      advertiser,
      agent,
      groupId: group_id,
      showFront: show_front,
      porderNotify: porder_notify,
      businessJudgeInfo
    } = this.business
    const { enable: judgeEnable, type: judgeType } = businessJudgeInfo || {}
    const {
      limitComments: limit_comments,
      policyId: policy_id,
      redirectUrl: redirect_url
    } = this.advanced
    const { neutralMark: oldNeutralMark } = this.resetData.archive || {}
    // 2.拼接后的form
    const bindState = actionList.find((item) => item.value === `${state}`)
    const { noteTags, auditTagsContent } = this.getAuditTags(auditTags)
    const { grayTags, hasGrayTagChange } = this.getGrayTags(frontendGrayTags)
    // 3. 处理逃单研判信息
    const initBusiness = this.resetData.business
    const { businessJudgeInfo: initBizJudgeInfo } = initBusiness || {}
    const judgeTypeChanged = !(judgeType === initBizJudgeInfo?.type)
    const business_judge_type = judgeTypeChanged ? judgeType : ''
    const business_judge =
      judgeEnable === initBizJudgeInfo?.enable && !judgeTypeChanged
        ? ''
        : judgeEnable
    // form的
    const qsUrlObj = qs.parse(window.location.href)
    const [noteTagReason, grayTagReason] = overrideAllLimitReasons(
      this.operation.noteTagReason,
      this.operation.grayTagReason
    )
    const form = cloneDeep({
      id,
      title,
      mid,
      mtime,
      cancel_mission,
      cancel_topic,
      reject_reason,
      reject_reason_id,
      copyright,
      ptime,
      content: richToRawText(content),
      content_v2: richToServiceV2(content),
      dynamic: richToRawText(dynamic),
      dynamic_v2: richToServiceV2(dynamic),
      desc_format_id,
      source,
      cover,
      note,
      delay,
      dtime: delayTime,
      tag: tags.join(','),
      state,
      sendnotify,
      // 下面是商单boForm的信息
      boForm: {
        porder_notify,
        industry_id,
        brand_id,
        brand_name,
        official,
        show_type,
        advertiser,
        agent,
        group_id,
        show_front
      },
      reason_multi:
        Array.isArray(multipleReason) && multipleReason.length
          ? JSON.stringify(multipleReason)
          : '',
      note_tag: noteTags,
      note_tag_reason: noteTagReason ? JSON.stringify(noteTagReason) : '[]',
      gray_tag: grayTags,
      gray_tag_reason: grayTagReason ? JSON.stringify(grayTagReason) : '[]',
      has_gray_tag_change: hasGrayTagChange,
      has_common_forbid_change: this.checkIfCommonForbidChanged(),
      noteTagContent: auditTagsContent,
      ...this.attrForm,
      // NOTE:，attrForm里也有is_porder属性位
      is_porder,
      limit_comments,
      policy_id,
      redirect_url,
      business_judge,
      business_judge_type,
      is_appeal: qsUrlObj.from_v2 === 'true',
      remove_neutral_mark: !!oldNeutralMark && neutralMark === '',
      auditversion,
      from: 'list_frontend',
      operation_tags: genOperationTags({
        archiveState: state,
        reasonTagId: multipleReason?.map((e) => e.reason_tag_id),
        noteTag:
          Array.isArray(auditTags) && auditTags.length
            ? auditTags.map((e) => e.tag_id)
            : [],
        grayTag:
          Array.isArray(frontendGrayTags) &&
          frontendGrayTags.length &&
          hasGrayTagChange
            ? frontendGrayTags.map((e) => e[e.length - 1])
            : [],
        noteText: note
      })
    })
    if (this.listReview >= 0) {
      form.from_list = FROM_LIST_MAP[this.listReview] || '' // 旧版 from_review
      form.from_review = this.listReview
    }

    delete form.show_main
    if (!this.enableRedBlue) delete form.noteTagContent

    // 地区策略选择自定义默认值为1
    if (form.policy_id === -1) {
      form.policy_id = 1
    }

    if (bindState && bindState.bind_id) {
      form.bind_id = bindState.bind_id
    }

    return form
  }

  extractOptions(form) {
    const { contentRecState, secondCheck } = this.archive
    const { noteOptions } = this.operation
    const { readOnly, isBusinessOrder } = this.business
    const resetAttrForm = this.resetData.attrForm

    return {
      noteOptions,
      isBlockState: [-2, -4].includes(+form.state),
      initialBusiness: this.resetData.business,
      initForbidAttrs: {
        dynamicfold: resetAttrForm.dynamicfold,
        hot_down: resetAttrForm.hot_down,
        rank_down: resetAttrForm.rank_down,
        hot_push_whitelist: resetAttrForm.hot_push_whitelist,
        no_recommend_pool: resetAttrForm.no_recommend_pool,
        no_relay: resetAttrForm.no_relay,
        no_share: resetAttrForm.no_share,
        no_share_download: resetAttrForm.no_share_download,
        nochannel: resetAttrForm.nochannel,
        nohot: resetAttrForm.nohot,
        noindex: resetAttrForm.noindex,
        nomobile: resetAttrForm.nomobile,
        norank: resetAttrForm.norank,
        norecommend: resetAttrForm.norecommend,
        nosearch: resetAttrForm.nosearch,
        no_space: resetAttrForm.no_space,
        noweb: resetAttrForm.noweb,
        online_smooth_down: resetAttrForm.online_smooth_down,
        oversea_block: resetAttrForm.oversea_block,
        push_blog: this.rawAttr?.push_blog ?? resetAttrForm.push_blog,
        recommend_apart: resetAttrForm.recommend_apart,
        no_recommend_live: resetAttrForm.no_recommend_live,
        no_recommend_activity: resetAttrForm.no_recommend_activity,
        no_play_ad: resetAttrForm.no_play_ad,
        no_play_ad_recommend: resetAttrForm.no_play_ad_recommend,
        no_play_bgm: resetAttrForm.no_play_bgm,
        no_play_center_card: resetAttrForm.no_play_center_card,
        no_play_comment_bar: resetAttrForm.no_play_comment_bar,
        no_play_negative: resetAttrForm.no_play_negative,
        no_play_tag: resetAttrForm.no_play_tag,
        no_play_toast: resetAttrForm.no_play_toast
      },
      // 这个字段还是得服务端返回
      resetUpdateAttr: cloneDeep(this.resetUpdateAttr),
      readOnly,
      isBusinessOrder,
      initialLimitComments: this.resetData?.advanced?.limitComments,
      contentRecState,
      warnings: this.getArcConfirm({
        fans: this.archive.author?.followerNum,
        views: this.stickyNav.cardInfo?.view,
        isSpecialUp: this.archive.userGroup.some((item) => item.id === 55),
        isSpecialArc: this.archive.arcProtected
      }),
      arcAttributes: {
        only_fav_view: resetAttrForm.only_fav_view,
        no_public: resetAttrForm.no_public
      },
      submitConfirmCb: this.submitConfirmCb,
      forceDialogCb: this.forceDialogCb,
      recommendConfirmCb: this.recommendConfirmCb,
      bizOrderConfirmCb: this.bizOrderConfirmCb,
      listReview: this.listReview,
      secondCheck,
      doubleConfirmCb: this.doubleConfirmCb,
      reasonOnErrorCb: this.reasonOnErrorCb,
      noteOnErrorCb: this.noteOnErrorCb,
      communityNegativeOperLimitCb: this.communityNegativeOperLimitCb
    }
  }

  // 只有数值变化了的才会提交
  interceptForm(form, options) {
    const interceptForm = cloneDeep(form)
    const { resetUpdateAttr, listReview } = options
    const noteUpdateAttrArr = UPDATE_ATTR_SUBMIT.filter(
      (attrKey) =>
        interceptForm[attrKey] === (resetUpdateAttr && resetUpdateAttr[attrKey])
    )
    // 1.部分变量变化了的才会提交
    for (const noteUpdateAttr of noteUpdateAttrArr) {
      if (listReview !== 1 || noteUpdateAttr !== 'nohot') {
        delete interceptForm[noteUpdateAttr]
      }
    }
    // 2.剔除多余禁止项
    for (const forbidAttrKey of FORBID_ATTR_DELETE) {
      delete interceptForm[forbidAttrKey]
    }
    return interceptForm
  }

  // 新的校验逻辑
  getArcConfirm({ fans, views, isSpecialArc, isSpecialUp }) {
    const warnings = []
    // // 10万粉
    // if (fans > 100000) {
    //   warnings.push('高粉用户稿件')
    // }
    // // 10万播放量
    // if (views > 500000) {
    //   warnings.push('高播放量稿件')
    // }
    if (isSpecialArc) {
      warnings.push('镇站之宝稿件')
    }
    if (isSpecialUp) {
      warnings.push('特殊用户稿件')
    }
    if (isSpecialArc || isSpecialUp) {
      warnings.push('下线稿件前请联系分区编辑')
    }
    if (!warnings.length) return null
    return warnings
  }

  // 返回备注结构化
  getAuditTags(auditTags) {
    const auditTagsArr = []
    const auditTagsContent = []
    auditTags.map((tag) => {
      // 有id
      if (tag.tag_id !== null) {
        auditTagsArr.push({
          tag_id: tag.tag_id,
          remark: tag.remark
        })
        auditTagsContent.push(`${tag.tag_name}`)
      }
      return tag
    })
    return {
      auditTagsArr,
      auditTagsContent: auditTagsContent.join(','),
      noteTags: auditTagsArr.length === 0 ? '' : JSON.stringify(auditTagsArr)
    }
  }

  getGrayTags(frontendGrayTags) {
    if (!this.perms.ARCH_GRAY_TAGS_WRITE)
      return { grayTags: '', hasGrayTagChange: false }
    const result = {}
    frontendGrayTags.forEach((fullIds) => {
      if (Array.isArray(fullIds) && fullIds.length) {
        const tagId = fullIds[fullIds.length - 1]
        const pid = fullIds[fullIds.length - 2]
        if (!pid) {
          // 处理一级节点
          result[tagId] = []
        } else if (result[pid]) {
          result[pid].push(tagId)
        } else {
          result[pid] = [tagId]
        }
      }
    })
    return {
      grayTags: JSON.stringify(result),
      hasGrayTagChange:
        JSON.stringify(frontendGrayTags) !==
        JSON.stringify(this.resetData.operation?.grayTags)
    }
  }

  // 校验备注填写
  validHasNote(form, options) {
    const { noteOptions } = options
    const isUserNote = noteOptions && noteOptions.length > 0
    let hasNote = false
    if (isUserNote) {
      hasNote = form.note_tag.length > 0
    } else {
      hasNote = form.note.length > 0
    }
    return {
      valid: hasNote
    }
  }

  // 校验打回理由
  async validateRejectReason(form, options) {
    const { isBlockState, reasonOnErrorCb } = options
    // 不管是单理由提交，还是多理由提交，最后都会使用 multipleReason 字段提交
    let hasEmptyReason = false
    // 非打回状态下删除打回理由
    if (isBlockState === false) {
      delete form.reject_reason
      delete form.reject_reason_id
      delete form.reason_multi
    } else {
      hasEmptyReason = !this.operation.multipleReason?.some((e) => e.reason)
    }
    const reasonError = this.validateReason()

    if (reasonError || hasEmptyReason) {
      if (reasonOnErrorCb && typeof reasonOnErrorCb === 'function') {
        await reasonOnErrorCb(reasonError || '驳回理由不能为空')
      }
      return {
        valid: false,
        payload: {
          type: 'error',
          data: {
            action: 'donothing'
          }
        }
      }
    }
    return { valid: true }
  }

  // 校验结构化备注
  async validateStructNote(form, options) {
    const { noteOnErrorCb } = options
    const noteError = this.validateNote()
    // 新增结构化备注校验
    if (noteError) {
      if (noteOnErrorCb && typeof noteOnErrorCb === 'function') {
        await noteOnErrorCb(noteError)
      }
      return {
        valid: false,
        payload: {
          type: 'error',
          data: {
            action: 'donothing'
          }
        }
      }
    }
    return { valid: true }
  }

  // 校验定时权限
  vaildateDelay(form, options) {
    const { DELAY } = options
    if (
      form.state === 0 &&
      ['-2', '-4'].includes(this.resetData.archive.state) &&
      form.delay &&
      !DELAY
    ) {
      return {
        valid: false,
        payload: {
          type: 'error',
          data: {
            action: 'message',
            data: {
              message: `这是一个定时稿件，但你没有权限，请联系组长或同组有权限的人。权限点：DELAY`,
              type: 'error',
              duration: 5000,
              offset: 5
            }
          }
        }
      }
    }
    return { valid: true }
  }

  // 校验禁止项变动
  validForbid(form, options) {
    const { noteOptions, initForbidAttrs, listReview } = options
    const { valid: hasNote } = this.validHasNote(form, {
      noteOptions
    })
    const hasTurnOnForbid = []
    // 检查禁止项是否有变化, find是查最后一个变化的元素
    Object.keys(initForbidAttrs).map((key) => {
      if (form[key] !== initForbidAttrs[key] && form[key] === 1) {
        hasTurnOnForbid.push(key)
      }
      return key
    })
    const forbidAttrChanged = Object.keys(initForbidAttrs).find((key) => {
      // 热门回查页，开启热门push时当作没变动
      if (key === 'hot_push_whitelist' && listReview === 1) {
        return form.hot_push_whitelist === 1
          ? false
          : form[key] !== initForbidAttrs[key]
      } else if (key === 'nohot' && listReview === 1) {
        // 如果是热门列表，有备注的话，勾选禁热门不算变化
        return form.nohot === 1 && hasNote
          ? false
          : form[key] !== initForbidAttrs[key]
      } else if (key === 'norank' && listReview === 10) {
        // 如果是排行列表，有备注的话，勾选禁排行不算变化
        return form.norank === 1 && hasNote
          ? false
          : form[key] !== initForbidAttrs[key]
      } else if (key === 'push_blog' && listReview === 42) {
        // 花火补绑商单回查, 六限解除 需要根据原始属性做判断
        return form.push_blog === 0
          ? false
          : form.push_blog !== initForbidAttrs[key]
      }
      return form[key] !== initForbidAttrs[key]
    })
    if (forbidAttrChanged && !hasNote) {
      return {
        valid: false,
        payload: {
          type: 'error',
          data: {
            action: 'message',
            data: {
              message: `${forbidAttrChanged}禁止项变动，请补充备注`,
              type: 'info',
              duration: 1000,
              offset: 5
            }
          }
        },
        forbidAttrChanged,
        hasTurnOnForbid
      }
    } else {
      return {
        valid: true,
        forbidAttrChanged,
        hasTurnOnForbid
      }
    }
  }

  // 校验属性项（仅收藏可见等）
  validAttr(form, options) {
    const { arcAttributes, noteOptions } = options
    const tip = []
    let changed = false
    NEED_NOTE_ATTR.map((item) => {
      const { key, name } = item
      if (arcAttributes[key] !== form[key]) {
        changed = true
        tip.push(name)
      }
      return item
    })
    const { valid: hasNote } = this.validHasNote(form, {
      noteOptions
    })
    if (changed && !hasNote) {
      return {
        valid: false,
        payload: {
          type: 'error',
          data: {
            action: 'message',
            data: {
              message: `${tip.join('、')}变动，请补充备注`,
              type: 'info',
              duration: 1000,
              offset: 5
            }
          }
        }
      }
    } else {
      return {
        valid: true
      }
    }
  }

  // 校验商单
  validBusinessOrder(form, options) {
    const { readOnly, isBusinessOrder, initialBusiness } = options
    const { boForm } = form
    if (form.is_porder && !readOnly && !boForm.group_id) {
      return {
        valid: false,
        payload: {
          type: 'error',
          data: {
            action: 'message',
            data: {
              message: `请选择流量套餐`,
              type: 'error',
              duration: 1000,
              offset: 5
            }
          }
        }
      }
    }
    if (!form.is_porder && isBusinessOrder && !boForm.group_id) {
      // 商单稿件只允许修改流量套餐
      return {
        valid: false,
        payload: {
          type: 'error',
          data: {
            action: 'message',
            data: {
              message: `请选择流量套餐`,
              type: 'error',
              duration: 1000,
              offset: 5
            }
          }
        }
      }
    }
    if (form.is_porder && !readOnly) {
      // 私单&有权限，推广品牌必填
      if (!boForm.brand_id && !boForm.brand_name && this.perms?.ARC_PORDER) {
        return {
          valid: false,
          payload: {
            type: 'error',
            data: {
              action: 'message',
              data: {
                message: `请选择推广品牌`,
                type: 'error',
                duration: 1000,
                offset: 5
              }
            }
          }
        }
      }
    }
    if (form.business_judge && !form.business_judge_type) {
      return {
        valid: false,
        payload: {
          type: 'error',
          data: {
            action: 'message',
            data: {
              message: `请选择逃单研判选项`,
              type: 'error',
              duration: 1000,
              offset: 5
            }
          }
        }
      }
    }
    const { businessJudgeInfo: initBizJudgeInfo } = initialBusiness || {}
    const waitingForJudge =
      initBizJudgeInfo?.enable === 1 && initBizJudgeInfo?.type === 1
    const noop = form.business_judge === '' && form.business_judge_type === ''
    // 逃单研判内通道，必须给出研判结果才能提交
    if (form.list_type === '123' && waitingForJudge && noop) {
      return {
        valid: false,
        payload: {
          type: 'error',
          data: {
            action: 'message',
            data: {
              message: `请选择逃单研判结果`,
              type: 'error',
              duration: 1000,
              offset: 5
            }
          }
        }
      }
    }
    return {
      valid: true
    }
  }

  async confirmBizOrder(form, options) {
    const { isBusinessOrder, bizOrderConfirmCb } = options
    if (
      isBusinessOrder &&
      this.resetData.archive.state === '0' &&
      (+form.state === -2 || +form.state === -4)
    ) {
      const confirmCbResult = await bizOrderConfirmCb()
      if (confirmCbResult) {
        return { valid: true }
      }
      return {
        valid: false,
        payload: { type: 'error', data: { action: 'donothing', data: '' } }
      }
    }
    return { valid: true }
  }

  // 校验推荐提示
  async validRecommendConfirm(form, options) {
    const {
      forbidAttrChanged,
      initialLimitComments,
      contentRecState,
      recommendConfirmCb
    } = options

    // 推荐提示
    let confirmTipCall = true
    if (
      (+form.state !== 0 ||
        forbidAttrChanged ||
        form.limit_comments !== initialLimitComments) &&
      contentRecState
    ) {
      confirmTipCall = await recommendConfirmCb()
    }
    if (!confirmTipCall) {
      return {
        valid: false,
        payload: {
          type: 'error',
          data: {
            action: 'normal',
            data: 'rec'
          }
        }
      }
    } else {
      return {
        valid: true
      }
    }
  }

  // 校验高粉提交
  async validSubmitConfirm(form, options) {
    const {
      isConfirm,
      forbidAttrChanged,
      isBlockState,
      warnings,
      submitConfirmCb
    } = options
    // 如果是高粉提交
    if (!isConfirm && (forbidAttrChanged || isBlockState) && warnings) {
      // setTimeout加的原因是，confirm类型的弹窗，element-ui会自动聚焦confirm button按钮，
      // 如果confirm弹窗是按enter回车键显示的，confirm-button 按钮也会监听到，导致弹窗一闪而过直接进then
      const submitConfirmCbResult = await submitConfirmCb(warnings, form.force)
      return {
        valid: submitConfirmCbResult.type === 'success',
        payload: submitConfirmCbResult
      }
    } else {
      return {
        valid: true
      }
    }
  }

  //
  async validDoubleConfirm(form, options) {
    const {
      secondCheck,
      doubleConfirmCb,
      forbidAttrChanged,
      hasTurnOnForbid,
      isBlockState
    } = options
    if (!secondCheck?.length) {
      return {
        valid: true
      }
    } else {
      // 1.先命中维度高粉，高播，时政UP
      // 2.操作时打回/锁定提交，或者勾选勾选禁止项提交， 或者取消/设置定时
      // 打回/锁定提交
      const stateStr = `${isBlockState ? VIDEO_STATES[form.state] : ''}`
      // 勾选勾选禁止项提交
      const forbidStr = `${
        forbidAttrChanged && hasTurnOnForbid.length > 0
          ? `勾选${hasTurnOnForbid
              .map((item) => `"${FORBID_TEXT_MAP[item]}"`)
              .join('、')}`
          : ''
      }`
      // 或者取消/设置定时
      const delayStr = `${
        this.resetData.archive.delay !== form.delay
          ? form.delay
            ? '启用定时'
            : '取消定时'
          : ''
      }`
      const totalStr = [stateStr, forbidStr, delayStr]
        .filter((item) => item)
        .join('，')

      if (!totalStr) {
        // 其余场景无需校验
        return {
          valid: true
        }
      }

      const warnings = `当前稿件命中【${secondCheck.join(
        '、'
      )}】，确认执行【${totalStr}】`
      const doubleConfirmCbResult = await doubleConfirmCb(warnings, form.force)
      return {
        valid: doubleConfirmCbResult.type === 'success',
        payload: doubleConfirmCbResult
      }
    }
  }

  async appendSubSumit(originForm, options) {
    const { initForbidAttrs } = options
    const form = cloneDeep(originForm)
    let hasChanged = false
    for (let i = 0; i < FORBID_ATTR_SUBMIT.length; i++) {
      const key = FORBID_ATTR_SUBMIT[i]
      // 如果是禁止项，过滤调note,note_tag
      if (initForbidAttrs[key] !== undefined) {
        if (form[key] !== initForbidAttrs[key]) {
          hasChanged = true
          break
        }
      }
    }
    // 变化过才需要提交
    if (hasChanged) {
      const forbidForm = {}
      for (const attrKey of FORBID_ATTR_SUBMIT) {
        forbidForm[attrKey] = form[attrKey] ?? ''
      }
      forbidForm.aid = form.id
      forbidForm.note_tag = form.note_tag

      await archiveApi
        .submitForbid(forbidForm)
        .then((_) => {})
        .catch((e) => {
          // throw error，外层捕获，不进行后续操作，停留在此稿件
          console.error(e)
          throw new Error('禁止项提交失败')
        })
    }
  }

  // 提交接口
  async submit(appendOptions = {}) {
    const form = this.extractFormData()
    form.is_fixed_oper = true
    if (this.xcode2) {
      form.xcode2 = this.xcode2
    }
    const options = {
      ...this.extractOptions(form),
      ...appendOptions
    }
    const {
      listReview,
      noteOptions,
      auditTags,
      initForbidAttrs,
      readOnly,
      isBusinessOrder,
      isBlockState,
      isConfirm,
      initialBusiness,
      initialLimitComments,
      contentRecState,
      warnings,
      resetUpdateAttr,
      arcAttributes,
      submitConfirmCb,
      recommendConfirmCb,
      bizOrderConfirmCb,
      forceDialogCb,
      skip,
      isForce,
      secondCheck,
      doubleConfirmCb
    } = options

    // 强制覆盖状态
    if (isForce === true) {
      form.force = 1
    }
    // 带上list_type
    form.list_type = this.listType

    // 如果需要二转弹窗确认
    if (this.needSuppressToast && !skip) {
      this.suppressDialogShow = true
      return {
        type: 'error',
        data: {
          action: 'donothing',
          data: ''
        }
      }
    }

    // 1. 驳回的话校验理由
    const { valid, payload } = await this.validateRejectReason(form, options)
    if (!valid) return payload

    // 校验结构化备注
    const { valid: noteValid, payload: resp } = await this.validateStructNote(
      form,
      options
    )
    if (!noteValid) return resp

    // 2. 校验禁止项
    const {
      valid: forbidValid,
      payload: forbidPayload,
      forbidAttrChanged,
      hasTurnOnForbid
    } = this.validForbid(form, {
      noteOptions,
      auditTags,
      initForbidAttrs,
      listReview
    })

    if (forbidValid === false) {
      return forbidPayload
    }

    // 3.校验定时权限
    const { valid: delayValid, payload: delayPayload } = this.vaildateDelay(
      form,
      {
        DELAY: this.perms.DELAY || false
      }
    )

    if (delayValid === false) {
      return delayPayload
    }

    // 4.校验属性位
    const { valid: attrValid, payload: attrPayload } = this.validAttr(form, {
      arcAttributes,
      noteOptions,
      auditTags
    })

    if (attrValid === false) {
      return attrPayload
    }

    // 5.校验私单, 商单
    const { valid: businessOrderValid, payload: businessOrderPayload } =
      this.validBusinessOrder(form, {
        readOnly,
        isBusinessOrder,
        initialBusiness
      })

    if (businessOrderValid === false) {
      return businessOrderPayload
    } else {
      const boForm = form.boForm
      if (form.is_porder) {
        Object.keys(boForm).forEach((key) => {
          form[key] = boForm[key]
        })
      } else if (isBusinessOrder) {
        form.group_id = boForm.group_id
      }
      delete form.boForm
      // 处理参数
    }

    // 6.已开发商单打锁二次确认
    const { valid: confirmBizOrderValid, payload: confirmBizOrderPayload } =
      await this.confirmBizOrder(form, {
        isBusinessOrder,
        bizOrderConfirmCb
      })

    if (confirmBizOrderValid === false) return confirmBizOrderPayload

    // 7.校验推荐提示
    const { valid: recommendConfirmValid, payload: recommendConfirmPayload } =
      await this.validRecommendConfirm(form, {
        forbidAttrChanged,
        initialLimitComments,
        contentRecState,
        recommendConfirmCb
      })

    if (recommendConfirmValid === false) {
      return recommendConfirmPayload
    }

    // 8.校验高粉提交
    const { valid: submitConfirmValid, payload: submitConfirmPayload } =
      await this.validSubmitConfirm(form, {
        isConfirm,
        forbidAttrChanged,
        isBlockState,
        warnings,
        submitConfirmCb
      })

    if (submitConfirmValid === false) {
      return submitConfirmPayload
    }

    // 9.校验高粉，高播，时政号，
    const { valid: doubleConfirmValid, payload: doubleConfirmPayload } =
      await this.validDoubleConfirm(form, {
        secondCheck,
        doubleConfirmCb,
        forbidAttrChanged,
        hasTurnOnForbid,
        isBlockState
      })

    if (doubleConfirmValid === false) {
      return doubleConfirmPayload
    }

    // 10.参数拦截
    const interForm = this.interceptForm(form, {
      resetUpdateAttr,
      listReview
    })

    // 11.校验是否为课堂稿件
    const { valid: classVideoValid, payload: classVideoPayload } =
      await this.validateClassArchive({ isArchiveFinalState: true })
    if (!classVideoValid) return classVideoPayload

    const apiResult = await detailApi
      .submitDetail(interForm)
      .then(async () => {
        let result = {
          type: 'error',
          data: {}
        }
        await this.appendSubSumit(form, {
          initForbidAttrs,
          auditTags
        })
          .then(() => {
            result = {
              type: 'success',
              data: {
                action: 'goback',
                data: {
                  message: `修改成功`
                }
              }
            }
          })
          .catch((e) => {
            console.error(e)
            notify.error(e)
          })
        return result
      })
      .catch(async (e) => {
        const msg = e.message || e.msg
        const { code } = e
        if (parseInt(code, 10) === 21134) {
          const forceDialogCbResult = await forceDialogCb()
          return forceDialogCbResult
        } else {
          return {
            type: 'error',
            data: {
              action: 'message',
              data: {
                message: msg || `提交失败`,
                type: 'error',
                duration: 1000,
                offset: 5
              }
            }
          }
        }
      })
    return apiResult
    // 1.校验失败，返回弹窗
    // 2.提交错误
    // 3.提交成功
    // 4.提交弹窗

    // const {

    // } = data
  }

  genSnapshot() {
    let {
      advanced,
      archive,
      archiveCover,
      attrForm,
      business,
      communityAiSummary,
      history,
      listType,
      mission,
      operation,
      operTagInfo,
      playletInfo,
      rawAttr,
      resetUpdateAttr,
      stickyNav,
      submitLog,
      tagOptions,
      todoName,
      topic,
      video,
      videosCompetitor,
      warnings
    } = this

    // 稿件历史、提交记录、用户信息使用实时信息, 保留作者信息供稿件新人消费
    archive = cloneDeep(archive)

    return JSON.stringify({
      advanced,
      archive,
      archiveCover,
      attrForm,
      business,
      communityAiSummary,
      history,
      listType,
      mission,
      operation,
      operTagInfo,
      playletInfo,
      rawAttr,
      resetUpdateAttr,
      snapshotVersion: 7,
      stickyNav,
      submitLog,
      tagOptions,
      todoName, // 仅工作台存在
      topic,
      video,
      videosCompetitor,
      warnings,
      workbenchHistory: store?.state?.pageState?.history || ''
    })
  }

  confirmLimit(data) {
    return archiveApi
      .sendLimitNotify({
        ...data,
        aid: this.archive.aid
      })
      .then((res) => {
        // 无需提示
        this.showLimitDialog = false
        if (data.type === 3) {
          return '限流成功'
        } else {
          return '解限成功'
        }
      })
  }

  checkIfCommonForbidChanged() {
    return ATTR_FIELDS.some(
      (forbidName) => this.attrForm[forbidName] !== this.rawAttr[forbidName]
    )
  }

  checkIfCommonForbidAdded() {
    return ATTR_FIELDS.some(
      (forbidName) =>
        this.attrForm[forbidName] !== this.rawAttr[forbidName] &&
        !!this.attrForm[forbidName]
    )
  }

  // 校验是否为课堂稿件
  // 校验打回理由
  async validateClassArchive(params) {
    const { isArchiveFinalState = false } = params || {}
    try {
      if (isArchiveFinalState) {
        await confirmClassArchive(`${this.archive.aid}`, {
          isArchiveFinalState
        })
      } else if (
        !isArchiveFinalState &&
        [-2, -4].includes(+this.operation.state)
      ) {
        await confirmClassArchive(`${this.archive.aid}`)
      }
      return { valid: true }
    } catch {
      return {
        valid: false,
        payload: {
          type: 'error',
          data: {
            action: 'donothing'
          }
        }
      }
    }
  }
}
