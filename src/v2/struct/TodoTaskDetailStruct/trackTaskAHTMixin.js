import { recordAHT, batchReportAHT } from '@/utils/track-AHT/report'

export default {
  methods: {
    recordTaskAHT(event_name, extra_data) {
      if (this.bizConfig?.track_AHT || this.todoConfig?.track_AHT) {
        recordAHT({
          business_id: this.businessId,
          item_id: this.itemId,
          todo_id: this.todoId,
          task_id: this.task?.id,
          event_name,
          extra_data
        })
      }
    },
    batchReportTaskAHT() {
      if (this.bizConfig?.track_AHT || this.todoConfig?.track_AHT) {
        batchReportAHT()
      }
    }
  }
}
