<template>
  <div>
    <TaskNav
      :todoId="todoId"
      :todoName="todoName"
      :task="task"
      :hasTransfer="transList.length > 0"
      :showQuitAfterSubmit="showQuitAfterSubmit"
      :quitAfterSubmit.sync="quitAfterSubmit"
      :dispatch_degrade="dispatch_degrade"
      @quit="onClickQuit"
      @transfer="showTransfer"
    >
      <slot name="nav"></slot>
    </TaskNav>

    <EmptyDetail
      v-if="showEmptyDetail"
      tip="正在获取下一个任务~"
      :noOperation="true"
    />

    <template v-else>
      <Degrade
        v-if="dispatch_degrade"
        v-bind="dispatch_degrade"
        :transList="transList"
        @showTransfer="showTransfer"
        @afterDelete="afterTransfer"
        @refresh="getResource(INITTAG)"
      ></Degrade>
      <slot v-else></slot>
    </template>

    <TimeoutDialog
      :visible.sync="timeoutDialogVisible"
      :delayTime="task && task.timeCount"
      :leftTime="leftTime"
      :addTime="RENEWAL_TIME"
      @confirm="renewalTime"
    />

    <TransferDialog
      v-if="transferDialogVisible"
      :visible.sync="transferDialogVisible"
      :transList="transList"
      :todoConfig="todoConfig"
      @confirm="transfer"
    />

    <PushTipDialog
      :quitConfirmModal="quitConfirmModal"
      quitContent="点击确认将退出，退出后可以再次领取任务进入"
      @confirm="
        () =>
          logout(
            showEmptyDetail ||
              (dispatchConf && dispatchConf.dispatch_off_release_time > 0)
          )
      "
      @cancel="cancelQuit"
    />

    <slot name="cache"></slot>
  </div>
</template>

<script>
import EmptyDetail from '@/components/TaskDetail/EmptyDetail.vue'
import PushTipDialog from '@/pages/workbench/push/components/PushTipDialog.vue'
import { workbenchDetailApi } from '@/api'
import Base from './Base.vue'
import TransferDialog from '../components/TransferDialog.vue'
import TimeoutDialog from '../components/TimeoutDialog.vue'
import TaskNav from '../components/TaskNav.vue'
import notify from '@/lib/notify'
import { report } from '@/utils'
import { mapActions, mapState } from 'vuex'
import Degrade from '../components/Degrade.vue'
import { EVENT_NAME_MAP } from '@/utils/track-AHT/report'
import trackTaskAHTMixin from '@/v2/struct/TodoTaskDetailStruct/trackTaskAHTMixin.js'

const POLLING_TAG = 'POLLING_TAG'
const IMMEDIATE_POLLING_TAG = 'IMMEDIATE_POLLING_TAG'
const INITTAG = 'INITTAG'
const IMMEDIATE_COUNT = 5
const INTERVAL_TIME = {
  POLLING_TAG: 1000,
  IMMEDIATE_POLLING_TAG: 200
}

export default {
  name: 'PushMode',
  extends: Base,
  inject: ['pageEventBus'],
  mixins: [trackTaskAHTMixin],
  components: {
    PushTipDialog,
    EmptyDetail,
    TransferDialog,
    TimeoutDialog,
    TaskNav,
    Degrade
  },
  computed: {
    ...mapState('pageState', ['dispatchConf']),
    showEmptyDetail() {
      return (
        (typeof this.loading === 'undefined' || this.loading) &&
        !this.preLoading
      )
    },
    overReleaseTime() {
      return this.task?.timeCount > this.RELEASE_TIME_INTERVAL
    },
    showQuitAfterSubmit() {
      if (
        (typeof this.loading === 'undefined' || this.loading) &&
        !this.preLoading
      )
        return false
      return this.dispatchConf?.no_dispatch_task
    }
  },
  data() {
    return {
      loading: undefined,
      preLoading: false, // 用于区分即将超时预先加载, 不展示loading态
      submiting: false,
      // 退出弹窗
      quitConfirmModal: {
        isShow: false,
        width: '300px',
        title: '未完成任务情况',
        close: () => {
          this.$emit('quitConfirm', false)
        }
      },
      timeCounterId: null,
      immediateCount: 0, // 快速定时器执行计数, 50ms定时器执行了多少次
      socketTimerId: null, // 轮询接收任务的定时器,
      quitAfterSubmit: false,
      dispatch_degrade: null,
      INITTAG
    }
  },
  watch: {
    'task.timeCount': {
      handler(nextTimeCount) {
        // 如果超过提示时间，且没有提示过，则弹超时提示
        if (
          this.needTimeoutRemind &&
          nextTimeCount > this.timeoutRemindTime &&
          !this.timeoutHasRemind
        ) {
          this.showTimeout()
          return
        }
        // 即将超时场景：如果距离超时不足30s，且没有计时器，且弹窗不展示, 提前开始获取下一个任务
        if (
          this.needTimeoutRelease &&
          nextTimeCount > this.RELEASE_TIME_INTERVAL &&
          this.socketTimerId === null &&
          !this.timeoutDialogVisible
        ) {
          this.preLoading = true
          this.startRun()
        }
      }
    },
    showEmptyDetail(newVal) {
      const structDetail = document.querySelector('.todo-struct-detail')
      if (!structDetail) return
      structDetail.style.overflow = newVal ? 'hidden' : 'visible'
    }
  },
  async created() {
    this.addListeners()
    try {
      await this.login()
    } catch (err) {
      console.error(err)
      return this.backToList(true)
    }
    this.getResource(INITTAG)
    if (this.autoGetOpers) {
      this.getOpers()
    }
    this.getTrans()
  },
  beforeDestroy() {
    this.removeListeners()
    this.clearRun()
    this.clearTimers()
  },
  methods: {
    ...mapActions('pageState', ['updateDispatchConfig', 'updateIsRedblue']),
    onClickQuit() {
      this.backToList()
      this.pageEventBus.$emit('trackQuitClick')
    },
    addListeners() {
      this.pageEventBus.$on('getResource', this.getResource)
      this.pageEventBus.$on('submit', this.submit)
      this.pageEventBus.$on('afterTransfer', this.afterTransfer)
      this.pageEventBus.$on('showTransfer', this.showTransfer)
      this.pageEventBus.$on('transfer', this.transfer)
      this.pageEventBus.$on('backToList', this.backToList)
      this.pageEventBus.$on(
        'beforeRouteLeave',
        this.beforeRouteLeaveInterceptor
      )
    },
    removeListeners() {
      this.pageEventBus.$off('getResource', this.getResource)
      this.pageEventBus.$off('submit', this.submit)
      this.pageEventBus.$off('afterTransfer', this.afterTransfer)
      this.pageEventBus.$off('showTransfer', this.showTransfer)
      this.pageEventBus.$off('transfer', this.transfer)
      this.pageEventBus.$off('backToList', this.backToList)
      this.pageEventBus.$off(
        'beforeRouteLeave',
        this.beforeRouteLeaveInterceptor
      )
    },
    async getResource(tag = INITTAG) {
      this.loading = true
      this.dispatch_degrade = null
      // 如果是快速计时器，则需加计数
      if (tag === IMMEDIATE_POLLING_TAG) {
        this.immediateCount += 1
      }

      let err, res, resource

      try {
        res = await workbenchDetailApi.getTaskPush({
          business_id: this.businessId,
          todo_id: this.todoId,
          is_push: 1,
          todo_ids: this.todoIds
        })
        const single_dispatch = res.data?.single_dispatch
        const dispatch_conf = res.data?.dispatch_conf
        this.updateDispatchConfig(dispatch_conf)
        // 非0场景的服务异常，除了登录失效，其他错误忽略, 继续轮询
        if (res.code === 92805) {
          notify.error('登录失效')
          this.recordTaskAHT(EVENT_NAME_MAP.Timeout_release, {
            code: 92805
          })
          this.backToList(true)
          return
        } else if (res.code === 98024) {
          notify.error('非审核状态不能领取任务')
          this.logout()
          this.backToList(true)
          return
        }

        // DEBUG: 记录非0异常task
        if (res.code !== 0 && single_dispatch?.task) {
          report('workbench-task-error', {
            taskId: single_dispatch?.task?.id,
            todoId: this.todoId
          })
        }
        // 降级
        if (res.code !== 0 && res.data?.dispatch_degrade) {
          this.dispatch_degrade = res.data.dispatch_degrade
          this.dispatch_degrade.message = res.message || ''
          this.clearTimers() // 清空任务计时
          this.clearRun() // 结束轮询
          this.updateTask({
            itemId: this.dispatch_degrade.item_id || 0, // 延迟流转用
            task: { id: this.dispatch_degrade.task_id || 0 } // 延迟流转用
          })
          this.loading = false
          return
        }

        if (single_dispatch?.task) {
          const task = single_dispatch.task
          const todo = single_dispatch.todo

          if (task) {
            if (this.task?.id === task.id) {
              // this.clearRun()
              clearTimeout(this.socketTimerId)
              this.socketTimerId = null
              this.loading = false
              // 如果是同一个任务，则不更新
              return
            }

            if (this.overReleaseTime && this.needTimeoutRelease) {
              this.preLoading = false
              notify.info('已自动领取下个任务')
              this.recordTaskAHT(EVENT_NAME_MAP.Timeout_release)
            }

            const taskTodoId = todo.id // 当前任务对应的todoId
            const taskTodoName = todo.name
            const taskTodoType = todo.type

            // 混排场景，会把其他通道的任务混入当前通道，所以任务的todoId会变更
            // 假如下发任务的todoId与当前通道的todoId不一致时，需要更新
            if (taskTodoId !== this.todoId) {
              this.updateTodo({
                todoId: taskTodoId,
                todoName: taskTodoName,
                todoType: taskTodoType
              })
              this.getTrans()
              if (this.autoGetOpers) {
                this.getOpers()
              }

              // 混排先调用，但不使用，用于对比分析
              await this.getTodoConfig(true)

              // DEBUG：混排场景下，前后不一致
              if (!this.todoId || !taskTodoId) {
                report('mixin-todos', {
                  taskTodoId,
                  taskTodoName,
                  taskTodoType,
                  todoId: this.todoId
                })
              }
            }

            this.clearTimers() // 清空任务计时
            this.updateTask({
              itemId: Number(task.oid),
              task: {
                id: task.id,
                state: task.state,
                timeCount: Number(task.utime) || 0,
                admin_id: task.admin_id,
                gtime: task.gtime
              }
            })

            this.timeCounterId = setInterval(() => {
              this.updateTaskTimeCount(this.task.timeCount + 1)
            }, 1000)

            await this.getHistory() // 需获取到item_id才去获取history
          }

          this.updateTimeoutConfig(dispatch_conf)
          resource = single_dispatch.resource

          const { audit_mode } = resource
          this.updateIsRedblue({ isRedblue: audit_mode === 2 })

          const loadingFinishedCb = () => {
            this.loading = false
          }

          this.pageEventBus.$emit(
            'afterGetResource',
            err,
            resource,
            res,
            loadingFinishedCb
          )

          // 领到任务
          this.recordTaskAHT(EVENT_NAME_MAP.Task_claimed_successfully)

          if (this.autoFinishLoading) {
            loadingFinishedCb()
          }

          this.$nextTick(() => {
            // 获取到详情之后 -> dom渲染完成
            this.recordTaskAHT(EVENT_NAME_MAP.Page_rendering_completed)
          })
        } else {
          // 下一个任务为空, 需重置状态为空
          if (this.loading) {
            // 已loading完则无需重置，防止超时服务在正常服务后回调返回导致错误重置
            this.clearTimers() // 清空任务计时
            this.updateTask({
              itemId: 0,
              task: null
            })
            this.updateTimeoutConfig(dispatch_conf)
          }
        }
      } catch (error) {
        if (this.loading) {
          this.clearTimers()
          this.updateTask({
            itemId: 0,
            task: null
          })
          this.updateTimeoutConfig(null)
          notify.error(error.message)
        }
      }

      if (tag === INITTAG) {
        // 初始请求
        if (!resource) {
          // resource 为空则开始轮询
          this.startRun()
        }
      } else {
        // 轮询请求
        if (resource) {
          // resource 有值则结束轮询
          this.clearRun()
        }
      }
    },
    run(tag) {
      // 定时器间隔
      const intervalTime = INTERVAL_TIME[tag]
      // 下一个定时器类型
      const nextTag =
        this.immediateCount >= IMMEDIATE_COUNT - 1
          ? POLLING_TAG
          : IMMEDIATE_POLLING_TAG
      // 执行逻辑
      this.getResource(tag)
      // 自调用，模拟定时器
      this.socketTimerId = setTimeout(() => {
        this.run(nextTag)
      }, intervalTime)
    },
    /**
     * 开启轮询
     */
    startRun() {
      this.clearRun()
      this.run(IMMEDIATE_POLLING_TAG)
    },
    /**
     * 移除轮询
     */
    clearRun() {
      clearTimeout(this.socketTimerId)
      this.socketTimerId = null
      this.immediateCount = 0
    },
    // 清除任务计时器
    clearTimers() {
      clearInterval(this.timeCounterId)
      this.timeCounterId = null
    },
    cancelQuit() {
      this.$emit('quitConfirm', false)
      this.quitConfirmModal.isShow = false
    },
    async login() {
      await workbenchDetailApi.taskLogin({
        business_id: this.businessId,
        todo_id: this.todoId,
        is_push: 1,
        todo_ids: this.todoIds
      })
    },
    async logout(no_release_task) {
      try {
        await workbenchDetailApi.taskLogout({
          business_id: this.businessId,
          todo_id: this.todoId,
          is_push: 1,
          todo_ids: this.todoIds,
          no_release_task
        })
        this.$emit('quitConfirm', true)
      } catch (error) {
        this.$emit('quitConfirm', false)
        console.error(error)
      }
    },
    async submit(params, autoGetNextParam = true, autoQuitParam = false) {
      let autoGetNext = autoGetNextParam
      let autoQuit = autoQuitParam
      if (this.quitAfterSubmit) {
        // 完成此页后退出：不继续领任务，自动退出
        autoGetNext = false
        autoQuit = true
      }
      if (
        this.submiting ||
        (this.loading && !this.preLoading) ||
        this.timeoutDialogVisible ||
        this.transferDialogVisible ||
        this.quitConfirmModal.isShow
      ) {
        return
      }

      // 拦截耗时为0的任务提交 快捷键场景会触发
      if (
        !this.task ||
        !this.task.timeCount ||
        this.task.timeCount < 1 ||
        this.task.timeCount - this.task.utime < 1
      ) {
        return
      }

      this.submiting = true

      let err, res

      try {
        res = await workbenchDetailApi.submitAuditTask({
          business_id: this.businessId,
          todo_id: this.todoId,
          task_id: this.task.id,
          item_id: this.itemId,
          ...params
        })
        const { tips } = res.data

        if (tips) {
          notify.warning(tips)
          // 特殊处理：构造一个空对象，保证auditversion校验失败后，不继续获取任务
          if (tips.includes('！！请刷新页面！！稿件审核人工提交时mtime校验失败')) {
            err = {
              code: 21531,
              message: '！！请刷新页面！！稿件审核人工提交时mtime校验失败'
            }
          }
        } else {
          notify.success('操作成功')
          this.recordTaskAHT(EVENT_NAME_MAP.Task_submitted_successfully)
        }

        if (autoGetNext) {
          this.getResource(INITTAG)
        }
      } catch (error) {
        err = error
      }

      this.submiting = false
      this.pageEventBus.$emit(
        'afterSubmit',
        err,
        res,
        params,
        autoGetNext,
        autoQuit
      )
      if (!err && autoQuit) {
        this.logout()
        this.backToList(true)
        if (this.quitAfterSubmit) {
          this.$tracker('quit-after-submit')
        }
      }
      this.activeElementBlur()
      this.batchReportTaskAHT()
    },
    afterTransfer(err) {
      if (!err) {
        if (this.quitAfterSubmit) {
          this.logout()
          this.backToList(true)
          this.$tracker('quit-after-submit')
        } else {
          this.getResource(INITTAG)
        }
      }
    },
    beforeRouteLeaveInterceptor(to, from, next) {
      if (this.forceBack) {
        next()
        return
      }

      this.$off('quitConfirm')
      this.quitConfirmModal.isShow = true
      this.$on('quitConfirm', (result) => {
        if (result) {
          next()
        } else {
          next(false)
        }
      })
    }
  }
}
</script>
