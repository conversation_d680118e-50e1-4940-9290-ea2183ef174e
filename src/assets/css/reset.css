/* http://meyerweb.com/eric/tools/css/reset/
   v2.0 | 20110126
   License: none (public domain)
*/

html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
article, aside, canvas, details, embed,
figure, figcaption, footer, header, hgroup,
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
	margin: 0;
	padding: 0;
	border: 0;
	font-size: 100%;
	font: inherit;
	vertical-align: baseline;
}
/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure,
footer, header, hgroup, menu, nav, section {
	display: block;
}
body {
	line-height: 1;
	position: unset !important;
}
ol, ul {
	list-style: none;
}
blockquote, q {
	quotes: none;
}
blockquote:before, blockquote:after,
q:before, q:after {
	content: '';
	content: none;
}
table {
	border-collapse: collapse;
	border-spacing: 0;
}
input, textarea {
	outline: none;
}
a {
	text-decoration: none;
}
.photo-imager-container, .image-container {
  background-color: rgba(0,0,0,0.1) !important
}
.close-button, .image-count-hinter {
  background-color: rgba(0,0,0,0.4) !important
}
::-webkit-scrollbar {
	width: 6px;
	height: 6px;

}
/* 滚动槽 */
::-webkit-scrollbar-track {
	border-radius: 4px;
	opacity: 0;
	transition: opacity .12s ease-out;

}
/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
	border-radius: 4px;
	background-color: hsla(220,4%,58%,.1);
	transition: background-color .3s;
}
/*滚动条的hover样式*/
::-webkit-scrollbar-thumb:hover {
	background-color: hsla(220,4%,58%,.3);
	-webkit-border-radius: 6px;
}
.el-message {
	max-width: 500px
}
.el-message__content {
	width: 100%;
	word-break: break-word;
}
.el-table--small .cell {
	line-height: 18px !important
}
.el-table--small td {
	padding: 6px 0px !important
}
.el-table--medium .cell {
	line-height: 20px !important
}
.el-table--medium td {
	padding: 6px 0px !important
}
.el-drawer:focus {
	outline: none
}
