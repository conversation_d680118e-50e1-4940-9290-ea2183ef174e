// const Mock = require('mockjs') // mockjs 导入依赖模块
const util = require('./util') // 自定义工具模块

// 返回一个函数

module.exports = function(app) {
  // 监听http请求
  app.all('/mock/x/*', function (req, res) {
    // 每次响应请求时读取json文件
    // util.getJsonFile方法定义了如何读取json文件并解析成数据对象
    var str = req.url.split('?')[0]
    var url = str.replace(/\/mock/, '.')
    var json = util.getJsonFile(url + '.json')
    // 使用mockjs，将json传入 Mock.mock 方法中，生成的数据返回给浏览器
    // res.json(Mock.mock(json))
    // 不使用mockjs
    res.send(json)
  })
}
