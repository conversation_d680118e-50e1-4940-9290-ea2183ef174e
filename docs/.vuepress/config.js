const path = require('path')
const proxy = require('http-proxy-middleware')
const target = 'http://uat-manager.bilibili.co'
const inProduction =
  process.env.NODE_ENV === 'production' || process.env.NODE_ENV === 'pre'
console.log('process.env.NODE_ENV: ', process.env.NODE_ENV)
console.log('inProduction: ', inProduction)
function getTarget() {
  return '//uat-manager.bilibili.co'
}

module.exports = {
  base: inProduction ? '/static/' : '',
  title: 'TITLE',
  description: 'DESCRIPTION',
  themeConfig: {},
  port: 80,
  // devServer: {
  //   proxy: {
  //     '/x/admin': {
  //       changeOrigin: true,
  //       secure: false,
  //       onProxyReq(proxyReq) {
  //         proxyReq.setHeader('Referer', getTarget())
  //       },
  //       target: getTarget()
  //     },
  //     '/lds': {
  //       changeOrigin: true,
  //       secure: false,
  //       onProxyReq(proxyReq) {
  //         proxyReq.setHeader('Referer', getTarget())
  //       },
  //       target: getTarget()
  //     },
  //     '/v2': {
  //       changeOrigin: true,
  //       secure: false,
  //       onProxyReq(proxyReq) {
  //         proxyReq.setHeader('Referer', getTarget())
  //       },
  //       target: getTarget()
  //     },
  //     '/x/internal': {
  //       changeOrigin: true,
  //       secure: false,
  //       onProxyReq(proxyReq) {
  //         proxyReq.setHeader('Referer', getTarget())
  //       },
  //       target: getTarget()
  //     },
  //     '/api': {
  //       changeOrigin: true,
  //       secure: false,
  //       onProxyReq(proxyReq) {
  //         proxyReq.setHeader('Referer', getTarget())
  //       },
  //       target: getTarget()
  //     },
  //     '/template': {
  //       changeOrigin: true,
  //       secure: false,
  //       onProxyReq(proxyReq) {
  //         proxyReq.setHeader('Referer', getTarget())
  //       },
  //       target: getTarget()
  //     },
  //     '/x/aegis-qa': {
  //       changeOrigin: true,
  //       secure: false,
  //       onProxyReq(proxyReq) {
  //         proxyReq.setHeader('Referer', getTarget())
  //       },
  //       target: getTarget()
  //     },
  //   },
  // },
  chainWebpack(config) {
    config.resolve.alias.set('@global', path.resolve('./'))
    config.resolve.alias.set('plyrlib', path.resolve('public/libs/plyr'))
    config.resolve.alias.set('@', path.resolve(process.cwd(), 'src'))
    config.module
      .rule('thejs')
      .test(/\.js$/)
      .include.add(path.resolve('src'))
      .add(path.resolve('node_modules/element-ui/packages'))
      .end()
      .use('babel-loader')
      .loader('babel-loader')
      .end()
    function genAssetKey(str) {
      return str.replace(/(\/|\.|-|@|:)/g, '_')
    }
    const getGitLink = function () {
      try {
        return require('child_process')
          .execSync('git remote get-url --all origin')
          .toString()
          .replace(/\s/, '')
      } catch (e) {
        console.log('error', e)
        return Math.random().toString(36).substr(2)
      }
    }
    function processGitLink(gitLink) {
      // from: https://agile:************@git.bilibili.co/yorktown/ct-component.git
      // to: *******************:yorktown/ct-component.git
      let atIndex = gitLink.indexOf('@')
      let latterPart = gitLink.substring(
        atIndex + 'git.bilibili.co/'.length + 1
      )
      // console.log("latterPart: ", latterPart);
      return `*******************:${latterPart}`
    }
    config.output.publicPath(
      inProduction
        ? `http://uat-boss.bilibili.co/york/static_${genAssetKey(
            processGitLink(getGitLink())
          )}/` // 资产平台上线后要修改
        : '/'
    )
    config.module
      .rule('svg')
      .exclude.add([path.resolve('src/assets/icons')])
      .end()

    config.module
      .rule('svg2')
      .test(/src\/assets\/icons\//)
      .include.add([path.resolve('src/assets/icons')])
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
  },
  extraWatchFiles: ['../src/**/*.vue'],
  plugins: [
    '@bilibili-studio/vuepress-plugin-assets',
    [
      'vuepress-plugin-medium-zoom',
      {
        selector: '.asset-img'
      }
    ]
  ]
}
