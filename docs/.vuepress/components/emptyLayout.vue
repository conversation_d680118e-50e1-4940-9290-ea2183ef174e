<template>
  <div class="empty-layout">
    <Content />
    <!-- asset域的iframe，会被挂上本页面的宽高数据 -->
    <iframe
      id="asset_iframe"
      :src="iframeUrl[$page.frontmatter.build_env]"
      width="0"
      height="0"
      style="display: none;"
    />
  </div>
</template>

<script>
export default {
  name: "",
  components: {},
  props: {},
  data() {
    return {
      iframeUrl: {
        prod: '//asset.bilibili.co/agent.html',
        uat: '//uat-asset.bilibili.co/agent.html',
        local: '//asset.bilibili.co/agent.html'
      }
    };
  },
  computed: {},
  watch: {},
  mounted() {
    setInterval(() => {
      this.calculateData();
    }, 500);
    this.calculateData();
  },
  methods: {
    calculateData() {
      let self_width = Math.max(
        document.body.scrollWidth,
        document.body.clientWidth
      );
      let self_height = Math.max(
        document.body.scrollHeight,
        document.body.clientHeight,
        document.querySelector('html').offsetHeight,
      );
      let asset_iframe = document.getElementById("asset_iframe");
      // 通过修改asset_iframe的src-hash传递本页面的宽高，由asset_iframe操作parent里本页面iframe的宽高
      let href = asset_iframe.src,
        index = asset_iframe.src.indexOf("#");
      if (index !== -1) {
        // 有带hash，重写，没有则直接附加
        href = href.slice(0, index); // 截掉hash
      }
      let newSrc = href + "#/demo_height=" + self_height;
      if (newSrc != asset_iframe.src) {
        //仅在改变时赋值，防止一直刷新
        console.log("修改iframe src", newSrc);
        asset_iframe.src = newSrc;
      }
    },
  },
};
</script>
