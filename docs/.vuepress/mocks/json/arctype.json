{"code": 0, "message": "0", "ttl": 1, "data": [{"id": 1, "name": "动画", "state": 0, "children": [{"id": 24, "pid": 1, "name": "MAD·AMV", "state": 0}, {"id": 25, "pid": 1, "name": "MMD·3D", "state": 0}, {"id": 27, "pid": 1, "name": "综合", "state": 0}, {"id": 47, "pid": 1, "name": "短片·手书·配音", "state": 0}, {"id": 86, "pid": 1, "name": "特摄", "state": 0}, {"id": 210, "pid": 1, "name": "手办·模玩", "state": 0}]}, {"id": 3, "name": "音乐", "state": 0, "children": [{"id": 28, "pid": 3, "name": "原创音乐", "state": 0}, {"id": 29, "pid": 3, "name": "音乐现场", "state": 0}, {"id": 30, "pid": 3, "name": "VOCALOID·UTAU", "state": 0}, {"id": 31, "pid": 3, "name": "翻唱", "state": 0}, {"id": 54, "pid": 3, "name": "OP/ED/OST", "state": -1}, {"id": 59, "pid": 3, "name": "演奏", "state": 0}, {"id": 130, "pid": 3, "name": "音乐综合", "state": 0}, {"id": 193, "pid": 3, "name": "MV", "state": 0}, {"id": 194, "pid": 3, "name": "电音", "state": 0}, {"id": 243, "pid": 3, "name": "乐评盘点", "state": 0}, {"id": 244, "pid": 3, "name": "音乐教学", "state": 0}]}, {"id": 4, "name": "游戏", "state": 0, "children": [{"id": 17, "pid": 4, "name": "单机游戏", "state": 0}, {"id": 19, "pid": 4, "name": "Mugen", "state": 0}, {"id": 65, "pid": 4, "name": "网络游戏", "state": 0}, {"id": 121, "pid": 4, "name": "GMV", "state": 0}, {"id": 136, "pid": 4, "name": "音游", "state": 0}, {"id": 171, "pid": 4, "name": "电子竞技", "state": 0}, {"id": 172, "pid": 4, "name": "手机游戏", "state": 0}, {"id": 173, "pid": 4, "name": "桌游棋牌", "state": 0}]}, {"id": 5, "name": "娱乐", "state": 0, "children": [{"id": 71, "pid": 5, "name": "综艺", "state": 0}, {"id": 131, "pid": 5, "name": "Korea相关", "state": -1}, {"id": 137, "pid": 5, "name": "明星综合", "state": 0}, {"id": 241, "pid": 5, "name": "娱乐杂谈", "state": 0}, {"id": 242, "pid": 5, "name": "粉丝创作", "state": 0}]}, {"id": 11, "name": "电视剧", "state": 0, "children": [{"id": 185, "pid": 11, "name": "国产剧", "state": 0}, {"id": 186, "pid": 11, "name": "港台剧", "state": 0}, {"id": 187, "pid": 11, "name": "海外剧", "state": 0}]}, {"id": 13, "name": "番剧", "state": 0, "children": [{"id": 32, "pid": 13, "name": "完结动画", "state": 0}, {"id": 33, "pid": 13, "name": "连载动画", "state": 0}, {"id": 51, "pid": 13, "name": "资讯", "state": 0}, {"id": 152, "pid": 13, "name": "官方延伸", "state": 0}]}, {"id": 23, "name": "电影", "state": 0, "children": [{"id": 83, "pid": 23, "name": "其他国家", "state": 0}, {"id": 145, "pid": 23, "name": "欧美电影", "state": 0}, {"id": 146, "pid": 23, "name": "日本电影", "state": 0}, {"id": 147, "pid": 23, "name": "国产电影", "state": 0}]}, {"id": 36, "name": "知识", "state": 0, "children": [{"id": 39, "pid": 36, "name": "演讲• 公开课", "state": -1}, {"id": 96, "pid": 36, "name": "星海", "state": -1}, {"id": 98, "pid": 36, "name": "机械", "state": -1}, {"id": 122, "pid": 36, "name": "野生技能协会", "state": 0}, {"id": 124, "pid": 36, "name": "社科·法律·心理", "state": 0}, {"id": 201, "pid": 36, "name": "科学科普", "state": 0}, {"id": 207, "pid": 36, "name": "财经商业", "state": 0}, {"id": 208, "pid": 36, "name": "校园学习", "state": 0}, {"id": 209, "pid": 36, "name": "职业职场", "state": 0}, {"id": 228, "pid": 36, "name": "人文历史", "state": 0}, {"id": 229, "pid": 36, "name": "设计·创意", "state": 0}]}, {"id": 119, "name": "鬼畜", "state": 0, "children": [{"id": 22, "pid": 119, "name": "鬼畜调教", "state": 0}, {"id": 26, "pid": 119, "name": "音MAD", "state": 0}, {"id": 126, "pid": 119, "name": "人力VOCALOID", "state": 0}, {"id": 127, "pid": 119, "name": "教程演示", "state": 0}, {"id": 216, "pid": 119, "name": "鬼畜剧场", "state": 0}]}, {"id": 129, "name": "舞蹈", "state": 0, "children": [{"id": 20, "pid": 129, "name": "宅舞", "state": 0}, {"id": 154, "pid": 129, "name": "舞蹈综合", "state": 0}, {"id": 156, "pid": 129, "name": "舞蹈教程", "state": 0}, {"id": 198, "pid": 129, "name": "街舞", "state": 0}, {"id": 199, "pid": 129, "name": "明星舞蹈", "state": 0}, {"id": 200, "pid": 129, "name": "中国舞", "state": 0}]}, {"id": 155, "name": "时尚", "state": 0, "children": [{"id": 157, "pid": 155, "name": "美妆护肤", "state": 0}, {"id": 158, "pid": 155, "name": "穿搭", "state": 0}, {"id": 159, "pid": 155, "name": "时尚潮流", "state": 0}, {"id": 192, "pid": 155, "name": "风尚标", "state": 0}]}, {"id": 160, "name": "生活", "state": 0, "children": [{"id": 21, "pid": 160, "name": "日常", "state": 0}, {"id": 138, "pid": 160, "name": "搞笑", "state": 0}, {"id": 161, "pid": 160, "name": "手工", "state": 0}, {"id": 162, "pid": 160, "name": "绘画", "state": 0}, {"id": 163, "pid": 160, "name": "运动", "state": 0}, {"id": 174, "pid": 160, "name": "其他", "state": 0}, {"id": 175, "pid": 160, "name": "ASMR", "state": -1}, {"id": 239, "pid": 160, "name": "家居房产", "state": 0}]}, {"id": 165, "name": "广告[已删除]", "state": 0, "children": [{"id": 166, "pid": 165, "name": "广告[已删除]", "state": -1}]}, {"id": 167, "name": "国创", "state": 0, "children": [{"id": 153, "pid": 167, "name": "国产动画", "state": 0}, {"id": 168, "pid": 167, "name": "国产原创相关", "state": 0}, {"id": 169, "pid": 167, "name": "布袋戏", "state": 0}, {"id": 170, "pid": 167, "name": "资讯", "state": 0}, {"id": 195, "pid": 167, "name": "动态漫·广播剧", "state": 0}]}, {"id": 177, "name": "纪录片", "state": 0, "children": [{"id": 37, "pid": 177, "name": "人文·历史", "state": 0}, {"id": 178, "pid": 177, "name": "科学·探索·自然", "state": 0}, {"id": 179, "pid": 177, "name": "军事", "state": 0}, {"id": 180, "pid": 177, "name": "社会·美食·旅行", "state": 0}]}, {"id": 181, "name": "影视", "state": 0, "children": [{"id": 85, "pid": 181, "name": "短片", "state": 0}, {"id": 182, "pid": 181, "name": "影视杂谈", "state": 0}, {"id": 183, "pid": 181, "name": "影视剪辑", "state": 0}, {"id": 184, "pid": 181, "name": "预告 资讯", "state": 0}]}, {"id": 188, "name": "科技", "state": 0, "children": [{"id": 95, "pid": 188, "name": "数码", "state": 0}, {"id": 189, "pid": 188, "name": "电脑装机[已删除]", "state": -1}, {"id": 190, "pid": 188, "name": "摄影摄像[已删除]", "state": -1}, {"id": 191, "pid": 188, "name": "影音智能[已删除]", "state": -1}, {"id": 230, "pid": 188, "name": "软件应用", "state": 0}, {"id": 231, "pid": 188, "name": "计算机技术", "state": 0}, {"id": 232, "pid": 188, "name": "科工机械", "state": 0}, {"id": 233, "pid": 188, "name": "极客DIY", "state": 0}]}, {"id": 196, "name": "付费", "state": 0, "children": [{"id": 197, "pid": 196, "name": "综合", "state": 0}]}, {"id": 202, "name": "资讯", "state": 0, "children": [{"id": 203, "pid": 202, "name": "热点", "state": 0}, {"id": 204, "pid": 202, "name": "环球", "state": 0}, {"id": 205, "pid": 202, "name": "社会", "state": 0}, {"id": 206, "pid": 202, "name": "综合", "state": 0}]}, {"id": 211, "name": "美食", "state": 0, "children": [{"id": 76, "pid": 211, "name": "美食制作", "state": 0}, {"id": 212, "pid": 211, "name": "美食侦探", "state": 0}, {"id": 213, "pid": 211, "name": "真香评测", "state": 0}, {"id": 214, "pid": 211, "name": "田园美食", "state": 0}, {"id": 215, "pid": 211, "name": "美食记录", "state": 0}]}, {"id": 217, "name": "动物圈", "state": 0, "children": [{"id": 75, "pid": 217, "name": "动物综合", "state": 0}, {"id": 218, "pid": 217, "name": "喵星人", "state": 0}, {"id": 219, "pid": 217, "name": "汪星人", "state": 0}, {"id": 220, "pid": 217, "name": "大熊猫", "state": 0}, {"id": 221, "pid": 217, "name": "野生动物", "state": 0}, {"id": 222, "pid": 217, "name": "爬宠", "state": 0}]}, {"id": 223, "name": "汽车", "state": 0, "children": [{"id": 176, "pid": 223, "name": "汽车生活", "state": 0}, {"id": 224, "pid": 223, "name": "汽车文化", "state": 0}, {"id": 225, "pid": 223, "name": "汽车极客", "state": 0}, {"id": 226, "pid": 223, "name": "智能出行", "state": 0}, {"id": 227, "pid": 223, "name": "购车攻略", "state": 0}, {"id": 240, "pid": 223, "name": "摩托车", "state": 0}, {"id": 245, "pid": 223, "name": "赛车", "state": 0}]}, {"id": 234, "name": "运动", "state": 0, "children": [{"id": 164, "pid": 234, "name": "健身", "state": 0}, {"id": 235, "pid": 234, "name": "篮球·足球", "state": 0}, {"id": 236, "pid": 234, "name": "竞技体育", "state": 0}, {"id": 237, "pid": 234, "name": "运动文化", "state": 0}, {"id": 238, "pid": 234, "name": "运动综合", "state": 0}]}]}