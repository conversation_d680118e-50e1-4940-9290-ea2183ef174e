import Router from 'vue-router'
import 'element-ui/lib/theme-chalk/index.css'
import ElementUI from 'element-ui'
import ajaxPlugin from '@/plugins/ajax'
import { formatPerms } from '@/utils'
import store from '@/store'

// 引入svg图片，生成symbol
const requireAll = (requireContext) => requireContext.keys().map(requireContext)
const req = require.context('@/assets/icons', false, /\.svg$/)
requireAll(req)

// worker的启动代码放在enhance的最外层即可
if (typeof window != 'undefined') {
  window.worker = require('./mocks/browser').worker
  window.worker.start()
}

export default async function ({
  Vue, // VuePress 正在使用的 Vue 构造函数
  options, // 附加到根实例的一些选项
  router, // 当前应用的路由实例
  siteData, // 站点元数据
  isServer // 当前应用配置是处于 服务端渲染 或 客户端
}) {
  /************* 自动生成内容，不要删除 **************/
  if (typeof window !== 'undefined') {
    window.global = window
  }
  /************************************************/

  /************* 自定义部分 **************/
  Vue.use(ElementUI)
  Vue.use(ajaxPlugin)
  Vue.use(Router)

  Vue.prototype.$EventBus = new Vue()

  options.store = store // 注入store

  store.dispatch('env/setEnv', { host: 'uat-manager.bilibili.co' })
  // 查询用户信息
  const { data } = await Vue.ajax.get('/x/admin/aegis/engine/auth', {
    handle: true
  })
  // 添加分区信息
  store.dispatch('')

  const { uid, username, permissions, admin, nickname } = data
  const perms = formatPerms(permissions)

  store.commit('todoDetail/updateDetail', {
    detail: {
      id: 427205,
      business_id: 18,
      oid: '654405608524480513',
      mid: 1111116511,
      content: '创建投票动态测试 ​1 ',
      extra1: 4,
      extra2: 4,
      extra3: 0,
      extra4: 0,
      extra1s: '1651204079',
      metadata:
        '{"atUids":"","build":"","buvid":"","createScene":"","default_audit_type":0,"dynAiRiskPic":{},"dynAiRiskPicReminder":{},"hit":"用户黑名单","hit_pic_only":0,"ignoreAudit":0,"ip":"","lottery":0,"mobileApp":"","origDynID":"","origDynMid":0,"path":6,"platform":"","preDynID":"","preDynMid":0,"reason":"","reserve_lottery":0}',
      attribute: 0,
      state: 0,
      pubtime: '0001-01-01 00:00:00',
      deltime: '0001-01-01 00:00:00',
      ctime: '2022-04-29 11:48:01',
      mtime: '2022-04-29 11:48:01',
      metas: {
        atUids: '',
        build: '',
        buvid: '',
        createScene: '',
        default_audit_type: 0,
        dynAiRiskPic: {},
        dynAiRiskPicReminder: {},
        hit: '用户黑名单',
        hit_pic_only: 0,
        ignoreAudit: 0,
        ip: '',
        lottery: 0,
        mobileApp: '',
        origDynID: '',
        origDynMid: 0,
        path: 6,
        platform: '',
        preDynID: '',
        preDynMid: 0,
        reason: '',
        reserve_lottery: 0
      },
      extra5: 0,
      extra6: 2,
      extratime1: '0001-01-01 00:00:00',
      octime: '0001-01-01 00:00:00',
      ptime: '0001-01-01 00:00:00',
      audit_mtime: '2022-04-29 11:48:01',
      item_id: 1090471,
      user_info: {
        mid: 1111116511,
        name: '动态服务端自动化测试专用-勿动',
        official: { role: 0, title: '', desc: '', type: -1 },
        follower: 32,
        adult: 2
      },
      user_group: '',
      user_special: '',
      feature_group_info: [
        {
          id: 74274,
          oid: '1111116511',
          group_id: 93,
          group_name: '动态社区先审后发',
          remark: '涉黄'
        }
      ],
      hit: null,
      topic: {},
      historys: [
        '2022-04-29 11:48:03 社区先审后发 业务方 提交[未处理] 结果[待审]'
      ],
      resource: {
        id: 427205,
        business_id: 18,
        oid: '654405608524480513',
        mid: 1111116511,
        content: '创建投票动态测试 ​1 ',
        extra1: 4,
        extra2: 4,
        extra3: 0,
        extra4: 0,
        extra1s: '1651204079',
        metadata:
          '{"atUids":"","build":"","buvid":"","createScene":"","default_audit_type":0,"dynAiRiskPic":{},"dynAiRiskPicReminder":{},"hit":"用户黑名单","hit_pic_only":0,"ignoreAudit":0,"ip":"","lottery":0,"mobileApp":"","origDynID":"","origDynMid":0,"path":6,"platform":"","preDynID":"","preDynMid":0,"reason":"","reserve_lottery":0}',
        attribute: 0,
        state: 0,
        pubtime: '0001-01-01 00:00:00',
        deltime: '0001-01-01 00:00:00',
        ctime: '2022-04-29 11:48:01',
        mtime: '2022-04-29 11:48:01',
        metas: {
          atUids: '',
          build: '',
          buvid: '',
          createScene: '',
          default_audit_type: 0,
          dynAiRiskPic: {},
          dynAiRiskPicReminder: {},
          hit: '用户黑名单',
          hit_pic_only: 0,
          ignoreAudit: 0,
          ip: '',
          lottery: 0,
          mobileApp: '',
          origDynID: '',
          origDynMid: 0,
          path: 6,
          platform: '',
          preDynID: '',
          preDynMid: 0,
          reason: '',
          reserve_lottery: 0
        },
        extra5: 0,
        extra6: 2,
        extratime1: '0001-01-01 00:00:00',
        octime: '0001-01-01 00:00:00',
        ptime: '0001-01-01 00:00:00',
        audit_mtime: '2022-04-29 11:48:01',
        item_id: 1090471,
        user_info: {
          mid: 1111116511,
          name: '动态服务端自动化测试专用-勿动',
          official: { role: 0, title: '', desc: '', type: -1 },
          follower: 32,
          adult: 2
        },
        user_group: '',
        user_special: '',
        feature_group_info: [
          {
            id: 74274,
            oid: '1111116511',
            group_id: 93,
            group_name: '动态社区先审后发',
            remark: '涉黄'
          }
        ],
        hit: null,
        topic: {}
      }
    },
    task: {},
    resource: {
      id: 427205,
      business_id: 18,
      oid: '654405608524480513',
      mid: 1111116511,
      content: '创建投票动态测试 ​1 ',
      extra1: 4,
      extra2: 4,
      extra3: 0,
      extra4: 0,
      extra1s: '1651204079',
      metadata:
        '{"atUids":"","build":"","buvid":"","createScene":"","default_audit_type":0,"dynAiRiskPic":{},"dynAiRiskPicReminder":{},"hit":"用户黑名单","hit_pic_only":0,"ignoreAudit":0,"ip":"","lottery":0,"mobileApp":"","origDynID":"","origDynMid":0,"path":6,"platform":"","preDynID":"","preDynMid":0,"reason":"","reserve_lottery":0}',
      attribute: 0,
      state: 0,
      pubtime: '0001-01-01 00:00:00',
      deltime: '0001-01-01 00:00:00',
      ctime: '2022-04-29 11:48:01',
      mtime: '2022-04-29 11:48:01',
      metas: {
        atUids: '',
        build: '',
        buvid: '',
        createScene: '',
        default_audit_type: 0,
        dynAiRiskPic: {},
        dynAiRiskPicReminder: {},
        hit: '用户黑名单',
        hit_pic_only: 0,
        ignoreAudit: 0,
        ip: '',
        lottery: 0,
        mobileApp: '',
        origDynID: '',
        origDynMid: 0,
        path: 6,
        platform: '',
        preDynID: '',
        preDynMid: 0,
        reason: '',
        reserve_lottery: 0
      },
      extra5: 0,
      extra6: 2,
      extratime1: '0001-01-01 00:00:00',
      octime: '0001-01-01 00:00:00',
      ptime: '0001-01-01 00:00:00',
      audit_mtime: '2022-04-29 11:48:01',
      item_id: 1090471,
      user_info: {
        mid: 1111116511,
        name: '动态服务端自动化测试专用-勿动',
        official: { role: 0, title: '', desc: '', type: -1 },
        follower: 32,
        adult: 2
      },
      user_group: '',
      user_special: '',
      feature_group_info: [
        {
          id: 74274,
          oid: '1111116511',
          group_id: 93,
          group_name: '动态社区先审后发',
          remark: '涉黄'
        }
      ],
      hit: null,
      topic: {}
    },
    delayTasks: '__vue_devtool_undefined__',
    tableData: [],
    attribute: {},
    snapshotDetail: null
  })
  store.commit('todoDetail/updateExtra', {})
  store.commit('todoDetail/updateEnumData', {})
  store.dispatch('user/saveUserInfo', {
    perms,
    username,
    uid,
    admin,
    nickname
  })

  /*************************************/
}
