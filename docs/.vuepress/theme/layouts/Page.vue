// 文件跟随 @bilibili-studio/doc-generator 保持最新，请勿修改
<template>
  <main class="page">
    <slot name="top" />
    <Content class="theme-default-content" />
    <PageEdit />

    <PageNav v-bind="{ sidebarItems }" />
    <div class="iframe-wrapper" ref="iframe_wrapper"></div>
    <slot name="bottom" />
  </main>
</template>

<script>
import PageEdit from "@theme/components/PageEdit.vue";
import PageNav from "@theme/components/PageNav.vue";

export default {
  components: { PageEdit, PageNav },
  props: ["sidebarItems"],
  data() {
    return {
      iframeEl: null,
    };
  },
  watch: {
    "$page.activeDemoLink"(val) {
      console.log("val: ", val);
      if (this.iframeEl) {
        this.iframeEl.src = val;
      }
    },
  },
  mounted() {
    console.log(
      "$page.frontmatter.haveH5Demo: ",
      this.$page.frontmatter.haveH5Demo
    );
    if (this.$page.frontmatter.haveH5Demo) {
      this.iframeEl = document.createElement("iframe");
      this.iframeEl.height = "600";
      this.iframeEl.width = "360";
      this.iframeEl.style.border = "none";
      this.iframeEl.src = this.$page.activeDemoLink;
      console.log("this.$page.activeDemoLink: ", this.$page.activeDemoLink);
      this.$refs.iframe_wrapper.appendChild(this.iframeEl);
      // 默认展示第一个demo
      console.log("this.$refs.iframe_wrapper: ", this.$refs.iframe_wrapper);
    }
  },
};
</script>

<style lang="stylus">
$wrapper
  max-width $contentWidth
  margin 0 auto
  padding 2rem 2.5rem
  @media (max-width: $MQNarrow)
    padding 2rem
  @media (max-width: $MQMobileNarrow)
    padding 1.5rem

.page
  padding-bottom 2rem
  display block

.iframe-wrapper
  position fixed
  right 10px
  bottom 10px
  border-radius 12px
  box-shadow #ebedf0 0 4px 12px
  overflow hidden
</style>
