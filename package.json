{"name": "audit-manager-fe", "version": "1.0.0", "private": true, "description": "审核平台前端", "author": "jianghuirong <<EMAIL>>", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "test:unit": "vue-cli-service test:unit", "lint": "vue-cli-service lint", "cz": "npx git-cz", "dev": "vue-cli-service serve", "dev:mock": "vue-cli-service serve --mode mock", "dev:pre": "vue-cli-service serve --mode pre", "dev:prod": "vue-cli-service serve --mode prod", "dev:uat": "vue-cli-service serve --mode uat", "docs:deploy": "NODE_ENV=production vuepress buildnossr docs", "docs:dev": "vuepress dev docs", "log": "conventional-changelog -p angular -i CHANGELOG.md -s -r 0 && git add CHANGELOG.md", "start": "vue-cli-service serve", "test:unit:coverage": "npm run test:unit -- --coverage --coverageProvider=v8"}, "dependencies": {"@babel/plugin-syntax-dynamic-import": "^7.8.3", "@bilibili-firebird/lib.indexer": "1.0.1", "@bilibili-player/aegis-web-player": "^0.0.20", "@bilibili-player/subtitle-x": "^0.1.1", "@bilibili-studio/clipboard": "^1.0.0", "@bilibili-studio/crypto": "^1.2.0", "@bilibili-studio/media-info": "^2.4.5", "@bilibili-studio/wash": "^0.18.17", "@bilibili/at": "^0.3.0", "@bilibili/awesome-tech-report": "^0.0.3", "@bilibili/b-style": "^3.6.5", "@bilibili/bili-mirror": "^1.6.33", "@bilibili/bili-user": "^1.1.3", "@bilibili/bmg-tracker": "^1.7.0", "@bilibili/bmg-vue2": "^1.8.0", "@bilibili/csr-gray": "^0.1.1", "@bilibili/gitcmd": "^1.0.12", "@bilibili/js-bridge": "^2.0.2", "@bilibili/kv-sdk": "^1.3.1", "@bilibili/plyr": "^2.0.0", "@bilibili/svg-icon-next": "0.0.13", "@bilibili/svg-icon-next-vue2": "^1.1.4", "@bilibili/tiny-http": "^1.0.2", "@bilibili/video-tech-reporter": "^1.0.2", "@bilibili/web-jank-stats": "^0.0.1", "@bplus-common/components-vue2": "^4.30.7", "@lancercomet/vue2-jsx-runtime": "^0.6.0", "@micro-zoe/micro-app": "^0.8.10", "@vue/composition-api": "^1.4.9", "ajax-hook": "^3.0.1", "axios": "^0.18.0", "bytes": "^3.1.1", "core-js": "^3.8.3", "dayjs": "^1.11.11", "devtools-detector": "^2.0.3", "diff": "^5.0.0", "diff2html": "^3.4.16", "echarts": "^5.3.0", "element-ui": "^2.15.6", "file-saver": "^2.0.5", "hls.js": "^1.5.1", "hotkeys-js": "^3.8.7", "husky": "^4.3.6", "intersection-observer": "^0.5.1", "lodash-es": "^4.17.11", "mark.js": "^8.11.1", "moment": "^2.22.2", "mp4box": "^0.5.2", "pinyin-match": "^1.2.5", "popper.js": "^1.16.1", "qs": "^6.10.2", "resize-detector": "^0.1.9", "sortablejs": "^1.10.2", "svg-sprite-loader": "^6.0.11", "tippy.js": "^6.3.1", "uuid": "^8.3.2", "v-jsoneditor": "^1.2.1", "v-viewer": "^1.5.1", "video.js": "^8.3.0", "viewerjs": "^1.10.1", "vue": "^2.7.16", "vue-quill-editor": "^3.0.6", "vue-resource": "^1.5.1", "vue-router": "3.2.0", "vue-socket.io": "^3.0.10", "vue-swatches": "^2.1.1", "vue-top-progress": "^0.7.0", "vuedraggable": "^2.23.2", "vuex": "^3.1.1", "vuex-router-sync": "^5.0.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@bilibili-studio/asset-migrate-helper": "0.0.57", "@bilibili-studio/doc-generator": "0.0.65", "@bilibili-studio/vuepress-plugin-assets": "0.0.74", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-unit-jest": "^5.0.6", "@vue/cli-service": "^5.0.8", "@vue/test-utils": "^1.0.0", "@vue/vue2-jest": "^28.0.0", "autoprefixer": "^9.8.6", "babel-core": "^6.22.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^7.1.1", "babel-plugin-istanbul": "^4.1.1", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-runtime": "^6.23.0", "babel-plugin-transform-vue-jsx": "^4.0.1", "babel-preset-env": "^1.3.2", "babel-register": "^6.22.0", "chalk": "^2.0.1", "commitizen": "^3.0.5", "conventional-changelog-cli": "^2.0.11", "copy-webpack-plugin": "^4.0.1", "cross-spawn": "^5.0.1", "css-loader": "^0.28.0", "cz-conventional-changelog": "^2.1.0", "esbuild-loader": "^4.0.2", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-config-standard": "^17.1.0", "eslint-friendly-formatter": "^3.0.0", "eslint-plugin-html": "^7.0.0", "eslint-plugin-import": "^2.7.0", "eslint-plugin-listeners": "^1.4.0", "eslint-plugin-n": "^15.7.0", "eslint-plugin-node": "^5.2.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-vue": "^8.0.3", "express": "^4.16.4", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "html-loader": "^0.5.5", "html-webpack-plugin": "^5.5.0", "jest": "^28.1.1", "jest-canvas-mock": "^2.4.0", "lint-staged": "^10.5.3", "mockjs": "^1.0.1-beta3", "msw": "^0.36.8", "optimize-css-assets-webpack-plugin": "^3.2.0", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "postcss-url": "^7.2.1", "prettier": "^2.4.1", "rimraf": "^2.6.0", "selenium-server": "^3.0.1", "semver": "^5.3.0", "shelljs": "^0.7.6", "speed-measure-webpack-plugin": "^1.5.0", "stylus": "^0.54.5", "stylus-loader": "^3.0.2", "url-loader": "^0.5.8", "vue-eslint-parser": "^9.3.2", "vue-loader": "^15.7.1", "vue-server-renderer": "^2.6.11", "vue-style-loader": "^3.0.1", "vue-template-compiler": "^2.7.16", "vuepress": "^1.9.7", "vuepress-plugin-medium-zoom": "^1.1.9", "webpack": "^5.0.0"}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "engines": {"node": ">= 8.9.0", "npm": ">= 3.0.0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/v2/**/*.{js,vue}": ["prettier --write", "eslint --fix"]}}