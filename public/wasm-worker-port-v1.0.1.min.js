function _toConsumableArray(e){return _arrayWithoutHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray(e)||_nonIterableSpread()}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray(e,t):void 0}}function _iterableToArray(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray(e)}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,s=new Array(t);r<t;r++)s[r]=e[r];return s}function _extends(){return(_extends=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e}).apply(this,arguments)}function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var r=0;r<t.length;r++){var s=t[r];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),e}!function(){"use strict";var e=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};_classCallCheck(this,e),this.filePath=t.path,this.fetchInit=t.fetchInit||{},this.config=t,this.asmPath=t.asmPath||"",this.memFile=void 0!==t.memFile&&t.memFile,this.asmPath.endsWith&&!this.asmPath.endsWith(".js")&&(this.asmPath=this.asmPath+".js"),this.resourceWasm=null,this.compiledWasm=null,this.resourceJs=null,this.exportModule=null,this.useAsm=!1,this.memRequest=null,this.status="waiting",this.errorMessage="",this.loadingPromise=null}return _createClass(e,[{key:"isSupportedWebAssembly",value:function(){return"object"===("undefined"==typeof WebAssembly?"undefined":_typeof(WebAssembly))&&"function"==typeof WebAssembly.instantiate}},{key:"getLoaderStatus",value:function(){var e={status:this.status};return"error"===this.status&&(e.errorMessage=this.errorMessage),"waiting"!==this.status&&(this.useAsm?(e.useAsm=!0,e.useWasm=!1):(e.useAsm=!1,e.useWasm=!0)),"loaded"===this.status&&(e.Module=this.exportModule),e}},{key:"load",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=t.reload,s=void 0!==r&&r,o=t.asm,n=void 0!==o&&o,a=this.isSupportedWebAssembly();if(a){if(!this.filePath&&!this.asmPath){var i="[WasmLoader] No available file path.";return this.status="error",this.errorMessage=i,Promise.reject(new Error(i))}}else if(!this.asmPath){var u="[WasmLoader] WebAssembly is not supported. You may use asm.js instead.";return this.status="error",this.errorMessage=u,Promise.reject(new Error(u))}if(!this.loadingPromise||s){var l=[];this.resourceWasm=null,this.compiledWasm=null,this.resourceJs=null,this.exportModule=null,this.memRequest=null,this.status="waiting",!this.asmPath||this.filePath&&a&&!n?(l.push(this.loadJsFile()),l.push(this.loadWasmFile()),this.useAsm=!1):(l.push(this.loadJsFile(this.asmPath)),this.memFile&&l.push(this.loadMemFile()),this.useAsm=!0),this.status="loading",this.loadingPromise=Promise.all(l).then((function(){return e.initializeRuntime({asm:n})})).catch((function(t){return e.status="error",e.errorMessage=t&&t.message||"",Promise.reject(t)}))}return this.loadingPromise}},{key:"fetchFile",value:function(e){return fetch(e,this.fetchInit).then((function(e){if(e.ok)return e;var t=new Error("[WasmLoader] Fetch failed with ".concat(e.status," ").concat(e.statusText));return Promise.reject(t)}))}},{key:"compileStreamingWasm",value:function(e){return e.arrayBuffer().then((function(e){return WebAssembly.compile(e)}))}},{key:"loadWasmFile",value:function(){var e=this;return this.resourceWasm?Promise.resolve():(this.resourceWasm?Promise.resolve():this.fetchFile("".concat(this.filePath,".wasm")).then((function(t){e.resourceWasm=t}))).then((function(){return void 0!==WebAssembly.compileStreaming?WebAssembly.compileStreaming(e.resourceWasm).catch((function(){return e.compileStreamingWasm(e.resourceWasm)})):e.compileStreamingWasm(e.resourceWasm)})).then((function(t){e.compiledWasm=t}))}},{key:"loadJsFile",value:function(e){var t=this;return this.resourceJs?Promise.resolve():this.fetchFile(e||"".concat(this.filePath,".js")).then((function(e){return e.text()})).then((function(e){t.resourceJs=e}))}},{key:"loadMemFile",value:function(){var e=this;return this.memRequest?Promise.resolve():new Promise((function(t,r){var s=new XMLHttpRequest;e.memRequest=s,s.open("GET","".concat(e.asmPath,".mem"),!0),s.responseType="arraybuffer",s.onload=function(){200===s.status||0===s.status&&s.response?t(s.response):r(new Error("[WasmLoader] Request .mem file failed with ".concat(s.status)))},s.onerror=function(){r(new Error("[WasmLoader] Request .mem file failed with ".concat(s.status)))},s.send(null)}))}},{key:"initializeRuntime",value:function(e){var t=this,r=e.asm,s=void 0!==r&&r;return this.status="initializing",new Promise((function(e,r){var o=t.compiledWasm,n=t.config.Module?_extends({},t.config.Module):{};s||(n.instantiateWasm=function(s,a){return WebAssembly.instantiate(o,s).then((function(r){a(r,o),t.useAsm=!1,t.status="loaded",t.exportModule=n,e(t.getLoaderStatus())}),(function(e){t.status="error",t.errorMessage=e&&e.message||"",r(e)})),{}}),s&&t.memFile&&(n.memoryInitializerRequest=t.memRequest,n.onRuntimeInitialized=function(){t.useAsm=!0,t.status="loaded",t.exportModule=n,e(t.getLoaderStatus())}),n.print=n.print||function(e){console.log(e)},n.printErr=n.printErr||function(e){console.error(e)},n.onAbort=n.onAbort||function(e){console.error(e)};var a="".concat(t.resourceJs,";return Module;");try{new Function("Module",a)(n)}catch(e){t.status="error",t.errorMessage=e&&e.message||"",r(e)}s&&!t.memFile&&(n.onRuntimeInitialized=function(){t.useAsm=!0,t.status="loaded",t.exportModule=n,e(t.getLoaderStatus())})}))}}]),e}();if("function"==typeof importScripts&&"undefined"!=typeof WorkerGlobalScope){var t=function(e){return e&&("function"==typeof MessagePort&&e instanceof MessagePort||"function"==typeof ArrayBuffer&&e instanceof ArrayBuffer||"function"==typeof ImageBitmap&&e instanceof ImageBitmap)},r=null,s=null,o="worker-port";self.addEventListener("message",(function(n){if(n.data&&n.data.type){var a=n.data.id;switch(n.data.type){case"".concat(o,".load"):var i=n.data.config||{};(r=new e(i)).load({asm:!!i.forceAsm}).then((function(e){s=e.Module,e.type="".concat(o,".loaded"),e.id=a,delete e.Module,self.postMessage(e)})).catch((function(e){self.postMessage({type:"".concat(o,".loadError"),id:a,errorMessage:e&&e.message||""})}));break;case"".concat(o,".getStatus"):if(r){var u=r.getLoaderStatus();u.Module&&delete u.Module,u.type="".concat(o,".getStatusReturn"),u.id=a,self.postMessage(u)}else self.postMessage({type:"".concat(o,".getStatusReturn"),id:a,status:"waiting"});break;case"".concat(o,".callModuleFunction"):if(!n.data.function){self.postMessage({type:"".concat(o,".callModuleFunctionReturn"),function:"",id:a,error:!0,errorMessage:"[WasmWorkerPort] Attribute 'function' is required"});break}if(!s){self.postMessage({type:"".concat(o,".callModuleFunctionReturn"),function:"",id:a,error:!0,errorMessage:"[WasmWorkerPort] Module is not loaded"});break}var l=n.data.function.split(".").reduce((function(e,t){return e&&e[t]?e[t]:void 0}),s);if("function"==typeof l){var c=n.data.args||[],f=l.apply(void 0,_toConsumableArray(c));if(f&&f.then&&"function"==typeof f.then)f.then((function(e){var r={type:"".concat(o,".callModuleFunctionReturn"),function:n.data.function,id:a,error:!1,errorMessage:"",data:e};t(e)?self.postMessage(r,e):self.postMessage(r)}));else{var m={type:"".concat(o,".callModuleFunctionReturn"),function:n.data.function,id:a,error:!1,errorMessage:"",data:f};t(f)?self.postMessage(m,f):self.postMessage(m)}}else self.postMessage({type:"".concat(o,".callModuleFunctionReturn"),function:"",id:a,error:!0,errorMessage:"[WasmWorkerPort] Cannot find function '".concat(n.data.function,"' in Module")})}}}))}}();
